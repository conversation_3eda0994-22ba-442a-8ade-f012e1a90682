_format_version: "2.1"
_transform: true

services:
  - name: reporting
    host: reporting-service
    port: 5000
    routes:
      - name: datainsights
        strip_path: false
        regex_priority: 1
        methods:
          - GET
        paths:
          - /datainsights
          - /datainsights/v1.1/health
          - /datainsights/openapi.json
          - /datainsights/v1.1/reports/[0-9]*/export
          - /datainsights/v1.1/reports/[0-9]+$
          - /datainsights/v1.1/schedules
          - /datainsights/v1.1/charts
          - /datainsights/v1.1/tasks/token/*

      - name: datainsights-options
        strip_path: false
        methods:
          - OPTIONS
        paths:
          - /datainsights

      - name: datainsights-oidc
        strip_path: false
        methods:
          - GET
          - PUT
          - DELETE
          - POST
          - PATCH
        paths:
          - /datainsights/v1.1
          - /datainsights/v1.1/reports/[0-9]*/export/async

    plugins:
      - name: rate-limiting
        route: datainsights
        config:
          minute: 7200
          policy: local

      - name: rate-limiting
        route: datainsights-options
        config:
          minute: 7200
          policy: local

      - name: rate-limiting
        route: datainsights-oidc
        config:
          minute: 7200
          policy: local

      - name: oidc
        route: datainsights-oidc
        config:
          response_type: code
          token_endpoint_auth_method: client_secret_post
          discovery: $KONG_OIDC_DISCOVERY
          introspection_endpoint: $KONG_OIDC_INSTROSPECTION_ENDPOINT
          client_id: $KONG_OIDC_CLIENT_ID
          client_secret: $KONG_OIDC_CLIENT_SECRET
          session_secret: $KONG_OIDC_SESSION_SECRET
          unauth_action: deny
          bearer_jwt_auth_enable: "yes"
          bearer_only: "yes"
          ssl_verify: "no"
