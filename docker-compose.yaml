services:
  reporting-service:
    container_name: reporting-service
    build:
      context: .
      dockerfile: Dockerfile.dev
      secrets:
        - token
    networks:
      - kong-net
    extra_hosts:
      - host.docker.internal:host-gateway
      - kubernetes.docker.internal:host-gateway
    image: reporting-service
    volumes:
      - ./app:/usr/src/app
      - ./celery_app:/usr/src/celery_app
      - ./migrations:/usr/src/migrations
      - ./tests:/usr/src/tests
      - ./local:/usr/src/local
      - ~/.aws:/root/.aws:ro
      - ~/.ssh:/root/.ssh:ro
      - ./dependencies/sqlalchemy_filters:/usr/src/dependencies/sqlalchemy_filters
    ports:
      - 5000:5000
      - 5678:5678
    environment:
      - FLASK_APP=manage.py
      - FLASK_DEBUG=TRUE
      - FLASK_ENV=development
      - DEBUG=1
      - LOG_LEVEL=10
      - ENV=LOCAL
    env_file:
      - .env
    depends_on:
      - reporting-kong
      - reporting-redis
      - reporting-database
      - reporting-aws
    restart: always
    # Command to debug locally
    command: |
      python -m debugpy --listen 0.0.0.0:5678
      -m gunicorn manage:app
      --reload
      --reload-engine=inotify
      --worker-class=gthread
      --bind=0.0.0.0:5000
      --timeout=0
      --log-level=info
      --access-logfile='-'
      --logger-class=wsgi_conf.gunicorn_logger.CustomGunicornLogger
    # Command to test deployment settings
    # command: |
    #   python -m gunicorn manage:app
    #   --reload
    #   --reload-engine=inotify
    #   --preload
    #   --workers=5
    #   --graceful-timeout=300
    #   --max-requests=100000
    #   --max-requests-jitter=20000
    #   --worker-class=gthread
    #   --threads=10
    #   --bind=0.0.0.0:5000
    #   --timeout=30
    #   --log-level=info
    #   --access-logfile='-'
    #   --logger-class=wsgi_conf.gunicorn_logger.CustomGunicornLogger

  reporting-redis:
    container_name: reporting-redis
    ports:
      - "6379:6379"
    networks:
      - kong-net
    extra_hosts:
      - host.docker.internal:host-gateway
    image: redis:7.0.0-alpine
    command: ["redis-server", "--appendonly", "yes"]
    hostname: ${CACHE_REDIS_HOST}
    volumes:
      - reporting-redis:/data

  reporting-database:
    container_name: reporting-database
    networks:
      - kong-net
    extra_hosts:
      - host.docker.internal:host-gateway
    image: postgres:15.3
    expose:
      - ${DATABASE_PORT}
    ports:
      - ${DATABASE_PORT}:${DATABASE_PORT}
    command: -p ${DATABASE_PORT}
    volumes:
      - reporting-database:/var/lib/postgresql/data
    hostname: ${DATABASE_HOST}
    environment:
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_USER=${DATABASE_USER}
      - POSTGRES_DB=${DATABASE_NAME}
      - POSTGRES_PORT=${DATABASE_PORT}

  reporting-aws:
    container_name: reporting-aws
    networks:
      - kong-net
    extra_hosts:
      - host.docker.internal:host-gateway
    image: localstack/localstack:2.1
    hostname: localstack
    ports:
      - "4565:4565"
    environment:
      - EDGE_PORT=4565
      - SERVICES=s3
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DATA_DIR=/tmp/localstack/data
      - HOSTNAME_EXTERNAL=localstack
      - LS_LOG=trace
      - AWS_ACCESS_KEY_ID=${AWS_S3_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_S3_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_REGION}
    env_file:
      - .env
    volumes:
      - .:/usr/src
      - ./reporting-aws-entrypoint.sh:/etc/localstack/init/ready.d/init-aws.sh
      - /var/run/docker.sock:/var/run/docker.sock
  reporting-kong:
    container_name: reporting-kong
    networks:
      - kong-net
    extra_hosts:
      - host.docker.internal:host-gateway
    image: revomatico/docker-kong-oidc:2.8.1-1
    ports:
      - "8000:8000"
      - "8001:8001"
      - "8002:8002"
      - "8443:8443"
    environment:
      KONG_DATABASE: "off"
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_PROXY_LISTEN: "0.0.0.0:8000"
      KONG_ADMIN_LISTEN: "0.0.0.0:8001"
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_DECLARATIVE_CONFIG: "/opt/kong/declarative/kong.yaml"
      KONG_CONNECT_TIMEOUT: 300000
      KONG_READ_TIMEOUT: 300000
      KONG_WRITE_TIMEOUT: 3000000
      KONG_RETRIES: 3
      KONG_PLUGINS: bundled,rate-limiting,oidc
      KONG_X_SESSION_NAME: oidc_session
      NGINX_UPSTREAM_CONNECT_TIMEOUT: 300000
    volumes:
      - ./kong:/opt/kong/declarative/

  # reporting-worker:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.dev
  #     secrets:
  #       - token
  #   deploy:
  #     replicas: 3
  #     resources:
  #       limits:
  #         cpus: "2"
  #         memory: 4G
  #       reservations:
  #         cpus: "1"
  #         memory: 2G
  #   command: watchfiles --filter python 'celery --quiet -A celery_app.make_celery worker --loglevel error -P gevent --prefetch-multiplier 1 --autoscale=8,1'
  #   volumes:
  #     - ./app:/usr/src/app
  #     - ./celery_app:/usr/src/celery_app
  #     - ~/.aws:/root/.aws:ro
  #     - ~/.ssh:/root/.ssh:ro
  #   environment:
  #     - FLASK_APP=manage.py
  #     - FLASK_DEBUG=TRUE
  #     - FLASK_ENV=development
  #     - DEBUG=TRUE
  #     - LOG_LEVEL=10
  #     - ENV=LOCAL
  #   env_file:
  #     - .env
  #   depends_on:
  #     - reporting-service
  #     - reporting-redis
  #   networks:
  #     - kong-net
  #   extra_hosts:
  #     - host.docker.internal:host-gateway

  # reporting-worker-monitor:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.dev
  #     secrets:
  #       - token
  #   container_name: reporting-worker-monitor
  #   ports:
  #     - 5555:5555
  #   command: celery --broker=redis://${CACHE_REDIS_HOST}:${CACHE_REDIS_PORT}// flower
  #   volumes:
  #     - ./app:/usr/src/app
  #     - ./celery_app:/usr/src/celery_app
  #   environment:
  #     - FLOWER_UNAUTHENTICATED_API=true
  #   env_file:
  #     - .env
  #   depends_on:
  #     - reporting-worker
  #     - reporting-redis
  #   networks:
  #     - kong-net
  #   extra_hosts:
  #     - host.docker.internal:host-gateway

volumes:
  reporting-redis:
  reporting-database:

networks:
  kong-net:
    external: false

secrets:
  token:
    environment: "GITHUB_TOKEN"
