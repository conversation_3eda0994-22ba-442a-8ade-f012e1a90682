name: Integration Test on Stage

on:
  workflow_call:

jobs:
  smoke-test-stage-us1:
    name: US1 Smoke Tests 🤖
    runs-on: x8-core
    permissions:
      id-token: write
      contents: read
    timeout-minutes: 60
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/integration-tests
        name: Smoke Test with Association User
        env:
          ENV_PREFIX: stage
          ISLAND: us1
          FULL_ACCESS_EMAIL: <EMAIL>
          AWS_ARN: arn:aws:iam::565955521116:role/GitHub-OIDC-Insights
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          TEST_PATH_PATTERN: tests/integration
          DATA_INSIGHTS_URL: ${{ secrets.DATA_INSIGHTS_STAGE_URL }}
          MFD_API_URL: ${{ secrets.STAGE_BASE_URL }}
          LOG_LEVEL: error
          USERS_TO_NOTIFY: "@UU64VLJFP,@U03KMDLHTPW,@U03DN7L0K37"
          MAKE_COMMAND: "make setup-env-stage-us1-association-owner"
          EXTRA_JEST_ARGS: "--maxWorkers=75%"

      - uses: ./.github/actions/integration-tests
        name: Smoke Test with Super Admin User
        if: always()
        env:
          ENV_PREFIX: stage
          ISLAND: us1
          AWS_ARN: arn:aws:iam::565955521116:role/GitHub-OIDC-Insights
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          TEST_PATH_PATTERN: tests/integration
          DATA_INSIGHTS_URL: ${{ secrets.DATA_INSIGHTS_STAGE_URL }}
          MFD_API_URL: ${{ secrets.STAGE_BASE_URL }}
          LOG_LEVEL: error
          USERS_TO_NOTIFY: "@UU64VLJFP,@U03KMDLHTPW,@U03DN7L0K37"
          MAKE_COMMAND: "make setup-env-stage-us1-super-admin"
          EXTRA_JEST_ARGS: "--maxWorkers=75%"

  smoke-test-stage-us2:
    name: US2 Smoke Tests 🤖
    runs-on: x8-core
    permissions:
      id-token: write
      contents: read
    timeout-minutes: 60
    needs: smoke-test-stage-us1
    if: always()
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/integration-tests
        name: Smoke Test with Super Admin User
        if: always()
        env:
          ENV_PREFIX: stage
          ISLAND: us2
          AWS_ARN: arn:aws:iam::565955521116:role/GitHub-OIDC-Insights
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          TEST_PATH_PATTERN: tests/integration
          DATA_INSIGHTS_URL: ${{ secrets.DATA_INSIGHTS_STAGE_URL }}
          MFD_API_URL: ${{ secrets.STAGE_BASE_URL }}
          LOG_LEVEL: error
          USERS_TO_NOTIFY: "@UU64VLJFP,@U03KMDLHTPW,@U03DN7L0K37"
          MAKE_COMMAND: "make setup-env-stage-us2-super-admin"
          EXTRA_JEST_ARGS: "--maxWorkers=75%"
