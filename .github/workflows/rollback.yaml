name: Rollback Service version

on:
  workflow_dispatch:
    inputs:
      tag:
        description: "Version of the tag to deploy"
        required: true
        type: string

jobs:
  update-deployment:
    name: Triggers action to update ArgoCD application deployment manifest with an already created tag
    runs-on: default
    permissions:
      id-token: write
      contents: read
    environment: production
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["argocd-reporting"]
          app_id: 288283
          aws_role_arn: arn:aws:iam::048781935247:role/GH-OIDC-SQUAD-Insights
          aws_ssm_param_name: /github/app/CBSquadInsights/private-key

      - name: Update application on production cluster
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-reporting
          github_token: ${{ steps.gh-app-token.outputs.github-token }}
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "prod-ga",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ inputs.tag }}"
            }
