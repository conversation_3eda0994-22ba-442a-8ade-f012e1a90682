name: Generate properties

on:
  workflow_dispatch:
    inputs:
      EMAIL_PREFIX:
        description: "First part of your email before @ symbol"
        required: true
        type: string
      DOMAIN:
        description: "Domain of environment where to create properties"
        required: false
        type: string
        default: cloudbeds-stage.com
      EXISTING_COPIES_OF_PROPERTIES:
        description: "Add users to already previously generated properties"
        required: false
        type: string

run-name: "Run on <${{ inputs.DOMAIN}}: ${{ inputs.EXISTING_COPIES_OF_PROPERTIES}}>"

jobs:
  generate-properties:
    name: Generate properties
    runs-on: x16-core
    timeout-minutes: 10
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/generate-properties
        name: Generate properties
        env:
          EMAIL_PREFIX: ${{ inputs.EMAIL_PREFIX }}
          DOMAIN: ${{ inputs.DOMAIN }}
          EXISTING_COPIES_OF_PROPERTIES: ${{ inputs.EXISTING_COPIES_OF_PROPERTIES }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
