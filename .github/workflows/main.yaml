name: Test, Build, and Publish to Stage Environment

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  code-lint:
    name: Code Lint 🧹
    runs-on: default
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/code-lint
        name: Code Lint

  unit-tests:
    name: Unit Tests 🧪
    runs-on: default
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/unit-tests
        name: Unit Tests

  build-push:
    name: Build and push image 🧱
    runs-on: default
    needs:
      - "code-lint"
      - "unit-tests"
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2

      # Application Image
      - name: Build and push application image
        uses: cloudbeds/composite-actions/docker/build-push/aws-ecr@v2
        with:
          image_name: ${{ github.event.repository.name }}
          image_tag: ${{ github.sha }}
          dockerfile_path: ./Dockerfile
          docker_build_secrets: |
            token=${{ steps.gh-app-token.outputs.github-token }}

      #  Migrations Image
      - name: Build and push migrations image
        uses: cloudbeds/composite-actions/docker/build-push/aws-ecr@v2
        with:
          image_name: ${{ github.event.repository.name }}/migrations
          image_tag: ${{ github.sha }}
          dockerfile_path: ./migrations/Dockerfile
          docker_build_secrets: |
            token=${{ steps.gh-app-token.outputs.github-token }}

  update-deployment:
    name: Triggers action to update ArgoCD application deployment manifest with new image tag
    needs: build-push
    runs-on: default
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["argocd-reporting"]
          app_id: 288283
          aws_role_arn: arn:aws:iam::048781935247:role/GH-OIDC-SQUAD-Insights
          aws_ssm_param_name: /github/app/CBSquadInsights/private-key

      - name: Update application on stage cluster
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-reporting
          github_token: ${{ steps.gh-app-token.outputs.github-token }}
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "stage-ga",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ github.sha }}"
            }

  health-check:
    name: Health Check 🩺
    runs-on: default
    needs: update-deployment
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/health-check
        name: Health Check
        env:
          DATA_INSIGHTS_URL: ${{ secrets.DATA_INSIGHTS_STAGE_URL }}

  api-tests-stage:
    needs: health-check
    name: Stage API Tests 🤖
    uses: ./.github/workflows/stage-api-tests.yaml
    secrets: inherit
