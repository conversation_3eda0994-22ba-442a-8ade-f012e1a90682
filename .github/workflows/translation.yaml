name: Generate translations

on:
  workflow_dispatch:
  push:
    branches:
      - l10n_main

jobs:
  translations-compile:
    name: Compile Translations
    runs-on: default
    steps:
      - uses: actions/checkout@v4
        name: Checkout Repo
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Install dependencies
        shell: bash
        run: |
          python -m pip install --upgrade pip
          pip install pybabel
          if [ -f dependencies/requirements.txt ]
          then
            pip install -r dependencies/requirements.txt
          fi
          if [ -f dependencies/requirements-dev.txt ]
          then
            pip install -r dependencies/requirements-dev.txt
          fi
        env:
          GITHUB_ACCESS_TOKEN: ${{ secrets.CB_CI_WORKFLOW_TOKEN }}
      - name: Compile Crowdin Translations and Push generated mo files
        run: |
          make l10n-compile
          git status
          git config --global user.name 'cloudbeds-ci'
          git config --global user.email '<EMAIL>'
          if [[ "$(git status --porcelain)" == "" ]]; then
            echo "Nothing to commit. Skipping"
            exit 0
          fi
          git add -A
          git commit -m "[automated commit] generate mo files from po files"
          git push origin ${{ github.ref_name }}
