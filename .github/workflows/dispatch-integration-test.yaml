name: Manually Run Integration tests

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Runner Environment"
        required: true
        default: "stage-ga"
        type: choice
        options:
          - stage-ga

jobs:
  integration-test-stage-ga:
    if: ${{ inputs.environment == 'stage-ga' }}
    name: Integration Tests Stage-GA 🤖
    uses: ./.github/workflows/stage-api-tests.yaml
    secrets: inherit
