name: Test and build hotfix to deploy to prod

on:
  workflow_dispatch:
  push:
    branches:
      - hotfix/*

jobs:
  code-lint:
    name: Code Lint 🧹
    runs-on: default
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/code-lint
        name: Code Lint

  unit-tests:
    name: Unit Tests 🧪
    runs-on: default
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - uses: ./.github/actions/unit-tests
        name: Unit Tests

  build-push:
    name: Build and push image 🧱
    runs-on: x16-core
    environment: production
    needs:
      - "code-lint"
      - "unit-tests"
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2

      # Application Image
      - name: Build and push application image
        uses: cloudbeds/composite-actions/docker/build-push/aws-ecr@v2
        with:
          image_name: ${{ github.event.repository.name }}
          image_tag: ${{ github.sha }}
          dockerfile_path: ./Dockerfile
          docker_build_secrets: |
            token=${{ steps.gh-app-token.outputs.github-token }}

      #  Migrations Image
      - name: Build and push migrations image
        uses: cloudbeds/composite-actions/docker/build-push/aws-ecr@v2
        with:
          image_name: ${{ github.event.repository.name }}/migrations
          image_tag: ${{ github.sha }}
          dockerfile_path: ./migrations/Dockerfile
          docker_build_secrets: |
            token=${{ steps.gh-app-token.outputs.github-token }}
