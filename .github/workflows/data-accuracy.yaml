name: Execute E2E Test to verify if Data is refreshed

on:
  schedule:
    - cron: "*/30 * * * *"
  workflow_dispatch:
    inputs:
      environment:
        description: "Runner Environment"
        required: true
        default: "prod-ga"
        type: choice
        options:
          - prod-ga

jobs:
  data-accuracy-prod-ga-us1:
    name: Verify Data accuracy Prod-GA US1 🤖
    runs-on: x16-core
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/GitHub-OIDC-Insights
          role-session-name: prod-insights-github-oidc-role
          aws-region: us-west-2
      - name: Checkout latest tag
        uses: actions/checkout@v4
      - name: Fetch all tags
        run: git fetch --tags
      - name: Get the latest tag
        id: get_latest_tag
        run: |
          latest_tag=$(git describe --tags `git rev-list --tags --max-count=1`)
          echo "Latest tag: $latest_tag"
          echo "::set-output name=LATEST_TAG::$latest_tag"
      - name: Checkout the latest tag
        run: git checkout ${{ steps.get_latest_tag.outputs.LATEST_TAG }}
      - uses: ./.github/actions/e2e-tests
        name: Verify Data Accuracy Prod-GA US1
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          DATA_INSIGHTS_URL: ${{ secrets.DATA_INSIGHTS_PROD_URL }}
          MFD_API_URL: ${{ secrets.PROD_BASE_URL }}
          FULL_ACCESS_PROPERTY_ID: ${{ secrets.PROD_PROPERTY_ID }}
          FULL_ACCESS_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          FULL_ACCESS_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          PROPERTY_OWNER_VIEWER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          PROPERTY_OWNER_VIEWER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          FINANCIAL_DATASET_VIEWER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          FINANCIAL_DATASET_VIEWER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          NO_DATASET_VIEWER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          NO_DATASET_VIEWER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          DI_ACCESS_PROPERTY_OWNER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          DI_ACCESS_PROPERTY_OWNER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          LOG_LEVEL: error
          KONG_OIDC_ORGANIZATION_URL: ${{ secrets.PROD_KONG_OIDC_ORGANIZATION_URL }}
          KONG_OIDC_SERVER_ID: ${{ secrets.PROD_KONG_OIDC_SERVER_ID }}
          KONG_OIDC_CLIENT_ID: ${{ secrets.PROD_KONG_OIDC_CLIENT_ID }}
          KONG_OIDC_CLIENT_SECRET: ${{ secrets.PROD_KONG_OIDC_CLIENT_SECRET }}
          DATADOG_API_KEY: ""
          SUPER_USER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          SUPER_USER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          PROPERTY_ID: ${{ secrets.PROD_PROPERTY_ID }}
          DOMAIN: "cloudbeds.com"
          PUBLIC_API_DOMAIN: "cloudbeds.com"
          API_VERSION: "/api/v1.1"
          HTTP_PROTOCOL: "https"
          REJECT_UNAUTHORIZED: "false"
          BILLING_ENABLED: "false"
          GLOBAL_SETUP: "1" # This Argument is needed to run the Global Setup
          ENVIRONMENT: prod-ga
          ISLAND: us1
          SHIFT_HOURS_HOTEL_TIMEZONE: 1
          PAGERDUTY_DATA_SQUAD_ROUTING_KEY: ${{ secrets.PAGERDUTY_DATA_SQUAD_ROUTING_KEY }}
          SLACK_CHANNEL: "squad_data_qa" # This will set the slack channel to notify
          USERS_TO_NOTIFY: "@U012H4JHL1Y,@U03MU3YUL9L,@U06FR2GFBB9,@U07B2N01A07" # USER ID to be notified

  data-accuracy-prod-ga-us2:
    name: Verify Data accuracy Prod-GA US2 🤖
    runs-on: x16-core
    needs: data-accuracy-prod-ga-us1
    if: always()
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/GitHub-OIDC-Insights
          role-session-name: prod-insights-github-oidc-role
          aws-region: us-west-2

      - name: Checkout latest tag
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Ensure all tags are fetched
      - name: Get latest tag
        id: get-latest-tag
        run: echo "LATEST_TAG=$(git describe --tags $(git rev-list --tags --max-count=1))" >> $GITHUB_ENV
      - name: Checkout latest tag
        run: git checkout ${{ env.LATEST_TAG }}

      - uses: ./.github/actions/e2e-tests
        name: Verify Data Accuracy Prod-GA US2
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          DATA_INSIGHTS_URL: ${{ secrets.DATA_INSIGHTS_PROD_URL }}
          MFD_API_URL: ${{ secrets.PROD_BASE_URL_US2 }}
          FULL_ACCESS_PROPERTY_ID: ${{ secrets.PROD_PROPERTY_ID_US2 }}
          FULL_ACCESS_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL_US2 }}
          FULL_ACCESS_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD_US2 }}
          PROPERTY_OWNER_VIEWER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          PROPERTY_OWNER_VIEWER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          FINANCIAL_DATASET_VIEWER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          FINANCIAL_DATASET_VIEWER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          NO_DATASET_VIEWER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          NO_DATASET_VIEWER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          DI_ACCESS_PROPERTY_OWNER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL }}
          DI_ACCESS_PROPERTY_OWNER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD }}
          LOG_LEVEL: error
          KONG_OIDC_ORGANIZATION_URL: ${{ secrets.PROD_KONG_OIDC_ORGANIZATION_URL }}
          KONG_OIDC_SERVER_ID: ${{ secrets.PROD_KONG_OIDC_SERVER_ID }}
          KONG_OIDC_CLIENT_ID: ${{ secrets.PROD_KONG_OIDC_CLIENT_ID }}
          KONG_OIDC_CLIENT_SECRET: ${{ secrets.PROD_KONG_OIDC_CLIENT_SECRET }}
          DATADOG_API_KEY: ""
          SUPER_USER_EMAIL: ${{ secrets.PROD_TEST_PROPERTY_USER_EMAIL_US2 }}
          SUPER_USER_PASSWORD: ${{ secrets.PROD_TEST_PROPERTY_USER_PASSWORD_US2 }}
          PROPERTY_ID: ${{ secrets.PROD_PROPERTY_ID_US2 }}
          DOMAIN: "us2.cloudbeds.com"
          PUBLIC_API_DOMAIN: "cloudbeds.com"
          API_VERSION: "/api/v1.1"
          HTTP_PROTOCOL: "https"
          REJECT_UNAUTHORIZED: "false"
          BILLING_ENABLED: "false"
          GLOBAL_SETUP: "1" # This Argument is needed to run the Global Setup
          ENVIRONMENT: prod-ga
          ISLAND: us2
          SHIFT_HOURS_HOTEL_TIMEZONE: 1
          PAGERDUTY_DATA_SQUAD_ROUTING_KEY: ${{ secrets.PAGERDUTY_DATA_SQUAD_ROUTING_KEY }}
          SLACK_CHANNEL: "squad_data_qa" # This will set the slack channel to notify
          USERS_TO_NOTIFY: "@U012H4JHL1Y,@U03MU3YUL9L,@U06FR2GFBB9" # USER ID to be notified
