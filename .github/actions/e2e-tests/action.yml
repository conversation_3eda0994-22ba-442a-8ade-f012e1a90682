name: "E2E Test"
description: Action to run the E2E test that verify the Data being refreshed

runs:
  using: composite
  steps:
    - name: Set up Node
      uses: actions/setup-node@v4
      with:
        node-version-file: tests/api/.nvmrc

    - name: NPM Install
      working-directory: tests/api
      shell: bash
      run: |
        npm install

    - name: Run E2E Tests
      shell: bash
      working-directory: tests/api
      run: |
        TEST_USERNAME='Property User' \
        ISLAND=$ISLAND \
        EMAIL=$FULL_ACCESS_EMAIL \
        PASSWORD=$FULL_ACCESS_PASSWORD \
        npm run test:e2e
