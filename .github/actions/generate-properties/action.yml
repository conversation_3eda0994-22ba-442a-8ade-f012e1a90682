name: "Generate Properties"
description: Action to generate properties for the API tests

runs:
  using: composite
  steps:
    - name: Set up Node
      uses: actions/setup-node@v4
      with:
        node-version-file: tests/api/.nvmrc

    - name: NPM Install
      working-directory: tests/api
      shell: bash
      run: |
        npm install

    - name: <PERSON>ript
      working-directory: tests/api
      shell: bash
      run: |
        node generateProperties.js
      env:
        LOG_LEVEL: trace
        DATADOG_API_KEY: ''
