name: "Unit Tests"
description: Action to run the full suite of unit tests

runs:
  using: composite
  steps:
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"

    - name: Get GH app token
      id: gh-app-token
      uses: cloudbeds/composite-actions/gh-app-token@v2

    - name: Git credentials
      shell: bash
      run: |
        git config --global url.'https://x-access-token:${{ steps.gh-app-token.outputs.github-token }}@github.com/'.insteadOf 'https://github.com/'

    - name: Install dependencies
      shell: bash
      run: |
        python -m pip install --upgrade pip
        if [ -f dependencies/requirements.txt ]
        then 
          pip install -r dependencies/requirements.txt
        fi
        if [ -f dependencies/requirements-dev.txt ]
        then
          pip install -r dependencies/requirements-dev.txt
        fi

    - name: Test with pytest
      shell: bash
      run: |
        ENV=TEST LOG_LEVEL=10 python -m coverage \
        run -m pytest -s tests/unit --junitxml=./test-reports/junit.xml && \
        python -m coverage report
