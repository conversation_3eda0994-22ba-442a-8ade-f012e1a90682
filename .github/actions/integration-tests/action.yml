name: "Integration Test"
description: Action to run the integration test

runs:
  using: composite
  steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ env.AWS_ARN }}
        role-session-name: cloudbeds-github-oidc-role
        aws-region: us-west-2

    - name: Set up Node
      uses: actions/setup-node@v4
      with:
        node-version-file: tests/api/.nvmrc

    - name: NPM Install
      working-directory: tests/api
      shell: bash
      run: |
        npm install

    - name: Tests Setup
      working-directory: tests/api
      shell: bash
      run: |
        set -x
        if [[ -z "$MAKE_COMMAND" ]]; then
          MAKE_COMMAND="make setup-env"
        fi
        eval $MAKE_COMMAND

        set -a && source .env && set +a
        make echo-env

        {
          echo "ENV_ISLAND=${ENV_ISLAND}"
          echo "ENVIRONMENT=${ENVIRONMENT}"
          echo "MFD_URL=${MFD_URL}"
          echo "DATA_INSIGHTS_URL=${DATA_INSIGHTS_URL}"
          echo "FULL_ACCESS_EMAIL=${FULL_ACCESS_EMAIL}"
          echo "FULL_ACCESS_PROPERTY_ID=${FULL_ACCESS_PROPERTY_ID}"
          echo "PROPERTY_OWNER_VIEWER_EMAIL=${PROPERTY_OWNER_VIEWER_EMAIL}"
          echo "FINANCIAL_DATASET_VIEWER_EMAIL=${FINANCIAL_DATASET_VIEWER_EMAIL}"
          echo "NO_DATASET_VIEWER_EMAIL=${NO_DATASET_VIEWER_EMAIL}"
          echo "DI_ACCESS_PROPERTY_OWNER_EMAIL=${DI_ACCESS_PROPERTY_OWNER_EMAIL}"
          echo "VIEWER_PROPERTY_ID=${VIEWER_PROPERTY_ID}"
        } >> $GITHUB_ENV


    - name: Run Tests
      working-directory: tests/api
      shell: bash
      run: |
        set -x
        node setupTest
        npx jest $EXTRA_JEST_ARGS --testPathPattern $TEST_PATH_PATTERN --json --outputFile=results.json || true
        TEST_USERNAME=$FULL_ACCESS_EMAIL ISLAND=$ISLAND node slackNotify.js
