name: "Code Lint"
description: Python code linter

runs:
  using: composite
  steps:
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"

    - name: Install dependencies
      shell: bash
      run: |
        python -m pip install --upgrade pip
        pip install pre-commit==3.7.0 && pre-commit install

    - name: <PERSON><PERSON> with flake8 and black
      shell: bash
      run: |
        pre-commit run --all-files
