#!/usr/bin/env python3
"""
Test script to verify the daily revenue report fix.
This script tests that the custom CDFs now generate 0.0 instead of NULL in default cases.
"""

import sys
import os

# Import the configuration directly
sys.path.insert(0, '/usr/src')

# Import the configuration directly without importing the app
import importlib.util
spec = importlib.util.spec_from_file_location("daily_revenue_report", "/usr/src/app/classic_reports/static_reports/daily_activity/daily_revenue_report.py")
daily_revenue_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(daily_revenue_module)
DAILY_REVENUE_REPORT = daily_revenue_module.DAILY_REVENUE_REPORT

def test_daily_revenue_report_fix():
    """Test that the daily revenue report custom CDFs have proper default cases."""
    
    print("Testing Daily Revenue Report Custom CDF Fix...")
    print("=" * 60)
    
    custom_cdfs = DAILY_REVENUE_REPORT.get('custom_cdfs', [])
    
    # Test Revenue CDF
    revenue_cdf = None
    payments_cdf = None
    revenue_minus_payments_cdf = None
    
    for cdf in custom_cdfs:
        if cdf.get('name') == 'Revenue':
            revenue_cdf = cdf
        elif cdf.get('name') == 'Payments':
            payments_cdf = cdf
        elif cdf.get('name') == 'Revenue Minus Payments':
            revenue_minus_payments_cdf = cdf
    
    # Test Revenue CDF
    if revenue_cdf:
        default_case = revenue_cdf['formula'][0]['default_case']
        print(f"✅ Revenue CDF default_case: {default_case}")
        assert default_case['kind'] == 'number', f"Expected 'number', got '{default_case['kind']}'"
        assert default_case['value'] == 0.0, f"Expected 0.0, got {default_case['value']}"
        print("   ✓ Revenue CDF has correct default case (number, 0.0)")
    else:
        print("❌ Revenue CDF not found!")
        return False
    
    # Test Payments CDF
    if payments_cdf:
        default_case = payments_cdf['formula'][0]['default_case']
        print(f"✅ Payments CDF default_case: {default_case}")
        assert default_case['kind'] == 'number', f"Expected 'number', got '{default_case['kind']}'"
        assert default_case['value'] == 0.0, f"Expected 0.0, got {default_case['value']}"
        print("   ✓ Payments CDF has correct default case (number, 0.0)")
    else:
        print("❌ Payments CDF not found!")
        return False
    
    # Test Revenue Minus Payments CDF (has 2 case statements)
    if revenue_minus_payments_cdf:
        formula = revenue_minus_payments_cdf['formula']
        case_statements = [item for item in formula if item.get('kind') == 'case']
        
        print(f"✅ Revenue Minus Payments CDF has {len(case_statements)} case statements")
        
        for i, case_stmt in enumerate(case_statements):
            default_case = case_stmt['default_case']
            print(f"   Case {i+1} default_case: {default_case}")
            assert default_case['kind'] == 'number', f"Case {i+1}: Expected 'number', got '{default_case['kind']}'"
            assert default_case['value'] == 0.0, f"Case {i+1}: Expected 0.0, got {default_case['value']}"
            print(f"   ✓ Case {i+1} has correct default case (number, 0.0)")
    else:
        print("❌ Revenue Minus Payments CDF not found!")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED! The fix is working correctly.")
    print("   - Revenue CDF will generate 'ELSE 0.0' instead of 'ELSE NULL'")
    print("   - Payments CDF will generate 'ELSE 0.0' instead of 'ELSE NULL'")
    print("   - Revenue Minus Payments CDF will generate 'ELSE 0.0' instead of 'ELSE NULL'")
    print("   - This ensures proper numeric aggregation in SQL queries")
    return True

if __name__ == "__main__":
    try:
        success = test_daily_revenue_report_fix()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)
