[{"id": "451", "datasource_id": 1, "datasource_kind": "stock_report", "datasource_title": "Charges Trends by Transaction Type", "datasource_description": "Column chart with the sum of the debits by the transaction type vs transaction month. Visualizations view only.", "title": "Charges Trends by Transaction Type", "kind": "bar", "settings": {"legend": false, "toolbox": false, "stack": "standard"}, "categories": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "modifier": "month"}], "metrics": [{"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}], "user_id": "227001", "created_at": "2024-09-26T17:26:06Z", "updated_at": "2024-10-07T19:09:42Z"}]