{"id": 69, "title": "Breakfast report by add on ", "description": "Breakfast report based on add on posted in the reservation. Please note you have to configure the name of your add on", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "room_number"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "private_rate_plan"}}, {"cdf": {"type": "default", "column": "quantity_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "addon_item"}}, {"cdf": {"type": "default", "column": "item_service_type"}}, {"cdf": {"type": "default", "column": "transaction_type"}}], "group_rows": null, "group_columns": null, "periods": null, "filters": {"and": [{"or": [{"cdf": {"type": "default", "column": "item_service_type"}, "operator": "contains", "value": " breakfast"}, {"cdf": {"type": "default", "column": "addon_item"}, "operator": "contains", "value": "breakfast"}]}, {"cdf": {"type": "default", "column": "reservation_status"}, "operator": "list_contains", "value": ["In-House"]}]}, "sort": null, "settings": {"details": true, "totals": false, "transpose": false}, "type": "List", "updated_at": "2023-03-23T11:47:12Z", "property_ids": [22425], "user_id": 260679, "published": true, "formats": {"link": true}, "rules": {"property_ids": null, "country_codes": null, "feature_ids": ["report_builder"]}}