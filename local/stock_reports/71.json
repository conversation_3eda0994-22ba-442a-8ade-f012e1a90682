{"id": 71, "title": "Balance due test", "description": "DI-5058", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "balance_due_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "custom", "column": "custom_custom_test"}}, {"cdf": {"type": "custom", "column": "custom_custom_test_string"}}], "group_columns": null, "periods": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "Day", "start": "today", "end": "today", "start_relative_to_end": false}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "MTD", "start": "start_current_month", "end": "today", "start_relative_to_end": false}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "YTD", "start": "start_current_year", "end": "today", "start_relative_to_end": false}], "filters": null, "sort": [{"cdf": {"type": "custom", "column": "custom_custom_test"}, "direction": "asc"}, {"cdf": {"type": "custom", "column": "custom_custom_test_string"}, "direction": "asc"}], "settings": {"details": false, "totals": false, "transpose": false}, "type": "PeriodSummary", "updated_at": "2023-06-06T22:49:46Z", "property_ids": [79], "user_id": 1, "custom_cdfs": [{"id": 3, "column": "custom_custom_test", "name": "Custom test", "description": "", "formula": [{"kind": "cdf", "value": "addon_item"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "fee_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "item_service_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "pos_charge_description"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "room_revenue_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "tax_type"}, {"kind": "separator", "value": " "}], "kind": "String", "created_at": "2023-05-04T16:30:04Z", "updated_at": "2023-05-04T16:30:04Z"}, {"id": 4, "column": "custom_custom_test_string", "name": "Custom test_string", "description": "Contains only a string", "formula": [{"kind": "separator", "value": "Just some string"}], "kind": "String", "created_at": "2023-05-04T16:30:04Z", "updated_at": "2023-05-04T16:30:04Z"}], "published": true, "rules": {"property_ids": null, "country_codes": null, "feature_ids": null}}