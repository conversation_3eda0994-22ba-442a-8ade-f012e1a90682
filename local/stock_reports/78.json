{"id": "78", "title": "Taxes and Fees", "description": "List of all taxes and fees, broken down by type and with day, month to day and year to day values. ", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "default", "column": "tax_type"}}, {"cdf": {"type": "default", "column": "fee_type"}}], "group_columns": null, "periods": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "Day", "start": "today", "end": "today", "start_relative_to_end": false}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "MTD", "start": "start_current_month", "end": "today", "start_relative_to_end": false}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "YTD", "start": "start_current_year", "end": "today", "start_relative_to_end": false}], "filters": {"and": [{"or": [{"cdf": {"type": "default", "column": "tax_type"}, "operator": "is_not_empty", "value": ""}, {"cdf": {"type": "default", "column": "fee_type"}, "operator": "is_not_empty", "value": ""}]}, {"cdf": {"type": "default", "column": "transaction_status"}, "operator": "list_contains", "value": ["Posted"]}]}, "sort": [{"cdf": {"type": "default", "column": "tax_type"}, "direction": "asc"}, {"cdf": {"type": "default", "column": "fee_type"}, "direction": "asc"}], "settings": {"details": false, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "PeriodSummary", "folder_id": "360871725367296", "updated_at": "2023-08-29T15:59:13Z", "property_ids": ["22425"], "user_id": "338172", "custom_cdfs": [], "published": true, "rules": {"country_codes": null, "property_ids": null, "property_types": null, "feature_ids": null}}