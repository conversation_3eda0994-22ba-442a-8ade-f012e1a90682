{"id": 68, "title": "Breakfast report by rate plan", "description": "A report of all guest by stay date and rate plan to indicate which guest have a plan that includes breakfast.", "dataset_id": 3, "columns": [{"cdf": {"type": "default", "column": "room_numbers"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "guest_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "adults_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "children_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "private_rate_plan"}}, {"cdf": {"type": "default", "column": "public_rate_plan"}}, {"cdf": {"type": "default", "column": "reservation_source"}}], "group_rows": null, "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "stay_date", "multi_level_id": 1}, "operator": "equals", "value": "today"}, {"cdf": {"type": "default", "column": "stay_date", "multi_level_id": 1}, "operator": "equals", "value": "today"}, {"and": [{"cdf": {"type": "default", "column": "reservation_status"}, "operator": "not_list_contains", "value": ["No Show", "Cancelled", "Checked Out"]}]}]}, "sort": null, "settings": {"details": true, "totals": false, "transpose": false}, "type": "List", "updated_at": "2023-03-23T11:16:39Z", "property_ids": [22425], "user_id": 260679, "published": true, "formats": {"link": true}, "rules": {"property_ids": null, "country_codes": null, "feature_ids": ["report_builder"]}}