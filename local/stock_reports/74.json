{"id": "74", "title": "Daily Revenue Report", "description": "Revenue report by type and including Month to day and Year to day values. Filter by revenue type and export directly if the data exceeds the amount that can be presented in the table. ", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "default", "column": "transaction_type"}}, {"cdf": {"type": "custom", "column": "custom_revenue_type"}}], "group_columns": null, "periods": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "Day", "start": "today", "end": "tomorrow", "start_relative_to_end": false}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "MTD", "start": "start_current_month", "end": "tomorrow", "start_relative_to_end": false}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "name": "YTD", "start": "start_current_year", "end": "tomorrow", "start_relative_to_end": false}], "filters": {"and": [{"cdf": {"type": "default", "column": "transaction_type"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "greater_than_or_equal", "value": "start_current_year"}]}, "sort": [{"cdf": {"type": "default", "column": "transaction_type"}, "direction": "asc"}, {"cdf": {"type": "custom", "column": "custom_revenue_type"}, "direction": "asc"}], "settings": {"details": false, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "PeriodSummary", "folder_id": "360873287749632", "updated_at": "2023-11-28T14:22:07Z", "property_ids": ["22425"], "user_id": "338172", "custom_cdfs": [{"id": "2", "column": "custom_revenue_type", "name": "Revenue type", "description": "", "formula": [{"kind": "cdf", "value": "addon_item"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "addon_charge_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "fee_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "item_service_category"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "item_service_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "pos_charge_category"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "pos_charge_description"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "room_revenue_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "tax_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "payment_method"}, {"kind": "separator", "value": " "}], "kind": "String", "created_at": "2023-05-09T08:48:14Z", "updated_at": "2023-05-09T08:48:14Z"}], "published": true, "rules": {"feature_ids": null, "property_types": null, "country_codes": null, "property_ids": null}}