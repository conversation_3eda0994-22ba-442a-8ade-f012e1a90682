{"id": 67, "title": "Guest Ledger type Report per room", "description": "A report on In house and Checked out reservations with Balance due different than 0.", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "room_revenue_type"}}, {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "balance_due_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "transaction_type"}}], "group_rows": [{"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "room_number"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}], "group_columns": null, "periods": null, "filters": {"and": [{"or": [{"and": [{"cdf": {"type": "default", "column": "reservation_status"}, "operator": "list_contains", "value": ["Checked Out"]}]}, {"and": [{"cdf": {"type": "default", "column": "reservation_status"}, "operator": "list_contains", "value": ["In-House"]}]}]}, {"cdf": {"type": "default", "column": "balance_due_amount"}, "operator": "not_equals", "value": "0.00"}, {"cdf": {"type": "default", "column": "reservation_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}, "operator": "is_not_null", "value": ""}]}, "sort": null, "settings": {"details": false, "totals": false, "transpose": false}, "type": "Summary", "updated_at": "2023-03-23T11:01:19Z", "property_ids": [22425], "user_id": 260679, "published": true, "formats": {"link": true}, "rules": {"property_ids": null, "country_codes": null, "feature_ids": ["report_builder"]}}