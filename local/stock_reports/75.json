{"id": "75", "title": "In House Guests", "description": "List of all guests part of a reservation. Filter by reservation status, check in date and more. Note: if you haven't entered guest details for each guest they will not show up in this report.", "dataset_id": 2, "columns": [{"cdf": {"type": "default", "column": "room_numbers"}}, {"cdf": {"type": "default", "column": "guest_first_name"}}, {"cdf": {"type": "default", "column": "guest_surname"}}, {"cdf": {"type": "default", "column": "guest_status_level"}}, {"cdf": {"type": "default", "column": "is_repeat_guest"}}, {"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "duration_of_stay"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "guest_residence_country"}}, {"cdf": {"type": "default", "column": "guest_mobile_phone_number"}}, {"cdf": {"type": "default", "column": "guest_phone_number"}}, {"cdf": {"type": "default", "column": "guest_birth_date"}}], "group_rows": null, "group_columns": null, "periods": null, "comparisons": null, "filters": {"and": [{"cdf": {"type": "default", "column": "reservation_status"}, "operator": "list_contains", "value": ["In-House"]}, {"cdf": {"type": "default", "column": "guest_first_name"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "guest_surname"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "checkin_date"}, "operator": "is_not_null", "value": "2023-05-05T00:00:00Z"}, {"cdf": {"type": "default", "column": "checkin_date"}, "operator": "is_not_null", "value": "2023-05-05T00:00:00Z"}, {"cdf": {"type": "default", "column": "checkout_date"}, "operator": "is_not_null", "value": "2023-05-05T00:00:00Z"}, {"cdf": {"type": "default", "column": "checkout_date"}, "operator": "is_not_null", "value": "2023-05-05T00:00:00Z"}, {"cdf": {"type": "default", "column": "guest_residence_country"}, "operator": "list_contains", "value": ["Afghanistan", "Albania", "Algeria", "American Samoa", "Andorra", "Angola", "<PERSON><PERSON><PERSON>", "Antarctica", "Antigua and Barbuda", "Argentina", "Armenia", "Aruba", "Australia", "Austria", "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bermuda", "Bhutan", "Bolivia", "Bosnia and Herzegovina", "Botswana", "Bouvet Island", "Brazil", "British Indian Ocean Territory", "British Virgin Islands", "Brunei", "Bulgaria", "Burkina Faso", "Burundi", "Cambodia", "Cameroon", "Canada", "Canary Islands", "Cape Verde", "Caribbean Netherlands", "Cayman Islands", "Central African Republic", "Ceuta & Melilla", "Chad", "Chile", "China", "Christmas Island", "Clipperton Island", "Cocos (Keeling) Islands", "Colombia", "Comoros", "Cook Islands", "Costa Rica", "Cote d'Ivoire", "Croatia", "Cuba", "Curacao", "Cyprus", "Czech Republic", "Democratic Republic of the Congo", "Denmark", "<PERSON>", "Djibouti", "Dominica", "Dominican Republic", "Ecuador", "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Ethiopia", "European Union", "Falkland Islands (Islas Malvinas)", "Faroe Islands", "Fiji", "Finland", "France", "French Guiana", "French Polynesia", "French Southern Territories", "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Gibraltar", "Greece", "Greenland", "Grenada", "Guadeloupe", "Guam", "Guatemala", "Guernsey", "Guinea", "Guinea-Bissau", "Guyana", "Haiti", "Heard & McDonald Islands", "Honduras", "Hong Kong", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland", "Isle of Man", "Israel", "Italy", "Jamaica", "Japan", "Jersey", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg", "Macau SAR China", "Macedonia", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Martinique", "Mauritania", "Mauritius", "Mayotte", "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Montserrat", "Morocco", "Mozambique", "Myanmar", "Namibia", "Nauru", "Nepal", "Netherlands", "Netherlands Antilles", "New Caledonia", "New Zealand", "Nicaragua", "Niger", "Nigeria", "Niue", "Norfolk Island", "North Korea", "Northern Mariana Islands", "Norway", "Oman", "Pakistan", "<PERSON><PERSON>", "Palestinian Territory", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Pitcairn Islands", "Poland", "Portugal", "Puerto Rico", "Qatar", "Republic of the Congo", "Reunion", "Romania", "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", "Saint <PERSON>", "Saint Vincent and the Grenadines", "Samoa", "San Marino", "Sao Tome and Principe", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Sint Maarten", "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa", "South Georgia & South Sandwich Islands", "South Korea", "South Sudan", "Spain", "Sri Lanka", "St. <PERSON>", "St. Helena", "St. Pierre & Miquelon", "Sudan", "Suriname", "Svalbard and <PERSON>", "Swaziland", "Sweden", "Switzerland", "Syria", "Taiwan", "Tajikistan", "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tokelau", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan", "Turks & Caicos Islands", "Tuvalu", "U.S. Outlying Islands", "U.S. Virgin Islands", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "United States of America", "Unknown Region", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City", "Venezuela", "Vietnam", "Wallis and Futuna", "Western Sahara", "Yemen", "Zambia", "Zimbabwe", "Åland Islands"]}]}, "sort": [{"cdf": {"type": "default", "column": "room_numbers"}, "direction": "asc"}], "settings": {"details": true, "totals": false, "transpose": false}, "formats": {"date": "YYYY-MM-DD", "link": true}, "type": "List", "folder_id": "360881890529280", "tags": [{"id": "1", "name": "night_audit"}], "updated_at": "2024-12-09T17:14:42Z", "property_ids": ["22425"], "user_id": "535042", "custom_cdfs": [], "published": true, "rules": {"feature_ids": null, "property_ids": null, "country_codes": null, "property_types": null}}