{"id": "76", "title": "Point of Sale Reconciliation", "description": "All transactions from a point of sale system that have been posted into the Cloudbeds PMS. Filter by date, room number and more.", "dataset_id": 1, "columns": [{"cdf": {"type": "custom", "column": "custom_description"}}, {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "quantity_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "transaction_type"}}, {"cdf": {"type": "default", "column": "room_number"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "reservation_number"}}], "group_rows": [{"cdf": {"type": "default", "column": "transaction_notes"}}], "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "greater_than_or_equal", "value": "start_current_month"}, {"or": [{"cdf": {"type": "default", "column": "transaction_notes"}, "operator": "contains", "value": "check"}, {"cdf": {"type": "default", "column": "transaction_notes"}, "operator": "contains", "value": "receipt"}]}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "less_than", "value": "tomorrow"}, {"cdf": {"type": "default", "column": "transaction_status"}, "operator": "list_contains", "value": ["Posted"]}, {"cdf": {"type": "default", "column": "room_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_number"}, "operator": "is_not_null", "value": ""}]}, "sort": [{"cdf": {"type": "default", "column": "transaction_notes"}, "direction": "asc"}], "settings": {"details": true, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "Summary", "folder_id": "360871725367296", "updated_at": "2023-07-12T09:44:59Z", "property_ids": ["22425"], "user_id": "260679", "custom_cdfs": [{"id": "3", "column": "custom_description", "name": "Description", "description": "", "formula": [{"value": "fee_type", "kind": "cdf"}, {"value": " ", "kind": "separator"}, {"value": "pos_charge_category", "kind": "cdf"}, {"value": " ", "kind": "separator"}, {"value": "pos_charge_description", "kind": "cdf"}, {"value": " ", "kind": "separator"}, {"value": "tax_type", "kind": "cdf"}, {"value": " ", "kind": "separator"}, {"value": "payment_method", "kind": "cdf"}, {"value": " ", "kind": "separator"}], "kind": "String", "created_at": "2023-05-19T11:11:59Z", "updated_at": "2023-05-19T11:11:59Z"}], "published": true, "rules": {"country_codes": null, "feature_ids": ["report_builder"], "property_ids": null, "property_types": null}}