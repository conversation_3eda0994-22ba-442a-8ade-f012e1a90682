{"id": "77", "title": "Add-ons, Items, and Services Sold", "description": "Add-ons, extras, and items and services added to reservations, grouped by type with room number and reservation details. Filtered by date, add-on name, and more. ", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "room_number"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "room_type"}}, {"cdf": {"type": "default", "column": "quantity_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}}, {"cdf": {"type": "default", "column": "reservation_source"}}, {"cdf": {"type": "default", "column": "reservation_status"}}, {"cdf": {"type": "default", "column": "transaction_notes"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "group_profile_name"}}, {"cdf": {"type": "default", "column": "private_rate_plan"}}], "group_rows": [{"cdf": {"type": "custom", "column": "custom_category"}}, {"cdf": {"type": "custom", "column": "custom_item_name"}}, {"cdf": {"type": "default", "column": "checkin_date"}}], "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "greater_than_or_equal", "value": "start_current_year"}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "less_than", "value": "start_next_quarter"}, {"cdf": {"type": "default", "column": "room_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_source"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_status"}, "operator": "list_contains", "value": ["Confirmation Pending", "Confirmed", "Cancelled", "In-House"]}, {"cdf": {"type": "default", "column": "transaction_status"}, "operator": "list_contains", "value": ["Posted", "Pending"]}, {"and": [{"or": [{"cdf": {"type": "default", "column": "addon_charge_type"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "addon_item"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "item_service_category"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "item_service_type"}, "operator": "is_not_null", "value": ""}]}]}, {"cdf": {"type": "default", "column": "is_void"}, "operator": "equals", "value": "No"}, {"cdf": {"type": "default", "column": "checkin_date"}, "operator": "greater_than_or_equal", "value": "start_current_month"}, {"cdf": {"type": "default", "column": "checkin_date"}, "operator": "less_than", "value": "start_next_quarter"}]}, "sort": [{"cdf": {"type": "custom", "column": "custom_category"}, "direction": "asc"}, {"cdf": {"type": "custom", "column": "custom_item_name"}, "direction": "asc"}, {"cdf": {"type": "default", "column": "checkin_date"}, "direction": "asc"}], "settings": {"details": true, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "Summary", "folder_id": "360865300480000", "updated_at": "2023-11-15T22:43:18Z", "property_ids": ["22425"], "user_id": "338172", "custom_cdfs": [{"id": "5", "column": "custom_category", "name": "Category", "description": "Category", "formula": [{"value": "addon_charge_type", "kind": "cdf"}, {"value": " ", "kind": "separator"}, {"value": "item_service_category", "kind": "cdf"}, {"value": " ", "kind": "separator"}], "kind": "String", "created_at": "2023-06-28T09:34:41Z", "updated_at": "2023-11-15T22:41:32Z"}, {"id": "6", "column": "custom_item_name", "name": "Item Name", "description": "Item Name", "formula": [{"value": "addon_item", "kind": "cdf"}, {"value": " ", "kind": "separator"}, {"value": "item_service_type", "kind": "cdf"}, {"value": " ", "kind": "separator"}], "kind": "String", "created_at": "2023-06-28T09:35:14Z", "updated_at": "2023-11-15T22:43:18Z"}], "published": true, "rules": {"country_codes": null, "property_types": null, "property_ids": null, "feature_ids": null}}