{"id": "83", "title": "Payment Reconciliation", "description": "Last and current month payments by payment method and user. Filter for other periods.", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "default", "column": "payment_method"}}, {"cdf": {"type": "default", "column": "user"}}], "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "greater_than_or_equal", "value": "start_last_month"}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "less_than", "value": "tomorrow"}, {"cdf": {"type": "default", "column": "transaction_status"}, "operator": "list_contains", "value": ["Posted"]}, {"cdf": {"type": "default", "column": "user"}, "operator": "is_not_null", "value": ""}]}, "sort": null, "settings": {"details": false, "totals": true, "transpose": false}, "formats": {"link": true}, "type": "Summary", "folder_id": "360884559937536", "updated_at": "2023-07-14T09:08:38Z", "property_ids": ["22425"], "user_id": "260679", "custom_cdfs": [], "published": true, "rules": {"country_codes": null, "property_types": null, "property_ids": null, "feature_ids": null}}