{"id": "96", "title": "Pace Report", "description": "Provides a snapshot of future bookings, comparing current on-the-books data against historical trends to help forecast occupancy, revenue, and identify booking patterns.\n", "dataset_id": 7, "columns": [{"cdf": {"type": "default", "column": "mfd_occupancy"}}, {"cdf": {"type": "default", "column": "occupancy"}}, {"cdf": {"type": "default", "column": "room_revenue"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "adr"}}, {"cdf": {"type": "default", "column": "revpar"}}, {"cdf": {"type": "default", "column": "capacity_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "rooms_sold"}, "metrics": ["sum"]}, {"cdf": {"type": "custom", "column": "custom_rooms_available"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "default", "column": "stay_date"}, "modifier": "d"}], "group_columns": null, "periods": null, "comparisons": [{"name": "This Year", "filters": {"and": [{"cdf": {"type": "default", "column": "booking_date", "multi_level_id": 4}, "operator": "less_than", "value": "tomorrow"}, {"cdf": {"type": "default", "column": "stay_date"}, "operator": "greater_than_or_equal", "value": "start_current_year"}, {"cdf": {"type": "default", "column": "stay_date"}, "operator": "less_than", "value": "start_next_year"}]}}, {"name": "Last Year", "filters": {"and": [{"cdf": {"type": "default", "column": "booking_date", "multi_level_id": 4}, "operator": "less_than_or_equal", "value": "years_prior;1"}, {"cdf": {"type": "default", "column": "stay_date"}, "operator": "greater_than_or_equal", "value": "start_last_year"}, {"cdf": {"type": "default", "column": "stay_date"}, "operator": "less_than", "value": "start_current_year"}]}}], "filters": null, "sort": [{"cdf": {"type": "default", "column": "stay_date"}, "direction": "asc"}], "settings": {"details": false, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "SummaryComparison", "folder_id": "360875169153024", "updated_at": "2024-07-24T16:21:47Z", "property_ids": ["22425"], "user_id": "338172", "custom_cdfs": [{"id": "7", "column": "custom_rooms_available", "name": "Rooms Available", "description": "The total number of available rooms, not counting those already sold, blocked, or out of service.", "formula": [{"kind": "cdf", "value": "capacity_count", "metric": null}, {"kind": "operator", "value": "-", "metric": null}, {"kind": "cdf", "value": "rooms_sold", "metric": null}, {"kind": "operator", "value": "-", "metric": null}, {"kind": "cdf", "value": "blocked_room_count", "metric": null}, {"kind": "operator", "value": "-", "metric": null}, {"kind": "cdf", "value": "out_of_service_count", "metric": null}], "kind": "Number", "created_at": "2024-05-23T18:13:38Z", "updated_at": "2024-07-23T21:56:40Z", "format": "number"}], "published": true, "rules": {"feature_ids": null, "property_ids": null, "country_codes": null, "property_types": null}}