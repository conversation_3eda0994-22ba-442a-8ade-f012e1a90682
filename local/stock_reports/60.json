{"id": 60, "title": "Credit notes report", "description": "A report on all issued Credit notes", "dataset_id": 6, "columns": [{"cdf": {"type": "default", "column": "invoice_number"}}, {"cdf": {"type": "default", "column": "invoice_type"}}, {"cdf": {"type": "default", "column": "invoice_generate_datetime"}}, {"cdf": {"type": "default", "column": "credit_reason"}}, {"cdf": {"type": "default", "column": "bill_to_party"}}, {"cdf": {"type": "default", "column": "bill_to_tax_id"}}, {"cdf": {"type": "default", "column": "bill_to_country_code"}}, {"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "total_gross_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "taxes_value_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "original_invoice_number"}}, {"cdf": {"type": "default", "column": "original_invoice_date"}}], "group_rows": null, "group_columns": null, "filters": {"and": [{"cdf": {"type": "default", "column": "invoice_type"}, "operator": "equals", "value": "Credit Note"}, {"cdf": {"type": "default", "column": "invoice_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "invoice_generate_datetime"}, "operator": "greater_than_or_equal", "value": "start_last_year"}, {"cdf": {"type": "default", "column": "bill_to_party"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "bill_to_tax_id"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "bill_to_country_code"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "total_gross_amount"}, "operator": "is_not_null", "value": null}, {"cdf": {"type": "default", "column": "original_invoice_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "original_invoice_date"}, "operator": "greater_than_or_equal", "value": "start_last_year"}, {"cdf": {"type": "default", "column": "reservation_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_checkin_date"}, "operator": "greater_than_or_equal", "value": "start_last_year"}, {"cdf": {"type": "default", "column": "reservation_checkout_date"}, "operator": "greater_than_or_equal", "value": "start_last_year"}]}, "sort": null, "settings": {"details": true, "totals": false, "transpose": false}, "type": "List", "updated_at": "2023-02-09T14:19:38Z", "property_ids": [22425], "user_id": 260679, "published": true, "formats": {"link": true}, "rules": {"property_ids": null, "feature_ids": ["report_builder"], "country_codes": null}}