{"id": "87", "title": "(INTERNAL) Onboarding Reservation Import", "description": "This is for internal use only", "dataset_id": 3, "columns": [{"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "primary_guest_first_name"}}, {"cdf": {"type": "default", "column": "primary_guest_surname"}}, {"cdf": {"type": "default", "column": "primary_guest_email"}}, {"cdf": {"type": "default", "column": "primary_guest_phone_number"}}, {"cdf": {"type": "default", "column": "primary_guest_mobile_phone_number"}}, {"cdf": {"type": "default", "column": "primary_guest_address"}}, {"cdf": {"type": "default", "column": "primary_guest_address_line_2"}}, {"cdf": {"type": "default", "column": "primary_guest_city"}}, {"cdf": {"type": "default", "column": "primary_guest_state"}}, {"cdf": {"type": "default", "column": "primary_guest_postal_code"}}, {"cdf": {"type": "default", "column": "primary_guest_residence_country_code"}}, {"cdf": {"type": "default", "column": "primary_guest_residence_country"}}, {"cdf": {"type": "default", "column": "third_party_confirmation_number"}}, {"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "room_types"}}, {"cdf": {"type": "default", "column": "room_numbers"}}, {"cdf": {"type": "default", "column": "room_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "room_nights_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "adults_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "children_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "guest_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "reservation_status"}}, {"cdf": {"type": "default", "column": "room_revenue_total_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "grand_total_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "taxes_value_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "deposit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "reservation_paid_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "reservation_balance_due_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "card_type"}}, {"cdf": {"type": "default", "column": "reservation_source"}}, {"cdf": {"type": "default", "column": "booking_datetime_property_timezone"}}, {"cdf": {"type": "default", "column": "active_booking_notes"}}, {"cdf": {"type": "default", "column": "active_primary_guest_notes"}}, {"cdf": {"type": "default", "column": "active_group_profile_notes"}}, {"cdf": {"type": "default", "column": "allotment_block_notes"}}, {"cdf": {"type": "default", "column": "primary_guest_birth_date"}}, {"cdf": {"type": "default", "column": "estimated_arrival_time"}}, {"cdf": {"type": "default", "column": "primary_guest_status_level"}}, {"cdf": {"type": "default", "column": "primary_guest_document_type"}}, {"cdf": {"type": "default", "column": "primary_guest_document_number"}}, {"cdf": {"type": "default", "column": "primary_guest_document_issuing_country_code"}}, {"cdf": {"type": "default", "column": "primary_guest_document_issue_date"}}, {"cdf": {"type": "default", "column": "primary_guest_document_expiration_date"}}, {"cdf": {"type": "default", "column": "primary_guest_document_issuing_country"}}, {"cdf": {"type": "default", "column": "cancellation_datetime_property_timezone"}}], "group_rows": null, "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "checkout_date"}, "operator": "greater_than_or_equal", "value": "today"}]}, "sort": null, "settings": {"details": true, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "List", "folder_id": "360891570716672", "updated_at": "2024-01-10T14:24:57Z", "property_ids": ["22425"], "user_id": "338172", "custom_cdfs": [], "published": true, "rules": {"country_codes": null, "property_types": null, "property_ids": ["22425", "185735", "185736", "200370", "200935", "202062"], "feature_ids": null}}