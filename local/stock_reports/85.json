{"id": "85", "title": "Rooms Sold and Occupancy", "description": "Rooms sold and occupancy by stay date with details. Filter for other periods. ", "dataset_id": 4, "columns": [{"cdf": {"type": "default", "column": "booking_qty_type_a"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "occupancy"}}, {"cdf": {"type": "default", "column": "adr"}}, {"cdf": {"type": "default", "column": "revpar"}}, {"cdf": {"type": "default", "column": "room_revenue_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "booking_date"}}, {"cdf": {"type": "default", "column": "room_type"}}, {"cdf": {"type": "default", "column": "blocked_room_type_a"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "capacity_type_a"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "default", "column": "stay_date"}}], "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "stay_date"}, "operator": "greater_than_or_equal", "value": "months_prior;1"}, {"cdf": {"type": "default", "column": "stay_date"}, "operator": "less_than", "value": "months_later;1"}]}, "sort": null, "settings": {"details": false, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "Summary", "folder_id": "360875169153024", "updated_at": "2023-08-04T20:21:26Z", "property_ids": ["22425"], "user_id": "338172", "custom_cdfs": [], "published": true, "rules": {"country_codes": null, "property_ids": null, "property_types": null, "feature_ids": ["report_builder"]}}