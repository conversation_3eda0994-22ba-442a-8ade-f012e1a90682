{"id": 58, "title": "Invoices and credit notes report", "description": "A report on all issued invoices and credit notes", "dataset_id": 6, "columns": [{"cdf": {"type": "default", "column": "invoice_number"}}, {"cdf": {"type": "default", "column": "invoice_generate_datetime"}}, {"cdf": {"type": "default", "column": "bill_to_party"}}, {"cdf": {"type": "default", "column": "bill_to_tax_id"}}, {"cdf": {"type": "default", "column": "bill_to_country_code"}}, {"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "total_gross_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "taxes_value_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "invoice_type"}}, {"cdf": {"type": "default", "column": "invoice_status"}}], "group_rows": null, "group_columns": null, "filters": {"and": [{"cdf": {"type": "default", "column": "invoice_generate_datetime"}, "operator": "greater_than_or_equal", "value": "start_last_quarter"}, {"cdf": {"type": "default", "column": "invoice_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "bill_to_party"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "bill_to_tax_id"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "bill_to_country_code"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_checkin_date"}, "operator": "greater_than_or_equal", "value": "start_last_quarter"}, {"cdf": {"type": "default", "column": "reservation_checkout_date"}, "operator": "less_than_or_equal", "value": "start_next_year"}, {"cdf": {"type": "default", "column": "invoice_status"}, "operator": "list_contains", "value": ["Open", "Voided"]}, {"cdf": {"type": "default", "column": "invoice_type"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "total_gross_amount"}, "operator": "is_not_null", "value": null}]}, "sort": [{"cdf": {"type": "default", "column": "invoice_number"}, "direction": "asc"}], "settings": {"details": true, "totals": false, "transpose": false}, "type": "List", "updated_at": "2023-03-09T10:50:29Z", "property_ids": [22425], "user_id": 260679, "published": true, "formats": {"link": true}, "rules": {"feature_ids": null, "property_ids": [887, 1587, 2089, 2228, 2771, 2942, 3359, 3474, 4466, 5524, 5806, 5903, 5905, 5913, 6238, 6273, 6632, 6743, 7008, 7022, 7025, 7164, 7806, 8189, 8266, 8379, 8503, 8583, 8697, 8809, 8836, 8873, 8874, 8889, 8890, 8918, 9543, 9565, 9827, 10095, 10321, 10637, 10668, 10837, 11136, 15124, 15155, 15322, 15395, 15608, 15611, 16774, 17066, 17102, 17164, 17226, 17280, 17627, 17741, 17781, 17888, 17898, 17947, 18056, 18184, 18977, 19280, 19344, 19910, 20106, 20458, 20617, 20690, 20751, 21308, 21422, 21633, 21950, 22247, 22289, 22838, 23215, 23258, 23583, 24197, 24239, 24306, 24498, 24684, 24791, 24940, 24955, 24970, 24987, 25129, 25180, 25470, 25670, 25679, 25698, 25736, 25837, 25904, 25954, 25961, 26086, 26213, 26215, 26344, 26457, 26780, 26974, 27072, 27270, 27371, 27396, 27519, 27562, 27662, 27668, 27801, 27808, 27810, 28173, 28334, 28533, 28776, 160269, 163824, 163828, 163868, 163910, 163987, 164191, 164194, 164266, 164360, 164418, 164567, 164627, 164651, 164782, 164868, 165030, 165032, 165066, 165506, 165542, 165549, 165659, 165844, 165858, 165892, 168922, 168927, 169049, 169217, 169256, 169419, 169489, 169622, 169669, 169678, 169751, 169816, 170064, 170198, 170306, 170309, 170312, 170379, 170418, 170444, 170667, 170729, 170750, 170851, 171153, 171393, 171554, 171833, 171912, 171922, 172020, 172023, 172235, 172416, 172435, 172550, 172588, 172618, 172718, 172749, 172895, 172910, 173097, 173306, 173390, 173399, 173433, 173538, 173554, 173613, 174006, 174039, 174061, 174062, 174116, 174143, 174162, 174175, 174265, 174302, 174414, 174622, 174801, 174805, 174810, 174936, 175170, 175247, 175284, 175297, 175340, 175408, 175639, 175878, 175914, 175947, 176010, 176065, 176207, 176210, 176238, 176296, 176350, 176453, 176522, 176690, 176817, 176819, 176994, 177034, 177111, 177519, 177582, 177653, 177714, 177723, 177793, 177828, 177951, 177987, 177998, 178058, 178297, 178715, 178724, 178750, 179329, 179397, 179611, 179619, 179639, 179640, 179787, 179847, 179855, 179906, 179950, 179962, 180056, 180101, 180251, 180330, 180658, 180740, 181111, 181265, 181334, 181426, 181642, 181802, 181947, 181986, 182001, 182050, 182080, 182178, 182360, 182390, 182452, 182487, 182528, 182565, 182732, 182763, 182958, 183021, 183065, 183240, 183397, 183399, 183529, 183543, 183631, 183635, 183669, 183724, 184081, 184091, 184149, 184291, 184385, 184543, 184556, 184558, 184575, 184577, 184657, 184667, 184687, 184909, 184936, 184945, 184971, 185021, 185122, 185169, 185321, 185358, 185432, 185433, 185470, 185543, 185548, 185680, 185752, 185916, 185945, 185954, 186001, 186205, 186212, 186252, 186274, 186753, 186784, 186813, 186821, 186872, 186945, 187065, 187092, 187195, 187232, 187258, 187299, 191085, 191224, 191281, 191347, 191411, 191529, 191642, 191694, 191736, 191974, 192108, 192224, 192297, 192313, 192440, 192456, 192471, 192526, 192542, 192685, 192737, 192786, 192826, 192854, 192883, 192962, 192990, 192993, 193000, 193078, 193298, 193473, 193500, 193508, 193720, 193779, 193862, 193966, 193967, 193968, 194057, 194061, 194096, 194105, 194146, 194192, 194268, 194274, 194317, 194395, 194668, 194686, 194688, 194711, 194712, 194738, 194786, 194902, 195067, 195098, 195246, 195277, 195351, 195475, 195480, 195594, 195603, 195638, 195655, 195685, 195708, 195768, 195769, 195778, 195791, 195851, 195972, 195996, 196036, 196091, 196122, 196165, 196316, 196343, 196496, 196524, 196699, 196751, 196804, 196809, 196826, 196856, 196919, 196961, 197004, 197110, 197127, 197149, 197202, 197206, 197213, 197214, 197298, 197329, 197350, 197438, 197476, 197500, 197515, 197538, 197564, 197633, 197655, 197657, 197770, 197850, 198050, 198107, 198139, 198177, 198295, 198304, 198332, 198467, 198469, 198470, 198481, 198511, 198512, 198519, 198566, 198623, 198712, 198725, 198727, 198748, 198874, 198932, 199008, 199021, 199093, 199275, 199311, 199384, 199447, 199456, 199470, 199508, 199533, 199535, 199593, 199932, 200000, 200039, 200284, 200422, 200497, 200567, 200592, 200625, 200707, 200815, 200958, 201030, 201037, 201106, 201155, 201214, 201323, 201366, 201436, 201445, 201460, 201676, 201759, 201760, 201805, 201894, 202079, 202092, 202249, 202252, 202383, 202561, 202563, 202564, 202620, 202743, 202787, 202794, 202808, 202812, 202832, 202969, 202995, 203000, 203009, 203044, 203156, 203165, 203166, 203167, 203194, 203271, 203285, 203297, 203345, 203394, 203435, 203474, 203548, 203563, 203595, 203619, 203633, 203672, 203809, 203955, 203957, 203966, 203983, 204011, 204043, 204053, 204102, 204136, 204225, 204275, 204295, 204302, 204359, 204661, 204675, 204725, 204829, 204868, 204878, 205135, 205139, 205176, 205221, 205259, 205306, 205328, 205374, 205471, 205475, 205498, 205523, 205605, 205670, 205678, 205694, 205708, 205730, 205735, 205805, 205851, 205892, 205984, 205989, 206024, 206094, 206097, 206132, 206322, 206353, 206355, 206361, 206411, 206476, 206525, 206535, 206766, 207419, 207496, 207497, 207498, 207621, 208103, 208155, 208510, 208511, 208586, 208781, 208831, 208894, 209862, 210085, 210086, 210222, 210533, 210837, 210861, 210875, 210903, 210969, 210972, 210986, 210987, 211621, 211656, 212086, 212120, 212188, 212197, 212637, 212896, 212907, 213032, 213718, 213797, 213870, 213871, 213872, 213873, 213874, 213875, 214014, 214086, 214343, 216735, 216841, 216990, 217016, 218579, 219224, 220517, 221471, 226649, 233455, 241207, 254363, 254645, 256202, 256453], "country_codes": null}}