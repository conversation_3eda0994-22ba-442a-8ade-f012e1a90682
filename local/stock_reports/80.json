{"id": "80", "title": "Payment Processing Report", "description": "Payment processing report with full payment details. Filter by date, reservation, status and more. ", "dataset_id": 5, "columns": [{"cdf": {"type": "default", "column": "payment_schedule_datetime"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "payment_gateway_transaction_id"}}, {"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "card_type"}}, {"cdf": {"type": "default", "column": "payment_gateway_status"}}, {"cdf": {"type": "default", "column": "payment_gateway_result"}}, {"cdf": {"type": "default", "column": "payment_gateway"}}, {"cdf": {"type": "default", "column": "payment_submitted_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "payment_fee_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "payment_net_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "payment_method"}}, {"cdf": {"type": "default", "column": "card_issue_country"}}, {"cdf": {"type": "default", "column": "card_last_4_digits"}}, {"cdf": {"type": "default", "column": "terminal_label"}}, {"cdf": {"type": "default", "column": "pos_entry_mode"}}], "group_rows": null, "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "payment_schedule_datetime"}, "operator": "greater_than_or_equal", "value": "start_current_month"}, {"cdf": {"type": "default", "column": "payment_schedule_datetime"}, "operator": "less_than", "value": "tomorrow"}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "checkin_date"}, "operator": "is_not_null", "value": "2023-05-15T00:00:00Z"}, {"cdf": {"type": "default", "column": "checkout_date"}, "operator": "is_not_null", "value": "2023-05-15T00:00:00Z"}, {"cdf": {"type": "default", "column": "payment_gateway_status"}, "operator": "is_not_null", "value": ""}]}, "sort": [{"cdf": {"type": "default", "column": "payment_schedule_datetime"}, "direction": "asc"}], "settings": {"details": true, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "List", "folder_id": "360884559937536", "updated_at": "2023-07-14T09:08:27Z", "property_ids": ["22425"], "user_id": "260679", "custom_cdfs": [], "published": true, "rules": {"country_codes": null, "property_types": null, "property_ids": null, "feature_ids": null}}