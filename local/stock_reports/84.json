{"id": "84", "title": "User Reconciliation", "description": "Current month transactions by user and by type. Filter for other periods or to see a specific user only. ", "dataset_id": 1, "columns": [{"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "default", "column": "user"}}, {"cdf": {"type": "default", "column": "transaction_type"}}, {"cdf": {"type": "custom", "column": "custom_revenue_type"}}], "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "greater_than_or_equal", "value": "start_current_month"}, {"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "less_than", "value": "tomorrow"}, {"cdf": {"type": "default", "column": "transaction_status"}, "operator": "list_contains", "value": ["Posted"]}, {"cdf": {"type": "default", "column": "user"}, "operator": "is_not_null", "value": ""}]}, "sort": [{"cdf": {"type": "default", "column": "user"}, "direction": "asc"}, {"cdf": {"type": "default", "column": "transaction_type"}, "direction": "asc"}, {"cdf": {"type": "custom", "column": "custom_revenue_type"}, "direction": "asc"}], "settings": {"details": false, "totals": true, "transpose": false}, "formats": {"link": true}, "type": "Summary", "folder_id": "360871725367296", "updated_at": "2023-07-14T09:10:52Z", "property_ids": ["22425"], "user_id": "260679", "custom_cdfs": [{"id": "4", "column": "custom_revenue_type", "name": "Revenue type", "description": "", "formula": [{"kind": "cdf", "value": "addon_item"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "addon_charge_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "fee_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "item_service_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "item_service_category"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "pos_charge_category"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "pos_charge_description"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "room_revenue_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "tax_type"}, {"kind": "separator", "value": " "}, {"kind": "cdf", "value": "payment_method"}, {"kind": "separator", "value": " "}], "kind": "String", "created_at": "2023-06-27T13:50:53Z", "updated_at": "2023-06-27T13:50:53Z"}], "published": true, "rules": {"country_codes": null, "property_types": null, "property_ids": null, "feature_ids": null}}