{"id": "81", "title": "Channel Production", "description": "Channel production report with details by reservation source and category. Filter by stay date, room type and source category. ", "dataset_id": 4, "columns": [{"cdf": {"type": "default", "column": "room_revenue_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "booking_qty_type_a"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "adr"}}], "group_rows": [{"cdf": {"type": "default", "column": "reservation_source"}}], "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "stay_date"}, "operator": "less_than", "value": "months_later;2"}, {"cdf": {"type": "default", "column": "stay_date"}, "operator": "greater_than_or_equal", "value": "months_prior;6"}, {"cdf": {"type": "default", "column": "room_type"}, "operator": "is_not_empty", "value": ""}, {"cdf": {"type": "default", "column": "reservation_source_category"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_source"}, "operator": "is_not_null", "value": ""}]}, "sort": [{"cdf": {"type": "default", "column": "reservation_source"}, "direction": "asc"}], "settings": {"details": false, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "Summary", "folder_id": "360865300480000", "updated_at": "2023-08-04T20:22:15Z", "property_ids": ["22425"], "user_id": "338172", "custom_cdfs": [], "published": true, "rules": {"country_codes": null, "feature_ids": ["report_builder"], "property_ids": null, "property_types": null}}