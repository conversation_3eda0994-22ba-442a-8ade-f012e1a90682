{"id": 66, "title": "Guest Ledger Report", "description": "A report that shows total, paid and balance due amount for In House reservation, with reservation details. ", "dataset_id": 3, "columns": [{"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "room_numbers"}}, {"cdf": {"type": "default", "column": "reservation_status"}}, {"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "room_nights_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "guest_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "grand_total_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "reservation_paid_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "reservation_balance_due_amount"}, "metrics": ["sum"]}], "group_rows": null, "group_columns": null, "periods": null, "filters": {"and": [{"cdf": {"type": "default", "column": "reservation_status"}, "operator": "list_contains", "value": ["In-House"]}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}, "operator": "is_not_empty", "value": ""}, {"cdf": {"type": "default", "column": "room_numbers"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "checkin_date"}, "operator": "is_not_null", "value": "2023-03-23T00:00:00Z"}, {"cdf": {"type": "default", "column": "grand_total_amount"}, "operator": "is_not_null", "value": null}, {"cdf": {"type": "default", "column": "reservation_balance_due_amount"}, "operator": "is_not_null", "value": null}]}, "sort": null, "settings": {"details": true, "totals": false, "transpose": false}, "type": "List", "updated_at": "2023-03-23T10:38:27Z", "property_ids": [22425], "user_id": 260679, "published": true, "formats": {"link": true}, "rules": {"feature_ids": ["report_builder"], "country_codes": null, "property_ids": null}}