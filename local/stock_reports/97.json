{"id": "97", "title": "Occupancy History and Forecast", "description": "The report provides an overview of the property, showing the number of rooms sold, their total capacity, and the remaining inventory. It also displays the occupancy rate and blocked rooms.", "dataset_id": 7, "columns": [{"cdf": {"type": "default", "column": "rooms_sold"}, "metrics": ["sum"]}, {"cdf": {"type": "custom", "column": "custom_rooms_available"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "capacity_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "blocked_room_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "out_of_service_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "mfd_occupancy"}}, {"cdf": {"type": "default", "column": "occupancy"}}, {"cdf": {"type": "default", "column": "additional_room_revenue"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "room_rate"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "room_revenue"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"type": "default", "column": "stay_date"}}], "group_columns": null, "periods": null, "comparisons": null, "filters": {"and": [{"cdf": {"type": "default", "column": "stay_date"}, "operator": "greater_than_or_equal", "value": "start_last_week"}, {"cdf": {"type": "default", "column": "stay_date"}, "operator": "less_than", "value": "days_later;14"}, {"cdf": {"type": "default", "column": "reservation_source", "multi_level_id": 4}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_source_category", "multi_level_id": 4}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "room_type"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "reservation_status", "multi_level_id": 4}, "operator": "list_contains", "value": ["Confirmation Pending", "Confirmed", "Checked Out", "In-House"]}]}, "sort": [{"cdf": {"type": "default", "column": "stay_date"}, "direction": "asc"}], "settings": {"details": false, "totals": false, "transpose": false}, "formats": {"link": true}, "type": "Summary", "folder_id": "360875169153024", "tags": [], "updated_at": "2024-09-26T13:20:08Z", "property_ids": ["22425"], "user_id": "227001", "custom_cdfs": [{"id": "8", "column": "custom_rooms_available", "name": "Rooms Available", "description": "The total number of available rooms, not counting those already sold, blocked, or out of service.", "formula": [{"kind": "cdf", "value": "capacity_count", "metric": null}, {"kind": "operator", "value": "-", "metric": null}, {"kind": "cdf", "value": "rooms_sold", "metric": null}, {"kind": "operator", "value": "-", "metric": null}, {"kind": "cdf", "value": "blocked_room_count", "metric": null}, {"kind": "operator", "value": "-", "metric": null}, {"kind": "cdf", "value": "out_of_service_count", "metric": null}], "kind": "Number", "created_at": "2024-05-23T21:15:14Z", "updated_at": "2024-07-23T21:58:10Z", "format": "number"}], "published": true, "rules": {"feature_ids": null, "property_ids": null, "country_codes": null, "property_types": null}}