{"id": 6, "title": "Expanded Reservation Report with Financials", "description": "A detailed reservation report with 30 fields.  This can be saved as a local reports and edited to include exactly what you want.", "dataset_id": 3, "columns": [{"cdf": {"type": "default", "column": "property_name"}}, {"cdf": {"type": "default", "column": "group_profile_name"}}, {"cdf": {"type": "default", "column": "primary_guest_full_name"}}, {"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "is_repeat_guest"}}, {"cdf": {"type": "default", "column": "primary_guest_status_level"}}, {"cdf": {"type": "default", "column": "reservation_status"}}, {"cdf": {"type": "default", "column": "booking_datetime"}}, {"cdf": {"type": "default", "column": "reservation_source_category"}}, {"cdf": {"type": "default", "column": "reservation_source"}}, {"cdf": {"type": "default", "column": "public_rate_plan"}}, {"cdf": {"type": "default", "column": "is_hotel_collect_booking"}}, {"cdf": {"type": "default", "column": "primary_guest_mobile_phone_number"}}, {"cdf": {"type": "default", "column": "primary_guest_email"}}, {"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "room_nights_count"}}, {"cdf": {"type": "default", "column": "room_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "adults_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "children_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "guest_count"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "deposit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "reservation_paid_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "room_revenue_total_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "taxes_value_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "grand_total_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "reservation_balance_due_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "active_booking_notes"}}, {"cdf": {"type": "default", "column": "active_primary_guest_notes"}}, {"cdf": {"type": "default", "column": "active_group_profile_notes"}}], "group_rows": null, "group_columns": null, "filters": {"or": [{"cdf": {"type": "default", "column": "booking_datetime"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"and": [{"cdf": {"type": "default", "column": "checkin_date"}, "operator": "less_than_or_equal", "value": "tomorrow"}, {"cdf": {"type": "default", "column": "checkout_date"}, "operator": "greater_than_or_equal", "value": "today"}]}]}, "sort": null, "settings": {"details": true, "totals": false, "transpose": false}, "type": "List", "updated_at": "2023-01-09T17:53:32Z", "property_ids": [22425], "user_id": 260679, "published": true, "rules": {"property_ids": null, "feature_ids": ["report_builder"], "country_codes": null}, "formats": {"link": true}}