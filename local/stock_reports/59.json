{"id": 59, "title": "Invoices and credit notes report with details", "description": "A report on all invoices and credit notes with information about all transactions included in each one of them", "dataset_id": 6, "columns": [{"cdf": {"type": "default", "column": "invoice_number"}}, {"cdf": {"type": "default", "column": "invoice_generate_datetime"}}, {"cdf": {"type": "default", "column": "bill_to_party"}}, {"cdf": {"type": "default", "column": "bill_to_tax_id"}}, {"cdf": {"type": "default", "column": "bill_to_country_code"}}, {"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "reservation_checkin_date"}}, {"cdf": {"type": "default", "column": "reservation_checkout_date"}}, {"cdf": {"type": "default", "column": "total_gross_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "taxes_value_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "invoice_type"}}, {"cdf": {"type": "default", "column": "invoice_status"}}, {"cdf": {"type": "default", "column": "payment_due_date"}}, {"cdf": {"type": "default", "column": "description", "multi_level_id": 3}}, {"cdf": {"type": "default", "column": "quantity", "multi_level_id": 3}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "amount", "multi_level_id": 3}, "metrics": ["sum"]}], "group_rows": null, "group_columns": null, "filters": {"and": [{"cdf": {"type": "default", "column": "invoice_number"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "invoice_generate_datetime"}, "operator": "greater_than_or_equal", "value": "start_current_month"}, {"cdf": {"type": "default", "column": "bill_to_party"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "bill_to_tax_id"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "bill_to_country_code"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "total_gross_amount"}, "operator": "is_not_null", "value": null}, {"cdf": {"type": "default", "column": "taxes_value_amount"}, "operator": "is_not_null", "value": null}, {"cdf": {"type": "default", "column": "invoice_type"}, "operator": "is_not_null", "value": ""}, {"cdf": {"type": "default", "column": "invoice_status"}, "operator": "list_contains", "value": ["Open", "Voided"]}, {"cdf": {"type": "default", "column": "payment_due_date"}, "operator": "greater_than_or_equal", "value": "start_last_quarter"}, {"cdf": {"type": "default", "column": "reservation_checkin_date"}, "operator": "greater_than_or_equal", "value": "start_last_quarter"}, {"cdf": {"type": "default", "column": "reservation_checkout_date"}, "operator": "greater_than_or_equal", "value": "start_last_quarter"}, {"cdf": {"type": "default", "column": "reservation_number"}, "operator": "is_not_null", "value": ""}]}, "sort": null, "settings": {"details": true, "totals": false, "transpose": false}, "type": "List", "updated_at": "2023-02-09T14:12:53Z", "property_ids": [22425], "user_id": 260679, "published": true, "formats": {"link": true}, "rules": {"property_ids": null, "feature_ids": ["report_builder"], "country_codes": null}}