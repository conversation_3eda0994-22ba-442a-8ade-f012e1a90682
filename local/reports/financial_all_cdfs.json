{"property_id": "79", "title": "ALL FINANCIAL", "description": "", "property_ids": ["79"], "dataset_id": 1, "filters": {"and": [{"value": "start_last_month", "cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}, "operator": "greater_than_or_equal"}, {"value": ["Posted"], "cdf": {"type": "default", "column": "transaction_status"}, "operator": "list_contains"}]}, "columns": [{"cdf": {"type": "default", "column": "transaction_datetime_property_timezone"}}, {"cdf": {"type": "default", "column": "reservation_number"}}, {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "balance_due_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "transaction_status"}}, {"cdf": {"type": "default", "column": "booking_datetime_property_timezone"}}, {"cdf": {"type": "default", "column": "booking_datetime"}}, {"cdf": {"type": "default", "column": "card_type"}}, {"cdf": {"type": "default", "column": "checkin_date"}}, {"cdf": {"type": "default", "column": "checkout_date"}}, {"cdf": {"type": "default", "column": "group_profile_name"}}, {"cdf": {"type": "default", "column": "group_profile_type"}}, {"cdf": {"type": "default", "column": "is_hotel_collect_booking"}}, {"cdf": {"type": "default", "column": "private_rate_plan"}}, {"cdf": {"type": "default", "column": "public_rate_plan"}}, {"cdf": {"type": "default", "column": "reservation_source"}}, {"cdf": {"type": "default", "column": "reservation_source_category"}}, {"cdf": {"type": "default", "column": "reservation_status"}}, {"cdf": {"type": "default", "column": "room_number"}}, {"cdf": {"type": "default", "column": "room_type"}}, {"cdf": {"type": "default", "column": "addon_charge_type"}}, {"cdf": {"type": "default", "column": "addon_item"}}, {"cdf": {"type": "default", "column": "is_transaction_adjusted"}}, {"cdf": {"type": "default", "column": "card_last_4_digits"}}, {"cdf": {"type": "default", "column": "internal_transaction_code"}}, {"cdf": {"type": "default", "column": "internal_transaction_code_description"}}, {"cdf": {"type": "default", "column": "transaction_code"}}, {"cdf": {"type": "default", "column": "general_ledger_code"}}, {"cdf": {"type": "default", "column": "general_ledger_account"}}, {"cdf": {"type": "default", "column": "custom_code"}}, {"cdf": {"type": "default", "column": "is_transaction_deleted"}}, {"cdf": {"type": "default", "column": "fee_type"}}, {"cdf": {"type": "default", "column": "invoice_created_datetime_property_timezone"}}, {"cdf": {"type": "default", "column": "invoice_created_datetime"}}, {"cdf": {"type": "default", "column": "invoice_guest_name"}}, {"cdf": {"type": "default", "column": "item_service_category"}}, {"cdf": {"type": "default", "column": "item_service_type"}}, {"cdf": {"type": "default", "column": "latest_invoice_number"}}, {"cdf": {"type": "default", "column": "pos_charge_category"}}, {"cdf": {"type": "default", "column": "pos_charge_description"}}, {"cdf": {"type": "default", "column": "payment_method"}}, {"cdf": {"type": "default", "column": "quantity_amount"}, "metrics": ["sum"]}, {"cdf": {"type": "default", "column": "is_refund"}}, {"cdf": {"type": "default", "column": "room_revenue_type"}}, {"cdf": {"type": "default", "column": "tax_type"}}, {"cdf": {"type": "default", "column": "transaction_datetime"}}, {"cdf": {"type": "default", "column": "transaction_notes"}}, {"cdf": {"type": "default", "column": "transaction_type"}}, {"cdf": {"type": "default", "column": "is_void"}}, {"cdf": {"type": "default", "column": "res_room_identifier"}}], "settings": {"totals": false, "details": true, "transpose": false}, "formats": null, "user_id": 260679, "updated_at": "2023-03-01T10:00:00.000Z"}