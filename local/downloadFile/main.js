const GET_TASK_BY_TOKEN_URL = 'http://localhost:8000/datainsights/v1.1/tasks/token/';
const SUCCESS_MSG_BLOCK = '.notification-block--success';
const EXPIRED_MSG_BLOCK = '.notification-block--expired';

function downloadFile(url) {
    const download = document.createElement('a');
    download.href = url;
    download.download = 'test';
    download.target = '_blank';
    document.body.appendChild(download);
    download.click();
    document.body.removeChild(download);
}

function switchMessages(status) {
    document.querySelectorAll('.show').forEach(item => item.classList.remove('show'));
    switch(status) {
        case 'success':
            document.querySelector(SUCCESS_MSG_BLOCK).classList.add('show');
            break;
        case 'expired':
            document.querySelector(EXPIRED_MSG_BLOCK).classList.add('show');
            break;
    }
}

async function getTokenInfo(token) {
    try {
        const response = await fetch(`${GET_TASK_BY_TOKEN_URL}/${token}`);
        const body = await response.json();
        return body.result.url;
    } catch {
        throw new Error(`Can't obtain file url info from the task`);
    }
}

function getTokenURLParam() {
    const searchParams = new URLSearchParams(window.location.search);
    const token = searchParams.get('token');

    if (!token) {
        throw new Error('no URL token is provided');
    }

    return token;
}

async function init() {
    try {
        const token = getTokenURLParam();
        const fileUrl = await getTokenInfo(token);
        downloadFile(fileUrl);
        switchMessages('success')
    } catch (e) {
        switchMessages('expired')
        throw new Error(e);
    }
}

init();