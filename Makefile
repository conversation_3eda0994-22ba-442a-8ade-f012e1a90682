ifneq (,$(wildcard ./.env))
	include .env
endif

SERVICE_IMAGE_NAME ?= reporting-service
SERVICE_HOST ?= reporting-api-local.cloudbeds-dev.com
DOCKER_HOST ?= host.docker.internal
DOCKER_KUBERNETES_HOST ?= kubernetes.docker.internal
DOCKER_REDIS_HOST ?= reporting-redis
DOCKER_DATABASE_HOST ?= reporting-database
DOCKER_AWS_HOST ?= reporting-aws

## Environments available stage-ga
ENV_PREFIX = stage
ENVIRONMENT = ${ENV_PREFIX}-ga
AWS_PROFILE = stage-insights

# Microservices
DISTRIBUTED_ID_SERVICE_URL= distributed-id-service.cloudbeds-${ENV_PREFIX}.com:443
ORGANIZATION_SERVICE_URL = organization-service.cloudbeds-${ENV_PREFIX}.com:443
USER_SERVICE_URL = user-service.cloudbeds-${ENV_PREFIX}.com:443
PERMISSION_SERVICE_URL ?= ${DOCKER_HOST}:9090 # Needs to Port Forward
PERMISSIOM_SERVICE_SSL=0

GX_LINK_SHORTENER_URL = https://api.whistle.cloudbeds-${ENV_PREFIX}.com/url-shortener/urls/short_url


.PHONY: help
help: ## Available commands
	@fgrep -h "##" $(MAKEFILE_LIST) | fgrep -v fgrep | sed -e 's/:.*##\s*/##/g' | awk -F'##' '{ printf "%-30s %s\n", $$1, $$2 }'

.PHONY: install-git-precommit-hook
install-git-precommit-hook: # if host machines has python3, install and configure pre-commit
	command -v python3 >/dev/null 2>&1
	@echo "Python 3 is installed"
	pip install pre-commit==3.7.0
	pre-commit install
	pre-commit install --hook-type commit-msg

.PHONY: setup-env
setup-env: ## Setup environment file
ifeq (, $(shell which envsubst))
	$(error "No envsubst in $(PATH), consider doing apt-get install gettext-base (https://command-not-found.com/envsubst)")
endif

	@$(eval KONG_OIDC_ORGANIZATION_URL=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/signin_organization_url" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_SERVER_ID=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/auth/server_id" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_CLIENT_ID=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/client/id" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_CLIENT_SECRET=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/client/secret" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_SESSION_SECRET=$(shell aws ssm get-parameter --name "/eks/${ENVIRONMENT}/shared/kong/oidc/mfd-idp/session" --with-decryption --query 'Parameter.Value'))
	@$(eval LAUNCH_DARKLY_SDK_KEY=$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/ld-relay/env --with-decryption --output json --query 'Parameter.Value' | jq '.|fromjson' | jq '.sdk_key'))
	@$(eval GX_API_KEY=$(shell aws ssm get-parameter --name "/gx/${ENVIRONMENT}/data-insights/gx-api-key" --with-decryption --query 'Parameter.Value'))
	@$(eval INSIGHTS_STREAMING_API_URL=$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/reporting/dot-env-file --with-decryption --output json --query 'Parameter.Value' | jq '.|fromjson' | jq '.INSIGHTS_STREAMING_API_URL'))
	@$(eval OPENAI_API_KEY=$(shell aws ssm get-parameter --name "/datainsights/$(ENVIRONMENT)/openai/api-key" --with-decryption --query 'Parameter.Value'))

	AURORA_HOST="$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/reporting/dot-env-file --with-decryption --output json --query 'Parameter.Value' | python3 -c "import sys, json; print(json.loads(json.load(sys.stdin))['AURORA_HOST'])")" \
	AURORA_USER="$(shell aws ssm get-parameter --name /data/$(ENVIRONMENT)/postgresql/data_insights_user --with-decryption --output json --query 'Parameter.Value')" \
	AURORA_PASSWORD="$(shell aws ssm get-parameter --name "/data/$(ENVIRONMENT)/postgresql/data_insights_password" --with-decryption --query 'Parameter.Value')" \
	DATASET_USER="$(shell aws ssm get-parameter --name /data/$(ENVIRONMENT)/postgresql/dataset_views_user --with-decryption --output json --query 'Parameter.Value')" \
	DATASET_PASSWORD=$(shell aws ssm get-parameter --name "/data/$(ENVIRONMENT)/postgresql/dataset_views_password" --with-decryption --query 'Parameter.Value') \
	MFD_USER="$(shell aws ssm get-parameter --name "/eks/$(ENVIRONMENT)/postgresql/insights_mfd_user" --with-decryption --output json --query 'Parameter.Value')" \
	MFD_PASSWORD=$(shell aws ssm get-parameter --name "/eks/$(ENVIRONMENT)/postgresql/insights_mfd_password" --with-decryption --query 'Parameter.Value') \
	MFD_DOMAIN="cloudbeds-{ENV_PREFIX}.com" \
	INSIGHTS_STREAMING_API_URL=${INSIGHTS_STREAMING_API_URL} \
	DISTRIBUTED_ID_SERVICE_URL=${DISTRIBUTED_ID_SERVICE_URL} \
	USER_SERVICE_URL=${USER_SERVICE_URL} \
	ORGANIZATION_SERVICE_URL=$(ORGANIZATION_SERVICE_URL) \
	PERMISSION_SERVICE_URL=${PERMISSION_SERVICE_URL} \
	PERMISSION_SERVICE_SSL=${PERMISSION_SERVICE_SSL} \
	GITHUB_TOKEN="$(shell echo $$GITHUB_TOKEN)" \
	KONG_OIDC_INSTROSPECTION_ENDPOINT=${KONG_OIDC_ORGANIZATION_URL}/oauth2/${KONG_OIDC_SERVER_ID}/v1/introspect \
	KONG_OIDC_CLIENT_ID=${KONG_OIDC_CLIENT_ID} \
	KONG_OIDC_CLIENT_SECRET=${KONG_OIDC_CLIENT_SECRET} \
	LAUNCH_DARKLY_SDK_KEY=${LAUNCH_DARKLY_SDK_KEY} \
	AWS_S3_ACCESS_KEY_ID="$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/reporting/dot-env-file --with-decryption --output json --query 'Parameter.Value' | jq '.|fromjson' | jq '.AWS_S3_ACCESS_KEY_ID')" \
	AWS_S3_SECRET_ACCESS_KEY="$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/reporting/dot-env-file --with-decryption --output json --query 'Parameter.Value' | jq '.|fromjson' | jq '.AWS_S3_SECRET_ACCESS_KEY')" \
	REPORTING_CHARTS_LAMBDA=reporting-charts-$(ENVIRONMENT) \
	GX_LINK_SHORTENER_URL=${GX_LINK_SHORTENER_URL} \
	GX_API_KEY=${GX_API_KEY} \
	DI_SECURE_DOWNLOAD_PAGE="$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/reporting/dot-env-file --with-decryption --output json --query 'Parameter.Value' | jq '.|fromjson' | jq '.DI_SECURE_DOWNLOAD_PAGE')" \
	DI_TOKEN_SECRET=$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/reporting/dot-env-file --with-decryption --output json --query 'Parameter.Value' | jq '.|fromjson' | jq '.DI_TOKEN_SECRET') \
	OPENAI_API_KEY=${OPENAI_API_KEY} \
		envsubst < .env.example > .env

	KONG_OIDC_DISCOVERY=${KONG_OIDC_ORGANIZATION_URL}/oauth2/${KONG_OIDC_SERVER_ID}/.well-known/oauth-authorization-server \
	KONG_OIDC_INSTROSPECTION_ENDPOINT=${KONG_OIDC_ORGANIZATION_URL}/oauth2/${KONG_OIDC_SERVER_ID}/v1/introspect \
	KONG_OIDC_CLIENT_ID=${KONG_OIDC_CLIENT_ID} \
	KONG_OIDC_CLIENT_SECRET=${KONG_OIDC_CLIENT_SECRET} \
	KONG_OIDC_SESSION_SECRET=${KONG_OIDC_SESSION_SECRET} \
	INSIGHTS_STREAMING_API_URL=${INSIGHTS_STREAMING_API_URL} \
		envsubst < kong/kong.example.yaml > kong/kong.yaml

.PHONY: add-hosts
add-hosts: ## Add hosts to /etc/hosts
	@echo Adding host: $(SERVICE_HOST)
	SERVICES=$$(command -v getent > /dev/null && echo "getent ahostsv4" || echo "dscacheutil -q host -a name"); \
	if [ ! "$$($$SERVICES $(SERVICE_HOST) | grep 127.0.0.1 > /dev/null; echo $$?)" -eq 0 ]; then sudo bash -c 'echo "127.0.0.1 $(SERVICE_HOST)" >> /etc/hosts; echo "Entry was added"'; else echo 'Entry already exists'; fi;

	@echo Adding host: $(DOCKER_HOST)
	SERVICES=$$(command -v getent > /dev/null && echo "getent ahostsv4" || echo "dscacheutil -q host -a name"); \
	if [ ! "$$($$SERVICES $(DOCKER_HOST) | grep 127.0.0.1 > /dev/null; echo $$?)" -eq 0 ]; then sudo bash -c 'echo "127.0.0.1 $(DOCKER_HOST)" >> /etc/hosts; echo "Entry was added"'; else echo 'Entry already exists'; fi;

	@echo Adding host: $(DOCKER_KUBERNETES_HOST)
	SERVICES=$$(command -v getent > /dev/null && echo "getent ahostsv4" || echo "dscacheutil -q host -a name"); \
	if [ ! "$$($$SERVICES $(DOCKER_KUBERNETES_HOST) | grep 127.0.0.1 > /dev/null; echo $$?)" -eq 0 ]; then sudo bash -c 'echo "127.0.0.1 $(DOCKER_KUBERNETES_HOST)" >> /etc/hosts; echo "Entry was added"'; else echo 'Entry already exists'; fi;

	@echo Adding host: $(DOCKER_REDIS_HOST)
	SERVICES=$$(command -v getent > /dev/null && echo "getent ahostsv4" || echo "dscacheutil -q host -a name"); \
	if [ ! "$$($$SERVICES $(DOCKER_REDIS_HOST) | grep 127.0.0.1 > /dev/null; echo $$?)" -eq 0 ]; then sudo bash -c 'echo "127.0.0.1 $(DOCKER_REDIS_HOST)" >> /etc/hosts; echo "Entry was added"'; else echo 'Entry already exists'; fi;

	@echo Adding host: $(DOCKER_DATABASE_HOST)
	SERVICES=$$(command -v getent > /dev/null && echo "getent ahostsv4" || echo "dscacheutil -q host -a name"); \
	if [ ! "$$($$SERVICES $(DOCKER_DATABASE_HOST) | grep 127.0.0.1 > /dev/null; echo $$?)" -eq 0 ]; then sudo bash -c 'echo "127.0.0.1 $(DOCKER_DATABASE_HOST)" >> /etc/hosts; echo "Entry was added"'; else echo 'Entry already exists'; fi;

	@echo Adding host: $(DOCKER_AWS_HOST)
	SERVICES=$$(command -v getent > /dev/null && echo "getent ahostsv4" || echo "dscacheutil -q host -a name"); \
	if [ ! "$$($$SERVICES $(DOCKER_AWS_HOST) | grep 127.0.0.1 > /dev/null; echo $$?)" -eq 0 ]; then sudo bash -c 'echo "127.0.0.1 $(DOCKER_AWS_HOST)" >> /etc/hosts; echo "Entry was added"'; else echo 'Entry already exists'; fi;

.PHONY: migrations-run
migrations-run: ## Run migrations
	@docker exec -it $(SERVICE_IMAGE_NAME) flask db upgrade

.PHONY: migrations-downgrade
migrations-downgrade: ## Downgrade migrations
	@docker exec -it $(SERVICE_IMAGE_NAME) flask db downgrade

.PHONY: migrations-create
migrations-create: ## Create migrations make migrations-create
	@docker exec -it $(SERVICE_IMAGE_NAME) flask db migrate

.PHONY: migrations-revision
migrations-revision: ## Create revisions make migrations-revision message="MESSAGE" - Optionally define the migration message
	@docker exec -it $(SERVICE_IMAGE_NAME) flask db revision $(if $(message), --message "$(message)" $(else))

l10n-extract: ## Extract translation strings into messages.pot
	@pybabel extract -F babel.cfg -o messages.pot .

l10n-init: ## Initialize a new language, e.g., make l10n-init lang=fa
	@test -n "$(lang)" || (echo "Usage: make l10n-init lang=<language_code>" && exit 1)
	@pybabel init -i messages.pot -d app/translations -l $(lang)

l10n-compile: ## Compile all translations
	@pybabel compile -d app/translations

l10n-init-all: ## Initialize all supported languages
	@for l in en es es_ES pt_BR pt_PT ru th de it fr ar ach ca id; do \
		echo "Initializing language: $$l"; \
		pybabel init -i messages.pot -d app/translations -l $$l || true; \
	done

.PHONY: lint
lint: ## Run pre commit lint
	@docker exec -it $(SERVICE_NAME) pre-commit run --all-files

.PHONY: unit-tests-run	
unit-tests-run: ## Run unit tests - Optionally define the file to be executed
	@docker exec -e ENV=TEST -it $(SERVICE_IMAGE_NAME) python3 -m pytest -s $(if $(file),$(file),tests/unit)

.PHONY: unit-tests-coverage-report
coverage-report: ## Run unit tests and generate a coverage report - Optionally define the file to be executed
	@docker exec -e ENV=TEST -it $(SERVICE_IMAGE_NAME) python3 -m coverage run -m pytest -s $(if $(file),$(file),tests/unit)
	coverage report
	coverage html

.PHONY: docker-build
docker-build: ## Docker build in detached mode
	@docker compose up --build -d

.PHONY: docker-delete
docker-delete: ## Docker delete containers, volumes, and images not being used by other containers
	@if docker compose down --rmi all --volumes; then \
		echo "Docker images, volumes and its dependencies were deleted"; \
	else \
		docker compose down --volumes; \
		echo "Docker containers, volumes, but not images, were deleted"; \
	fi

.PHONY: docker-prune
docker-prune: ## Docker image prune
	@docker image prune --force

.PHONY: docker-up
docker-up: ## Docker compose up
	@docker compose up

.PHONY: docker-down
docker-down: ## Docker compose down
	@docker compose down

.PHONY: start
start: docker-up ## Start application

.PHONY: stop
stop: docker-down ## Stop application

.PHONY: install
install: setup-env add-hosts docker-build migrations-run install-git-precommit-hook setup-local-data ## Install application

.PHONY: uninstall
uninstall: docker-delete docker-prune ## Uninstall application and its dependencies (images, volumes, networks)

.PHONY: recreate
recreate: uninstall install ## Recreate application

.PHONY: seed-stock-tags
seed-stock-tags: ## Seed stock tags
	@docker exec -it $(SERVICE_IMAGE_NAME) python local/seed.py stock_tags

.PHONY: seed-stock-reports
seed-stock-reports: ## Seed stock reports
	@docker exec -it $(SERVICE_IMAGE_NAME) python local/seed.py stock_reports

.PHONY: seed-reports
seed-reports: ## Seed reports
	@docker exec -it $(SERVICE_IMAGE_NAME) python local/seed.py reports

.PHONY: seed-schedules
seed-schedules: ## Seed schedules
	@docker exec -it $(SERVICE_IMAGE_NAME) python local/seed.py schedules

.PHONY: seed-stock-report-folders
seed-stock-report-folders: ## Seed stock report folders
	@docker exec -it $(SERVICE_IMAGE_NAME) python local/setup_stock_report_folders.py

.PHONY: seed-stock-report-charts
seed-stock-report-charts: ## Seed stock report charts
	@docker exec -it $(SERVICE_IMAGE_NAME) python local/seed.py stock_report_charts	

.PHONY: setup-local-data
setup-local-data: seed-stock-tags seed-stock-reports seed-stock-report-folders seed-reports seed-schedules seed-stock-report-charts ## Seed local data

.PHONE: port-forward 
	@echo "Checking AWS SSO session..."
	export AWS_PROFILE=$(AWS_PROFILE)
	@if ! aws sts get-caller-identity --profile $(AWS_PROFILE) &> /dev/null; then \
		echo "AWS SSO session expired. Logging in..."; \
		aws sso login --profile $(AWS_PROFILE); \
	else \
		echo "AWS SSO session is active."; \
	fi
	@echo "Updating EKS kubeconfig..."
	@aws eks update-kubeconfig --region us-west-2 --name stage-us1 --profile $(AWS_PROFILE)
	@echo "Checking Kubernetes contexts..."
	@kubectl config get-contexts
	@echo "Starting port-forwarding..."
	@kubectl port-forward -n permission deployment/permission-service 9090:9090
