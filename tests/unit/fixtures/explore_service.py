from datetime import datetime
from unittest.mock import patch

from flask import Flask, g

from flask_caching import <PERSON>ache

import pytest

from pytest_mock.plugin import Mo<PERSON>Fixture

from sqlalchemy import <PERSON>umn, Integer, MetaData, String, Table

from tests.unit.data.parameters import FAKE_API_KEY


@pytest.fixture
def app():
    app = Flask(__name__)
    app.config["TESTING"] = True
    app.config["OPENAI_API_KEY"] = FAKE_API_KEY
    app.config["CACHE_TYPE"] = "SimpleCache"
    cache = Cache(app)
    app.extensions["cache"] = cache
    yield app


@pytest.fixture
def app_context(app):
    with app.app_context():
        g.property_ids = [1, 2, 3]
        g.organization_id = 1
        g.property_id = 1
        yield


@pytest.fixture
def mock_openai():
    with patch("app.services.explore_service.OpenAI") as mock_openai:
        yield mock_openai


@pytest.fixture
def mock_dataset():
    with patch("app.services.explore_service.Dataset") as mock_dataset:
        yield mock_dataset


@pytest.fixture
def mock_report():
    with patch("app.services.explore_service.Report") as mock_report:
        yield mock_report


@pytest.fixture
def mock_datetime():
    with patch("app.services.explore_service.datetime") as mock_datetime:
        mock_datetime.now.return_value = datetime(2024, 6, 19, 12, 0, 0)
        yield mock_datetime


@pytest.fixture
def mock_sqlalchemy_table():
    metadata = MetaData()
    table = Table(
        "table",
        metadata,
        Column("col1", Integer, info=dict(name="Col 1", description="Col 1...")),
        Column("col2", String, info=dict(name="Col 2", description="Col 2...")),
    )
    return table


@pytest.fixture
def mock_cache():
    with patch("app.services.explore_service.cache.set") as mock_set, patch(
        "app.services.explore_service.cache.get"
    ) as mock_get:
        yield {"set": mock_set, "get": mock_get}


@pytest.fixture(scope="function")
def mock_explore_service(mocker: MockerFixture):
    def mock_service(data: tuple):
        mocker.patch(
            "app.services.explore_service.ExploreService.explore_report",
            mocker.Mock(return_value=data),
        )

    yield mock_service


@pytest.fixture(scope="function")
def mock_stock_reports_query(mocker):
    stock_report_1 = mocker.Mock(
        id=1,
        dataset_id=1,
        title="Report 1",
        description="Description 1",
        columns=[{"cdf": {"column": "col1"}}],
        filters=None,
        group_rows=None,
        group_columns=None,
        sort=None,
    )
    stock_report_2 = mocker.Mock(
        id=2,
        dataset_id=2,
        title="Report 2",
        description="Description 2",
        columns=[{"cdf": {"column": "col2"}}],
        filters=None,
        group_rows=None,
        group_columns=None,
        sort=None,
    )
    query_mock = mocker.Mock()
    query_mock.all.return_value = [stock_report_1, stock_report_2]
    mocker.patch(
        "flask_sqlalchemy.model._QueryProperty.__get__", return_value=query_mock
    )
