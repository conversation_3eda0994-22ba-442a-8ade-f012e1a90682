import pytest

from app.common.constants.permission_service import MFD_ACCESS_REPORT_BUILDER_ACL
from app.common.enums.permissions import PermissionRole, Resources
from app.common.user import User
from app.enums.dataset import Dataset
from app.services.user_service import UserService

from tests.unit.data.parameters import PROPERTY_ID


class TestUserService:
    CREATED_BY_RESPONSE = dict(
        user={"first_name": "Some", "last_name": "Last Name", "id": 1}
    )
    USER_BY_ID_RESPONSE = dict(
        user={
            "id": 1,
            "email": "<EMAIL>",
            "is_active": True,
            "type": "USER_TYPE_CUSTOMER",
        }
    )
    USER_BY_ID_INACTIVE_RESPONSE = dict(
        user={
            "id": 1,
            "email": "<EMAIL>",
            "is_active": False,
            "type": "USER_TYPE_CUSTOMER",
        }
    )
    USER_BY_ID_EMPLOYEE_RESPONSE = dict(
        user={
            "id": 1,
            "email": "<EMAIL>",
            "is_active": False,
            "type": "USER_TYPE_EMPLOYEE",
        }
    )

    USER = User(
        id=1,
        email="<EMAIL>",
        admin=False,
        token_type="none",
        scopes=[],
        enabled_datasets=[],
    )
    SUPER_USER = User(
        id=1,
        email="<EMAIL>",
        admin=True,
        token_type="none",
        scopes=[],
        enabled_datasets=[],
    )

    @pytest.mark.usefixtures("clean_cache", "flask_app")
    def test_get_created_by(self, user_service_client):
        user_service_client(self.CREATED_BY_RESPONSE)
        created_by = UserService.get_created_by(1)
        assert created_by["id"] == self.CREATED_BY_RESPONSE["user"]["id"]

    @pytest.mark.usefixtures("clean_cache")
    def test_get_created_by_user_not_found(self, user_service_client):
        user_service_client(None)
        created_by = UserService.get_created_by(1)
        assert created_by["id"] == 0

    insights_acls_data = [
        (
            [
                "payment_reports",
                "financial_reports",
                "production_reports",
                "daily_activity_reports",
                "police_report",
                "housekeeping",
            ],
            False,
            [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        ),
        ([], True, []),
        (["any_required_acls"], False, []),
        (["housekeeping", "housekeeping"], False, [8]),
    ]

    @pytest.mark.parametrize("acls, cache, expected_result", insights_acls_data)
    def test_get_insights_acls(self, acls, cache, expected_result, get_insights_acl):
        get_insights_acl(acls, cache)
        assert UserService.get_insights_acls(self.USER, PROPERTY_ID) == expected_result

    permissions = [
        (
            {
                "stock_reports": [
                    {
                        "resource": {
                            "id": str(Dataset.Financial.value),
                            "name": Dataset.Financial.name,
                        }
                    },
                    {
                        "resource": {
                            "id": str(Dataset.Guests.value),
                            "name": Dataset.Guests.name,
                        }
                    },
                    {
                        "resource": {
                            "id": str(Dataset.Payment.value),
                            "name": Dataset.Payment.name,
                        }
                    },
                    {
                        "resource": {
                            "id": str(Dataset.Housekeeping.value),
                            "name": Dataset.Housekeeping.name,
                        }
                    },
                    {
                        "resource": {
                            "id": str(Dataset.Occupancy.value),
                            "name": Dataset.Occupancy.name,
                        }
                    },
                    {
                        "resource": {
                            "id": str(Dataset.OccupancyV1.value),
                            "name": Dataset.OccupancyV1.name,
                        }
                    },
                    {
                        "resource": {
                            "id": str(Dataset.Invoices.value),
                            "name": Dataset.Invoices.name,
                        }
                    },
                    {
                        "resource": {
                            "id": str(Dataset.Reservations.value),
                            "name": Dataset.Reservations.name,
                        }
                    },
                ]
            },
            [1, 2, 3, 4, 5, 6, 7, 8],
        ),
        (
            {"stock_reports": []},
            [],
        ),
        (
            {
                "stock_reports": [
                    {
                        "resource": {
                            "id": str(Dataset.Financial.value),
                            "name": Dataset.Financial.name,
                        }
                    },
                ]
            },
            [1],
        ),
    ]

    @pytest.mark.usefixtures("clean_cache")
    @pytest.mark.parametrize("permissions, expected_result", permissions)
    def test_get_insights_acls_using_permission_service(
        self, permissions, expected_result, launch_darkly_service, mocker
    ):
        launch_darkly_service(True)

        # Not sure why, but the mock on get_permission does not work so need to force it here
        import app.services.user_service

        mock = mocker.Mock()
        mock.get_permissions.return_value = {"permissions": permissions}
        app.services.user_service.PermissionService = mock

        assert (
            app.services.user_service.UserService.get_insights_acls(
                self.USER, PROPERTY_ID
            )
            == expected_result
        )

        # Test the cache
        mock.get_permissions.return_value = ["invalid_key_to_test_cache_is_hit"]
        app.services.user_service.PermissionService = mock
        assert (
            app.services.user_service.UserService.get_insights_acls(
                self.USER, PROPERTY_ID
            )
            == expected_result
        )

    scope_params = [
        (
            ["read:dataInsightsFinancialTransactions"],
            [1],
        ),
        (["read:dataInsightsGuests"], [2]),
        (["read:dataInsightsReservations"], [3]),
        (["read:dataInsightsOccupancy"], [4, 7]),
        (["read:dataInsightsPayments"], [10, 5]),
        (["read:dataInsightsInvoices"], [6]),
        (
            [
                "read:dataInsightsFinancialTransactions",
                "read:dataInsightsGuests",
                "read:dataInsightsGuests",
                "read:dataInsightsReservations",
                "read:dataInsightsOccupancy",
                "read:dataInsightsPayments",
                "read:dataInsightsInvoices",
            ],
            [1, 2, 3, 4, 5, 6, 7, 10],
        ),
    ]

    @pytest.mark.parametrize("scopes, expected_result", scope_params)
    @pytest.mark.usefixtures("clean_cache")
    def test_get_insights_acls_with_scopes(self, scopes, expected_result):
        user = User(
            id=1,
            email="<EMAIL>",
            admin=False,
            token_type="partner",
            scopes=scopes,
        )
        assert UserService.get_insights_acls(user, PROPERTY_ID) == expected_result

    @pytest.mark.usefixtures("clean_cache")
    def test_get_insights_acls_with_super_user(self):
        assert UserService.get_insights_acls(self.SUPER_USER, PROPERTY_ID) == [
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
            9,
            10,
        ]

    def test_has_acl(self, user_service_has_acl):
        user_service_has_acl("some_acl")

        assert UserService.has_acl(self.USER, PROPERTY_ID, "some_acl")

    def test_super_user_has_acl(self, user_service_has_acl):
        user_service_has_acl("some_acl")

        assert UserService.has_acl(self.SUPER_USER, PROPERTY_ID, "some_acl")

    def test_has_acl_none(self, user_service_has_acl):
        user_service_has_acl(None)
        assert not UserService.has_acl(self.USER, PROPERTY_ID, "some_acl")

    permissions = [
        (
            {
                "permissions": {
                    "reports": [
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "1", "name": Dataset.Financial.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "2", "name": Dataset.Guests.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "3", "name": Dataset.Reservations.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "4", "name": Dataset.Occupancy.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "5", "name": Dataset.Invoices.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "6", "name": Dataset.OccupancyV1.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "7", "name": Dataset.Payment.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "8", "name": Dataset.Housekeeping.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "10", "name": Dataset.Payout.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "11", "name": Dataset.BedOccupancy.name},
                        },
                    ]
                }
            },
            MFD_ACCESS_REPORT_BUILDER_ACL,
            True,
        ),
        (
            {
                "permissions": {
                    "stock_reports": [
                        {"resource": {"id": str(Dataset.Payment.value)}},
                        {"resource": {"id": str(Dataset.Payout.value)}},
                    ]
                }
            },
            Resources.PaymentReports.value,
            True,
        ),
        (
            {
                "permissions": {
                    "stock_reports": [
                        {"resource": {"id": str(Dataset.Reservations.value)}},
                        {"resource": {"id": str(Dataset.Occupancy.value)}},
                        {"resource": {"id": str(Dataset.OccupancyV1.value)}},
                        {"resource": {"id": str(Dataset.BedOccupancy.value)}},
                        {"resource": {"id": str(Dataset.Guests.value)}},
                    ]
                }
            },
            Resources.DailyActivityReports.value,
            True,
        ),
        (
            {"permissions": {}},
            Resources.FinancialReports.value,
            False,
        ),
        (
            {"permissions": {"stock_reports": [{"role": PermissionRole.VIEWER.value}]}},
            MFD_ACCESS_REPORT_BUILDER_ACL,
            False,
        ),
    ]

    @pytest.mark.usefixtures("clean_cache", "has_feature_flag")
    @pytest.mark.parametrize("permissions, acl, expected_result", permissions)
    def test_has_acl_using_permission_service(
        self, permissions, acl, expected_result, launch_darkly, mocker
    ):
        launch_darkly((True,))

        import app.services.user_service

        mock = mocker.Mock()
        mock.get_permissions.return_value = permissions
        app.services.user_service.PermissionService = mock

        assert UserService.has_acl(self.USER, PROPERTY_ID, acl) == expected_result

    not_housekeeping = [
        (
            {
                "permissions": {
                    "reports": [
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "1", "name": Dataset.Financial.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "2", "name": Dataset.Guests.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "3", "name": Dataset.Reservations.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "4", "name": Dataset.Occupancy.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "5", "name": Dataset.Invoices.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "6", "name": Dataset.OccupancyV1.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "7", "name": Dataset.Payment.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "10", "name": Dataset.Payout.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "11", "name": Dataset.BedOccupancy.name},
                        },
                    ]
                }
            },
            MFD_ACCESS_REPORT_BUILDER_ACL,
            True,
        ),
        (
            {
                "permissions": {
                    "reports": [
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "1", "name": Dataset.Financial.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "2", "name": Dataset.Guests.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "3", "name": Dataset.Reservations.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "4", "name": Dataset.Occupancy.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "6", "name": Dataset.OccupancyV1.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "7", "name": Dataset.Payment.name},
                        },
                        {
                            "role": PermissionRole.AUTHOR.value,
                            "resource": {"id": "11", "name": Dataset.BedOccupancy.name},
                        },
                    ]
                }
            },
            MFD_ACCESS_REPORT_BUILDER_ACL,
            False,
        ),
        (
            {
                "permissions": {
                    "stock_reports": [
                        {"resource": {"id": str(Dataset.Payment.value)}},
                        {"resource": {"id": str(Dataset.Payout.value)}},
                    ]
                }
            },
            Resources.PaymentReports.value,
            True,
        ),
        (
            {
                "permissions": {
                    "stock_reports": [
                        {"resource": {"id": str(Dataset.Reservations.value)}},
                        {"resource": {"id": str(Dataset.Occupancy.value)}},
                        {"resource": {"id": str(Dataset.OccupancyV1.value)}},
                        {"resource": {"id": str(Dataset.Guests.value)}},
                        {"resource": {"id": str(Dataset.BedOccupancy.value)}},
                    ]
                }
            },
            Resources.DailyActivityReports.value,
            True,
        ),
        (
            {"permissions": {}},
            Resources.FinancialReports.value,
            False,
        ),
        (
            {"permissions": {"stock_reports": [{"role": PermissionRole.VIEWER.value}]}},
            MFD_ACCESS_REPORT_BUILDER_ACL,
            False,
        ),
    ]

    @pytest.mark.usefixtures("clean_cache", "not_has_feature_flag")
    @pytest.mark.parametrize("permissions, acl, expected_result", not_housekeeping)
    def test_has_acl_using_permission_service_and_not_housekeeping(
        self, permissions, acl, expected_result, launch_darkly, mocker
    ):
        launch_darkly((True,))

        import app.services.user_service

        mock = mocker.Mock()
        mock.get_permissions.return_value = permissions
        app.services.user_service.PermissionService = mock

        assert UserService.has_acl(self.USER, PROPERTY_ID, acl) == expected_result

    def test_user_class(self):
        assert False == User(1, "<EMAIL>", admin=False).admin
        assert True == User(1, "<EMAIL>", admin=False).admin
        assert True == User(1, "<EMAIL>", admin=True).admin
        assert (
            False == User(1, "<EMAIL>", admin=False).admin
        )

    @pytest.mark.usefixtures("clean_cache")
    def test_get_user_active(self, user_service_client):
        user_service_client(self.USER_BY_ID_RESPONSE)
        active = UserService.get_user_active(1)
        assert active

    @pytest.mark.usefixtures("clean_cache")
    def test_get_user_inactive(self, user_service_client):
        user_service_client(self.USER_BY_ID_INACTIVE_RESPONSE)
        active = UserService.get_user_active(1)
        assert not active

    @pytest.mark.usefixtures("clean_cache")
    def test_get_user_empty(self, user_service_client):
        user_service_client(dict())
        active = UserService.get_user_active(1)
        assert not active

    users = [
        (dict(users=[USER_BY_ID_RESPONSE["user"]])),
        (dict(users=[USER_BY_ID_EMPLOYEE_RESPONSE["user"]])),
        (dict(users=[USER_BY_ID_INACTIVE_RESPONSE["user"]])),
        (dict()),
        (dict(users=[])),
    ]

    @pytest.mark.parametrize("users", users)
    @pytest.mark.usefixtures("clean_cache")
    def test_get_id_by_email(self, user_service_client, users):
        users_list = users.get("users", [])
        user = users_list[0] if users_list else None
        user_service_client(user=user, users=users)
        user_id = UserService.get_id_by_email("<EMAIL>")
        assert user_id == (user.get("id") if user else None)

    is_employee_users = [
        (USER_BY_ID_RESPONSE, False),
        (USER_BY_ID_EMPLOYEE_RESPONSE, True),
        (dict(), False),
    ]

    @pytest.mark.parametrize("user, expected_result", is_employee_users)
    @pytest.mark.usefixtures("clean_cache")
    def test_is_employee(self, user_service_client, user, expected_result):
        user_service_client(user)
        is_employee = UserService.is_employee(1)
        assert is_employee is expected_result

    user_properties = [
        (
            dict(
                assignment=dict(
                    properties=[
                        dict(id=1),
                        dict(id=2),
                        dict(id=3),
                        dict(id=1234567890987654321),
                    ]
                )
            )
        ),
        (dict(assignment=dict(properties=[dict(id=1)]))),
        (
            dict(
                assignment=dict(
                    properties=[
                        dict(id="1"),
                        dict(id="2"),
                        dict(id="3"),
                        dict(id="1234567890987654321"),
                    ]
                )
            )
        ),
        (dict(assignment=dict(properties=[]))),
        (dict()),
    ]

    @pytest.mark.parametrize("user_properties", user_properties)
    @pytest.mark.usefixtures("clean_cache")
    def test_get_user_properties(self, user_service_client, user_properties):
        user_service_client(self.USER_BY_ID_RESPONSE, user_properties=user_properties)
        properties = UserService.get_user_properties(
            self.USER_BY_ID_RESPONSE["user"]["id"]
        )
        assert properties == [
            property.get("id")
            for property in user_properties.get("assignment", {}).get("properties", [])
        ]
