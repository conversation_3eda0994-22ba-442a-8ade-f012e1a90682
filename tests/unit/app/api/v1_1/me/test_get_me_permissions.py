from http import HTTPStatus

import pytest

from app.common.enums.permissions import (
    PermissionAction,
    PermissionResource,
    PermissionRole,
)
from app.enums.dataset import Dataset

from tests.unit.data.parameters import (
    DATA_INSIGHTS_BASE_URL,
    PROPERTY_ID,
)
from tests.unit.mocks.token import PROPERTY_USER_TOKEN

BASE_URL = f"{DATA_INSIGHTS_BASE_URL}/me/permissions"


@pytest.mark.usefixtures("flask_app")
class TestGetMePermissions:
    authorization_property_headers = dict(
        Authorization=PROPERTY_USER_TOKEN, X_PROPERTY_ID=PROPERTY_ID
    )

    @pytest.mark.usefixtures(
        "authorized_user_mock",
        "mock_grpc_client",
        "clean_cache",
        "is_whitelisted_false",
    )
    def test_get_me_permissions_when_is_not_whitelisted(
        self, test_client, permission_service, launch_darkly
    ):
        launch_darkly((False, False))

        permission_service(
            [
                dict(
                    actions=[PermissionAction.CREATE.value],
                    resource=PermissionResource.FINANCIAL_REPORTS.value,
                )
            ]
        )

        response = test_client.get(
            BASE_URL, headers=self.authorization_property_headers
        )
        assert response.status_code == HTTPStatus.OK
        assert len(response.json["permissions"]["stock_reports"]) == 0
        assert len(response.json["permissions"]["explore"]) == 0
        assert len(response.json["permissions"]["settings"]) == 0
        assert (
            response.json["permissions"]["reports"][0]["role"]
            == PermissionRole.VIEWER.value
        )
        assert response.json["permissions"]["reports"][0]["resource"]["id"] == str(
            Dataset.Financial.value
        )
        assert (
            response.json["permissions"]["reports"][0]["resource"]["name"]
            == Dataset.Financial.name
        )

        # Test Cache
        response = test_client.get(
            BASE_URL, headers=self.authorization_property_headers
        )
        assert len(response.json["permissions"]["stock_reports"]) == 0
        assert (
            response.json["permissions"]["reports"][0]["role"]
            == PermissionRole.VIEWER.value
        )
        assert response.json["permissions"]["reports"][0]["resource"]["id"] == str(
            Dataset.Financial.value
        )
        assert (
            response.json["permissions"]["reports"][0]["resource"]["name"]
            == Dataset.Financial.name
        )

    @pytest.mark.usefixtures(
        "authorized_user_mock",
        "mock_grpc_client",
        "clean_cache",
        "is_whitelisted_false",
    )
    def test_get_me_permissions_when_is_not_whitelisted_and_does_not_have_permissions_assigned(
        self, test_client, permission_service, launch_darkly
    ):
        launch_darkly((False, False))

        permission_service(
            [
                dict(
                    actions=[],
                    resource=PermissionResource.FINANCIAL_REPORTS.value,
                )
            ]
        )

        response = test_client.get(
            BASE_URL, headers=self.authorization_property_headers
        )
        assert response.status_code == HTTPStatus.OK
        assert len(response.json["permissions"]["stock_reports"]) == 0
        assert len(response.json["permissions"]["explore"]) == 0
        assert len(response.json["permissions"]["reports"]) == 0
        assert len(response.json["permissions"]["settings"]) == 0

    @pytest.mark.usefixtures(
        "authorized_user_mock", "mock_grpc_client", "clean_cache", "is_whitelisted"
    )
    def test_get_me_permissions_when_is_whitelisted(self, test_client, launch_darkly):
        launch_darkly((False, False))

        response = test_client.get(
            BASE_URL, headers=self.authorization_property_headers
        )
        assert response.status_code == HTTPStatus.OK
        assert len(response.json["permissions"]["stock_reports"]) == 9
        assert len(response.json["permissions"]["reports"]) == 10
        assert len(response.json["permissions"]["explore"]) == 0
        assert len(response.json["permissions"]["settings"]) == 1
        assert (
            response.json["permissions"]["settings"][0]["role"]
            == PermissionRole.PUBLISHER.value
        )
        assert (
            response.json["permissions"]["settings"][0]["actions"][0]
            == PermissionAction.MANAGE.value
        )
        assert (
            response.json["permissions"]["reports"][0]["role"]
            == PermissionRole.PUBLISHER.value
        )

    @pytest.mark.usefixtures(
        "authorized_user_mock", "mock_grpc_client", "clean_cache", "is_whitelisted"
    )
    def test_get_me_permissions_when_explore_features_are_enabled(
        self, test_client, launch_darkly
    ):
        launch_darkly((True, True))

        response = test_client.get(
            BASE_URL, headers=self.authorization_property_headers
        )
        assert response.status_code == HTTPStatus.OK
        assert len(response.json["permissions"]["explore"]) == 10
        assert (
            response.json["permissions"]["explore"][0]["role"]
            == PermissionRole.VIEWER.value
        )
        assert (
            PermissionAction.EXPLORE.value
            in response.json["permissions"]["explore"][0]["actions"]
        )
        assert (
            PermissionAction.EXPLORE_REPORT.value
            in response.json["permissions"]["explore"][0]["actions"]
        )

    @pytest.mark.usefixtures(
        "authorized_user_mock",
        "mock_grpc_client",
        "clean_cache",
        "is_whitelisted_false",
    )
    def test_get_me_permissions_for_explore_report_enabled(
        self, test_client, permission_service, launch_darkly
    ):
        launch_darkly((True, False))

        permission_service(
            [
                dict(
                    actions=[PermissionAction.CREATE.value],
                    resource=PermissionResource.FINANCIAL_REPORTS.value,
                ),
                dict(
                    actions=[PermissionAction.CREATE.value],
                    resource=PermissionResource.FINANCIAL_STOCK_REPORTS.value,
                ),
            ]
        )

        response = test_client.get(
            BASE_URL, headers=self.authorization_property_headers
        )
        assert response.status_code == HTTPStatus.OK
        assert len(response.json["permissions"]["explore"]) == 1
