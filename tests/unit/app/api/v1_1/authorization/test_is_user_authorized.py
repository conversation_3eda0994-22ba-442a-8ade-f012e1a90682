import pytest

from werkzeug.exceptions import HTTPException

from app.common.enums.permissions import PermissionAction, PermissionRole
from app.decorators.authorization import (
    is_user_authorized,
)

from tests.unit.data.users import (
    PARTNER_USER_ALL_DATASETS,
    PARTNER_USER_FINANCIAL_DATASET,
    PARTNER_USER_NO_DATASETS,
)


@pytest.mark.usefixtures("clean_cache")
class TestCheckUserHasPermissionScopes:
    def test_is_user_authorized_partner_token(self, mocker):
        g = mocker.Mock()
        g.user = PARTNER_USER_ALL_DATASETS
        mocker.patch("app.decorators.authorization.g", g)

        assert is_user_authorized(
            79, PermissionAction.VIEW, "stock_report", PermissionRole.VIEWER
        )
        assert len(g.user.enabled_datasets) == 8

    def test_is_user_authorized_partner_token_financial_scope(self, mocker):
        g = mocker.Mock()
        g.user = PARTNER_USER_FINANCIAL_DATASET
        mocker.patch("app.decorators.authorization.g", g)

        assert is_user_authorized(
            79, PermissionAction.VIEW, "stock_report", PermissionRole.VIEWER
        )
        assert len(g.user.enabled_datasets) == 1

    def test_is_user_authorized_partner_token_no_acl_stock_report(self, mocker):
        g = mocker.Mock()
        g.user = PARTNER_USER_NO_DATASETS
        mocker.patch("app.decorators.authorization.g", g)

        mocker.patch(
            "app.decorators.authorization.PermissionService.is_whitelisted",
            return_value=False,
        )

        with pytest.raises(HTTPException) as exception:
            assert is_user_authorized(
                79, PermissionAction.VIEW, "stock_report", PermissionRole.VIEWER
            )

        assert exception.value.code == 403
        assert len(g.user.enabled_datasets) == 0
