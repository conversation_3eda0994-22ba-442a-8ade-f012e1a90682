import datetime
from http import HTTPStatus
from unittest.mock import patch
from zoneinfo import ZoneInfo

from dateutil.parser import parse

import pytest

from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum
from app.enums.filter_operator import FilterOperator
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.enums.relative_date import RelativeDate as RelativeDateEnum
from app.reports.report_filters import ReportFilters

from manage import app

from tests.unit.data.parameters import ORGANIZATION_ID, PROPERTY_ID


@pytest.mark.usefixtures("flask_app")
class TestReportFilters:
    dataset = Dataset(DatasetEnum.Financial)
    filters = dict()
    property_ids = [PROPERTY_ID]
    organization_id = ORGANIZATION_ID

    def test_get_filters_instance(self):
        report_filters = ReportFilters(
            self.dataset, self.filters, self.property_ids, self.organization_id
        )

        assert report_filters.__repr__()
        assert isinstance(report_filters.get_filters(), list)

    def test_get_filters_property_ids(self):
        report_filters = ReportFilters(
            self.dataset, self.filters, self.property_ids, self.organization_id
        )
        organization_id = report_filters.get_filters()[0]["and"][0]
        assert organization_id["value"] == self.organization_id
        property_ids = report_filters.get_filters()[0]["and"][1]
        assert property_ids["value"] == self.property_ids

    def test_get_filters_no_organization_id(self):
        with pytest.raises(Exception):
            ReportFilters(
                self.dataset, self.filters, self.property_ids, None
            ).get_filters()

    def test_get_filters_static_filters(self):
        report_filters = ReportFilters(
            self.dataset, self.filters, self.property_ids, self.organization_id
        )
        static_filters = report_filters.get_filters()[0]["and"][2]
        assert (
            static_filters["field"] == self.dataset.static_filters[0]["cdf"]["column"]
        )
        filters = {
            "or": [
                {
                    "cdf": {"type": "default", "column": "checkin_date"},
                    "operator": "less_than_or_equal",
                    "value": "today",
                },
                {
                    "or": [
                        {
                            "cdf": {"type": "default", "column": "booking_datetime"},
                            "operator": "less_than_or_equal",
                            "value": "2021-10-19T00:00:00",
                        },
                        {
                            "cdf": {"type": "default", "column": "booking_datetime"},
                            "operator": "greater_than_or_equal",
                            "value": "2020-10-19T00:00:00",
                        },
                    ]
                },
            ]
        }
        report_filters = ReportFilters(
            Dataset(DatasetEnum.Guests),
            filters,
            self.property_ids,
            self.organization_id,
        )
        static_filters = report_filters.get_filters()[0]["and"][1]
        assert static_filters is not None

    def test_get_filters_filters(self, launch_darkly_service):
        launch_darkly_service(True)
        filters = {
            "or": [
                {
                    "cdf": {"type": "default", "column": "checkin_date"},
                    "operator": "less_than_or_equal",
                    "value": "today",
                },
                {
                    "cdf": {"type": "default", "column": "credit_amount"},
                    "operator": "greater_than_or_equal",
                    "value": 10,
                },
            ]
        }
        report_filters = ReportFilters(
            self.dataset, filters, self.property_ids, self.organization_id
        )
        filters = report_filters.get_filters()[0]["and"][4]

        assert filters["or"][0] == {
            "field": "checkin_date",
            "model": self.dataset.model.__name__,
            "op": "le",
            "value": datetime.datetime.combine(
                datetime.datetime.now(tz=ZoneInfo("UTC")).date(),
                datetime.datetime.min.time(),
            ).strftime("%Y-%m-%d"),
        }
        assert filters["or"][1] == {
            "field": "credit_amount",
            "model": self.dataset.model.__name__,
            "op": "ge",
            "value": 10,
        }

    relative_date_duration_test_data = [
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.DaysLater.value};13",
            "2022-01-13",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.DaysPrior.value};1",
            "2021-12-30",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.DaysPrior.value}",
            "2021-12-31",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.DaysPrior.value};",
            "2021-12-31",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.WeeksLater.value};2",
            "2022-01-14",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.WeeksPrior.value};1",
            "2021-12-24",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.MonthsLater.value};3",
            "2022-03-31",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.MonthsPrior.value};2",
            "2021-10-31",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.YearsLater.value};4",
            "2025-12-31",
        ),
        (
            datetime.date(2021, 12, 31),
            f"{RelativeDateEnum.YearsPrior.value};1",
            "2020-12-31",
        ),
    ]

    @pytest.mark.parametrize(
        "today_mock, relative_date, expected",
        relative_date_duration_test_data,
    )
    def test_get_filters_relative_dates_duration(
        self, mocker, today_mock, relative_date, expected, launch_darkly_service
    ):
        launch_darkly_service(True)
        now_mock = mocker.Mock()
        now_mock.date.return_value = today_mock
        datetime_mock = mocker.Mock()
        datetime_mock.return_value = parse(expected)
        datetime_mock.now.return_value = now_mock
        mocker.patch("app.filters.relative_date.datetime", datetime_mock)

        filters = {
            "or": [
                {
                    "cdf": {"type": "default", "column": "checkin_date"},
                    "operator": FilterOperator.Equals.value,
                    "value": relative_date,
                }
            ]
        }
        report_filters = ReportFilters(
            self.dataset, filters, self.property_ids, self.organization_id
        )
        filters = report_filters.get_filters()[0]["and"][4]
        assert filters["or"][0]["value"] == expected

    relative_date_test_data = [
        (
            datetime.date(2021, 12, 31),
            RelativeDateEnum.Yesterday.value,
            "2021-12-30",
        ),
        (
            datetime.date(2021, 12, 31),
            RelativeDateEnum.Tomorrow.value,
            "2022-01-01",
        ),
        (
            datetime.date(2021, 12, 31),
            RelativeDateEnum.StartCurrentMonth.value,
            "2021-12-01",
        ),
        (
            datetime.date(2021, 12, 31),
            RelativeDateEnum.StartNextMonth.value,
            "2022-01-01",
        ),
        (
            datetime.date(2021, 12, 31),
            RelativeDateEnum.StartCurrentYear.value,
            "2021-01-01",
        ),
        (
            datetime.date(2021, 12, 31),
            RelativeDateEnum.Today.value,
            "2021-12-31",
        ),
        (
            datetime.date(2021, 12, 31),
            RelativeDateEnum.Yesterday.value,
            "2021-12-30",
        ),
        (
            datetime.date(2021, 7, 28),
            RelativeDateEnum.StartNextMonth.value,
            "2021-08-01",
        ),
        (
            datetime.date(2021, 2, 1),
            RelativeDateEnum.StartNextMonth.value,
            "2021-03-01",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartCurrentWeek.value,
            "2021-10-11",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartLastWeek.value,
            "2021-10-04",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartNextWeek.value,
            "2021-10-18",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartCurrentQuarter.value,
            "2021-10-01",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartLastQuarter.value,
            "2021-07-01",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartNextQuarter.value,
            "2022-01-01",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartLastYear.value,
            "2020-01-01",
        ),
        (
            datetime.date(2021, 10, 15),
            RelativeDateEnum.StartNextYear.value,
            "2022-01-01",
        ),
    ]

    @pytest.mark.parametrize(
        "today_mock, relative_date, expected", relative_date_test_data
    )
    def test_get_filters_relative_dates(
        self, mocker, today_mock, relative_date, expected, launch_darkly_service
    ):
        # The expected value is Mocked only for Quarter dates when a new instance of datetime is created
        launch_darkly_service(True)
        now_mock = mocker.Mock()
        now_mock.date.return_value = today_mock
        datetime_mock = mocker.Mock()
        datetime_mock.return_value = parse(expected)
        datetime_mock.now.return_value = now_mock
        mocker.patch("app.filters.relative_date.datetime", datetime_mock)

        filters = {
            "or": [
                {
                    "cdf": {"type": "default", "column": "checkin_date"},
                    "operator": FilterOperator.Equals.value,
                    "value": relative_date,
                }
            ]
        }
        report_filters = ReportFilters(
            self.dataset, filters, self.property_ids, self.organization_id
        )
        filters = report_filters.get_filters()[0]["and"][4]
        assert filters["or"][0]["value"] == expected

    test_data = [
        (FilterOperator.Begins.value, "confirmed%"),
        (FilterOperator.Ends.value, "%confirmed"),
        (FilterOperator.Contains.value, "%confirmed%"),
        (FilterOperator.NotBegins.value, "confirmed%"),
        (FilterOperator.NotEnds.value, "%confirmed"),
        (FilterOperator.NotContains.value, "%confirmed%"),
        (FilterOperator.IsNull.value, None),
        (FilterOperator.IsNotNull.value, None),
        (FilterOperator.IsEmpty.value, ""),
        (FilterOperator.IsNotEmpty.value, ""),
    ]

    @pytest.mark.parametrize("operator, expected", test_data)
    def test_get_filters_values(self, operator, expected, launch_darkly_service):
        launch_darkly_service(True)
        filters = {
            "or": [
                {
                    "cdf": {"type": "default", "column": "reservation_status"},
                    "operator": operator,
                    "value": "confirmed",
                }
            ]
        }
        report_filters = ReportFilters(
            self.dataset, filters, self.property_ids, self.organization_id
        )
        filters = report_filters.get_filters()[0]["and"][4]

        assert filters["or"][0]["value"] == expected

    test_data = [
        (FilterOperator.IsNull.value, "is_null"),
        (FilterOperator.IsNotNull.value, "is_not_null"),
        (FilterOperator.Equals.value, "eq"),
        (FilterOperator.IsEmpty.value, "eq"),
        (FilterOperator.NotEquals.value, "ne"),
        (FilterOperator.IsNotEmpty.value, "ne"),
        (FilterOperator.GreaterThan.value, "gt"),
        (FilterOperator.LessThan.value, "lt"),
        (FilterOperator.GreaterThanOrEqual.value, "ge"),
        (FilterOperator.LessThanOrEqual.value, "le"),
        (FilterOperator.Begins.value, "ilike"),
        (FilterOperator.Ends.value, "ilike"),
        (FilterOperator.Contains.value, "ilike"),
        (FilterOperator.NotBegins.value, "not_ilike"),
        (FilterOperator.NotEnds.value, "not_ilike"),
        (FilterOperator.NotContains.value, "not_ilike"),
        (FilterOperator.ListContains.value, "in"),
        (FilterOperator.NotListContains.value, "not_in"),
    ]

    @pytest.mark.parametrize("operator, expected", test_data)
    def test_get_filters_operators(self, operator, expected, launch_darkly_service):
        launch_darkly_service(True)
        filters = {
            "or": [
                {
                    "cdf": {"type": "default", "column": "reservation_status"},
                    "operator": operator,
                    "value": "confirmed",
                }
            ]
        }
        report_filters = ReportFilters(
            self.dataset, filters, self.property_ids, self.organization_id
        )
        filters = report_filters.get_filters()[0]["and"][4]

        assert filters["or"][0]["op"] == expected

    def test_get_filters_rules(self):
        filters = {
            "or": [
                {
                    "value": "yesterday",
                    "cdf": {"type": "default", "column": "booking_datetime"},
                    "operator": "greater_than_or_equal",
                },
                {
                    "and": [
                        {
                            "value": "today",
                            "cdf": {"type": "default", "column": "checkin_date"},
                            "operator": "less_than_or_equal",
                        },
                        {
                            "value": "today",
                            "cdf": {"type": "default", "column": "checkout_date"},
                            "operator": "greater_than_or_equal",
                        },
                        {
                            "or": [
                                {
                                    "value": "2022-06-03T00:00:00Z",
                                    "cdf": {
                                        "type": "default",
                                        "column": "stay_date",
                                        "multi_level_id": 1,
                                    },
                                    "operator": "greater_than_or_equal",
                                }
                            ]
                        },
                    ]
                },
            ]
        }
        report_filters = ReportFilters(
            Dataset(DatasetEnum.Reservations),
            filters,
            self.property_ids,
            self.organization_id,
        )
        rules = report_filters.get_rules()

        expected_result = [
            {
                "value": "yesterday",
                "cdf": {"type": "default", "column": "booking_datetime"},
                "operator": "greater_than_or_equal",
            },
            {
                "value": "today",
                "cdf": {"type": "default", "column": "checkin_date"},
                "operator": "less_than_or_equal",
            },
            {
                "value": "today",
                "cdf": {"type": "default", "column": "checkout_date"},
                "operator": "greater_than_or_equal",
            },
            {
                "value": "2022-06-03T00:00:00Z",
                "cdf": {"type": "default", "column": "stay_date", "multi_level_id": 1},
                "operator": "greater_than_or_equal",
            },
        ]

        assert rules == expected_result

    def test_validate_date_cdf_is_pased_to_date_when_is_send_as_datetime(
        self, flask_app, launch_darkly_service
    ):
        launch_darkly_service(True)
        dataset = Dataset(DatasetEnum.Reservations)
        filters = {
            "and": [
                {
                    "cdf": {"column": "checkin_date", "type": "default"},
                    "operator": "less_than_or_equal",
                    "value": "2023-01-01T21:55:55",
                },
                {
                    "cdf": {"column": "checkout_date", "type": "default"},
                    "operator": "less_than_or_equal",
                    "value": "2023-01-01T21:55:55+00:00",
                },
                {
                    "cdf": {"column": "booking_datetime", "type": "default"},
                    "operator": "less_than_or_equal",
                    "value": "2023-01-01T21:55:55",
                },
                {
                    "and": [
                        {
                            "cdf": {
                                "column": "room_checkin_date",
                                "type": "default",
                                "multi_level_id": MultiLevelEnum.RoomReservations.value,
                            },
                            "operator": "less_than_or_equal",
                            "value": "2023-01-01T21:55:55",
                        },
                    ]
                },
            ]
        }
        report_filters = ReportFilters(
            dataset, filters, self.property_ids, self.organization_id
        ).get_filters()
        assert report_filters[0]["and"][2]["and"][0]["value"] == "2023-01-01"
        assert report_filters[0]["and"][2]["and"][1]["value"] == "2023-01-01"
        assert report_filters[0]["and"][2]["and"][2]["value"] == "2023-01-01T21:55:55"
        assert report_filters[0]["and"][2]["and"][3]["and"][0]["value"] == "2023-01-01"

    def test_validate_translate_cdf_on_multilevel(
        self, flask_app, launch_darkly_service
    ):
        with app.app_context():
            launch_darkly_service(True)
            dataset = Dataset(DatasetEnum.OccupancyV1)
            filters = {
                "and": [
                    {
                        "cdf": {"column": "occupancy", "type": "default"},
                        "operator": "greater_than",
                        "value": 1,
                    },
                    {
                        "and": [
                            {
                                "cdf": {
                                    "column": "reservation_source",
                                    "type": "default",
                                    "multi_level_id": MultiLevelEnum.OccupancyReservation.value,
                                },
                                "operator": "begins",
                                "value": "Booking Engine",
                            },
                        ]
                    },
                ]
            }
            report_filters = ReportFilters(
                dataset, filters, self.property_ids, self.organization_id
            ).get_filters()
            assert report_filters[0]["and"][2]["and"][0]["value"] == 1
            report_filters[0]["and"][2]["and"][1]["and"][0]["or"][1]["op"] == "ilike"

    @pytest.mark.skip("All cdfs are not translated now")
    def test_validate_translate_cdf_on_reservation(
        self, flask_app, launch_darkly_service
    ):
        with app.app_context():
            with app.test_request_context(headers={"Accept-Language": "pt-BR"}):
                launch_darkly_service(True)
                dataset = Dataset(DatasetEnum.Reservations)
                filters = {
                    "and": [
                        {
                            "cdf": {"column": "reservation_source", "type": "default"},
                            "operator": "begins",
                            "value": "booking",
                        },
                    ]
                }
                report_filters = ReportFilters(
                    dataset, filters, self.property_ids, self.organization_id
                ).get_filters()
                assert report_filters[0]["and"][2]["and"][0]["or"][0]["op"] == "in"
                assert (
                    report_filters[0]["and"][2]["and"][0]["or"][0]["value"][0]
                    == "booking"
                )

    def test_date_cdf_filter_throws_error_when_invalid(
        self, flask_app, launch_darkly_service
    ):
        with pytest.raises(Exception) as value_error:
            launch_darkly_service(True)
            dataset = Dataset(DatasetEnum.Reservations)
            filters = {
                "and": [
                    {
                        "cdf": {"column": "checkin_date", "type": "default"},
                        "operator": "less_than_or_equal",
                        "value": "Invalid Date Time",
                    },
                    {
                        "cdf": {"column": "booking_datetime", "type": "default"},
                        "operator": "less_than_or_equal",
                        "value": "2023-01-01T21:55:55",
                    },
                    {
                        "and": [
                            {
                                "cdf": {
                                    "column": "room_checkin_date",
                                    "type": "default",
                                    "multi_level_id": MultiLevelEnum.RoomReservations.value,
                                },
                                "operator": "less_than_or_equal",
                                "value": "2023-01-01T21:55:55",
                            },
                        ]
                    },
                ]
            }
            ReportFilters(
                dataset, filters, self.property_ids, self.organization_id
            ).get_filters()
        assert value_error.value.code == HTTPStatus.INTERNAL_SERVER_ERROR

    @pytest.mark.parametrize(
        "op,user_input,expected_keys",
        [
            ("equals", "Booking", ["booking"]),
            (
                "not_equals",
                "Nonexistent",
                ["booking_engine", "booking", "website", "expedia", "other"],
            ),
            ("begins", "Book", ["booking_engine", "booking"]),
            ("not_begins", "OTA", ["booking_engine", "booking", "website", "other"]),
            ("ends", "site", ["website"]),
            ("not_ends", "Engine", ["booking", "website", "expedia", "other"]),
        ],
    )
    def test_get_translation_keys_from_value(self, op, user_input, expected_keys):
        with app.app_context():
            with app.test_request_context(headers={"Accept-Language": "pt-BR"}):
                with patch(
                    "app.reports.report_filters.get_reverse_translation_map",
                    return_value={
                        "Booking Engine": ["booking_engine"],
                        "Booking": ["booking"],
                        "Website": ["website"],
                        "OTA Expedia": ["expedia"],
                        "Other Channel": ["other"],
                    },
                ):
                    dataset = Dataset(DatasetEnum.Reservations)
                    filters = {
                        "and": [
                            {
                                "cdf": {
                                    "column": "reservation_source",
                                    "type": "default",
                                },
                                "operator": "equals",
                                "value": "BE",
                            },
                        ]
                    }
                    filter_instance = ReportFilters(
                        dataset, filters, self.property_ids, self.organization_id
                    )
                    result = (
                        filter_instance._ReportFilters__get_translation_keys_from_value(
                            user_input, op
                        )
                    )
                    assert sorted(result) == sorted(expected_keys)

    def test_get_translation_keys_fallback_to_user_input_if_no_match(self):
        with app.app_context():
            with app.test_request_context(headers={"Accept-Language": "pt-BR"}):
                with patch(
                    "app.common.translation_keys.get_reverse_translation_map",
                    return_value={},
                ):
                    dataset = Dataset(DatasetEnum.Reservations)
                    filters = {
                        "and": [
                            {
                                "cdf": {
                                    "column": "reservation_source",
                                    "type": "default",
                                },
                                "operator": "equals",
                                "value": "BE",
                            },
                        ]
                    }
                    filter_instance = ReportFilters(
                        dataset, filters, self.property_ids, self.organization_id
                    )
                    result = (
                        filter_instance._ReportFilters__get_translation_keys_from_value(
                            "Anything", "equals"
                        )
                    )
                    assert result == ["Anything"]

    def test_validate_dynamic_cdf_functon(self, flask_app, launch_darkly_service):
        with app.app_context():
            launch_darkly_service(True)
            dataset = Dataset(DatasetEnum.OccupancyV1)
            filters = {
                "and": [
                    {
                        "cdf": {"column": "adr_converted_rate", "type": "default"},
                        "operator": "greater_than",
                        "value": "150",
                    },
                    {
                        "and": [
                            {
                                "cdf": {
                                    "column": "adr_converted_rate",
                                    "type": "default",
                                },
                                "operator": "greater_than",
                                "value": "100",
                            },
                        ]
                    },
                ]
            }
            report_filters = ReportFilters(
                dataset, filters, self.property_ids, self.organization_id
            ).get_filters()
            assert report_filters[0]["and"][2]["and"][0]["value"] == "150"
            assert report_filters[0]["and"][2]["and"][0]["field"].expression is not None
