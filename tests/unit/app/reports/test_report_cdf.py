from http import HTTPStatus
from types import SimpleNamespace

import pytest

from sqlalchemy.sql.elements import Label
from sqlalchemy.sql.schema import Column

from app.api.v1_1.schemas.custom_cdf import CustomCdfFormulaSchema
from app.common.constants.dynamic_cdf_label import DYNAMIC_CDF_LABEL
from app.datasets.financial import FinancialView
from app.datasets.guests import GuestsView
from app.datasets.housekeeping import HousekeepingView
from app.datasets.invoices import InvoicesView
from app.datasets.occupancy import OccupancyView
from app.datasets.occupancy_v1 import OccupancyV1FFView
from app.datasets.payment import PaymentView
from app.datasets.reservations import ReservationsView
from app.enums import (
    Cdf,
    CdfKind,
    CustomCdfFormulaKind,
    GroupModifier,
    Metric,
    MultiLevel as MultiLevelEnum,
    NumericFormat,
    Operator,
)
from app.enums.custom_cdf_numeric_formats import CustomCdfNumericFormats
from app.enums.dataset import Dataset
from app.multi_levels.room_nights import RoomNightsView
from app.multi_levels.room_reservations import RoomReservationsView
from app.reports.report_cdf import ReportCDF

from manage import app


class TestReportCDF:
    def get_custom_cdf_dynamic(name, column):
        return SimpleNamespace(
            name=name,
            formula=[
                dict(
                    kind=CustomCdfFormulaKind.Cdf.value,
                    value=column,
                    metric=Metric.sum.value,
                ),
                dict(
                    kind=CustomCdfFormulaKind.Operator.value,
                    value=Operator.subtract.value,
                ),
                dict(
                    kind=CustomCdfFormulaKind.Cdf.value,
                    value=column,
                    metric=Metric.mean.value,
                ),
                dict(
                    kind=CustomCdfFormulaKind.Operator.value, value=Operator.add.value
                ),
                dict(kind=CustomCdfFormulaKind.Parenthesis.value, value="("),
                dict(kind=CustomCdfFormulaKind.Operand.value, value="30.5"),
                dict(
                    kind=CustomCdfFormulaKind.Operator.value,
                    value=Operator.divide.value,
                ),
                dict(kind=CustomCdfFormulaKind.Operand.value, value="10"),
                dict(kind=CustomCdfFormulaKind.Parenthesis.value, value=")"),
            ],
            kind=CdfKind.Dynamic.name,
            format=CustomCdfNumericFormats.Number.value,
        )

    def get_custom_cdf_string(name, column):
        return SimpleNamespace(
            name=name,
            formula=[
                dict(kind=CustomCdfFormulaKind.Cdf.value, value=column),
                dict(kind=CustomCdfFormulaKind.Separator.value, value=" - "),
                dict(kind=CustomCdfFormulaKind.Cdf.value, value=column),
            ],
            kind=CdfKind.String.name,
            format=None,
        )

    def get_custom_cdf_number(name, column):
        return SimpleNamespace(
            name=name,
            formula=[
                dict(kind=CustomCdfFormulaKind.Cdf.value, value=column),
                dict(
                    kind=CustomCdfFormulaKind.Operator.value,
                    value=Operator.subtract.value,
                ),
                dict(kind=CustomCdfFormulaKind.Cdf.value, value=column),
                dict(
                    kind=CustomCdfFormulaKind.Operator.value, value=Operator.add.value
                ),
                dict(kind=CustomCdfFormulaKind.Parenthesis.value, value="("),
                dict(kind=CustomCdfFormulaKind.Operand.value, value="30.5"),
                dict(
                    kind=CustomCdfFormulaKind.Operator.value,
                    value=Operator.divide.value,
                ),
                dict(kind=CustomCdfFormulaKind.Operand.value, value="10"),
                dict(kind=CustomCdfFormulaKind.Parenthesis.value, value=")"),
            ],
            kind=CdfKind.Number.name,
            format=None,
        )

    test_data_get_cdf_default = [
        (
            (
                FinancialView,
                dict(cdf=dict(column="reservation_status", type=Cdf.Default.value)),
            ),
            "reservation_status",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="credit_amount", type=Cdf.Default.value)),
            ),
            "credit_amount",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="reservation_number", type=Cdf.Default.value)),
            ),
            "reservation_number",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="room_types", type=Cdf.Default.value)),
            ),
            "room_types",
        ),
        (
            (GuestsView, dict(cdf=dict(column="guest_email", type=Cdf.Default.value))),
            "guest_email",
        ),
        (
            (GuestsView, dict(cdf=dict(column="guest_city", type=Cdf.Default.value))),
            "guest_city",
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_default)
    def test_get_cdf_default(self, test_input, expected):
        assert ReportCDF(*test_input, dataset_id=1).get_cdf().key == expected

    test_data_get_cdf_custom_string = [
        (
            (
                FinancialView,
                dict(
                    cdf=dict(column="custom_reservation_status", type=Cdf.Custom.value)
                ),
                Dataset.Financial.value,
                get_custom_cdf_string(
                    "custom_reservation_status", "reservation_status"
                ),
            ),
            get_custom_cdf_string(
                "custom_reservation_status", "reservation_status"
            ).name,
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="custom_credit_amount", type=Cdf.Custom.value)),
                Dataset.Financial.value,
                get_custom_cdf_string("custom_credit_amount", "credit_amount"),
            ),
            get_custom_cdf_string("custom_credit_amount", "credit_amount").name,
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(column="custom_reservation_number", type=Cdf.Custom.value)
                ),
                Dataset.Reservations.value,
                get_custom_cdf_string(
                    "custom_reservation_number", "reservation_number"
                ),
            ),
            get_custom_cdf_string(
                "custom_reservation_number", "reservation_number"
            ).name,
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="custom_room_types", type=Cdf.Custom.value)),
                Dataset.Reservations.value,
                get_custom_cdf_string("custom_room_types", "room_types"),
            ),
            get_custom_cdf_string("custom_room_types", "room_types").name,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="custom_guest_email", type=Cdf.Custom.value)),
                Dataset.Guests.value,
                get_custom_cdf_string("custom_guest_email", "guest_email"),
            ),
            get_custom_cdf_string("custom_guest_email", "guest_email").name,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="custom_guest_city", type=Cdf.Custom.value)),
                Dataset.Guests.value,
                get_custom_cdf_string("custom_guest_city", "guest_city"),
            ),
            get_custom_cdf_string("custom_guest_city", "guest_city").name,
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_custom_string)
    def test_get_cdf_custom_string(self, test_input, expected):
        assert ReportCDF(*test_input).get_cdf().key == expected

    test_data_get_cdf_custom_number = [
        (
            (
                FinancialView,
                dict(
                    cdf=dict(column="custom_reservation_status", type=Cdf.Custom.value)
                ),
                Dataset.Financial.value,
                get_custom_cdf_number(
                    "custom_reservation_status", "reservation_status"
                ),
            ),
            get_custom_cdf_number(
                "custom_reservation_status", "reservation_status"
            ).name,
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="custom_credit_amount", type=Cdf.Custom.value)),
                Dataset.Financial.value,
                get_custom_cdf_number("custom_credit_amount", "credit_amount"),
            ),
            get_custom_cdf_number("custom_credit_amount", "credit_amount").name,
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(column="custom_reservation_number", type=Cdf.Custom.value)
                ),
                Dataset.Reservations.value,
                get_custom_cdf_number(
                    "custom_reservation_number", "reservation_number"
                ),
            ),
            get_custom_cdf_number(
                "custom_reservation_number", "reservation_number"
            ).name,
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="custom_room_types", type=Cdf.Custom.value)),
                Dataset.Reservations.value,
                get_custom_cdf_number("custom_room_types", "room_types"),
            ),
            get_custom_cdf_number("custom_room_types", "room_types").name,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="custom_guest_email", type=Cdf.Custom.value)),
                Dataset.Guests.value,
                get_custom_cdf_number("custom_guest_email", "guest_email"),
            ),
            get_custom_cdf_number("custom_guest_email", "guest_email").name,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="custom_guest_city", type=Cdf.Custom.value)),
                Dataset.Guests.value,
                get_custom_cdf_number("custom_guest_city", "guest_city"),
            ),
            get_custom_cdf_number("custom_guest_city", "guest_city").name,
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_custom_number)
    def test_get_cdf_custom_number(self, test_input, expected):
        assert ReportCDF(*test_input).get_cdf().key == expected

    test_data_get_cdf_type = [
        (
            (
                FinancialView,
                dict(cdf=dict(column="reservation_status", type=Cdf.Default.value)),
                Dataset.Financial.value,
            ),
            Label,
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="reservation_number", type=Cdf.Default.value)),
                Dataset.Reservations.value,
            ),
            Label,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="guest_email", type=Cdf.Default.value)),
                Dataset.Guests.value,
            ),
            Label,
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_type)
    def test_get_cdf_type(self, test_input, expected):
        assert isinstance(ReportCDF(*test_input).get_cdf(), expected)

    test_data_get_cdf_expression = [
        (
            (
                FinancialView,
                dict(cdf=dict(column="reservation_status", type=Cdf.Default.value)),
                Dataset.Financial.value,
            ),
            Column,
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="reservation_number", type=Cdf.Default.value)),
                Dataset.Reservations.value,
            ),
            Column,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="guest_email", type=Cdf.Default.value)),
                Dataset.Guests.value,
            ),
            Column,
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_expression)
    def test_get_cdf_expression(self, test_input, expected):
        assert isinstance(ReportCDF(*test_input).get_cdf().element.expression, expected)

    test_data_get_cdf_metric = [
        (
            (
                FinancialView,
                dict(cdf=dict(column="credit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                Metric.sum.value,
            ),
            "credit_amount - sum",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="debit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                Metric.max.value,
            ),
            "debit_amount - max",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="guest_count", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                Metric.max.value,
            ),
            "guest_count - max",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="room_count", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                Metric.min.value,
            ),
            "room_count - min",
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_metric)
    def test_get_cdf_metric(self, test_input, expected):
        assert ReportCDF(*test_input).get_cdf().key == expected

    test_data_get_cdf_modifier = [
        (
            (
                FinancialView,
                dict(cdf=dict(column="booking_datetime", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                None,
                GroupModifier.Second.value,
            ),
            "date_trunc",
        ),
        (
            (
                FinancialView,
                dict(
                    cdf=dict(
                        column="transaction_datetime_property_timezone",
                        type=Cdf.Default.value,
                    )
                ),
                Dataset.Financial.value,
                None,
                None,
                None,
                GroupModifier.Minute.value,
            ),
            "date_trunc",
        ),
        (
            (
                FinancialView,
                dict(
                    cdf=dict(
                        column="transaction_datetime_property_timezone",
                        type=Cdf.Default.value,
                    )
                ),
                Dataset.Financial.value,
                None,
                None,
                None,
                GroupModifier.MonthWithoutYear.value,
            ),
            "date_trunc",
        ),
        (
            (
                FinancialView,
                dict(
                    cdf=dict(
                        column="transaction_datetime_property_timezone",
                        type=Cdf.Default.value,
                    ),
                    direction="asc",
                ),
                Dataset.Financial.value,
                None,
                None,
                None,
                GroupModifier.MonthWithoutYear.value,
            ),
            "date_trunc",
        ),
        (
            (
                FinancialView,
                dict(
                    cdf=dict(
                        column="transaction_datetime_property_timezone",
                        type=Cdf.Default.value,
                    ),
                    direction="desc",
                ),
                Dataset.Financial.value,
                None,
                None,
                None,
                GroupModifier.MonthWithoutYear.value,
            ),
            "date_trunc",
        ),
        (
            (
                FinancialView,
                dict(
                    cdf=dict(
                        column="transaction_datetime_property_timezone",
                        type=Cdf.Default.value,
                    )
                ),
                Dataset.Financial.value,
                None,
                None,
                None,
                GroupModifier.Week.value,
            ),
            "date_trunc",
        ),
        (
            (
                FinancialView,
                dict(
                    cdf=dict(
                        column="transaction_datetime_property_timezone",
                        type=Cdf.Default.value,
                    )
                ),
                Dataset.Financial.value,
                None,
                None,
                None,
                GroupModifier.Month.value,
            ),
            "date_trunc",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="checkin_date", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                GroupModifier.Hour.value,
            ),
            "date_trunc",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="checkout_date", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                GroupModifier.Day.value,
            ),
            "date_trunc",
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="cancellation_datetime", type=Cdf.Default.value)),
                Dataset.Guests.value,
                None,
                None,
                None,
                GroupModifier.Hour.value,
            ),
            "date_trunc",
        ),
        (
            (
                GuestsView,
                dict(
                    cdf=dict(
                        column="guest_document_expiration_date", type=Cdf.Default.value
                    )
                ),
                Dataset.Guests.value,
                None,
                None,
                None,
                GroupModifier.Day.value,
            ),
            "date_trunc",
        ),
        (
            (
                GuestsView,
                dict(
                    cdf=dict(
                        column="guest_document_expiration_date", type=Cdf.Default.value
                    )
                ),
                Dataset.Guests.value,
                None,
                None,
                None,
                GroupModifier.Week.value,
            ),
            "date_trunc",
        ),
        (
            (
                HousekeepingView,
                dict(cdf=dict(column="stay_date", type=Cdf.Default.value)),
                Dataset.Housekeeping.value,
                None,
                None,
                None,
                GroupModifier.Week.value,
            ),
            "date_trunc",
        ),
        (
            (
                HousekeepingView,
                dict(cdf=dict(column="estimated_arrival_time", type=Cdf.Default.value)),
                Dataset.Housekeeping.value,
                None,
                None,
                None,
                GroupModifier.Hour.value,
            ),
            "date_trunc",
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_modifier)
    def test_get_cdf_modifier(self, test_input, expected):
        assert expected in str(ReportCDF(*test_input).get_cdf().expression)

    test_data_get_cdf_dynamic = [
        (
            (
                OccupancyView,
                dict(cdf=dict(column=OccupancyView.adr.key, type=Cdf.Default.value)),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                True,
            ),
            f"adr - {DYNAMIC_CDF_LABEL}",
        ),
        (
            (
                OccupancyView,
                dict(
                    cdf=dict(column=OccupancyView.occupancy.key, type=Cdf.Default.value)
                ),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                True,
            ),
            f"occupancy - {DYNAMIC_CDF_LABEL}",
        ),
        (
            (
                OccupancyView,
                dict(
                    cdf=dict(
                        column=OccupancyView.adr.key,
                        type=Cdf.Default.value,
                    )
                ),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                True,
            ),
            f"adr - {DYNAMIC_CDF_LABEL}",
        ),
        (
            (
                OccupancyView,
                dict(
                    cdf=dict(
                        column=OccupancyView.adr.key,
                        type=Cdf.Default.value,
                    )
                ),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                True,
            ),
            f"adr - {DYNAMIC_CDF_LABEL}",
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_dynamic)
    def test_get_cdf_dynamic(self, test_input, expected):
        assert ReportCDF(*test_input).get_cdf().key == expected

    test_data_get_cdf_float_formatted = [
        (
            (
                OccupancyView,
                dict(cdf=dict(column="room_rate_amount", type=Cdf.Default.value)),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                OccupancyView,
                dict(cdf=dict(column="adr", type=Cdf.Default.value)),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="credit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="debit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="balance_due_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(cdf=dict(column="payment_fee_amount", type=Cdf.Default.value)),
                Dataset.Payment.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(cdf=dict(column="payment_net_amount", type=Cdf.Default.value)),
                Dataset.Payment.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(
                    cdf=dict(column="payment_submitted_amount", type=Cdf.Default.value)
                ),
                Dataset.Payment.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="total_gross_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="taxes_value_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="balance_due_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="commission_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="deposit_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="grand_total_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(
                        column="reservation_balance_due_amount", type=Cdf.Default.value
                    )
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(column="reservation_paid_amount", type=Cdf.Default.value)
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(column="room_revenue_total_amount", type=Cdf.Default.value)
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="taxes_value_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                RoomReservationsView,
                dict(
                    cdf=dict(
                        column="room_reservation_price",
                        type=Cdf.Default.value,
                        multi_level_id=MultiLevelEnum.RoomReservations.value,
                    )
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                RoomReservationsView,
                dict(
                    cdf=dict(
                        column="room_total_price",
                        type=Cdf.Default.value,
                        multi_level_id=MultiLevelEnum.RoomReservations.value,
                    )
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                RoomNightsView,
                dict(
                    cdf=dict(
                        column="room_rate",
                        type=Cdf.Default.value,
                        multi_level_id=MultiLevelEnum.RoomNights.value,
                    )
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                OccupancyV1FFView,
                dict(cdf=dict(column="adr_converted_rate", type=Cdf.Default.value)),
                Dataset.OccupancyV1.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_float_formatted)
    def test_get_cdf_float_formatted(
        self, test_input, expected, launch_darkly_service, flask_app
    ):
        launch_darkly_service(True)
        with app.app_context():
            assert expected in str(ReportCDF(*test_input).get_cdf().expression)

    test_data_get_cdf_float_raw = [
        (
            (
                OccupancyView,
                dict(cdf=dict(column="room_rate_amount", type=Cdf.Default.value)),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                OccupancyView,
                dict(cdf=dict(column="adr", type=Cdf.Default.value)),
                Dataset.Occupancy.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="credit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="debit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="balance_due_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(cdf=dict(column="payment_fee_amount", type=Cdf.Default.value)),
                Dataset.Payment.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(cdf=dict(column="payment_net_amount", type=Cdf.Default.value)),
                Dataset.Payment.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(
                    cdf=dict(column="payment_submitted_amount", type=Cdf.Default.value)
                ),
                Dataset.Payment.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="total_gross_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="taxes_value_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="balance_due_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="commission_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="deposit_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="grand_total_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(
                        column="reservation_balance_due_amount", type=Cdf.Default.value
                    )
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(column="reservation_paid_amount", type=Cdf.Default.value)
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(column="room_revenue_total_amount", type=Cdf.Default.value)
                ),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="taxes_value_amount", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                RoomReservationsView,
                dict(cdf=dict(column="room_reservation_price", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                RoomReservationsView,
                dict(cdf=dict(column="room_total_price", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
        (
            (
                RoomNightsView,
                dict(cdf=dict(column="room_rate", type=Cdf.Default.value)),
                Dataset.Reservations.value,
                None,
                None,
                None,
                None,
                False,
                True,
                NumericFormat.Raw.value,
            ),
            "to_char",
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_float_raw)
    def test_get_cdf_float_raw(self, test_input, expected):
        assert expected not in str(ReportCDF(*test_input).get_cdf().expression)

    test_data_get_cdf_metrics = [
        (
            (
                FinancialView,
                dict(cdf=dict(column="credit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "sum",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="debit_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "mean",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="balance_due_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "max",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(cdf=dict(column="payment_fee_amount", type=Cdf.Default.value)),
                Dataset.Payment.value,
                None,
                None,
                "min",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                PaymentView,
                dict(cdf=dict(column="payment_net_amount", type=Cdf.Default.value)),
                Dataset.Payment.value,
                None,
                None,
                "count",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "INTEGER",
        ),
        (
            (
                PaymentView,
                dict(
                    cdf=dict(column="payment_submitted_amount", type=Cdf.Default.value)
                ),
                Dataset.Payment.value,
                None,
                None,
                "median",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="total_gross_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                "std",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                InvoicesView,
                dict(cdf=dict(column="taxes_value_amount", type=Cdf.Default.value)),
                Dataset.Invoices.value,
                None,
                None,
                "var",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="quantity_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "sum",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "INTEGER",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="quantity_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "mean",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                OccupancyView,
                dict(cdf=dict(column="guest_count", type=Cdf.Default.value)),
                Dataset.Occupancy.value,
                None,
                None,
                "max",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "INTEGER",
        ),
        (
            (
                InvoicesView,
                dict(
                    cdf=dict(
                        column="reservation_room_nights_count", type=Cdf.Default.value
                    )
                ),
                Dataset.Invoices.value,
                None,
                None,
                "min",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "INTEGER",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="quantity_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "count",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "INTEGER",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="quantity_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "median",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="quantity_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "std",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="quantity_amount", type=Cdf.Default.value)),
                Dataset.Financial.value,
                None,
                None,
                "var",
                None,
                False,
                True,
                NumericFormat.Formatted.value,
            ),
            "to_char",
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_metrics)
    def test_get_cdf_metrics(self, test_input, expected):
        assert expected in str(ReportCDF(*test_input).get_cdf().expression)

    def test_query_report_export_schema(self):
        from werkzeug.exceptions import HTTPException

        with pytest.raises(HTTPException) as http_error:
            CustomCdfFormulaSchema().load(
                dict(kind=CustomCdfFormulaKind.Operator.value, value="/*")
            )
        assert http_error.value.code == HTTPStatus.BAD_REQUEST
        assert http_error.value.data["message"] == "Invalid operator for this kind: /*"

        with pytest.raises(HTTPException) as http_error:
            CustomCdfFormulaSchema().load(
                dict(kind=CustomCdfFormulaKind.Operand.value, value="/*")
            )
        assert http_error.value.code == HTTPStatus.BAD_REQUEST
        assert (
            http_error.value.data["message"]
            == "Invalid operand for this kind: /*. It must be a number."
        )

        with pytest.raises(HTTPException) as http_error:
            CustomCdfFormulaSchema().load(
                dict(kind=CustomCdfFormulaKind.Parenthesis.value, value="/*")
            )
        assert http_error.value.code == HTTPStatus.BAD_REQUEST
        assert (
            http_error.value.data["message"]
            == "Invalid parenthesis: supported values are ( or )"
        )

    test_data_dynamic_cdf = [
        (
            (
                OccupancyView,
                dict(cdf=dict(column="occupancy", type=Cdf.Default.value)),
                None,
                None,
                None,
                None,
                True,
                True,
                NumericFormat.Formatted.value,
            ),
            2,
        ),
        (
            (
                OccupancyView,
                dict(cdf=dict(column="revpar", type=Cdf.Default.value)),
                None,
                None,
                None,
                None,
                True,
                True,
                NumericFormat.Formatted.value,
            ),
            2,
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_dynamic_cdf)
    def test_get_dynamic_cdf(self, test_input, expected):
        assert expected == ReportCDF(*test_input).rounding_precision

    test_data_get_cdf_custom_dynamic = [
        (
            (
                FinancialView,
                dict(
                    cdf=dict(column="custom_reservation_status", type=Cdf.Custom.value)
                ),
                Dataset.Financial.value,
                get_custom_cdf_number(
                    "custom_reservation_status", "reservation_status"
                ),
            ),
            get_custom_cdf_number(
                "custom_reservation_status", "reservation_status"
            ).name,
        ),
        (
            (
                FinancialView,
                dict(cdf=dict(column="custom_credit_amount", type=Cdf.Custom.value)),
                Dataset.Financial.value,
                get_custom_cdf_number("custom_credit_amount", "credit_amount"),
            ),
            get_custom_cdf_number("custom_credit_amount", "credit_amount").name,
        ),
        (
            (
                ReservationsView,
                dict(
                    cdf=dict(column="custom_reservation_number", type=Cdf.Custom.value)
                ),
                Dataset.Reservations.value,
                get_custom_cdf_number(
                    "custom_reservation_number", "reservation_number"
                ),
            ),
            get_custom_cdf_number(
                "custom_reservation_number", "reservation_number"
            ).name,
        ),
        (
            (
                ReservationsView,
                dict(cdf=dict(column="custom_room_types", type=Cdf.Custom.value)),
                Dataset.Reservations.value,
                get_custom_cdf_number("custom_room_types", "room_types"),
            ),
            get_custom_cdf_number("custom_room_types", "room_types").name,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="custom_guest_email", type=Cdf.Custom.value)),
                Dataset.Guests.value,
                get_custom_cdf_number("custom_guest_email", "guest_email"),
            ),
            get_custom_cdf_number("custom_guest_email", "guest_email").name,
        ),
        (
            (
                GuestsView,
                dict(cdf=dict(column="custom_guest_city", type=Cdf.Custom.value)),
                Dataset.Guests.value,
                get_custom_cdf_number("custom_guest_city", "guest_city"),
            ),
            get_custom_cdf_number("custom_guest_city", "guest_city").name,
        ),
    ]

    @pytest.mark.parametrize("test_input, expected", test_data_get_cdf_custom_dynamic)
    def test_get_cdf_custom_dynamic(self, test_input, expected):
        assert ReportCDF(*test_input).get_cdf().key == expected
