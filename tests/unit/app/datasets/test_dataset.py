import pytest


from app.datasets.dataset import Dataset
from app.datasets.financial import FinancialFFView, FinancialView
from app.datasets.guests import Guests<PERSON>View, GuestsView
from app.datasets.housekeeping import HousekeepingFFView, HousekeepingView
from app.datasets.invoices import InvoicesFFView, InvoicesView
from app.datasets.occupancy import OccupancyView
from app.datasets.occupancy_v1 import OccupancyV1FFView, OccupancyV1View
from app.datasets.payment import PaymentFFView, PaymentView
from app.datasets.reservations import ReservationsFFView, ReservationsView
from app.enums import Cdf, Dataset as DatasetEnum, FilterOperator, Sort
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind

from tests.unit.data.parameters import DATA_INSIGHTS_BASE_URL, PROPERTY_ID
from tests.unit.mocks.token import PROPERTY_USER_TOKEN


BASE_URL = f"{DATA_INSIGHTS_BASE_URL}/datasets"
HEADERS = dict(Authorization=PROPERTY_USER_TOKEN, X_PROPERTY_ID=PROPERTY_ID)

extensions = ["__", "_summary", "_sa_class_manager"]


@pytest.mark.usefixtures("flask_app", "clean_cache")
class TestDataset:
    test_data_model = [
        (DatasetEnum.Financial, FinancialView),
        (DatasetEnum.Guests, GuestsView),
        (DatasetEnum.Reservations, ReservationsView),
        (DatasetEnum.Occupancy, OccupancyView),
        (DatasetEnum.Payment, PaymentView),
        (DatasetEnum.Invoices, InvoicesView),
        (DatasetEnum.Housekeeping, HousekeepingView),
    ]

    @pytest.mark.parametrize("kind, expected", test_data_model)
    def test_dataset_model(self, kind, expected, launch_darkly_service):
        launch_darkly_service(False)

        model = Dataset(kind).model

        assert model == expected

    test_data_model_ff = [
        (DatasetEnum.Financial, FinancialFFView),
        (DatasetEnum.Guests, GuestsFFView),
        (DatasetEnum.Reservations, ReservationsFFView),
        (DatasetEnum.Payment, PaymentFFView),
        (DatasetEnum.Invoices, InvoicesFFView),
        (DatasetEnum.Housekeeping, HousekeepingFFView),
    ]

    @pytest.mark.parametrize("kind, expected", test_data_model_ff)
    def test_dataset_model_behind_ff(self, kind, expected, launch_darkly_service):
        launch_darkly_service(True)

        model = Dataset(kind).model

        assert model == expected

    test_data_default_sorts = [
        (
            DatasetEnum.Financial,
            [
                dict(
                    cdf=dict(
                        type=Cdf.Default.value,
                        column=FinancialView.transaction_datetime.key,
                    ),
                    direction=Sort.desc.value,
                )
            ],
        ),
        (
            DatasetEnum.Guests,
            [
                dict(
                    cdf=dict(
                        type=Cdf.Default.value, column=GuestsView.booking_datetime.key
                    ),
                    direction=Sort.desc.value,
                )
            ],
        ),
        (
            DatasetEnum.Reservations,
            [
                dict(
                    cdf=dict(
                        type=Cdf.Default.value,
                        column=ReservationsView.booking_datetime.key,
                    ),
                    direction=Sort.asc.value,
                )
            ],
        ),
        (
            DatasetEnum.Occupancy,
            [
                dict(
                    cdf=dict(
                        type=Cdf.Default.value, column=OccupancyView.stay_date.key
                    ),
                    direction=Sort.desc.value,
                )
            ],
        ),
        (DatasetEnum.Payment, []),
        (DatasetEnum.Invoices, []),
        (DatasetEnum.Housekeeping, []),
    ]

    @pytest.mark.parametrize("kind, expected", test_data_default_sorts)
    def test_dataset_default_sorts(self, kind, expected):
        default_sorts = Dataset(kind).default_sorts

        assert default_sorts == expected

    test_data = [
        (
            DatasetEnum.Financial,
            [
                dict(
                    cdf=dict(
                        type=Cdf.Default.value,
                        column=FinancialView.is_transaction_deleted.key,
                    ),
                    operator=FilterOperator.Equals.value,
                    value="No",
                ),
                {
                    "or": [
                        dict(
                            cdf=dict(
                                type=Cdf.Default.value,
                                column=FinancialView.internal_transaction_code.key,
                            ),
                            operator=FilterOperator.NotListContains.value,
                            value=["6000", "6100", "7000", "7100"],
                        ),
                        dict(
                            cdf=dict(
                                type=Cdf.Default.value,
                                column=FinancialView.internal_transaction_code.key,
                            ),
                            operator=FilterOperator.IsNull.value,
                            value="",
                        ),
                    ]
                },
            ],
        ),
        (DatasetEnum.Guests, []),
        (DatasetEnum.Reservations, []),
        (DatasetEnum.Occupancy, []),
        (DatasetEnum.Payment, []),
        (DatasetEnum.Invoices, []),
        (DatasetEnum.Housekeeping, []),
    ]

    @pytest.mark.parametrize("kind, expected", test_data)
    def test_dataset_static_filters(self, kind, expected, launch_darkly_service):
        launch_darkly_service(True)
        static_filters = Dataset(kind).static_filters

        assert static_filters == expected

    test_data_dataset_cdfs = [
        (
            DatasetEnum.Reservations,
            len(
                [
                    key
                    for key in [
                        k for k in ReservationsFFView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.Financial,
            len(
                [
                    key
                    for key in [
                        k for k in FinancialFFView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            )
            - 1,
        ),
        (
            DatasetEnum.Guests,
            len(
                [
                    key
                    for key in [
                        k for k in GuestsFFView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.Occupancy,
            len(
                [
                    key
                    for key in [
                        k for k in OccupancyView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            )
            - 3,
        ),
        (
            DatasetEnum.Payment,
            len(
                [
                    key
                    for key in [
                        k for k in PaymentFFView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.Invoices,
            len(
                [
                    key
                    for key in [
                        k for k in InvoicesFFView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.OccupancyV1,
            len(
                [
                    key
                    for key in [
                        k for k in OccupancyV1FFView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(
                        extension in key
                        for extension in extensions + ["booking_room_id"]
                    )
                ]
            )
            - 9,  # Remove _cases_ cdf,
        ),
        (
            DatasetEnum.Housekeeping,
            len(
                [
                    key
                    for key in [
                        k for k in HousekeepingView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.Housekeeping,
            len(
                [
                    key
                    for key in HousekeepingFFView.__dict__.keys()
                    if key != "metadata"
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
    ]

    @pytest.mark.parametrize("kind, expected", test_data_dataset_cdfs)
    def test_dataset_cdfs_ld_true(self, kind, expected, launch_darkly_service):
        launch_darkly_service(True)
        cdfs = Dataset(kind).cdfs
        cdf_category_values = [item.value for item in CdfCategory]

        assert len(
            [category for category in cdfs for category in category["cdfs"]]
        ) == (expected)
        assert all([category["category"] in cdf_category_values for category in cdfs])
        assert all(
            [
                True
                for category in cdfs
                for category in category["cdfs"]
                if category["kind"].value in [kind.value for kind in CdfKind]
            ]
        )

    test_data_dataset_cdfs_ld_false = [
        (
            DatasetEnum.Reservations,
            len(
                [
                    key
                    for key in [
                        k for k in ReservationsView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.Financial,
            len(
                [
                    key
                    for key in [
                        k for k in FinancialView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            )
            - 4,
        ),
        (
            DatasetEnum.Guests,
            len(
                [
                    key
                    for key in [
                        k for k in GuestsView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.Occupancy,
            len(
                [
                    key
                    for key in [
                        k for k in OccupancyView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            )
            - 3,
        ),
        (
            DatasetEnum.Payment,
            len(
                [
                    key
                    for key in [
                        k for k in PaymentView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.Invoices,
            len(
                [
                    key
                    for key in [
                        k for k in InvoicesView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
        (
            DatasetEnum.OccupancyV1,
            len(
                [
                    key
                    for key in [
                        k for k in OccupancyV1View.__dict__.keys() if k != "metadata"
                    ]
                    if not any(
                        extension in key
                        for extension in extensions + ["booking_room_id"]
                    )
                ]
            )
            - 4,  # Remove _cases cdfs,
        ),
        (
            DatasetEnum.Housekeeping,
            len(
                [
                    key
                    for key in [
                        k for k in HousekeepingView.__dict__.keys() if k != "metadata"
                    ]
                    if not any(extension in key for extension in extensions)
                ]
            ),
        ),
    ]

    @pytest.mark.parametrize("kind, expected", test_data_dataset_cdfs_ld_false)
    def test_dataset_cdfs_ld_false(self, kind, expected, launch_darkly_service):
        launch_darkly_service(False)
        cdfs = Dataset(kind).cdfs
        cdf_category_values = [item.value for item in CdfCategory]

        assert len(
            [category for category in cdfs for category in category["cdfs"]]
        ) == (expected)
        assert all([category["category"] in cdf_category_values for category in cdfs])
        assert all(
            [
                True
                for category in cdfs
                for category in category["cdfs"]
                if category["kind"].value in [kind.value for kind in CdfKind]
            ]
        )

    test_data_flatten_cdfs = [
        (DatasetEnum.Financial),
        (DatasetEnum.Guests),
        (DatasetEnum.Reservations),
        (DatasetEnum.Occupancy),
        (DatasetEnum.Payment),
        (DatasetEnum.Invoices),
        (DatasetEnum.OccupancyV1),
        (DatasetEnum.Housekeeping),
    ]

    @pytest.mark.parametrize("kind", test_data_flatten_cdfs)
    def test_dataset_flatten_cdfs(self, kind):
        flatten_cdfs = Dataset(kind).flatten_cdfs

        assert isinstance(flatten_cdfs, list)
        assert all(isinstance(cdf, dict) for cdf in flatten_cdfs)

    @pytest.mark.parametrize("kind", test_data_flatten_cdfs)
    def test_dataset_flatten_all_cdfs(self, kind):
        flatten_cdfs = Dataset(kind).flatten_all_cdfs

        assert isinstance(flatten_cdfs, list)
        assert all(isinstance(cdf, dict) for cdf in flatten_cdfs)

    @pytest.mark.parametrize("kind", test_data_flatten_cdfs)
    def test_dataset_multi_level_flatten_cdfs(self, kind):
        flatten_cdfs = Dataset(kind).multi_level_flatten_cdfs

        assert isinstance(flatten_cdfs, list)
        assert all(isinstance(cdf, dict) for cdf in flatten_cdfs)

    @pytest.mark.usefixtures(
        "authorized_user_mock", "mock_datasets_memoized", "is_whitelisted"
    )
    def test_get_datasets(self, test_client):
        response = test_client.get(f"{BASE_URL}/", headers=HEADERS)
        assert response.status_code == 200
        assert len(response.json) == 11
        assert response.json[3]["name"] == "Occupancy (Legacy - Unsupported)"
        assert response.json[6]["name"] == "Occupancy"
        assert response.json[8]["name"] == "Accounting"

    @pytest.mark.usefixtures(
        "authorized_user_mock", "mock_datasets_memoized", "is_whitelisted"
    )
    def test_get_datasets_ff(self, test_client, launch_darkly_service):
        launch_darkly_service(False)
        response = test_client.get(f"{BASE_URL}/", headers=HEADERS)
        assert response.status_code == 200
        assert len(response.json) == 8
        assert response.json[3]["name"] == "Occupancy (Legacy - Unsupported)"

    @pytest.mark.usefixtures(
        "authorized_user_mock",
        "mock_datasets_memoized",
        "is_whitelisted",
        "accounting_off_occupancy_v1_on",
    )
    def test_get_datasets_ff_occupancy_v1(self, test_client):
        response = test_client.get(f"{BASE_URL}/", headers=HEADERS)
        assert response.status_code == 200
        assert len(response.json) == 9
        assert response.json[6]["name"] == "Occupancy"

    @pytest.mark.usefixtures(
        "authorized_user_mock",
        "mock_datasets_memoized",
        "is_whitelisted",
        "occupancy_v1_off_accounting_on",
    )
    def test_get_datasets_ff_housekeeping_and_accounting(self, test_client):
        response = test_client.get(f"{BASE_URL}/", headers=HEADERS)
        assert response.status_code == 200
        assert len(response.json) == 9
        assert response.json[6]["name"] == "Housekeeping"
        assert response.json[7]["name"] == "Accounting"

    test_data_cdfs_with_args = [
        (FinancialView, FinancialView.conversion_rate, {"currency": "EUR"}),
        (FinancialView, FinancialView.conversion_rate, {}),
        (FinancialView, FinancialView.debit_amount_converted_rate, {"currency": "EUR"}),
        (FinancialView, FinancialView.debit_amount_converted_rate, {}),
        (
            FinancialFFView,
            FinancialFFView.credit_amount_converted_rate,
            {"currency": "EUR"},
        ),
        (FinancialFFView, FinancialFFView.credit_amount_converted_rate, {}),
        (
            FinancialFFView,
            FinancialFFView.balance_due_amount_converted_rate,
            {"currency": "EUR"},
        ),
        (FinancialFFView, FinancialFFView.balance_due_amount_converted_rate, {}),
    ]

    @pytest.mark.parametrize("model, cdf, kwargs", test_data_cdfs_with_args)
    def test_dataset_cdfs_conversion_functions(self, model, cdf, kwargs):
        assert "function" in cdf.info
        assert cdf.info["function"](model, **kwargs) is not None
