{"name": "@cloudbeds/data-insights-qa-sdk", "version": "1.0.0", "description": "A library to test Data Insights API", "main": "src/index.js", "scripts": {"build": "tsdx build", "lint": "eslint \"{src,tests}/**/*.{js,ts}\"", "lint:show-unused-directives": "npm run lint -- --report-unused-disable-directives", "lint:fix": "npm run lint -- --fix", "test:smoke": "node setupTest && jest --testPathPattern tests/integration/ --json --outputFile=results.json ; node slackNotify.js", "test:test": "jest --testPathPattern tests/integration/bed_occupancy/", "test:smoke:dd-trace": "DD_TRACE_ENABLED=true npm run test:smoke", "test:integration": "node setupTest && jest --testPathPattern tests/integration --json --outputFile=results.json; node slackNotify.js", "test:integration:dd-trace": "DD_TRACE_ENABLED=true npm run test:integration", "test:regression": "node setupTest && jest --testPathPattern \"tests/integration/(financial|clone|query-report|reservation|stock-reports|me)/\"", "test:local": "jest --testPathPattern tests/integration/ --json --outputFile=results.json --maxWorkers=3", "test:e2e": "node setupTest && jest --testPathPattern tests/e2e --json --outputFile=results.json; node slackNotify.js", "release": "standard-version"}, "eslintConfig": {"extends": "@cloudbeds/eslint-config"}, "author": "Data Insights Team", "license": "ISC", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "dependencies": {"@cloudbeds/cloudbeds-qa-request": "3.0.88", "@cloudbeds/cloudbeds-qa-sdk": "4.124.1", "@cloudbeds/jest-slack-reporter": "1.8.0", "@faker-js/faker": "8.3.1", "aws-sdk": "2.1532.0", "builder-pattern": "2.2.0", "date-fns": "^4.1.0", "dd-trace": "5.0.0", "dotenv": "16.3.2", "guid-typescript": "1.0.9", "http-status": "1.7.3", "inversify": "6.0.2", "inversify-inject-decorators": "3.1.0", "jest": "29.7.0", "jest-environment-node": "29.7.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.4.4", "papaparse": "5.4.1", "pino": "8.17.2", "qs": "6.11.2", "reflect-metadata": "0.2.1", "ts-jest": "29.1.1", "typescript": "5.3.3", "xlsx": "^0.18.5"}, "devDependencies": {"@cloudbeds/eslint-config": "2.9.0", "@types/jest": "29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/luxon": "^3.4.2", "@types/papaparse": "^5.3.14", "@types/qs": "6.9.11", "@types/xlsx": "^0.0.36", "eslint-plugin-react-hooks": "4.6.0", "husky": "8.0.3", "jest-html-reporter": "3.10.2", "standard-version": "9.5.0", "tsdx": "0.14.1"}, "standard-version": {"skip": {"commit": true}}, "jestSlackReporter": {"channel": "squad_insights_qa"}, "engines": {"node": ">=20", "npm": ">=10"}}