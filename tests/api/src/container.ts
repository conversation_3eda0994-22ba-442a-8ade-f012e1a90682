import { Container, ContainerModule } from 'inversify';
import pino from 'pino';
import * as dotenv from 'dotenv';
import qs from 'qs';

import { Config } from '../config';

import { TYPES } from './ioc/types';
import {
  DotEnv, HttpRequest, Pino, QueryString, HttpStatus,
} from './ioc/interfaces';
import { ReportService } from './entities/report/report.service';
import { LogService } from './services/log.service';
import { FileDownloaderService } from './services/file.downloader.service';
import { AuthenticateService } from './services/authenticate.service';
import { TagService } from './entities/tag/tag.service';
import { FolderService } from './entities/folder/folder.service';
import { ScheduleService } from './entities/schedule/schedule.service';
import { MeService } from './entities/me/me.service';
import { DatasetService } from './entities/datasets/dataset.service';
import { FavoriteService } from './entities/favorite/favorite.service';
import { AdminService } from './entities/admin/admin.service';
import { PropertyService } from './entities/property/property.service';
import { PermissionService } from './entities/permission/permission.service';
import { StockReportFolderService } from './entities/stock-report-folder/stockReportFolder.service';
import { TaskService } from './entities/task/task.service';
import { HubService } from './entities/hub/hub.service';
import { XLSXService } from './entities/xlsx/xlsx.service';
import { CsvService } from './entities/csv/csv.service';
import { JsonService } from './entities/json/json.service';
import { ClassicReportsService } from './entities/classic_reports/classic-reports.service';
import { StockTagService } from './entities/stock-tag/stock-tag.service';

const httpRequest = require('@cloudbeds/cloudbeds-qa-request');
const httpStatus = require('http-status');

const thirdPartyDependencies = new ContainerModule(bind => {
  bind<HttpRequest>(TYPES.CloudbedsHttpRequest).toConstantValue(httpRequest);
  bind<Pino>(TYPES.Pino).toConstantValue(pino);
  bind<DotEnv>(TYPES.DotEnv).toConstantValue(dotenv);
  bind<QueryString>(TYPES.QueryString).toConstantValue(qs);
  bind<HttpStatus>(TYPES.HttpStatus).toConstantValue(httpStatus);
});

const applicationDependencies = new ContainerModule(bind => {
  bind<ReportService>(TYPES.ReportService).to(ReportService);
  bind<TagService>(TYPES.TagService).to(TagService);
  bind<FolderService>(TYPES.FolderService).to(FolderService);
  bind<ScheduleService>(TYPES.ScheduleService).to(ScheduleService);
  bind<MeService>(TYPES.MeService).to(MeService);
  bind<DatasetService>(TYPES.DatasetService).to(DatasetService);
  bind<FavoriteService>(TYPES.FavoriteService).to(FavoriteService);
  bind<FileDownloaderService>(TYPES.FileDownloaderService).to(FileDownloaderService);
  bind<Config>(TYPES.Config).to(Config).inSingletonScope();
  bind<LogService>(TYPES.LogService).to(LogService).inSingletonScope();
  bind<AuthenticateService>(TYPES.AuthenticateService).to(AuthenticateService).inSingletonScope();
  bind<AdminService>(TYPES.AdminService).to(AdminService).inSingletonScope();
  bind<PropertyService>(TYPES.PropertyService).to(PropertyService).inSingletonScope();
  bind<PermissionService>(TYPES.PermissionService).to(PermissionService).inSingletonScope();
  bind<StockReportFolderService>(TYPES.StockReportFolderService).to(StockReportFolderService).inSingletonScope();
  bind<TaskService>(TYPES.TaskService).to(TaskService).inSingletonScope();
  bind<HubService>(TYPES.HubService).to(HubService).inSingletonScope();
  bind<XLSXService>(TYPES.XLSXService).to(XLSXService).inSingletonScope();
  bind<CsvService>(TYPES.CsvService).to(CsvService).inSingletonScope();
  bind<JsonService>(TYPES.JsonService).to(JsonService).inSingletonScope();
  bind<ClassicReportsService>(TYPES.ClassicReportsService).to(ClassicReportsService).inSingletonScope();
  bind<StockTagService>(TYPES.StockTagService).to(StockTagService).inSingletonScope();
});

const container = new Container();

container.load(thirdPartyDependencies, applicationDependencies);

export { container };
