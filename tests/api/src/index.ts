import 'reflect-metadata';
import { Config } from '../config';

import { container } from './container';
import { TYPES } from './ioc/types';
import { ReportService } from './entities/report/report.service';
import { FileDownloaderService } from './services/file.downloader.service';
import { TagService } from './entities/tag/tag.service';
import { FolderService } from './entities/folder/folder.service';
import { ScheduleService } from './entities/schedule/schedule.service';
import { MeService } from './entities/me/me.service';
import { DatasetService } from './entities/datasets/dataset.service';
import { FavoriteService } from './entities/favorite/favorite.service';
import { AdminService } from './entities/admin/admin.service';
import { PropertyService } from './entities/property/property.service';
import { PermissionService } from './entities/permission/permission.service';
import { StockReportFolderService } from './entities/stock-report-folder/stockReportFolder.service';
import { TaskService } from './entities/task/task.service';
import { HubService } from './entities/hub/hub.service';
import { XLSXService } from './entities/xlsx/xlsx.service';
import { CsvService } from './entities/csv/csv.service';
import { JsonService } from './entities/json/json.service';
import { ClassicReportsService } from './entities/classic_reports/classic-reports.service';
import { StockTagService } from './entities/stock-tag/stock-tag.service';

const reportService = container.get<ReportService>(TYPES.ReportService);
const tagService = container.get<TagService>(TYPES.TagService);
const folderService = container.get<FolderService>(TYPES.FolderService);
const scheduleService = container.get<ScheduleService>(TYPES.ScheduleService);
const meService = container.get<MeService>(TYPES.MeService);
const datasetService = container.get<DatasetService>(TYPES.DatasetService);
const favoriteService = container.get<FavoriteService>(TYPES.FavoriteService);
const fileDownloaderService = container.get<FileDownloaderService>(TYPES.FileDownloaderService);
const config = container.get<Config>(TYPES.Config);
const adminService = container.get<AdminService>(TYPES.AdminService);
const propertyService = container.get<PropertyService>(TYPES.PropertyService);
const permissionService = container.get<PermissionService>(TYPES.PermissionService);
const stockReportFolderService = container.get<StockReportFolderService>(TYPES.StockReportFolderService);
const taskService = container.get<TaskService>(TYPES.TaskService);
const hubService = container.get<HubService>(TYPES.HubService);
const xlsxService = container.get<XLSXService>(TYPES.XLSXService);
const csvService = container.get<CsvService>(TYPES.CsvService);
const jsonService = container.get<JsonService>(TYPES.JsonService);
const classicReportsService = container.get<ClassicReportsService>(TYPES.ClassicReportsService);
const stockTagService = container.get<StockTagService>(TYPES.StockTagService);

export {
  reportService as ReportService,
  config as Config,
  fileDownloaderService as FileDownloaderService,
  tagService as TagService,
  folderService as FolderService,
  scheduleService as ScheduleService,
  meService as MeService,
  datasetService as DatasetService,
  favoriteService as FavoriteService,
  adminService as AdminService,
  propertyService as PropertyService,
  permissionService as PermissionService,
  stockReportFolderService as StockReportFolderService,
  taskService as TaskService,
  hubService as HubService,
  xlsxService as XLSXService,
  csvService as CsvService,
  jsonService as JsonService,
  classicReportsService as ClassicReportsService,
  stockTagService as StockTagService,
};
