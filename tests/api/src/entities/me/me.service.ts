import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

@injectable()
export class MeService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,

  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  async getCurrentUser(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.ME}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.get(url, undefined, headers);
    return response;
  }

  async getCurrentUserProperties(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.ME_PROPERTIES}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.get(url, undefined, headers);
    return response;
  }

  async getCurrentUserPolicies(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.ME_POLICIES}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.get(url, undefined, headers);
    return response;
  }

  async getCurrentUserPermissions(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.ME_PERMISSIONS}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.get(url, undefined, headers);
    return response;
  }
}
