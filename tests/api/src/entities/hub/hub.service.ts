import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { Hub } from './hub.model';
import { Card } from './card.model';

@injectable()
export class HubService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  async createHub(title: string, description: string, settings: any, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.HUBS}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const hub = new Hub(
      title,
      description,
      settings,
    );
    const response = await this.post(url, hub, undefined, headers);
    return response;
  }

  async getHubs(propertyId: string, filters?: any, sort?: any) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.HUBS}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const params = {
      ...filters,
      ...sort,
    };

    const response = await this.get(url, params, headers);
    return response;
  }

  async searchHubs(propertyId: string, filters?: any, sort?: any) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.HUBS_SEARCH}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const params = {
      ...filters,
      ...sort,
    };

    const response = await this.get(url, params, headers);
    return response;
  }

  async getHub(hubId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.HUB_BY_ID}`,
      replacements: { ':hubId': hubId },
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.get(url, undefined, headers);
    return response;
  }

  async updateHub(hubId: number, title: string, description: string, settings: any, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.HUB_BY_ID}`,
      replacements: {
        ':hubId': hubId,
      },
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const hub = new Hub(
      title,
      description,
      settings,
    );
    const response = await this.put(url, hub, undefined, headers);
    return response;
  }

  async deleteHub(hubId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.HUB_BY_ID}`,
      replacements: { ':hubId': hubId },
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.delete(url, undefined, headers);
    return response;
  }

  async createCard(hubId: number, chartId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CARDS}`,
      replacements: { ':hubId': hubId },
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const card = new Card(
      chartId,
    );
    const response = await this.post(url, card, undefined, headers);
    return response;
  }

  async getCard(hubId: number, cardId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CARD_BY_ID}`,
      replacements: { ':hubId': hubId, ':cardId': cardId },
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.get(url, undefined, headers);
    return response;
  }

  async updateCard(hubId: number, cardId: number, chartId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CARD_BY_ID}`,
      replacements: { ':hubId': hubId, ':cardId': cardId },
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const card = new Card(
      chartId,
    );

    const response = await this.put(url, card, undefined, headers);
    return response;
  }

  async deleteCard(hubId: number, cardId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CARD_BY_ID}`,
      replacements: { ':hubId': hubId, ':cardId': cardId },
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const response = await this.delete(url, undefined, headers);
    return response;
  }
}
