import <PERSON> from 'papa<PERSON><PERSON>';
import axios from 'axios';
import { injectable } from 'inversify'; // Import injectable decorator

@injectable()
export class CsvService {
  async readCsvFromUrl(url: string): Promise<any> {
    try {
      // Fetching the csv file from the url
      const response = await axios.get(url);

      // Parsing the csv file
      const results = await Papa.parse(response.data.trim(), {
        header: true,
      });
      return results;
    } catch (error) {
      console.error('Error reading the CSV file:', error);
    }
  }
}
