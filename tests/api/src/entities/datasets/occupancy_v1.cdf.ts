export enum OccupancyV1CDF {
  ORGANIZATION_ID = 'organization_id',
  PROPERTY_ID = 'property_id',
  ROOM_TYPE_ID = 'room_type_id',
  ROOM_TYPE = 'room_type',
  STAY_DATE = 'stay_date',
  BOOKING_ID = 'booking_id',
  RESERVATION_NUMBER = 'reservation_number',
  BOOKING_ROOM_ID = 'booking_room_id',
  ROOM_RATE = 'room_rate',
  ADDITIONAL_ROOM_REVENUE = 'additional_room_revenue',
  ROOM_RATE_CONVERTED_RATE = 'room_rate_converted_rate',
  ADDITIONAL_ROOM_REVENUE_CONVERTED_RATE = 'additional_room_revenue_converted_rate',
  ROOM_REVENUE = 'room_revenue',
  ROOM_REVENUE_CONVERTED_RATE = 'room_revenue_converted_rate',
  ADULTS_COUNT = 'adults_count',
  CHILDREN_COUNT = 'children_count',
  ROOM_GUEST_COUNT = 'room_guest_count',
  ROOMS_SOLD = 'rooms_sold',
  CAPACITY_COUNT = 'capacity_count',
  ADR = 'adr',
  ADR_CONVERTED_RATE = 'adr_converted_rate',
  REVPAR = 'revpar',
  REVPAR_CONVERTED_RATE = 'revpar_converted_rate',
  OCCUPANCY = 'occupancy',
  OCCUPANCY_CONVERTED_RATE = 'occupancy_converted_rate',
  MFD_OCCUPANCY = 'mfd_occupancy',
  MFD_OCCUPANCY_CONVERTED_RATE = 'mfd_occupancy_converted_rate',
  ROOM_TAXES = 'room_taxes',
  ROOM_FEES = 'room_fees',
  MISC_INCOME = 'misc_income',
  MISC_INCOME_CONVERTED_RATE = 'misc_income_converted_rate',
  TOTAL_REVENUE = 'total_revenue',
  TOTAL_REVENUE_CONVERTED_RATE = 'total_revenue_converted_rate',
  TOTAL_ROOM_REVENUE_ADJUSTMENTS = 'total_room_revenue_adjustments',
  TOTAL_ROOM_REVENUE_ADJUSTMENTS_CONVERTED_RATE = 'total_room_revenue_adjustments_converted_rate',
  NON_ROOM_REVENUE = 'non_room_revenue',
  NON_ROOM_REVENUE_CONVERTED_RATE = 'non_room_revenue_converted_rate',
  OTHER_REVENUE_ADJUSTMENTS = 'other_revenue_adjustments',
  OTHER_REVENUE_ADJUSTMENTS_CONVERTED_RATE = 'other_revenue_adjustments_converted_rate',
  TOTAL_OTHER_REVENUE = 'total_other_revenue',
  TOTAL_OTHER_REVENUE_CONVERTED_RATE = 'total_other_revenue_converted_rate',
  PROPERTY_NAME = 'property_name',
  CONVERSION_RATE = 'conversion_rate',
  HISTORICAL_EXCHANGE_RATES = 'historical_exchange_rates',
  ROOMS_AVAILABLE = 'rooms_available',
  ROOM_ID = 'room_id',
  ROOM_NUMBER = 'room_number',
  ROOM_TYPE_CATEGORY = 'room_type_category',
  ACCOMMODATION_KIND = 'accommodation_kind',
  ALLOTMENT_BLOCKED_ROOM_COUNT = 'allotment_blocked_room_count',
  ASSUMED_ASSIGNMENT_FLAG = 'assumed_assignment_flag',
  ORIGINAL_CURRENCY = 'original_currency',
  OUT_OF_SERVICE_COUNT = 'out_of_service_count',
  BLOCKED_ROOM_COUNT = 'blocked_room_count',
}
