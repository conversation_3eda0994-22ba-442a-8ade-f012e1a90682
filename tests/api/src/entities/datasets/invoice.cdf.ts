export enum InvoiceCDF {
  ID = 'id',
  PROPERTY_ID = 'property_id',
  RESERVATION_NUMBER = 'reservation_number',
  PROPERTY_NAME = 'property_name',
  BILL_FROM_NAME = 'bill_from_name',
  BILL_FROM_ADDRESS_LINE_1 = 'bill_from_address_line_1',
  BILL_FROM_ADDRESS_LINE_2 = 'bill_from_address_line_2',
  BILL_FROM_CITY = 'bill_from_city',
  BILL_FROM_STATE = 'bill_from_state',
  BILL_FROM_COUNTRY_CODE = 'bill_from_country_code',
  BILL_FROM_ZIP = 'bill_from_zip',
  BILL_FROM_HOTEL_PHONE = 'bill_from_hotel_phone',
  BILL_FROM_HOTEL_EMAIL = 'bill_from_hotel_email',
  BILL_TO_PARTY = 'bill_to_party',
  BILL_TO_ADDRESS_LINE_1 = 'bill_to_address_line_1',
  BILL_TO_ADDRESS_LINE_2 = 'bill_to_address_line_2',
  BILL_TO_CITY = 'bill_to_city',
  BILL_TO_COUNTRY_CODE = 'bill_to_country_code',
  BILL_TO_STATE = 'bill_to_state',
  BILL_TO_POSTAL_CODE = 'bill_to_postal_code',
  RESERVATION_CHECKIN_DATE = 'reservation_checkin_date',
  RESERVATION_CHECKOUT_DATE = 'reservation_checkout_date',
  RESERVATION_ROOM_NIGHTS_COUNT = 'reservation_room_nights_count',
  RESERVATION_DATETIME = 'reservation_datetime',
  INVOICE_LANGUAGE_CODE = 'invoice_language_code',
  INVOICE_STATUS = 'invoice_status',
  INVOICE_NUMBER = 'invoice_number',
  INVOICE_TYPE = 'invoice_type',
  INVOICE_GENERATE_DATETIME = 'invoice_generate_datetime',
  CREDIT_REASON = 'credit_reason',
  INVOICE_CURRENCY_CODE = 'invoice_currency_code',
  TOTAL_GROSS_AMOUNT = 'total_gross_amount',
  TAXES_VALUE_AMOUNT = 'taxes_value_amount',
  BALANCE_DUE_AMOUNT = 'balance_due_amount',
  BILL_FROM_LEGAL_NAME = 'bill_from_legal_name',
  BILL_FROM_TAX_ID_1 = 'bill_from_tax_id_1',
  BILL_FROM_TAX_ID_2 = 'bill_from_tax_id_2',
  BILL_FROM_CNJP = 'bill_from_cnjp',
  BILL_TO_ID = 'bill_to_id',
  BILL_TO_TAX_ID = 'bill_to_tax_id',
  PAYMENT_DUE_DATE = 'payment_due_date',
  ORIGINAL_INVOICE_NUMBER = 'original_invoice_number',
  ORIGINAL_INVOICE_DATE = 'original_invoice_date',
  INVOICE_AGE_PAYMENT_DUE_DATE = 'invoice_age_payment_due_date',
  INVOICE_AGE_INVOICE_DATE = 'invoice_age_invoice_date'
}
