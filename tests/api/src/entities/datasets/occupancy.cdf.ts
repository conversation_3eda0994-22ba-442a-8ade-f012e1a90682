export enum OccupancyCDF {
  ID = 'id',
  PROPERTY_ID = 'property_id',
  PROPERTY_NAME = 'property_name',
  ROOM_TYPE = 'room_type',
  ROOM_TYPE_ABBREVIATION = 'room_type_abbreviation',
  ROOM_NUMBER = 'room_number',
  BEDS_PER_ROOM = 'beds_per_room',
  BED_BASED_CAPACITY = 'bed_based_capacity',
  BLOCKED_ROOM_DURATION = 'blocked_room_duration',
  BLOCKED_ROOM_TYPE_A = 'blocked_room_type_a',
  OUT_OF_SERVICE_DURATION = 'out_of_service_duration',
  ROOM_AVAILABLE_TYPE_A = 'room_available_type_a',
  CAPACITY_TYPE_A = 'capacity_type_a',
  OUT_OF_SERVICE_TYPE_A = 'out_of_service_type_a',
  BOOKING_QTY_TYPE_A = 'booking_qty_type_a',
  BOOKING_DATE = 'booking_date',
  RESERVATION_SOURCE = 'reservation_source',
  RESERVATION_SOURCE_CATEGORY = 'reservation_source_category',
  ROOM_REVENUE_AMOUNT = 'room_revenue_amount',
  STAY_DATE = 'stay_date',
  ROOM_RATE_AMOUNT = 'room_rate_amount',
  GUEST_COUNT = 'guest_count',
  REVPAR = 'revpar',
  OCCUPANCY = 'occupancy',
  ADR = 'adr',
  ROOM_ID = 'room_id',
  BOOKING_ROOM_ID = 'booking_room_id',
  ROOM_DESCRIPTION = 'room_description',
  ROOM_RATE_ADDITIONAL_ADULTS_AMOUNT = 'room_rate_additional_adults_amount',
  ROOM_RATE_ADDITIONAL_KIDS_AMOUNT = 'room_rate_additional_kids_amount',
  ROOM_RATE_BASE_AMOUNT = 'room_rate_base_amount',
  ROOM_TYPE_ID = 'room_type_id',
  ADULTS_COUNT = 'adults_count',
  CHILDREN_COUNT = 'children_count',

}
