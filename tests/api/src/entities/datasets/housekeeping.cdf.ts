export enum HousekeepingCDF {
  ID = 'id',
  ORGANIZATION_ID = 'organization_id',
  PROPERTY_ID = 'property_id',
  PROPERTY_NAME = 'property_name',
  STAY_DATE = 'stay_date',
  ROOM_ID = 'room_id',
  ROOM_NAME = 'room_name',
  ROOM_TYPE_ID = 'room_type_id',
  ROOM_TYPE = 'room_type',
  ROOM_TYPE_ABBREVIATION = 'room_type_abbreviation',
  ESTIMATED_ARRIVAL_TIME = 'estimated_arrival_time',
  ROOM_STATUS = 'room_status',
  ROOM_COUNT = 'room_count',
  ROOM_CONDITION = 'room_condition',
  HOUSEKEEPER_ASSIGNED = 'housekeeper_assigned',
  DO_NOT_DISTURB = 'do_not_disturb',
  ACCOMMODATION_COMMENTS = 'accommodation_comments',
  ROOM_CONDITION_LAST_UPDATED_DATETIME = 'room_condition_last_updated_datetime',
  ROOM_CONDITION_LAST_UPDATED_DATETIME_PROPERTY_TIMEZONE = 'room_condition_last_updated_datetime_property_timezone',
  FRONTDESK_STATUS = 'frontdesk_status',
  ROOM_TYPE_CATEGORY = 'room_type_category',
}
