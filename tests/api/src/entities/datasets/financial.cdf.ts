export enum FinancialCDF {
  ID = 'id',
  ADDON_CHARGE_TYPE = 'addon_charge_type',
  ADDON_ITEM = 'addon_item',
  BALANCE_DUE_AMOUNT = 'balance_due_amount',
  BOOKING_DATETIME = 'booking_datetime',
  BOOKING_DATETIME_PROPERTY_TIMEZONE = 'booking_datetime_property_timezone',
  CARD_LAST_4_DIGITS = 'card_last_4_digits',
  CARD_TYPE = 'card_type',
  CHECKIN_DATE = 'checkin_date',
  CHECKOUT_DATE = 'checkout_date',
  CREDIT_AMOUNT = 'credit_amount',
  DEBIT_AMOUNT = 'debit_amount',
  FEE_TYPE = 'fee_type',
  GROUP_PROFILE_NAME = 'group_profile_name',
  GROUP_PROFILE_TYPE = 'group_profile_type',
  INVOICE_CREATED_DATETIME = 'invoice_created_datetime',
  INVOICE_CREATED_DATETIME_PROPERTY_TIMEZONE = 'invoice_created_datetime_property_timezone',
  INVOICE_GUEST_NAME = 'invoice_guest_name',
  IS_HOTEL_COLLECT_BOOKING = 'is_hotel_collect_booking',
  IS_REFUND = 'is_refund',
  IS_TRANSACTION_ADJUSTED = 'is_transaction_adjusted',
  IS_TRANSACTION_DELETED = 'is_transaction_deleted',
  IS_VOID = 'is_void',
  ITEM_SERVICE_CATEGORY = 'item_service_category',
  ITEM_SERVICE_TYPE = 'item_service_type',
  LATEST_INVOICE_NUMBER = 'latest_invoice_number',
  PAYMENT_METHOD = 'payment_method',
  POS_CHARGE_CATEGORY = 'pos_charge_category',
  POS_CHARGE_DESCRIPTION = 'pos_charge_description',
  PRIMARY_GUEST_FIRST_NAME = 'primary_guest_first_name',
  PRIMARY_GUEST_FULL_NAME = 'primary_guest_full_name',
  PRIMARY_GUEST_SURNAME = 'primary_guest_surname',
  PRIVATE_RATE_PLAN = 'private_rate_plan',
  PROPERTY_ID = 'property_id',
  PROPERTY_NAME = 'property_name',
  PUBLIC_RATE_PLAN = 'public_rate_plan',
  QUANTITY_AMOUNT = 'quantity_amount',
  RESERVATION_NUMBER = 'reservation_number',
  RESERVATION_SOURCE = 'reservation_source',
  RESERVATION_SOURCE_CATEGORY = 'reservation_source_category',
  RESERVATION_STATUS = 'reservation_status',
  ROOM_NUMBER = 'room_number',
  ROOM_REVENUE_TYPE = 'room_revenue_type',
  ROOM_TYPE = 'room_type',
  TAX_TYPE = 'tax_type',
  TRANSACTION_CODE = 'transaction_code',
  TRANSACTION_DATETIME = 'transaction_datetime',
  TRANSACTION_DATETIME_PROPERTY_TIMEZONE = 'transaction_datetime_property_timezone',
  TRANSACTION_NOTES = 'transaction_notes',
  TRANSACTION_STATUS = 'transaction_status',
  TRANSACTION_TYPE = 'transaction_type',
  USER = 'user',
  INTERNAL_TRANSACTION_CODE_DESCRIPTION = 'internal_transaction_code_description',
  INTERNAL_TRANSACTION_CODE = 'internal_transaction_code',
  GENERAL_LEDGER_CODE = 'general_ledger_code',
  GENERAL_LEDGER_ACCOUNT = 'general_ledger_account',
  CUSTOM_CODE = 'custom_code',
  RES_ROOM_IDENTIFIER = 'res_room_identifier',
  HOUSE_ACCOUNT_NAME = 'house_account_name',
  // PAYMENT_ID = 'payment_id', // Commented for now since is behind FF
  CHANNEL_RATE_PLAN = 'channel_rate_plan',
  ITEM_TYPE = 'item_type',
  MEAL_PLAN = 'meal_plan',
  ROOM_TYPE_CATEGORY = 'room_type_category',
  SERVICE_DATE = 'service_date',
  CREDIT_AMOUNT_CONVERTED_RATE = 'credit_amount_converted_rate',
  DEBIT_AMOUNT_CONVERTED_RATE = 'debit_amount_converted_rate',
  BALANCE_DUE_AMOUNT_CONVERTED_RATE = 'balance_due_amount_converted_rate',
  CONVERSION_RATE = 'conversion_rate',
  CURRENCY = 'currency'
}
