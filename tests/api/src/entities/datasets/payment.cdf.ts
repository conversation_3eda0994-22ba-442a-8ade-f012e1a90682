export enum PaymentCDF {
  ID = 'id',
  CARD_ISSUE_COUNTRY = 'card_issue_country',
  CARD_LAST_4_DIGITS = 'card_last_4_digits',
  CARD_TYPE = 'card_type',
  CHECKIN_DATE = 'checkin_date',
  CHECKOUT_DATE = 'checkout_date',
  PAYMENT_FEE_AMOUNT = 'payment_fee_amount',
  PAYMENT_GATEWAY = 'payment_gateway',
  PAYMENT_GATEWAY_RESULT = 'payment_gateway_result',
  PAYMENT_GATEWAY_STATUS = 'payment_gateway_status',
  PAYMENT_GATEWAY_TRANSACTION_ID = 'payment_gateway_transaction_id',
  PAYMENT_METHOD = 'payment_method',
  PAYMENT_TYPE = 'payment_type',
  PAYMENT_NET_AMOUNT = 'payment_net_amount',
  PAYMENT_SCHEDULE_DATETIME = 'payment_schedule_datetime',
  PAYMENT_SUBMITTED_AMOUNT = 'payment_submitted_amount',
  POS_ENTRY_MODE = 'pos_entry_mode',
  PRIMARY_GUEST_FULL_NAME = 'primary_guest_full_name',
  PROPERTY_ID = 'property_id',
  PROPERTY_NAME = 'property_name',
  RESERVATION_NUMBER = 'reservation_number',
  TERMINAL_LABEL = 'terminal_label',
  REFUNDED_AMOUNT = 'refunded_amount',
  CAPTURED_AMOUNT = 'captured_amount',
  PAYOUT_ID = 'payout_id',
  PAYOUT_DATE = 'payout_date',
  PAYOUT_STATUS = 'payout_status',
  PAYOUT_NET_AMOUNT = 'payout_net_amount',
  PAYOUT_FEE_AMOUNT = 'payout_fee_amount',
  PAYMENT_SCHEDULE_DATETIME_PROPERTY_TIMEZONE = 'payment_schedule_datetime_property_timezone',
  ORGANIZATION_ID = 'organization_id',
  PAYMENT_ENTRY_TYPE = 'payment_entry_type',
  CLOUDBEDS_PAYMENT_FLAG = 'cloudbeds_payment_flag',
  CARD_CREATED_DATETIME = 'card_created_datetime',
  GROUP_PROFILE_NAME = 'group_profile_name',
  HOUSE_ACCOUNT_NAME = 'house_account_name',
  PAYMENT_FEE_ADJUSTMENT = 'payment_fee_adjustment',
  PAYMENT_TOTAL_FEE = 'payment_total_fee',
  INVENTORY_OBJECT_ID = 'inventory_object_id',
  INVENTORY_OBJECT_TYPE = 'inventory_object_type',
}
