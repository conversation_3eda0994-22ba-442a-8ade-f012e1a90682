export enum ReservationCDF {
  ID = 'id',
  ADULTS_COUNT = 'adults_count',
  BOOKING_DATETIME = 'booking_datetime',
  BOOKING_DATETIME_PROPERTY_TIMEZONE = 'booking_datetime_property_timezone',
  CANCELLATION_DATETIME = 'cancellation_datetime',
  CANCELLATION_DATETIME_PROPERTY_TIMEZONE = 'cancellation_datetime_property_timezone',
  CHECKIN_DATE = 'checkin_date',
  CHECKOUT_DATE = 'checkout_date',
  CHILDREN_COUNT = 'children_count',
  COMMISSION_AMOUNT = 'commission_amount',
  GUEST_COUNT = 'guest_count',
  ROOM_COUNT = 'room_count',
  RESERVATION_PAID_AMOUNT = 'reservation_paid_amount',
  RESERVATION_BALANCE_DUE_AMOUNT = 'reservation_balance_due_amount',
  RESERVATION_NUMBER = 'reservation_number',
  RESERVATION_STATUS = 'reservation_status',
  ROOM_NIGHTS_COUNT = 'room_nights_count',
  THIRD_PARTY_CONFIRMATION_NUMBER = 'third_party_confirmation_number',
  TAXES_VALUE_AMOUNT = 'taxes_value_amount',
  IS_HOTEL_COLLECT_BOOKING = 'is_hotel_collect_booking',
  CARD_TYPE = 'card_type',
  ESTIMATED_ARRIVAL_TIME = 'estimated_arrival_time',
  DEPOSIT_AMOUNT = 'deposit_amount',
  GRAND_TOTAL_AMOUNT = 'grand_total_amount',
  CARD_LAST_4_DIGITS = 'card_last_4_digits',
  PRIMARY_GUEST_POSTAL_CODE = 'primary_guest_postal_code',
  PRIMARY_GUEST_ADDRESS = 'primary_guest_address',
  PRIMARY_GUEST_ADDRESS_LINE_2 = 'primary_guest_address_line_2',
  PRIMARY_GUEST_MOBILE_PHONE_NUMBER = 'primary_guest_mobile_phone_number',
  PRIMARY_GUEST_CITY = 'primary_guest_city',
  PRIMARY_GUEST_RESIDENCE_COUNTRY_CODE = 'primary_guest_residence_country_code',
  PRIMARY_GUEST_RESIDENCE_COUNTRY = 'primary_guest_residence_country',
  PRIMARY_GUEST_ID = 'primary_guest_id',
  PRIMARY_GUEST_FULL_NAME = 'primary_guest_full_name',
  PRIMARY_GUEST_BIRTH_DATE = 'primary_guest_birth_date',
  PRIMARY_GUEST_DOCUMENT_EXPIRATION_DATE = 'primary_guest_document_expiration_date',
  PRIMARY_GUEST_DOCUMENT_ISSUE_DATE = 'primary_guest_document_issue_date',
  PRIMARY_GUEST_DOCUMENT_ISSUING_COUNTRY_CODE = 'primary_guest_document_issuing_country_code',
  PRIMARY_GUEST_DOCUMENT_ISSUING_COUNTRY = 'primary_guest_document_issuing_country',
  PRIMARY_GUEST_DOCUMENT_NUMBER = 'primary_guest_document_number',
  PRIMARY_GUEST_DOCUMENT_TYPE = 'primary_guest_document_type',
  PRIMARY_GUEST_EMAIL = 'primary_guest_email',
  PRIMARY_GUEST_GENDER = 'primary_guest_gender',
  PRIMARY_GUEST_FIRST_NAME = 'primary_guest_first_name',
  PRIMARY_GUEST_SURNAME = 'primary_guest_surname',
  PRIMARY_GUEST_PHONE_NUMBER = 'primary_guest_phone_number',
  PRIMARY_GUEST_STATE = 'primary_guest_state',
  PRIMARY_GUEST_STATUS_LEVEL = 'primary_guest_status_level',
  IS_OPT_IN_MARKETING_EMAILS = 'is_opt_in_marketing_emails',
  PROPERTY_ID = 'property_id',
  PROPERTY_NAME = 'property_name',
  ACTIVE_BOOKING_NOTES = 'active_booking_notes',
  ROOM_REVENUE_TOTAL_AMOUNT = 'room_revenue_total_amount',
  ACTIVE_GROUP_PROFILE_NOTES = 'active_group_profile_notes',
  ACTIVE_PRIMARY_GUEST_NOTES = 'active_primary_guest_notes',
  GROUP_ALLOTMENT_CODE = 'group_allotment_code',
  GROUP_ALLOTMENT_NAME = 'group_allotment_name',
  GROUP_PROFILE_CODE = 'group_profile_code',
  GROUP_PROFILE_NAME = 'group_profile_name',
  GROUP_PROFILE_TYPE = 'group_profile_type',
  PUBLIC_RATE_PLAN = 'public_rate_plan',
  PRIVATE_RATE_PLAN = 'private_rate_plan',
  RESERVATION_SOURCE_CATEGORY = 'reservation_source_category',
  RESERVATION_SOURCE = 'reservation_source',
  IS_REPEAT_GUEST = 'is_repeat_guest',
  ROOM_TYPES = 'room_types',
  ROOM_NUMBERS = 'room_numbers',
  ROOM_RESERVATION_NUMBER = 'room_reservation_number',
  RESERVATION_SPECIAL_REQUESTS = 'reservation_special_requests',
  BOOKING_WINDOW = 'booking_window',
  ALLOTMENT_BLOCK_NOTES = 'allotment_block_notes',
  BOOKING_ORIGIN = 'booking_origin',
  CHANNEL_RATE_PLAN = 'channel_rate_plan',
  IS_MEAL_PLAN_INCLUDED = 'is_meal_plan_included',
}
