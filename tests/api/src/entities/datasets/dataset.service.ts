import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { Dataset } from './dataset.type';

@injectable()
export class DatasetService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  /**
 * Method that is added to first trigger the authentication, to allow using Promise All
 * Right now if multiple request hits MFD API it breaks, so is a workaround to improve test performance
 */
  authenticateService() {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
  }

  async getDatasets(propertyId: string, acceptLanguage = 'en') {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASETS}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
      'Accept-Language': acceptLanguage,
    };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getDatasetById(datasetId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASETS_BY_ID}`,
      replacements: {
        ':datasetId': datasetId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getDatasetByIdMultiLevel(dataset: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASET_BY_ID_MULTILEVELS}`,
      replacements: {
        ':datasetId': dataset,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getDatasetByIdMultiLevelById(dataset: number, multi_level: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASET_BY_ID_MULTILEVEL_BY_ID}`,
      replacements: {
        ':datasetId': dataset,
        ':multiLevelId': multi_level,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getDatasetByIdMultiLevelByIdCdfByColumn(dataset: number, multi_level: number, column: string, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASET_BY_ID_MULTILEVEL_BY_ID_CDF_BY_COLUMN}`,
      replacements: {
        ':datasetId': dataset,
        ':multiLevelId': multi_level,
        ':column': column,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getDatasetByIdCdfByName(dataset: Dataset, cdf: string, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASETS_BY_ID_CDF}`,
      replacements: {
        ':datasetId': dataset,
        ':cdf': cdf,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getDatasetFreshnessById(datasetId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASETS_BY_ID_FRESHNESS}`,
      replacements: {
        ':datasetId': datasetId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getPropertyCustomFields(datasetId: number, propertyId: string, propertyIds?: string[]) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASET_BY_ID_PROPERTY_CUSTOM_FIELDS}`,
      replacements: {
        ':datasetId': datasetId,
      },
    };
    const params = {
      ...(propertyIds && { property_ids: propertyIds }),
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getDatasetByIdCdfByNamePicklistValues(dataset: Dataset, cdf: string, propertyId: string, json: any, query: any = {}) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.DATASETS_BY_ID_CDF}/picklist`,
      replacements: {
        ':datasetId': dataset,
        ':cdf': cdf,
      },
    };
    const params = {
      ...query,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, json, params, headers);
    return response;
  }
}
