export enum AccountingCDF {
  ID = 'id',
  ORGANIZATION_ID = 'organization_id',
  PROPERTY_ID = 'property_id',
  SOURCE_DATETIME = 'source_datetime',
  TRANSACTION_DATETIME = 'transaction_datetime',
  SERVICE_DATE = 'service_date',
  INTERNAL_CODE = 'internal_code',
  INTERNAL_CODE_DESCRIPTION = 'internal_code_description',
  GENERAL_LEDGER_CODE = 'general_ledger_code',
  GENERAL_LEDGER_ACCOUNT = 'general_ledger_account',
  CUSTOM_CODE = 'custom_code',
  AMOUNT = 'amount',
  CURRENCY = 'currency',
  CURRENTY_SCALE = 'currency_scale',
  ROOT_ID = 'root_id',
  PARENT_ID = 'parent_id',
  SOURCE_ID = 'source_id',
  SUB_SOURCE_ID = 'sub_source_id',
  SOURCE_KIND = 'source_kind',
  EXTERNAL_RELATION_ID = 'external_relation_id',
  EXTERNAL_RELATION_KIND = 'external_relation_kind',
  ORIGIN_ID = 'origin_id',
  ROUTED_FROM = 'routed_from',
  QUANTITY = 'quantity',
  DESCRIPTION = 'description',
  NOTES = 'notes',
  USER_ID = 'user_id',
  CREATED_AT = 'created_at',
  STATE = 'state',
  STATUS = 'status',
  UPDATED_AT = 'updated_at',
  ROUTED_SOURCE_ID = 'routed_source_id',
  ROUTED_SOURCE_KIND = 'routed_source_kind',
}
