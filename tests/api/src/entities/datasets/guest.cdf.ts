export enum GuestCDF {
  ID = 'id',
  BOOKING_DATETIME = 'booking_datetime',
  BOOKING_DATETIME_PROPERTY_TIMEZONE = 'booking_datetime_property_timezone',
  CANCELLATION_DATETIME = 'cancellation_datetime',
  CANCELLATION_DATETIME_PROPERTY_TIMEZONE = 'cancellation_datetime_property_timezone',
  CHECKIN_DATE = 'checkin_date',
  CHECKOUT_DATE = 'checkout_date',
  GUEST_ADDRESS = 'guest_address',
  GUEST_ADDRESS_LINE_2 = 'guest_address_line_2',
  GUEST_BIRTH_DATE = 'guest_birth_date',
  GUEST_CITY = 'guest_city',
  GUEST_DOCUMENT_EXPIRATION_DATE = 'guest_document_expiration_date',
  GUEST_DOCUMENT_ISSUE_DATE = 'guest_document_issue_date',
  GUEST_DOCUMENT_ISSUING_COUNTRY = 'guest_document_issuing_country',
  GUEST_DOCUMENT_ISSUING_COUNTRY_CODE = 'guest_document_issuing_country_code',
  GUEST_DOCUMENT_NUMBER = 'guest_document_number',
  GUEST_DOCUMENT_TYPE = 'guest_document_type',
  GUEST_EMAIL = 'guest_email',
  GUEST_FIRST_NAME = 'guest_first_name',
  GUEST_FULL_NAME = 'guest_full_name',
  GUEST_GENDER = 'guest_gender',
  GUEST_MOBILE_PHONE_NUMBER = 'guest_mobile_phone_number',
  GUEST_PHONE_NUMBER = 'guest_phone_number',
  GUEST_POSTAL_CODE = 'guest_postal_code',
  GUEST_RESIDENCE_COUNTRY = 'guest_residence_country',
  GUEST_RESIDENCE_COUNTRY_CODE = 'guest_residence_country_code',
  GUEST_STATE = 'guest_state',
  GUEST_STATUS_LEVEL = 'guest_status_level',
  GUEST_SURNAME = 'guest_surname',
  IS_OPT_IN_MARKETING_EMAILS = 'is_opt_in_marketing_emails',
  IS_PRIMARY_GUEST = 'is_primary_guest',
  IS_REPEAT_GUEST = 'is_repeat_guest',
  PROPERTY_ID = 'property_id',
  PROPERTY_NAME = 'property_name',
  RESERVATION_NUMBER = 'reservation_number',
  RESERVATION_STATUS = 'reservation_status',
  THIRD_PARTY_CONFIRMATION_NUMBER = 'third_party_confirmation_number',
  PROPERTY_CHECKIN_TIME = 'property_checkin_time',
  PROPERTY_CHECKOUT_TIME = 'property_checkout_time',
  DURATION_OF_STAY = 'duration_of_stay',
  ROOM_NUMBERS = 'room_numbers',
  GROUP_PROFILE_NAME = 'group_profile_name',
  RESERVATION_SOURCE_CATEGORY = 'reservation_source_category',
  RESERVATION_SOURCE = 'reservation_source'
}
