import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { StockReportFolder } from './stockReportFolder.model';

@injectable()
export class StockReportFolderService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _endpoint = '/stock_reports/folders';
  private _path = `${this._apiVersion}${this._endpoint}`;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._authenticateService = authenticateService;
  }

  async createFolder(name: string, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: this._path,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, new StockReportFolder(name), null, headers);
    return response;
  }

  async getFolders(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: this._path,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async deleteFolder(folderId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._path}/${folderId}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async getFolderById(folderId: string, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._path}/${folderId}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }
}
