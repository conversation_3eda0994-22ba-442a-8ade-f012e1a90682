import jwt from 'jsonwebtoken';
import { DateTime } from 'luxon';
import { injectable } from 'inversify'; // Import injectable decorator

@injectable()
export class TokenService {
  private static readonly tokenSecret: string = process.env.DI_TOKEN_SECRET || '';
  private static readonly expirationDays: number = parseInt(process.env.DI_TOKEN_EXPIRATION_DAYS || '7', 10);

  public static mintToken(sub: string, extra: { [key: string]: any } | null = null): string {
    const expirationInSeconds = DateTime.utc().plus({ days: TokenService.expirationDays }).toSeconds();
    const message: { [key: string]: any } = {
      sub,
      exp: expirationInSeconds,
    };
    if (extra) {
      Object.assign(message, extra);
    }
    return jwt.sign(
      message,

      TokenService.tokenSecret,
      { algorithm: 'HS256' },
    );
  }
}
