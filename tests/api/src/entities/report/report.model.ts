import { Dataset } from '../datasets/dataset.type';

import { ReportSettings } from './settings/reportSettings';
import { Filters } from './filters/filters';
import { Sort } from './sort/sort.model';
import { Column } from './columns/column.model';
import { GroupRow } from './groupRows/groupRow.model';
import { GroupColumn } from './groupColumn/groupColumn.model';
import { Period } from './periods/period.model';
import { ReportFormats } from './formats/reportFormats';
import { Comparison } from './comparisons/comparison.model';
import { CustomFieldCdf } from './customFieldCdf/customFieldCdf.model';

export class Report {
  title: string;
  description: string;
  property_id: string;
  property_ids: string[];
  dataset_id: Dataset;
  columns: Column[];
  group_rows: GroupRow[] | null;
  group_columns: GroupColumn[] | null;
  filters: Filters | null;
  sort: Sort[] | null;
  settings: ReportSettings;
  periods: Period[] | null;
  formats: ReportFormats | null;
  comparisons: Comparison[] | null;
  custom_field_cdfs?: CustomFieldCdf[];

  constructor(
    title: string,
    description: string,
    propertyId: string,
    propertyIds: string[],
    datasetId: Dataset,
    columns: Column[],
    groupRows: GroupRow[] | null = null,
    groupColumns: GroupColumn[] | null = null,
    settings: ReportSettings | null = null,
    sort: Sort[] | null = null,
    filters: Filters | null = null,
    periods: Period[] | null = null,
    formats: ReportFormats | null = null,
    comparisons: Comparison[] | null = null,
    customFieldCdfs: CustomFieldCdf[] | [] = [],
  ) {
    this.title = title;
    this.description = description;
    this.property_id = propertyId;
    this.property_ids = propertyIds;
    this.dataset_id = datasetId;
    this.columns = columns;
    this.group_rows = groupRows;
    this.group_columns = groupColumns;
    this.settings = settings === null ? { totals: false, details: false } : settings;
    this.sort = sort;
    this.filters = filters;
    this.periods = periods;
    this.formats = formats;
    this.comparisons = comparisons;
    if (customFieldCdfs && customFieldCdfs.length > 0) {
      this.custom_field_cdfs = customFieldCdfs;
    }
  }
}
