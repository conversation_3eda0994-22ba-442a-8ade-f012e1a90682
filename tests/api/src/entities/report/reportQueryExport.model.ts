import { Dataset } from '../datasets/dataset.type';

import { ReportSettings } from './settings/reportSettings';
import { Filters } from './filters/filters';
import { Sort } from './sort/sort.model';
import { Column } from './columns/column.model';
import { GroupRow } from './groupRows/groupRow.model';
import { GroupColumn } from './groupColumn/groupColumn.model';
import { CustomCdf } from './customCdf/customCdf.model';
import { Period } from './periods/period.model';
import { ReportFormats } from './formats/reportFormats';
import { Comparison } from './comparisons/comparison.model';
import { CustomFieldCdf } from './customFieldCdf/customFieldCdf.model';

export class ReportQueryExport {
  title: string;
  property_ids: number[];
  dataset_id: Dataset;
  columns: Column[];
  group_rows: GroupRow[] | null;
  group_columns: GroupColumn[] | null;
  filters: Filters | null;
  sort: Sort[] | null;
  settings: ReportSettings;
  custom_cdfs: CustomCdf[] | null;
  periods: Period[] | null;
  formats: ReportFormats | null;
  comparisons: Comparison[] | null;
  custom_field_cdfs: CustomFieldCdf[] | [];

  constructor(
    title: string,
    propertyIds: number[],
    datasetId: Dataset,
    columns: Column[],
    groupRows: GroupRow[] | null = null,
    groupColumns: GroupColumn[] | null = null,
    settings: ReportSettings | null = null,
    sort: Sort[] | null = null,
    filters: Filters | null = null,
    custom_cdfs: CustomCdf[] | null = null,
    periods: Period[] | null = null,
    formats: ReportFormats | null = null,
    comparisons: Comparison[] | null = null,
    customFieldCdfs: CustomFieldCdf[] | [] = [],
  ) {
    this.title = title;
    this.property_ids = propertyIds;
    this.dataset_id = datasetId;
    this.columns = columns;
    this.group_rows = groupRows;
    this.group_columns = groupColumns;
    this.settings = settings === null ? { totals: false, details: false } : settings;
    this.sort = sort;
    this.filters = filters;
    this.custom_cdfs = custom_cdfs;
    this.periods = periods;
    this.formats = formats;
    this.comparisons = comparisons;
    this.custom_field_cdfs = customFieldCdfs;
  }
}
