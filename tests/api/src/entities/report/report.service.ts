import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';
import { Chart } from '../chart/chart.model';

import { ExportFormatType } from './enums/export.format.type';
import { Report } from './report.model';
import { CustomCdf } from './customCdf/customCdf.model';
import { CustomCdfValidate } from './customCdf/customCdfValidate.model';
import { ExportViewType } from './enums/export.view.type';
import { Mode } from './enums/mode.type';
import { NumericFormat } from './enums/numeric.format.type';
import { ReportQueryData } from './reportQueryData.model';
import { ReportClone } from './reportClone.model';
import { StockReportQuery } from './stockReportQuery.model';
import { ReportQueryExport } from './reportQueryExport.model';
import { StockReportPostBody } from './settings/rules';
import { StockReportQueryExport } from './stockReportQueryExport.model';
import { ReportKind } from './enums/report_kind.type';
import { CustomFieldCdf } from './customFieldCdf/customFieldCdf.model';

@injectable()
export class ReportService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  /**
   * Method that is added to first trigger the authentication, to allow using Promise All
   * Right now if multiple request hits MFD API it breaks, so is a workaround to improve test performance
   */
  authenticateService() {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
  }

  async createReport(report: Report, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORTS}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, report, null, headers);
    return response;
  }

  async updateReport(reportId: number, report: Report, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, report, null, headers);
    return response;
  }

  async cloneReport(
    reportId: number,
    propertyId: string,
    report?: ReportClone,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID}/clone`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, report, null, headers);
    return response;
  }

  async cloneStockReport(
    stockReportId: number,
    propertyId: string,
    report?: ReportClone,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_BY_ID}/clone`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, report, null, headers);
    return response;
  }

  async getReport(reportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const params = {};
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getReports(
    propertyId: string,
    folderIds?: number[],
    query: any = {},
    apiKey?: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORTS}`,
    };
    const headers: any = { 'X-PROPERTY-ID': propertyId };
    const params = {
      folder_ids: folderIds?.join(','),
      ...query,
    };

    if (apiKey) {
      const apiKeyHeaders = {
        'X-API-KEY': apiKey,
        'X-PROPERTY-ID': propertyId,
      };
      const response = await this.get(url, params, apiKeyHeaders);
      return response;
    }

    const response = await this.get(url, params, headers);
    return response;
  }

  async getReportsWithApiKey(
    propertyId: string,
    folderIds?: number[],
    query: any = {},
    apiKey = '',
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORTS}`,
    };
    const params = {
      folder_ids: folderIds?.join(','),
      ...query,
    };

    const apiKeyHeaders = { 'X-API-KEY': apiKey, 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, apiKeyHeaders);
    return response;
  }

  async linkTagToReport(reportId: number, tagId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_TAGS}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const data = {
      id: tagId,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, data, null, headers);
    return response;
  }

  async unlinkTagFromReport(
    reportId: number,
    tagId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_TAGS_BY_ID}`,
      replacements: {
        ':reportId': reportId,
        ':tagId': tagId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async getStockReports(
    propertyId: string,
    query: any = {},
    apiKey?: string,
    headers: any = {},
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}`,
    };
    const params = {
      ...query,
    };

    headers['X-PROPERTY-ID'] = propertyId;

    if (apiKey) {
      const apiKeyHeaders = {
        'X-API-KEY': apiKey,
        'X-PROPERTY-ID': propertyId,
      };
      const response = await this.get(url, params, apiKeyHeaders);
      return response;
    }

    const response = await this.get(url, params, headers);
    return response;
  }

  async getStockReportsWithApiKey(
    propertyId: string,
    query: any = {},
    apiKey = '',
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}`,
    };
    const params = {
      ...query,
    };

    const apiKeyHeaders = { 'X-API-KEY': apiKey, 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, apiKeyHeaders);
    return response;
  }

  async getStockReportById(id: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}/${id}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async getStockReportDataById(
    id: number,
    propertyId: string,
    propertyIds: number[],
    format: NumericFormat = NumericFormat.RAW,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}/${id}/data`,
    };
    const params = {
      property_ids: propertyIds.join(','),
      format,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getStockReportSummaryById(
    id: number,
    propertyId: string,
    propertyIds: number[],
    format: NumericFormat = NumericFormat.RAW,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}/${id}/summary`,
    };
    const params = {
      property_ids: propertyIds.join(','),
      format,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getStockReportExportById(
    id: number,
    propertyId: string,
    propertyIds: number[],
    view: ExportViewType = ExportViewType.DETAILS,
    format: ExportFormatType = ExportFormatType.XLSX,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}/${id}/export`,
    };
    const params = { property_ids: propertyIds.join(','), format, view };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async deleteReport(reportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async validateCustomCdf(
    reportId: number,
    propertyId: string,
    customCdf: CustomCdfValidate,
    reportKind: ReportKind = ReportKind.Report,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_VALIDATE_CUSTOM_CDFS}`,
      replacements: {
        ':reportKind': `${reportKind}s`,
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, customCdf, null, headers);
    return response;
  }

  async getCustomCdf(
    reportId: number,
    propertyId: string,
    customCdfId: number,
    reportKind: ReportKind = ReportKind.Report,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_CDF_BY_ID}`,
      replacements: {
        ':reportKind': `${reportKind}s`,
        ':reportId': reportId,
        ':customCdfId': customCdfId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async getCustomCdfs(
    reportId: number,
    propertyId: string,
    reportKind: ReportKind = ReportKind.Report,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_CDFS}`,
      replacements: {
        ':reportKind': `${reportKind}s`,
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async createCustomCdf(
    reportId: number,
    propertyId: string,
    customCdf: CustomCdf,
    reportKind: ReportKind = ReportKind.Report,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_CDFS}`,
      replacements: {
        ':reportKind': `${reportKind}s`,
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, customCdf, null, headers);
    return response;
  }

  async updateCustomCdf(
    reportId: number,
    customCdfId: number,
    propertyId: string,
    customCdf: CustomCdf,
    reportKind: ReportKind = ReportKind.Report,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_CDF_BY_ID}`,
      replacements: {
        ':reportKind': `${reportKind}s`,
        ':reportId': reportId,
        ':customCdfId': customCdfId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.put(url, customCdf, null, headers);
    return response;
  }

  async deleteCustomCdf(
    reportId: number,
    customCdfId: number,
    propertyId: string,
    reportKind: ReportKind = ReportKind.Report,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_CDF_BY_ID}`,
      replacements: {
        ':reportKind': `${reportKind}s`,
        ':reportId': reportId,
        ':customCdfId': customCdfId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async getReportData(
    reportId: number,
    propertyId: string,
    format: NumericFormat = NumericFormat.RAW,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_DATA}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const params = {
      format,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, params, headers);
    return response;
  }

  async getReportSummary(
    reportId: number,
    propertyId: string,
    format: NumericFormat = NumericFormat.RAW,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_SUMMARY}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const params = {
      format,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, params, headers);
    return response;
  }

  async getReportQuerySummary(
    report: ReportQueryData,
    propertyId: string,
    mode: Mode = Mode.PREVIEW,
    format: NumericFormat = NumericFormat.RAW,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_QUERY_SUMMARY}`,
    };
    const params = {
      mode,
      format,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, report, params, headers);
    return response;
  }

  async getReportQueryData(
    report: ReportQueryData,
    propertyId: string,
    mode: Mode = Mode.PREVIEW,
    format: NumericFormat = NumericFormat.RAW,
    apiKey?: string,
    offset?: number,
    limit?: number,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_QUERY_DATA}`,
    };

    let params: any;

    if (offset !== undefined && limit !== undefined) {
      params = {
        mode,
        format,
        offset,
        limit,
      };
    } else {
      params = {
        mode,
        format,
      };
    }

    const headers = apiKey
      ? { 'X-API-KEY': apiKey, 'X-PROPERTY-ID': propertyId }
      : { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, report, params, headers);
    return response;
  }

  async getReportQueryDataWithApiKey(
    report: ReportQueryData,
    propertyId: string,
    mode: Mode = Mode.PREVIEW,
    format: NumericFormat = NumericFormat.RAW,
    apiKey = '',
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_QUERY_DATA}`,
    };
    const params = {
      mode,
      format,
    };
    const headers = { 'X-API-KEY': apiKey, 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, report, params, headers);
    return response;
  }

  async exportById(
    reportId: number,
    formatReport: boolean,
    format: ExportFormatType = ExportFormatType.XLSX,
    propertyId: string,
    apiKey?: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID}/export`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const params = {
      format,
      view: formatReport ? 'formatted' : 'details',
    };

    const headers = apiKey
      ? { 'X-API-KEY': apiKey }
      : { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async exportByQuery(
    report: ReportQueryExport,
    formatReport: boolean,
    format: ExportFormatType = ExportFormatType.XLSX,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_QUERY_EXPORT}`,
    };
    const params = {
      format,
      view: formatReport ? 'formatted' : 'details',
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, report, params, headers);
    return response;
  }

  async exportStockReportByQuery(
    stockReportId: number,
    report: StockReportQueryExport,
    formatReport: boolean,
    format: ExportFormatType = ExportFormatType.XLSX,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_QUERY_EXPORT}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const params = {
      format,
      view: formatReport ? 'formatted' : 'details',
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, report, params, headers);
    return response;
  }

  async getReportRelativeDateFilter(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_RELATIVE_DATES}`,
    };

    const params = {};
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getStockReportQueryDataById(
    id: number,
    propertyId: string,
    stockReport: StockReportQuery,
    mode: Mode = Mode.PREVIEW,
    format: NumericFormat = NumericFormat.RAW,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}/${id}/query/data`,
    };
    const params = {
      mode,
      format,
    };

    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, stockReport, params, headers);
    return response;
  }

  async getStockReportQuerySummaryById(
    id: number,
    propertyId: string,
    stockReport: StockReportQuery,
    mode: Mode = Mode.PREVIEW,
    format: NumericFormat = NumericFormat.RAW,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}/${id}/query/summary`,
    };
    const params = {
      mode,
      format,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, stockReport, params, headers);
    return response;
  }

  async getSearchReportsResults(propertyId: string, query: any = {}) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_SEARCH}`,
    };
    const params = {
      ...query,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getStockReportsSearchResults(propertyId: string, query: any = {}) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_SEARCH}`,
    };
    const params = {
      ...query,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getSearchResults(propertyId: string, query: any = {}) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.SEARCH}`,
    };
    const params = {
      ...query,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getPropertiesByReportId(propertyId: string, reportId: number) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.GET_PROPERTIES_BY_REPORT_ID}`,
      replacements: {
        ':reportId': reportId,
      },
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async getStockReportRules(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const headers = { 'X-PROPERTY-ID': propertyId };

    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.GET_STOCK_REPORT_RULES}`,
    };
    const response = await this.get(url, null, headers);

    return response;
  }

  async getStockReportRulesDetails(propertyId: string, ruleKey: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const headers = { 'X-PROPERTY-ID': propertyId };

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.GET_STOCK_REPORT_RULES_DETAILS}`,
      replacements: {
        ':ruleKey': ruleKey,
      },
    };
    const response = await this.get(url, null, headers);

    return response;
  }

  async getReportLimits(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const headers = { 'X-PROPERTY-ID': propertyId };
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_LIMITS}`,
    };
    const response = await this.get(url, null, headers);

    return response;
  }

  async getStockReportLimits(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const headers = { 'X-PROPERTY-ID': propertyId };
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_LIMITS}`,
    };
    const response = await this.get(url, null, headers);

    return response;
  }

  async createStockReport(propertyId: string, postBody: StockReportPostBody) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}`,
    };

    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, postBody, undefined, headers);
    return response;
  }

  async updateStockReport(
    stockReportId: number,
    propertyId: string,
    putBody: StockReportPostBody,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_BY_ID}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, putBody, undefined, headers);
    return response;
  }

  async deleteStockReport(stockReportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_BY_ID}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async updateStockReportRules(
    stockReportId: number,
    propertyId: string,
    rulesBody: any,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.UPDATE_STOCK_REPORT_RULES}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };

    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, rulesBody, undefined, headers);
    return response;
  }

  async getStockReportRevisions(stockReportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_REVISIONS}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);

    return response;
  }

  async getStockReportRevision(
    stockReportId: number,
    revisionId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_REVISION}`,
      replacements: {
        ':stockReportId': stockReportId,
        ':revisionId': revisionId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);

    return response;
  }

  async getReportCharts(reportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_CHARTS}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async createReportChart(chart: Chart, reportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_CHARTS}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, chart, null, headers);
    return response;
  }

  async getReportChart(chartId: number, reportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_CHARTS_BY_ID}`,
      replacements: {
        ':chartId': chartId,
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async updateReportChart(
    chartId: number,
    reportId: number,
    propertyId: string,
    chart: Chart,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_CHARTS_BY_ID}`,
      replacements: {
        ':chartId': chartId,
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, chart, null, headers);
    return response;
  }

  async deleteReportChart(
    chartId: number,
    reportId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_CHARTS_BY_ID}`,
      replacements: {
        ':chartId': chartId,
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async getStockReportCharts(stockReportId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_CHARTS}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async createStockReportChart(
    chart: Chart,
    stockReportId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_CHARTS}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, chart, null, headers);
    return response;
  }

  async getStockReportChart(
    chartId: number,
    stockReportId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_CHARTS_BY_ID}`,
      replacements: {
        ':chartId': chartId,
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async updateStockReportChart(
    chartId: number,
    stockReportId: number,
    propertyId: string,
    chart: Chart,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_CHARTS_BY_ID}`,
      replacements: {
        ':chartId': chartId,
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, chart, null, headers);
    return response;
  }

  async deleteStockReportChart(
    chartId: number,
    stockReportId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_CHARTS_BY_ID}`,
      replacements: {
        ':chartId': chartId,
        ':stockReportId': stockReportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async assignFolderToStockReport(
    folderId: number,
    stockReportId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}/stock_reports/folders/${folderId}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(
      url,
      { stock_report_id: stockReportId },
      null,
      headers,
    );
    return response;
  }

  async removeFolderFromStockReport(
    folderId: number,
    stockReportId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}/stock_reports/folders/${folderId}/${stockReportId}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async queueExportStockReportById(
    id: number,
    propertyId: string,
    propertyIds: number[],
    view: ExportViewType = ExportViewType.DETAILS,
    format: ExportFormatType = ExportFormatType.XLSX,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}/${id}/export/async`,
    };
    const params = { property_ids: propertyIds.join(','), format, view };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, null, params, headers);
    return response;
  }

  async queueExportById(
    reportId: number,
    formatReport: boolean,
    format: ExportFormatType = ExportFormatType.XLSX,
    propertyId: string,
    apiKey?: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID}/export/async`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const params = {
      format,
      view: formatReport ? 'formatted' : 'details',
    };

    const headers = apiKey
      ? { 'X-API-KEY': apiKey }
      : { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, null, params, headers);
    return response;
  }

  async queueExportByQuery(
    report: ReportQueryExport,
    formatReport: boolean,
    format: ExportFormatType = ExportFormatType.XLSX,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.QUEUE_REPORT_QUERY_EXPORT}`,
    };
    const params = {
      format,
      view: formatReport ? 'formatted' : 'details',
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, report, params, headers);
    return response;
  }

  async queueExportStockReportByQuery(
    stockReportId: number,
    report: StockReportQueryExport,
    formatReport: boolean,
    format: ExportFormatType = ExportFormatType.XLSX,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.QUEUE_STOCK_REPORT_QUERY_EXPORT}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const params = {
      format,
      view: formatReport ? 'formatted' : 'details',
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, report, params, headers);
    return response;
  }

  async queueExportReportsByIds(
    reportIds: number[],
    propertyId: string,
    name?: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.QUEUE_REPORTS_EXPORTS_BY_ID}`,
    };
    const params: { [key: string]: string } = {
      report_ids: reportIds.join(','),
    };

    if (name) {
      params.name = name;
    }
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.post(url, null, params, headers);
    return response;
  }

  async searchCharts(
    propertyId: string,
    searchTerm?: string,
    searchColumns?: string,
    datasourceKind?: string,
    chartKind?: string,
    ids?: number[],
    tagIds?: number[],
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CHART_SEARCH}`,
    };

    const params = {
      ...(searchTerm && { search_term: searchTerm }),
      ...(searchColumns && { search_columns: searchColumns }),
      ...(datasourceKind && { datasource_kind: datasourceKind }),
      ...(chartKind && { chart_kind: chartKind }),
      ...(ids && ids.length > 0 && { ids: ids.join(',') }),
      ...(tagIds && tagIds.length > 0 && { tag_ids: tagIds.join(',') }),
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getAllCharts(
    propertyId: string,
    datasourceKind?: string,
    chartKind?: string,
    ids?: number[],
    tagIds?: number[],
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CHARTS}`,
    };

    const params = {
      datasource_kind: datasourceKind,
      chart_kind: chartKind,
      tag_ids: tagIds?.join(','),
      ids: ids?.join(','),
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async createCustomFieldCdf(
    reportId: number,
    propertyId: string,
    customFieldCdf: CustomFieldCdf,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_FIELD_CDFS}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, customFieldCdf, null, headers);
    return response;
  }

  async getCustomFieldCdf(
    reportId: number,
    propertyId: string,
    customFieldCdfId: number,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_FIELD_CDF_BY_ID}`,
      replacements: {
        ':reportId': reportId,
        ':customFieldCdfId': customFieldCdfId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async updateCustomFieldCdf(
    reportId: number,
    customFieldCdfId: number,
    propertyId: string,
    customFieldCdf: CustomFieldCdf,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_FIELD_CDF_BY_ID}`,
      replacements: {
        ':reportId': reportId,
        ':customFieldCdfId': customFieldCdfId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, customFieldCdf, null, headers);
    return response;
  }

  async validateCustomFieldCdf(
    reportId: number,
    propertyId: string,
    customFieldCdf: CustomFieldCdf,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_VALIDATE_CUSTOM_FIELD_CDFS}`,
      replacements: {
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, customFieldCdf, null, headers);
    return response;
  }

  async deleteCustomFieldCdf(
    reportId: number,
    propertyId: string,
    customFieldCdfId: number,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORT_BY_ID_CUSTOM_FIELD_CDF_BY_ID}`,
      replacements: {
        ':reportId': reportId,
        ':customFieldCdfId': customFieldCdfId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async linkTagToStockReport(
    stockReportId: number,
    tagId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_STOCK_TAGS}`,
      replacements: {
        ':stockReportId': stockReportId,
      },
    };
    const data = {
      id: tagId,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, data, null, headers);
    return response;
  }

  async unlinkTagFromStockReport(
    stockReportId: number,
    stockTagId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORT_BY_ID_STOCK_TAG_BY_ID}`,
      replacements: {
        ':stockReportId': stockReportId,
        ':stockTagId': stockTagId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }
}
