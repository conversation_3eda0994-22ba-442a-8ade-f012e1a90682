import { Dataset } from '../datasets/dataset.type';

import { ReportSettings } from './settings/reportSettings';
import { Filters } from './filters/filters';
import { Sort } from './sort/sort.model';
import { Column } from './columns/column.model';
import { GroupRow } from './groupRows/groupRow.model';
import { GroupColumn } from './groupColumn/groupColumn.model';
import { CustomCdf } from './customCdf/customCdf.model';
import { Period } from './periods/period.model';
import { ReportFormats } from './formats/reportFormats';
import { CustomFieldCdf } from './customFieldCdf/customFieldCdf.model';
import { Comparison } from './comparisons/comparison.model';

export class ReportQueryData {
  property_ids: string[];
  dataset_id: Dataset;
  columns: Column[];
  group_rows: GroupRow[] | null;
  group_columns: GroupColumn[] | null;
  filters: Filters | null;
  sort: Sort[] | null;
  settings: ReportSettings;
  custom_cdfs: CustomCdf[] | null;
  periods: Period[] | null;
  formats: ReportFormats | null;
  custom_field_cdfs: CustomFieldCdf[] | [];
  comparisons: Comparison[] | null;

  constructor(
    propertyIds: string[],
    datasetId: Dataset,
    columns: Column[],
    groupRows: GroupRow[] | null = null,
    groupColumns: GroupColumn[] | null = null,
    settings: ReportSettings | null = null,
    sort: Sort[] | null = null,
    filters: Filters | null = null,
    custom_cdfs: CustomCdf[] | null = null,
    periods: Period[] | null = null,
    formats: ReportFormats | null = null,
    customFieldCdfs: CustomFieldCdf[] | [] = [],
    comparisons: Comparison[] | null = null,
  ) {
    this.property_ids = propertyIds;
    this.dataset_id = datasetId;
    this.columns = columns;
    this.group_rows = groupRows;
    this.group_columns = groupColumns;
    this.settings = settings === null ? { totals: false, details: false } : settings;
    this.sort = sort;
    this.filters = filters;
    this.custom_cdfs = custom_cdfs;
    this.periods = periods;
    this.formats = formats;
    this.custom_field_cdfs = customFieldCdfs;
    this.comparisons = comparisons;
  }
}
