import { ReportSettings } from './settings/reportSettings';
import { Filters } from './filters/filters';
import { Sort } from './sort/sort.model';
import { Column } from './columns/column.model';
import { GroupRow } from './groupRows/groupRow.model';
import { GroupColumn } from './groupColumn/groupColumn.model';
import { ReportFormats } from './formats/reportFormats';
import { Period } from './periods/period.model';
import { Comparison } from './comparisons/comparison.model';

export class StockReportQuery {
  property_ids: number[];
  columns?: Column[];
  group_rows?: GroupRow[] | null;
  group_columns?: GroupColumn[] | null;
  filters?: Filters | null;
  sort?: Sort[] | null;
  settings?: ReportSettings | null;
  formats: ReportFormats | null;
  periods?: Period[] | null;
  comparisons?: Comparison[] | null;

  constructor(
    propertyIds: number[],
    columns?: Column[],
    groupRows?: GroupRow[] | null,
    groupColumns?: GroupColumn[] | null,
    settings?: ReportSettings | null,
    sort?: Sort[] | null,
    filters?: Filters | null,
    formats: ReportFormats | null = null,
    periods: Period[] | null = null,
    comparisons: Comparison[] | null = null,
  ) {
    this.property_ids = propertyIds;
    this.columns = columns;
    this.group_rows = groupRows;
    this.group_columns = groupColumns;
    this.settings = settings;
    this.sort = sort;
    this.filters = filters;
    this.formats = formats;
    this.periods = periods;
    this.comparisons = comparisons;

    // @ts-ignore
    Object.keys(this).forEach(key => this[key] === undefined && delete this[key]);
  }
}
