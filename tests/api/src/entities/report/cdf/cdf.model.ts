import { Multilevel } from '@/entities/multilevel/multilevel.type';

import { CdfKind } from '../enums/cdf.type';

export class Cdf {
  type: CdfKind | undefined;
  column: string;
  multi_level_id: Multilevel | undefined;

  constructor(column: string, type?: CdfKind, multi_level_id?: Multilevel) {
    this.column = column;
    this.type = type || CdfKind.DEFAULT;

    if (multi_level_id) {
      this.multi_level_id = multi_level_id;
    }
  }
}
