import { Modifier } from '../enums/time.type';

export abstract class InvoicesCDF {
  property_id = false;
  reservation_id = false;
  property_name = false;
  bill_from_name = false;
  bill_from_address_line_1 = false;
  bill_from_address_line_2 = false;
  bill_from_city = false;
  bill_from_state = false;
  bill_from_country_code = false;
  bill_from_zip = false;
  bill_from_hotel_phone = false;
  bill_from_hotel_email = false;
  bill_to_party = false;
  bill_to_address_line_1 = false;
  bill_to_address_line_2 = false;
  bill_to_city = false;
  bill_to_country_code = false;
  bill_to_state = false;
  bill_to_postal_code = false;
  reservation_checkin_date: boolean | Modifier = false;
  reservation_checkout_date: boolean | Modifier = false;
  reservation_room_nights_count = false;
  reservation_datetime = false;
  invoice_language_code = false;
  invoice_status = false;
  invoice_number = false;
  document_type = false;
  invoice_pdf_url = false;
  invoice_generate_datetime: boolean | Modifier = false;
  credit_reason = false;
  invoice_currency_code = false;
  total_gross_amount = false;
  taxes_value_amount = false;
  balance_due_amount = false;
  bill_from_legal_name = false;
  bill_from_tax_id_1 = false;
  bill_from_tax_id_2 = false;
  bill_from_cnjp = false;
  bill_to_id = false;
  bill_to_tax_id = false;
  payment_due_date: boolean | Modifier = false;
  original_invoice_number = false;
  original_invoice_date: boolean | Modifier = false;

  constructor() {
    // @ts-ignore
    Object.keys(this).forEach(property => this[property] === false && delete this[property]);
  }
}
