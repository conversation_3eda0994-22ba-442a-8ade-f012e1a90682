import { Cdf } from '../cdf/cdf.model';

export class Period {
  cdf: Cdf;
  name: string;
  start: string;
  end: string;
  start_relative_to_end: boolean;

  constructor(cdf: Cdf, name: string, start: string, end: string, start_relative_to_end = false) {
    this.cdf = cdf;
    this.name = name;
    this.start = start;
    this.end = end;
    this.start_relative_to_end = start_relative_to_end;
  }
}
