import { FinancialCDF } from '@/entities/datasets/financial.cdf';
import { InvoiceCDF } from '@/entities/datasets/invoice.cdf';
import { InvoiceItemsCDF } from '@/entities/multilevel/invoiceItems.cdf';
import { OccupancyCDF } from '@/entities/datasets/occupancy.cdf';
import { PaymentCDF } from '@/entities/datasets/payment.cdf';
import { ReservationCDF } from '@/entities/datasets/reservation.cdf';
import { RoomNightsCDF } from '@/entities/multilevel/roomNights.cdf';
import { RoomReservationCDF } from '@/entities/multilevel/roomReservation.cdf';
import { GuestCDF } from '@/entities/datasets/guest.cdf';
import { OccupancyV1CDF } from '@/entities/datasets/occupancy_v1.cdf';
import { HousekeepingCDF } from '@/entities/datasets/housekeeping.cdf';
import { AccountingCDF } from '@/entities/datasets/accounting.cdf';
import { PayoutCdf } from '@/entities/datasets/payout.cdf';
import { BedOccupancyCDF } from '@/entities/datasets/bed_occupancy';

import { MetricType } from '../../enums/metric.type';

export const metrics = [
  MetricType.SUM,
  MetricType.MEAN,
  MetricType.MAX,
  MetricType.MIN,
  MetricType.COUNT,
  MetricType.STD,
  MetricType.VAR,
];

export const financialMetricCdfs = [
  FinancialCDF.QUANTITY_AMOUNT,
  FinancialCDF.DEBIT_AMOUNT,
  FinancialCDF.CREDIT_AMOUNT,
  FinancialCDF.BALANCE_DUE_AMOUNT,
  FinancialCDF.DEBIT_AMOUNT_CONVERTED_RATE,
  FinancialCDF.CREDIT_AMOUNT_CONVERTED_RATE,
  FinancialCDF.BALANCE_DUE_AMOUNT_CONVERTED_RATE,
];

export const guestMetricCdfs = [
  GuestCDF.DURATION_OF_STAY,
];

export const reservationMetricCdfs = [
  ReservationCDF.ADULTS_COUNT,
  ReservationCDF.CHILDREN_COUNT,
  ReservationCDF.GUEST_COUNT,
  ReservationCDF.ROOM_COUNT,
  ReservationCDF.ROOM_NIGHTS_COUNT,
  ReservationCDF.DEPOSIT_AMOUNT,
  ReservationCDF.GRAND_TOTAL_AMOUNT,
  ReservationCDF.COMMISSION_AMOUNT,
  ReservationCDF.RESERVATION_PAID_AMOUNT,
  ReservationCDF.RESERVATION_BALANCE_DUE_AMOUNT,
  ReservationCDF.TAXES_VALUE_AMOUNT,
  ReservationCDF.ROOM_REVENUE_TOTAL_AMOUNT,
  ReservationCDF.BOOKING_WINDOW,
];

export const occupancyMetricCdfs = [
  OccupancyCDF.GUEST_COUNT,
  OccupancyCDF.ROOM_REVENUE_AMOUNT,
  OccupancyCDF.ROOM_RATE_AMOUNT,
  OccupancyCDF.ROOM_AVAILABLE_TYPE_A,
  OccupancyCDF.CAPACITY_TYPE_A,
  OccupancyCDF.OUT_OF_SERVICE_TYPE_A,
  OccupancyCDF.BOOKING_QTY_TYPE_A,
  OccupancyCDF.BLOCKED_ROOM_DURATION,
  OccupancyCDF.BLOCKED_ROOM_TYPE_A,
  OccupancyCDF.OUT_OF_SERVICE_DURATION,
  OccupancyCDF.BED_BASED_CAPACITY,
  OccupancyCDF.BEDS_PER_ROOM,
  OccupancyCDF.ROOM_RATE_ADDITIONAL_ADULTS_AMOUNT,
  OccupancyCDF.ROOM_RATE_ADDITIONAL_KIDS_AMOUNT,
  OccupancyCDF.ROOM_RATE_BASE_AMOUNT,
  OccupancyCDF.ADULTS_COUNT,
  OccupancyCDF.CHILDREN_COUNT,
];

export const paymentMetricCdfs = [
  PaymentCDF.PAYMENT_NET_AMOUNT,
  PaymentCDF.PAYMENT_SUBMITTED_AMOUNT,
  PaymentCDF.PAYMENT_FEE_AMOUNT,
];

export const payoutMetricCdfs = [
  PayoutCdf.NET_AMOUNT,
  PayoutCdf.FEE_AMOUNT,
  PayoutCdf.TOTAL_AMOUNT,
];

export const invoiceMetricCdfs = [
  InvoiceCDF.TOTAL_GROSS_AMOUNT,
  InvoiceCDF.TAXES_VALUE_AMOUNT,
  InvoiceCDF.BALANCE_DUE_AMOUNT,
];

export const roomreservationsMetricCdfs = [
  RoomReservationCDF.ADULTS_PER_ROOM_COUNT,
  RoomReservationCDF.KIDS_PER_ROOM_COUNT,
  RoomReservationCDF.ROOM_GUEST_COUNT,
  RoomReservationCDF.ROOM_LENGTH_OF_STAY_COUNT,
  RoomReservationCDF.ROOM_RESERVATION_PRICE,
  RoomReservationCDF.ROOM_TOTAL_PRICE,
];

export const roomnightMetricCdfs = [
  RoomNightsCDF.ROOM_RATE,
  RoomNightsCDF.ADDITIONAL_CHARGES_EXTRA_ADULTS,
  RoomNightsCDF.ADDITIONAL_CHARGES_EXTRA_KIDS,
];

export const invoiceItemsMetricCdfs = [
  InvoiceItemsCDF.QUANTITY,
  InvoiceItemsCDF.AMOUNT,
  InvoiceItemsCDF.TAX_TOTAL,
];

export const occupancyV1MetricCdfs = [
  OccupancyV1CDF.CAPACITY_COUNT,
  OccupancyV1CDF.OUT_OF_SERVICE_COUNT,
  OccupancyV1CDF.BLOCKED_ROOM_COUNT,
  OccupancyV1CDF.OCCUPANCY,
  OccupancyV1CDF.OCCUPANCY_CONVERTED_RATE,
  OccupancyV1CDF.MFD_OCCUPANCY,
  OccupancyV1CDF.MFD_OCCUPANCY_CONVERTED_RATE,
  OccupancyV1CDF.ADR,
  OccupancyV1CDF.ADR_CONVERTED_RATE,
  OccupancyV1CDF.REVPAR,
  OccupancyV1CDF.REVPAR_CONVERTED_RATE,
  OccupancyV1CDF.ADULTS_COUNT,
  OccupancyV1CDF.CHILDREN_COUNT,
  OccupancyV1CDF.ROOMS_SOLD,
  OccupancyV1CDF.ROOM_RATE,
  OccupancyV1CDF.ROOM_RATE_CONVERTED_RATE,
  OccupancyV1CDF.ROOM_GUEST_COUNT,
  OccupancyV1CDF.ROOM_TAXES,
  OccupancyV1CDF.ROOM_FEES,
  OccupancyV1CDF.ADDITIONAL_ROOM_REVENUE,
  OccupancyV1CDF.ADDITIONAL_ROOM_REVENUE_CONVERTED_RATE,
  OccupancyV1CDF.ROOM_REVENUE,
  OccupancyV1CDF.ROOM_REVENUE_CONVERTED_RATE,
  OccupancyV1CDF.CONVERSION_RATE,
  OccupancyV1CDF.TOTAL_REVENUE,
  OccupancyV1CDF.TOTAL_REVENUE_CONVERTED_RATE,
  OccupancyV1CDF.TOTAL_ROOM_REVENUE_ADJUSTMENTS,
  OccupancyV1CDF.TOTAL_ROOM_REVENUE_ADJUSTMENTS_CONVERTED_RATE,
  OccupancyV1CDF.NON_ROOM_REVENUE,
  OccupancyV1CDF.NON_ROOM_REVENUE_CONVERTED_RATE,
  OccupancyV1CDF.OTHER_REVENUE_ADJUSTMENTS,
  OccupancyV1CDF.OTHER_REVENUE_ADJUSTMENTS_CONVERTED_RATE,
  OccupancyV1CDF.TOTAL_OTHER_REVENUE,
  OccupancyV1CDF.TOTAL_OTHER_REVENUE_CONVERTED_RATE,
  OccupancyV1CDF.MISC_INCOME,
  OccupancyV1CDF.MISC_INCOME_CONVERTED_RATE,
  OccupancyV1CDF.ALLOTMENT_BLOCKED_ROOM_COUNT,
];

export const housekeepingMetricCdfs = [HousekeepingCDF.ROOM_COUNT];

export const accountingMetricCdfs = [
  AccountingCDF.AMOUNT,
];

export const bedOccupancyMetricCdfs = [
  BedOccupancyCDF.OUT_OF_SERVICE_BEDS_CAPACITY,
  BedOccupancyCDF.BLOCKED_BEDS_CAPACITY,
  BedOccupancyCDF.ADULTS_COUNT,
  BedOccupancyCDF.CHILDREN_COUNT,
  BedOccupancyCDF.GUEST_COUNT,
  BedOccupancyCDF.ROOM_RATE,
  BedOccupancyCDF.ADDITIONAL_ROOM_REVENUE,
  BedOccupancyCDF.ADDITIONAL_ROOM_REVENUE_CONVERTED_RATE,
  BedOccupancyCDF.TOTAL_ROOM_REVENUE_ADJUSTMENTS,
  BedOccupancyCDF.TOTAL_ROOM_REVENUE_ADJUSTMENTS_CONVERTED_RATE,
  BedOccupancyCDF.ROOM_REVENUE,
  BedOccupancyCDF.ROOM_REVENUE_CONVERTED_RATE,
  BedOccupancyCDF.MISC_INCOME,
  BedOccupancyCDF.MISC_INCOME_CONVERTED_RATE,
  BedOccupancyCDF.OTHER_REVENUE_ADJUSTMENTS,
  BedOccupancyCDF.OTHER_REVENUE_ADJUSTMENTS_CONVERTED_RATE,
  BedOccupancyCDF.TOTAL_OTHER_REVENUE,
  BedOccupancyCDF.TOTAL_OTHER_REVENUE_CONVERTED_RATE,
  BedOccupancyCDF.TOTAL_REVENUE,
  BedOccupancyCDF.TOTAL_REVENUE_CONVERTED_RATE,
  BedOccupancyCDF.ROOM_TAXES,
  BedOccupancyCDF.ROOM_TAXES_CONVERTED_RATE,
  BedOccupancyCDF.ROOM_FEES,
  BedOccupancyCDF.ROOM_FEES_CONVERTED_RATE,
];