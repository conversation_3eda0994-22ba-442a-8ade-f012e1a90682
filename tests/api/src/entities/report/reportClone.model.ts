import { Chart } from '../chart/chart.model';

import { ReportSettings } from './settings/reportSettings';
import { Filters } from './filters/filters';
import { Sort } from './sort/sort.model';
import { Column } from './columns/column.model';
import { GroupRow } from './groupRows/groupRow.model';
import { GroupColumn } from './groupColumn/groupColumn.model';
import { Period } from './periods/period.model';
import { ReportFormats } from './formats/reportFormats';

export class ReportClone {
  title?: string;
  description?: string;
  property_id?: number;
  property_ids?: number[];
  columns?: Column[];
  group_rows?: GroupRow[];
  group_columns?: GroupColumn[];
  filters?: Filters;
  sort?: Sort[];
  settings?: ReportSettings;
  periods?: Period[];
  charts?: Chart[];
  formats: ReportFormats | null;

  constructor(
    title?: string,
    description?: string,
    propertyId?: number,
    propertyIds?: number[],
    columns?: Column[],
    groupRows?: GroupRow[],
    groupColumns?: GroupColumn[],
    settings?: ReportSettings,
    sort?: Sort[],
    filters?: Filters,
    periods?: Period[],
    charts?: Chart[],
    formats: ReportFormats | null = null,
  ) {
    this.title = title;
    this.description = description;
    this.property_id = propertyId;
    this.property_ids = propertyIds;
    this.columns = columns;
    this.group_rows = groupRows;
    this.group_columns = groupColumns;
    this.settings = settings;
    this.sort = sort;
    this.filters = filters;
    this.periods = periods;
    this.formats = formats;
    this.charts = charts;

    Object.keys(this).forEach((key: string) => {
      if (this[key as keyof ReportClone] === undefined) {
        delete this[key as keyof ReportClone];
      }
    });
  }
}
