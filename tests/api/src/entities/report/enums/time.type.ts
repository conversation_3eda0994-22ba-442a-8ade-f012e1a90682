export const enum Modifier {
  YEAR = 'year',
  MONTH = 'month',
  DAY = 'day',
  HOUR = 'hour',
  MINUTE = 'minute',
  SECOND = 'second',
  WEEK = 'week',
  MONTH_WITHOUT_YEAR = 'm',
  DAY_WITHOUT_YEAR = 'd',
  TIME_HOUR = 'time_hour',
  TIME_MINUTE = 'time_minute',
  TIME_SECOND = 'time_second',
}

export const modifierValues = [Modifier.DAY, Modifier.HOUR, Modifier.MINUTE, Modifier.MONTH, Modifier.SECOND, Modifier.YEAR, Modifier.WEEK];
