export enum FilterByType {
  BEGINS = 'begins',
  NOT_BEGINGS = 'not_begins',
  ENDS = 'ends',
  NOT_ENDS = 'not_ends',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'contains',
  LIST_CONTAINS = 'list_contains',
  NOT_LIST_CONTAINS = 'not_list_contains',
  IS_NULL = 'is_null',
  IS_NOT_NULL = 'is_not_null',
  IS_EMTPTY = 'is_empty',
  IS_NOT_EMPTY = 'is_not_empty',
  IS_NULL_EMPTY = 'is_null_empty',
  IS_NOT_NULL_EMPTY = 'is_not_null_empty',
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  GREATER_THAN_OR_EQUAL = 'greater_than_or_equal',
  LESS_THAN = 'less_than',
  LESS_THAN_OR_EQUAL = 'less_than_or_equal'
}
