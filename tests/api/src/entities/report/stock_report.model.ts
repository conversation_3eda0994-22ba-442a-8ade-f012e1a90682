import { Dataset } from '../datasets/dataset.type';

import { ReportSettings } from './settings/reportSettings';
import { Filters } from './filters/filters';
import { Sort } from './sort/sort.model';
import { Column } from './columns/column.model';
import { GroupRow } from './groupRows/groupRow.model';
import { GroupColumn } from './groupColumn/groupColumn.model';
import { ReportFormats } from './formats/reportFormats';

export class StockReport {
  title: string;
  description: string;
  dataset_id: Dataset;
  columns: Column[];
  group_rows: GroupRow[] | null;
  group_columns: GroupColumn[] | null;
  filters: Filters | null;
  sort: Sort[] | null;
  settings: ReportSettings;
  formats: ReportFormats | null;

  constructor(
    title: string,
    description: string,
    datasetId: Dataset,
    columns: Column[],
    groupRows: GroupRow[] | null = null,
    groupColumns: GroupColumn[] | null = null,
    settings: ReportSettings | null = null,
    sort: Sort[] | null = null,
    filters: Filters | null = null,
    formats: ReportFormats | null = null,
  ) {
    this.title = title;
    this.description = description;
    this.dataset_id = datasetId;
    this.columns = columns;
    this.group_rows = groupRows;
    this.group_columns = groupColumns;
    this.settings = settings === null ? { totals: false, details: false } : settings;
    this.sort = sort;
    this.filters = filters;
    this.formats = formats;
  }
}
