import { CustomCdfKind } from '../enums/customCdf.type';

import { CustomCdfFormula } from './customCdfFormula.model';
import { CustomCdfValidate } from './customCdfValidate.model';

export class CustomCdf extends CustomCdfValidate {
  description: string;

  constructor(name: string, description: string, formula: CustomCdfFormula[], kind: CustomCdfKind, format?: string) {
    super(name, formula, kind, format);
    this.description = description;
  }
}
