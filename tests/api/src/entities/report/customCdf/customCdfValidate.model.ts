import { CustomCdfKind } from '../enums/customCdf.type';

import { CustomCdfFormula } from './customCdfFormula.model';

export class CustomCdfValidate {
  name: string;
  formula: CustomCdfFormula[];
  kind: CustomCdfKind;
  format?: string;

  constructor(name: string, formula: CustomCdfFormula[], kind: CustomCdfKind, format?: string) {
    this.name = name;
    this.formula = formula;
    this.kind = kind;
    this.format = format;
  }
}
