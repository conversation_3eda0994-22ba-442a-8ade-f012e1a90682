import { log } from 'console';

import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

@injectable()
export class TaskService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  /**
   * Method that is added to first trigger the authentication, to allow using Promise All
   * Right now if multiple request hits MFD API it breaks, so is a workaround to improve test performance
   */
  authenticateService() {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
  }

  async getTaskById(propertyId: string, taskId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.GET_TASK_BY_ID}`,
      replacements: {
        ':taskId': taskId,
      },
    };

    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async wait(seconds: number): Promise<void> {
    return new Promise<void>(resolve => {
      setTimeout(() => {
        resolve();
      }, seconds * 1000); // Convert seconds to milliseconds
    });
  }

  public isAllowedStatus(value: string) {
    return (
      value === 'STARTED'
      || value === 'COMPLETED'
      || value === 'PENDING'
      || value === 'SUCCESS'
    );
  }

  /* eslint-disable no-await-in-loop */
  async completeTask(exportReport: any, propertyId: string) {
    while (
      exportReport.status === 'STARTED'
      || exportReport.status === 'PENDING'
    ) {
      log('Task has not completed, retrying...');
      await this.wait(5);
      exportReport = await this.getTaskById(propertyId, exportReport.id);
    }
    return exportReport;
  }

  async getTaskByToken(
    token: string,
    authorization = 'invalid auth header',
  ) {
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.GET_TASK_BY_TOKEN}`,
      replacements: {
        ':token': token,
      },
    };

    const headers = { Authorization: authorization };

    const response = await this.get(url, null, headers);
    return response;
  }
}
/* eslint-disable no-await-in-loop */
