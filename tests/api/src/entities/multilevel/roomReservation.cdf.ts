export enum RoomReservationCDF {
  ROOM_IDENTIFIER = 'room_identifier',
  ROOM_TYPE = 'room_type',
  ROOM_NUMBER = 'room_number',
  ROOM_PRIMARY_GUEST_FIRST_NAME = 'room_primary_guest_first_name',
  ROOM_PRIMARY_GUEST_LAST_NAME = 'room_primary_guest_last_name',
  ROOM_PRIMARY_GUEST_FULL_NAME = 'room_primary_guest_full_name',
  ROOM_RESERVATION_PRICE = 'room_reservation_price',
  ROOM_TOTAL_PRICE = 'room_total_price',
  ROOM_CHECKIN_DATE = 'room_checkin_date',
  ROOM_CHECKOUT_DATE = 'room_checkout_date',
  ROOM_RESERVATION_STATUS = 'room_reservation_status',
  ROOM_LENGTH_OF_STAY_COUNT = 'room_length_of_stay_count',
  ADULTS_PER_ROOM_COUNT = 'adults_per_room_count',
  KIDS_PER_ROOM_COUNT = 'kids_per_room_count',
  ROOM_GUEST_COUNT = 'room_guest_count',
  // BREAKFAST_FLAG = 'breakfast_flag' // Commented now since is behind a feature flag
  // ROOM_CHANNEL_RATE_PLAN = 'room_channel_rate_plan' // Commented now since is behind a feature flag
}
