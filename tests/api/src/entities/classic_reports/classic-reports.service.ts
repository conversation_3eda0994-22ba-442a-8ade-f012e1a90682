import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

@injectable()
export class ClassicReportsService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  /**
   * Method that is added to first trigger the authentication, to allow using Promise All
   * Right now if multiple request hits MFD API it breaks, so is a workaround to improve test performance
   */
  authenticateService() {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
  }

  async getReport(
    propertyId: string,
    queryParams: any = {},
    pathAppend = '',
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CLASSIC_REPORTS}${pathAppend}`,
    };
    const headers: any = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, queryParams, headers);
    return response;
  }

  async postReport(
    propertyId: string,
    queryParams: any = null,
    jsonBody: any = {},
    path_append = '',
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.CLASSIC_REPORTS}${path_append}`,
    };
    const headers: any = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, jsonBody, queryParams, headers);
    return response;
  }
}
