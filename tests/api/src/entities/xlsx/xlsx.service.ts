import * as XLSX from 'xlsx';
import axios from 'axios';
import { injectable } from 'inversify'; // Import injectable decorator

@injectable()
export class XLSXService {
  async readExcelFromUrl(url: string): Promise<any> {
    try {
      // Fetching the file as a blob
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
      });

      // Converting the data to a buffer
      const data = new Uint8Array(response.data);

      // Reading the workbook from buffer
      const workbook = XLSX.read(data, { type: 'array' });

      // Example: Processing the first worksheet
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const json = XLSX.utils.sheet_to_json(worksheet);
      return json;
    } catch (error) {
      console.error('Error reading the Excel file:', error);
    }
  }
}
