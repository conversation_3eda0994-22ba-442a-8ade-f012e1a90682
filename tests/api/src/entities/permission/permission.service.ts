import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

@injectable()
export class PermissionService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
        @inject(TYPES.LogService) logService: LogService,
        @inject(TYPES.Config) config: Config,
        @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
        @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
        @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,

  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  async noReportBuilderGetStockReports(user: string, propertyId: string, query: any = {}) {
    this._authenticateService.getServiceAuthenticated(this, user);

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_REPORTS}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const params = {
      ...query,
    };

    const response = await this.get(url, params, headers);
    return response;
  }

  async noReportBuilderGetFolders(user: string, propertyId: string, query: any = {}) {
    this._authenticateService.getServiceAuthenticated(this, user);

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FOLDERS}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const params = {
      ...query,
    };

    const response = await this.get(url, params, headers);
    return response;
  }

  async noReportBuilderGetTags(user: string, propertyId: string, query: any = {}) {
    this._authenticateService.getServiceAuthenticated(this, user);

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.TAGS}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const params = {
      ...query,
    };

    const response = await this.get(url, params, headers);
    return response;
  }

  async noReportBuilderGetReports(user: string, propertyId: string, query: any = {}) {
    this._authenticateService.getServiceAuthenticated(this, user);

    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.REPORTS}`,
    };

    const headers = {
      'X-PROPERTY-ID': propertyId,
    };

    const params = {
      ...query,
    };

    const response = await this.get(url, params, headers);
    return response;
  }
}
