import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { Favorite } from './favorite.model';

@injectable()
export class FavoriteService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  async favoriteEntity(favorite: Favorite, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FAVORITES}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };
    const response = await this.post(url, favorite, undefined, headers);
    return response;
  }

  async getFavorites(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FAVORITES}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };
    const params = {};
    const response = await this.get(url, params, headers);
    return response;
  }

  async updateRank(favoriteId: number, rankId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FAVORITES}/${favoriteId}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };
    const params = {};
    const response = await this.patch(url, { rank: rankId }, params, headers);
    return response;
  }

  async deleteFavorite(favoriteId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FAVORITES}/${favoriteId}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };
    const params = {};
    const response = await this.delete(url, params, headers);
    return response;
  }

  async getFavoriteById(favoriteId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FAVORITES}/${favoriteId}`,
    };
    const headers = {
      'X-PROPERTY-ID': propertyId,
    };
    const params = {};
    const response = await this.get(url, params, headers);
    return response;
  }
}
