import axios from 'axios';
import { injectable } from 'inversify'; // Import injectable decorator

@injectable()
export class JsonService {
  async readJsonFromUrl(url: string): Promise<any> {
    try {
      // Fetching the file as JSON from the url
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Error reading the CSV file:', error);
    }
  }
}
