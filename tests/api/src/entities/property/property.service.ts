import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { PropertySettings } from './propertySettings.type';

@injectable()
export class PropertyService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  /**
 * Method that is added to first trigger the authentication, to allow using Promise All
 * Right now if multiple request hits MFD API it breaks, so is a workaround to improve test performance
 */
  authenticateService() {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
  }

  async getPropertySettings(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.PROPERTY_SETTINGS}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }

  async updatePropertySetting(propertySetting: PropertySettings, value: string, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.PROPERTY_SETTINGS}/${propertySetting}`,
    };
    const data = {
      value,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.put(url, data, null, headers);
    return response;
  }

  async getPropertySettingOptions(propertySetting: PropertySettings, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.PROPERTY_SETTINGS}/${propertySetting}`,
    };

    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, null, headers);
    return response;
  }
}
