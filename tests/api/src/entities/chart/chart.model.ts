import { ChartSettings } from './chartSettings';
import { ChartCategoryOrString } from './chartCategory.model';
import { ChartMetric } from './chartMetric.model';

export class Chart {
  title: string;
  kind: string;
  settings: ChartSettings;
  categories: ChartCategoryOrString[];
  metrics: ChartMetric[];

  constructor(
    title: string,
    kind: string,
    settings: ChartSettings,
    categories: ChartCategoryOrString[],
    metrics: ChartMetric[],
  ) {
    this.title = title;
    this.kind = kind;
    this.settings = settings;
    this.categories = categories;
    this.metrics = metrics;
  }
}
