import { Multilevel } from '@/entities/multilevel/multilevel.type';

import { Cdf } from '../report/cdf/cdf.model';
import { MetricType } from '../report/enums/metric.type';

export class ChartMetric {
  cdf: Cdf;
  metrics: MetricType[] | undefined;
  multi_level_id: number | undefined;

  constructor(cdf: Cdf, metrics?: MetricType[], multi_level_id?: Multilevel) {
    this.cdf = cdf;

    if (metrics) {
      this.metrics = metrics;
    }

    if (multi_level_id) {
      this.multi_level_id = multi_level_id;
    }
  }
}
