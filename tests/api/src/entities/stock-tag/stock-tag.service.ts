import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { StockTag } from './stock-tag.model';

@injectable()
export class StockTagService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  async createStockTag(tagName: string, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_TAGS}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const stockTag = new StockTag(tagName);

    const response = await this.post(url, stockTag, null, headers);
    return response;
  }

  async updateStockTag(tagName: string, tagId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_TAG_BY_ID}`,
      replacements: {
        ':tagId': tagId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, new StockTag(tagName), null, headers);
    return response;
  }

  async deleteStockTag(tagId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_TAG_BY_ID}`,
      replacements: {
        ':tagId': tagId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async getStockTags(propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.STOCK_TAGS}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }
}
