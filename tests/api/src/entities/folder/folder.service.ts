import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { Folder } from './folder.model';

@injectable()
export class FolderService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  /**
   * Method that is added to first trigger the authentication, to allow using Promise All
   * Right now if multiple request hits MFD API it breaks, so is a workaround to improve test performance
   */
  authenticateService() {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
  }

  async createFolder(folder: Folder, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FOLDERS}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, folder, null, headers);
    return response;
  }

  async deleteFolder(folderId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FOLDER_BY_ID}`,
      replacements: {
        ':folderId': folderId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async linkReportToFolder(reportId: number, folderId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FOLDER_BY_ID}/reports`,
      replacements: {
        ':folderId': folderId,
      },
    };
    const data = {
      report_id: reportId,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, data, null, headers);
    return response;
  }

  async unlinkReportFromFolder(reportId: number, folderId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.FOLDER_BY_ID_REPORT_BY_ID}`,
      replacements: {
        ':folderId': folderId,
        ':reportId': reportId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }
}
