import { ExportFormatType } from '../report/enums/export.format.type';

import { ScheduleView } from './enums/schedule.view.type';

class ScheduleSettings {
  require_login!: boolean;

  constructor(require_login: boolean) {
    this.require_login = require_login;
  }
}

export class Schedule {
  frequency!: string;
  view!: string;
  format!: string;
  subject!: string;
  recipients!: Array<string>;
  report_ids!: Array<number>;
  settings?: ScheduleSettings | null | undefined = undefined;

  constructor(
    frequency: string,
    view: ScheduleView,
    format: ExportFormatType,
    subject: string,
    recipients: Array<string>,
    report_ids: Array<number>,
    settings?: ScheduleSettings | null | undefined,
  ) {
    this.frequency = frequency;
    this.view = ScheduleView[view];
    this.format = format;
    this.subject = subject;
    this.recipients = recipients;
    this.report_ids = report_ids;
    this.settings = settings;
  }
}
