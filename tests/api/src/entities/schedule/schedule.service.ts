import { inject, injectable } from 'inversify';

import { AuthenticateService } from '../../services/authenticate.service';
import { Config } from '../../../config';
import { AxiosUrl } from '../../common/axiosUrl';
import { HTTP } from '../../common/http';
import { HttpRequest, HttpStatus } from '../../ioc/interfaces';
import { TYPES } from '../../ioc/types';
import { LogService } from '../../services/log.service';

import { Schedule } from './schedule.model';

@injectable()
export class ScheduleService extends HTTP {
  private _apiVersion = '/datainsights/v1.1';
  private _config: Config;
  private _authenticateService: AuthenticateService;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.Config) config: Config,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
    @inject(TYPES.AuthenticateService) authenticateService: AuthenticateService,
  ) {
    super(httprequest, logService, httpStatus);
    this._config = config;
    this._authenticateService = authenticateService;
  }

  /**
   * Method that is added to first trigger the authentication, to allow using Promise All
   * Right now if multiple request hits MFD API it breaks, so is a workaround to improve test performance
   */
  authenticateService() {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
  }

  async createSchedule(
    schedule: Schedule,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.SCHEDULES}`,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, schedule, null, headers);
    return response;
  }

  async updateSchedule(
    schedule: Schedule,
    scheduleId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.SCHEDULE_BY_ID}`,
      replacements: {
        ':scheduleId': scheduleId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.put(url, schedule, null, headers);
    return response;
  }

  async deleteSchedule(
    scheduleId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.SCHEDULE_BY_ID}`,
      replacements: {
        ':scheduleId': scheduleId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.delete(url, null, headers);
    return response;
  }

  async getSchedules(propertyId: string, sort?: string, query: any = {}) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.SCHEDULES}`,
    };
    const params = {
      sort,
      ...query,
    };
    const headers = { 'X-PROPERTY-ID': propertyId };
    const response = await this.get(url, params, headers);
    return response;
  }

  async getSchedule(scheduleId: number, propertyId: string) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.SCHEDULE_BY_ID}`,
      replacements: {
        ':scheduleId': scheduleId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.get(url, null, headers);
    return response;
  }

  async runSchedule(
    scheduleId: number,
    propertyId: string,
  ) {
    if (this._isAuthenticated === false) {
      this._authenticateService.getServiceAuthenticated(this);
    }
    const url: AxiosUrl = {
      path: `${this._apiVersion}${this._config.DATA_INSIGHTS_API.ENDPOINT_PATHS.SCHEDULE_RUN_BY_ID}`,
      replacements: {
        ':scheduleId': scheduleId,
      },
    };
    const headers = { 'X-PROPERTY-ID': propertyId };

    const response = await this.post(url, null, null, headers);
    return response;
  }
}
