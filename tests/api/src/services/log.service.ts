import { injectable, inject } from 'inversify';
import { pino as loggerInterface } from 'pino';

import { Config } from '../../config';
import { TYPES } from '../ioc/types';
import { <PERSON><PERSON> } from '../ioc/interfaces';

@injectable()
export class LogService {
  _logger: loggerInterface.Logger;
  constructor(@inject(TYPES.Config) config: Config, @inject(TYPES.Pino) pino: Pino) {
    this._logger = pino({ level: config.LOG_LEVEL });
  }

  public child(childName: string): loggerInterface.Logger {
    return this._logger.child({ module: childName });
  }
}
