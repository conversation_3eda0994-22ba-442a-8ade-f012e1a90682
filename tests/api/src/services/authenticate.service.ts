import { inject, injectable } from 'inversify';

import { Config } from '../../config';
import { HTTP } from '../common/http';
import { TYPES } from '../ioc/types';

import { LogService } from './log.service';

@injectable()
export class AuthenticateService {
  private _logger: any;
  private _config: Config;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.Config) config: Config,
  ) {
    this._logger = logService.child('AuthenticateService');
    this._config = config;
  }

  /**
   * Method that will login to MFD in order to get the access token
   * After that I will create a new Request client copying those cookies
   * Will call the get_session_token in order to have access to Data Insights API
   */
  public getServiceAuthenticated(
    httpService: HTTP,
    userKind = 'DI_FULL_ACCESS',
  ): HTTP {
    let token: string;

    switch (userKind) {
      case 'DI_FULL_ACCESS':
        token = process.env.DI_FULL_ACCESS_TOKEN || '';
        this._config.USERS.DI_FULL_ACCESS.TOKEN = token;
        break;
      case 'VIEWER_ORGANIZATION_OWNER':
        token = process.env.VIEWER_ORGANIZATION_OWNER_TOKEN || '';
        this._config.USERS.VIEWER_ORGANIZATION_OWNER.TOKEN = token;
        break;
      case 'VIEWER_FINANCIAL_DATASET_ACCESS':
        token = process.env.VIEWER_FINANCIAL_DATASET_ACCESS_TOKEN || '';
        this._config.USERS.VIEWER_FINANCIAL_DATASET_ACCESS.TOKEN = token;
        break;
      case 'NO_DI_ACCESS':
        token = process.env.NO_DI_ACCESS_TOKEN || '';
        this._config.USERS.NO_DI_ACCESS.TOKEN = token;
        break;
      case 'DI_ACCESS_PROPERTY_OWNER':
        token = process.env.DI_ACCESS_PROPERTY_OWNER_TOKEN || '';
        this._config.USERS.DI_ACCESS_PROPERTY_OWNER.TOKEN = token;
        break;
      default:
        throw new Error('The given user kind is not the expected');
    }

    httpService.createService(`${this._config.DATA_INSIGHTS_API.BASE_URL}`);
    const bearerHeaders = {
      authorization: `Bearer ${token}`,
    };

    this._logger.trace({
      Action: 'Starting Service Authentication',
      Method: 'getServiceAuthenticated',
      User: userKind,
    });

    httpService.updateDefaultHeaders(bearerHeaders);
    httpService._isAuthenticated = true;
    return httpService;
  }
}
