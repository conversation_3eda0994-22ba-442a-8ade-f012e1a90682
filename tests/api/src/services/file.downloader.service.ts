import { inject, injectable } from 'inversify';

import { AxiosUrl } from '@/common/axiosUrl';
import { HTTP } from '@/common/http';
import { HttpRequest, HttpStatus } from '@/ioc/interfaces';
import { TYPES } from '@/ioc/types';

import { LogService } from './log.service';

@injectable()
export class FileDownloaderService extends HTTP {
  private _fileDownloaderLogger: any;

  public constructor(
    @inject(TYPES.LogService) logService: LogService,
    @inject(TYPES.CloudbedsHttpRequest) httprequest: HttpRequest,
    @inject(TYPES.HttpStatus) httpStatus: HttpStatus,
  ) {
    super(httprequest, logService, httpStatus);
    this._fileDownloaderLogger = logService.child('DownloadFileService');
    this.createService('fake.com');
  }

  async downloadFileFromUrl(fileUrl: string) {
    const url: AxiosUrl = {
      path: fileUrl,
    };
    const config = {
      responseType: 'blob',
    };
    const response = await this.get(url, null, null, config);
    this._fileDownloaderLogger.info({ Action: 'File has been downloaded', Method: 'downloadFileFromUrl' });
    return response;
  }
}
