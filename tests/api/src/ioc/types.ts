const TYPES = {
  CloudbedsHttpRequest: Symbol.for('CloudbedsHttpRequest'),
  Pino: Symbol.for('Pino'),
  DotEnv: Symbol.for('DotEnv'),
  QueryString: Symbol.for('QueryString'),
  LogService: Symbol.for('LogService'),
  Config: Symbol.for('Config'),
  ReportService: Symbol.for('ReportService'),
  HttpStatus: Symbol.for('HttpStatus'),
  FileDownloaderService: Symbol.for('FileDownloaderService'),
  AuthenticateService: Symbol.for('AuthenticateService'),
  TagService: Symbol.for('TagService'),
  FolderService: Symbol.for('FolderService'),
  ScheduleService: Symbol.for('ScheduleService'),
  MeService: Symbol.for('MeService'),
  DatasetService: Symbol.for('DatasetService'),
  FavoriteService: Symbol.for('FavoriteService'),
  AdminService: Symbol.for('AdminService'),
  PropertyService: Symbol.for('PropertyService'),
  PermissionService: Symbol.for('PermissionService'),
  StockReportFolderService: Symbol.for('StockReportFolderService'),
  TaskService: Symbol.for('TaskService'),
  HubService: Symbol.for('HubService'),
  XLSXService: Symbol.for('XLSXService'),
  CsvService: Symbol.for('CsvService'),
  JsonService: Symbol.for('JsonService'),
  ClassicReportsService: Symbol.for('ClassicReportsService'),
  StockTagService: Symbol.for('StockTagService'),
};

export { TYPES };
