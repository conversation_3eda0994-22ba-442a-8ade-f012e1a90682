import { DatasetService, Config } from '@/index';

jest.retryTimes(2);
describe('- Get Datasets with Translations', () => {
  test('Get all Datasets by Language', async () => {
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;

    // Dataset Translations
    const defaultDatasetResponses = await DatasetService.getDatasets(propertyId);
    const englishDatasetResponse = await DatasetService.getDatasets(propertyId, 'en');
    const spanishDatasetResponse = await DatasetService.getDatasets(propertyId, 'es');
    const portugueseDatasetResponse = await DatasetService.getDatasets(propertyId, 'pt');
    const thailandDatasetResponse = await DatasetService.getDatasets(propertyId, 'th');
    const frenchDatasetResponse = await DatasetService.getDatasets(propertyId, 'fr');

    for (let i = 0; i < defaultDatasetResponses.length; i++) {
      expect(defaultDatasetResponses[i].id).toEqual(englishDatasetResponse[i].id);
      expect(defaultDatasetResponses[i].id).toEqual(spanishDatasetResponse[i].id);
      expect(defaultDatasetResponses[i].id).toEqual(portugueseDatasetResponse[i].id);
      expect(defaultDatasetResponses[i].id).toEqual(frenchDatasetResponse[i].id);
      expect(defaultDatasetResponses[i].name).toEqual(englishDatasetResponse[i].name);
      expect(defaultDatasetResponses[i].name).not.toEqual(spanishDatasetResponse[i].name);
      expect(defaultDatasetResponses[i].name).not.toEqual(portugueseDatasetResponse[i].name);
      expect(defaultDatasetResponses[i].name).not.toEqual(frenchDatasetResponse[i].name);
      expect(defaultDatasetResponses[i].name).not.toEqual(thailandDatasetResponse[i].id);
      expect(defaultDatasetResponses[i].name).not.toEqual(thailandDatasetResponse[i].name);
    }
  });
});
