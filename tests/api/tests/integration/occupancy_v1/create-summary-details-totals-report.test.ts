import { Guid } from 'guid-typescript';

import { Report } from '@/entities/report/report.model';
import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';

import { getSummaryReport, getDefaultFilter } from '../../common/data/occupancy_v1';

jest.retryTimes(2);
describe(`- A summary details with totals report is a report based in data grouped by columns
- It is a report which allows users to group rows data
- A max of 3 Group Rows can be selected
- The columns don't need to be of type numeric and have metrics associated
- To view the totals the columns need to be of type numeric
- The Summary Report with Totals will show the data grouped by the selected columns presenting the totals for each group`, () => {
  test('Create Occupancy V1 Summary Details Report with Totals v1.1', async () => {
    const title = `Auto-OccupancyV1-${Guid.create()}`;
    const description = 'OccupancyV1 Grouped Report Created by SDK';
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.OCCUPANCY_V1;
    const { columns, groupRows } = getSummaryReport();

    const settings: ReportSettings = {
      totals: true,
      details: true,
    };

    const myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      settings,
      null,
      getDefaultFilter(),
    );
    const report = await ReportService.createReport(myCustomReport, propertyId);
    const reportId = report.id;
    const response = await ReportService.getReport(reportId, propertyId);

    // Verify Columns with Metrics
    expect(response.columns).toEqual(myCustomReport.columns);

    // Verify Group By Rows
    expect(response.group_rows).toEqual(myCustomReport.group_rows);

    // Verify Group By Columns
    expect(response.group_columns).toEqual(myCustomReport.group_columns);

    // Verify Report Metadata
    expect(response.title).toEqual(myCustomReport.title);
    expect(response.description).toEqual(myCustomReport.description);
    expect(response.dataset_id).toEqual(datasetId);
    expect(response.settings.totals).toEqual(myCustomReport.settings.totals);
    expect(response.settings.details).toEqual(myCustomReport.settings.details);
    expect(response.property_id).toEqual(propertyId);
    expect(response.property_ids).toEqual(myCustomReport.property_ids);

    // Get Report Data
    const data = await ReportService.getReportData(reportId, propertyId);

    expect(data.headers.length).toBeGreaterThanOrEqual(0);
    expect(data.type).toBe('Summary');
    if (isNotEmptyObject(data.records)) {
      expect(data.group_rows.length).toEqual(Object.keys(groupRows).length);
      expect(Object.keys(data.totals).length).toBeGreaterThanOrEqual(0);
      expect(Object.keys(data.subtotals).length).toBeGreaterThanOrEqual(0);
    }

    data.index.forEach((item: any) => {
      const secondIndex = item[1];
      if (secondIndex !== '-') {
        const asNumber = parseInt(secondIndex, 10);
        expect(asNumber.toString() === secondIndex && !isNaN(asNumber)).toBeTruthy();
      } else {
        expect(secondIndex).toBe('-');
      }
    });

    // Delete created Report
    await ReportService.deleteReport(reportId, propertyId);
  });
});
