import { Guid } from 'guid-typescript';

import { Report } from '@/entities/report/report.model';
import { ReportService, Config } from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { Dataset } from '@/entities/datasets/dataset.type';
import { CustomCdfFormula } from '@/entities/report/customCdf/customCdfFormula.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { getDefaultFilter, getSummaryReport } from 'tests/common/data/occupancy_v1';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { OccupancyV1CDF } from '@/entities/datasets/occupancy_v1.cdf';

jest.retryTimes(2);
describe('- Custom CDF gives the user the ability to create a new cdf based on existing cdfs', () => {
  test('Create a Occupancy v1 Summary with Details Report with String Custom CDF, update Report with created CDF and pull report data', async () => {
    const title = `Auto-OccupancyV1-${Guid.create()}`;
    const description = 'OccupancyV1 Grouped Report Created by SDK';
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.OCCUPANCY_V1;
    const { columns, groupRows } = getSummaryReport();

    const settings: ReportSettings = {
      totals: false,
      details: false,
    };

    const myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      settings,
      null,
      getDefaultFilter(),
    );
    const report = await ReportService.createReport(myCustomReport, propertyId);
    const reportId = report.id;

    const response = await ReportService.getReport(reportId, propertyId);
    expect(response.custom_cdfs.length).toBe(0);

    // Create Custom CDF
    const customCdfName = 'room_id_abbreviated';
    const customCdf: CustomCdf = new CustomCdf(
      customCdfName,
      'Room Type ID + Abbreviation',
      [
        new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyV1CDF.ROOM_TYPE_ID),
        new CustomCdfFormula(CustomCdfFormulaKind.SEPARATOR, '-'),
        new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyV1CDF.ADULTS_COUNT),
      ],
      CustomCdfKind.STRING,
    );

    const customCdfResponse = await ReportService.createCustomCdf(reportId, propertyId, customCdf);
    expect(customCdfResponse).toBeTruthy();
    expect(customCdfResponse.name).toBe(customCdf.name);
    expect(customCdfResponse.kind).toBe(customCdf.kind);
    expect(customCdfResponse.formula.length).toBe(customCdf.formula.length);

    // Update Report to pull created Custom CDF
    const group_columns = [
      new GroupColumn(new Cdf(`custom_${customCdfName}`, CdfKind.CUSTOM)),
    ];
    const newReport = { ...myCustomReport, group_columns };

    const updatedReport = await ReportService.updateReport(reportId, newReport, propertyId);
    expect(updatedReport.group_columns.length).toBe(group_columns.length);
    expect(updatedReport.custom_cdfs.length).toBe(1);

    const customReportWithCustomCDFData = await ReportService.getReportData(reportId, propertyId);
    expect(customReportWithCustomCDFData.type).toBe('Pivot');

    // Delete created Report and Custom CDF
    await ReportService.deleteCustomCdf(reportId, customCdfResponse.id, propertyId);
    await ReportService.deleteReport(reportId, propertyId);
  });
});
