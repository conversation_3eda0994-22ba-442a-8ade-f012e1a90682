import { Guid } from 'guid-typescript';

import { Report } from '@/entities/report/report.model';
import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { Modifier } from '@/entities/report/enums/time.type';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';
import { Column } from '@/entities/report/columns/column.model';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { Cdf } from '@/entities/report/cdf/cdf.model';

import {
  getAdrDynamicCustomCdf,
  getDefaultFilter,
  getOccupancyDynamicCustomCdf,
  getPaceReport,
  getPmsOccupancyDynamicCustomCdf,
  getRevParDynamicCustomCdf,
  threeDaysBookingDatePeriods,
} from '../../common/data/occupancy_v1';

jest.retryTimes(2);
describe(`- A summary details report is a report based in data grouped by columns
- It is a report which allows users to group rows data
- A max of 3 Group Rows can be selected
- The columns don't need to be of type numeric and have metrics associated
- The Summary Report will show the data grouped by the selected columns`, () => {
  test('Create Occupancy V1 Pace Report By Day v1.1', async () => {
    const title = `Auto-OccupancyV1-${Guid.create()}`;
    const description = 'OccupancyV1 Grouped Report Created by SDK';
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.OCCUPANCY_V1;
    const { columns, groupRows, sorts } = getPaceReport(Modifier.DAY_WITHOUT_YEAR);
    const { periods } = threeDaysBookingDatePeriods();
    const filters = getDefaultFilter();

    const settings: ReportSettings = {
      totals: true,
      details: false,
    };

    const myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      settings,
      sorts,
      filters,
      periods,

    );

    let report = await ReportService.createReport(myCustomReport, propertyId);
    const reportId = report.id;
    const response = await ReportService.getReport(reportId, propertyId);

    // Verify Columns with Metrics
    expect(response.columns).toEqual(myCustomReport.columns);

    // Verify Group By Rows
    expect(response.group_rows).toEqual(myCustomReport.group_rows);

    // Verify Group By Columns
    expect(response.group_columns).toEqual(myCustomReport.group_columns);

    // Verify Report Metadata
    expect(response.title).toEqual(myCustomReport.title);
    expect(response.description).toEqual(myCustomReport.description);
    expect(response.dataset_id).toEqual(datasetId);
    expect(response.settings.totals).toEqual(myCustomReport.settings.totals);
    expect(response.settings.details).toEqual(myCustomReport.settings.details);
    expect(response.property_id).toEqual(propertyId);
    expect(response.property_ids).toEqual(myCustomReport.property_ids);

    const customOccupancyCdf = await ReportService.createCustomCdf(reportId, propertyId, getOccupancyDynamicCustomCdf());
    const customPmsOccupancyCdf = await ReportService.createCustomCdf(reportId, propertyId, getPmsOccupancyDynamicCustomCdf());
    const customRevParCdf = await ReportService.createCustomCdf(reportId, propertyId, getRevParDynamicCustomCdf());
    const customAdrCdfCdf = await ReportService.createCustomCdf(reportId, propertyId, getAdrDynamicCustomCdf());

    const newColumns = [
      ...report.columns,
      new Column(new Cdf(customOccupancyCdf.column, CdfKind.CUSTOM)),
      new Column(new Cdf(customPmsOccupancyCdf.column, CdfKind.CUSTOM)),
      new Column(new Cdf(customRevParCdf.column, CdfKind.CUSTOM)),
      new Column(new Cdf(customAdrCdfCdf.column, CdfKind.CUSTOM)),
    ];

    myCustomReport.columns = newColumns;
    report = await ReportService.updateReport(reportId, myCustomReport, propertyId);
    expect(report.columns).toEqual(myCustomReport.columns);

    // Get Report Data
    const data = await ReportService.getReportData(reportId, propertyId);
    expect(data.type).toBe('PeriodSummary');
    if (isNotEmptyObject(data.records)) {
      expect(data.group_rows.length).toEqual(Object.keys(groupRows).length);
    }

    // Delete created Report
    await ReportService.deleteReport(reportId, propertyId);
  });
});
