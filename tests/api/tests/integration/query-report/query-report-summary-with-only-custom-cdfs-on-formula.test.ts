import { getDefaultFilter } from 'tests/common/data/financial';
import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { ReportQueryData } from '@/entities/report/reportQueryData.model';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { FinancialCDF } from '@/entities/datasets/financial.cdf';
import { MetricType } from '@/entities/report/enums/metric.type';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { CustomCdfFormula } from '@/entities/report/customCdf/customCdfFormula.model';
import { Mode } from '@/entities/report/enums/mode.type';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';
import { OperatorType } from '@/entities/report/enums/operator.type';

jest.retryTimes(2);
describe('Report Query Data', () => {
  test('Query a report with only custom cdfs on the formula', async () => {
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.FINANCIAL;

    const settings: ReportSettings = {
      totals: true,
      details: false,
    };

    const report = new ReportQueryData(
      propertyIds,
      datasetId,
      [
        new Column(new Cdf(FinancialCDF.BOOKING_DATETIME)),
        new Column(new Cdf(FinancialCDF.DEBIT_AMOUNT), [MetricType.SUM]),
        new Column(new Cdf('custom_debit_converted', CdfKind.CUSTOM)),
      ],
      [
        new GroupRow(new Cdf('custom_test', CdfKind.CUSTOM)),
        new GroupRow(new Cdf('custom_rate', CdfKind.CUSTOM)),
      ],
      null,
      settings,
      null,
      getDefaultFilter(),
      [
        new CustomCdf('test', 'Test Custom CDF', [new CustomCdfFormula(CustomCdfFormulaKind.SEPARATOR, 'test')], CustomCdfKind.STRING),
        new CustomCdf('rate', 'Rate Custom CDF', [
          new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.CONVERSION_RATE),
          new CustomCdfFormula(CustomCdfFormulaKind.SEPARATOR, ' - Rate'),
        ], CustomCdfKind.STRING),
        new CustomCdf('debit_converted', 'Debit Converted', [
          new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.DEBIT_AMOUNT_CONVERTED_RATE),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, OperatorType.sub),
          new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.CREDIT_AMOUNT_CONVERTED_RATE),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, OperatorType.sub),
          new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.CONVERSION_RATE),
        ], CustomCdfKind.NUMBER),
      ],
    );

    const response = await ReportService.getReportQueryData(report, propertyId, Mode.PREVIEW);
    expect(response.headers.length).toBeGreaterThanOrEqual(0);
    expect(response.type).toBe('Summary');
    if (isNotEmptyObject(response.records)) {
      expect(response.index[0][0]).toBe('test');
    }
  });
});
