import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportQueryData } from '@/entities/report/reportQueryData.model';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Mode } from '@/entities/report/enums/mode.type';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { OccupancyV1CDF } from '@/entities/datasets/occupancy_v1.cdf';
import { NumericFormat } from '@/entities/report/enums/numeric.format.type';
import { getAdrDynamicCustomCdf, getCustomDynamicCdfDivideByZero, getCustomDynamicCdfDoubleNestedNumeratorAndDenominator, getCustomDynamicCdfNestedDenominator, getCustomDynamicCdfNestedNumeratorAndDenominator, getDefaultFilter, getOccupancyDynamicCustomCdf, getPmsOccupancyDynamicCustomCdf, getRevParDynamicCustomCdf } from 'tests/common/data/occupancy_v1';
import { DYNAMIC_CDF_LABEL } from 'tests/common/constants/dynamic-cdf-label';

jest.retryTimes(2);
describe('Report Query Data', () => {
  test('Query Occupancy V1 With occupancy, revpar, mfd_occupancy and compare against dynamic custom cdf with same formula', async () => {
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.OCCUPANCY_V1;

    const report = new ReportQueryData(
      propertyIds,
      datasetId,
      [
        new Column(new Cdf(OccupancyV1CDF.MFD_OCCUPANCY, CdfKind.DEFAULT)),
        new Column(new Cdf('custom_pms_occupancy', CdfKind.CUSTOM)),
        new Column(new Cdf(OccupancyV1CDF.OCCUPANCY, CdfKind.DEFAULT)),
        new Column(new Cdf('custom_occupancy', CdfKind.CUSTOM)),
        new Column(new Cdf(OccupancyV1CDF.REVPAR, CdfKind.DEFAULT)),
        new Column(new Cdf('custom_revpar', CdfKind.CUSTOM)),
        new Column(new Cdf(OccupancyV1CDF.ADR, CdfKind.DEFAULT)),
        new Column(new Cdf('custom_adr', CdfKind.CUSTOM)),
        new Column(new Cdf('custom_nested_denominator', CdfKind.CUSTOM)),
        new Column(new Cdf('custom_nested_numerator_and_denominator', CdfKind.CUSTOM)),
        new Column(new Cdf('custom_double_nested_numerator_and_denominator', CdfKind.CUSTOM)),
        new Column(new Cdf('custom_divide_by_zero', CdfKind.CUSTOM)),

      ],
      [
        new GroupRow(new Cdf(OccupancyV1CDF.STAY_DATE), Modifier.YEAR),
      ],
      null,
      { totals: false, details: false },
      null,
      getDefaultFilter(),
      [
        getPmsOccupancyDynamicCustomCdf(),
        getOccupancyDynamicCustomCdf(),
        getRevParDynamicCustomCdf(),
        getAdrDynamicCustomCdf(),
        getCustomDynamicCdfNestedDenominator(),
        getCustomDynamicCdfNestedNumeratorAndDenominator(),
        getCustomDynamicCdfDoubleNestedNumeratorAndDenominator(),
        getCustomDynamicCdfDivideByZero(),
      ],
    );

    const rawResponse = await ReportService.getReportQueryData(report, propertyId, Mode.PREVIEW, NumericFormat.RAW);

    if (Object.keys(rawResponse.records).length > 0) {
      expect(rawResponse.headers.length).toBeGreaterThanOrEqual(0);

      expect(rawResponse.headers[0][0]).toBe('mfd_occupancy');
      expect(rawResponse.headers[1][0]).toBe('custom_pms_occupancy');
      expect(rawResponse.headers[2][0]).toBe('occupancy');
      expect(rawResponse.headers[3][0]).toBe('custom_occupancy');
      expect(rawResponse.headers[4][0]).toBe('revpar');
      expect(rawResponse.headers[5][0]).toBe('custom_revpar');
      expect(rawResponse.headers[6][0]).toBe('adr');
      expect(rawResponse.headers[7][0]).toBe('custom_adr');
      expect(rawResponse.headers[8][0]).toBe('custom_nested_denominator');
      expect(rawResponse.headers[9][0]).toBe('custom_nested_numerator_and_denominator');
      expect(rawResponse.headers[10][0]).toBe('custom_double_nested_numerator_and_denominator');
      expect(rawResponse.headers[11][0]).toBe('custom_divide_by_zero');

      const rawRecords = rawResponse.records;

      const rawDates = Object.keys(rawRecords);
      for (const rawDate of rawDates) {
        const rawRecord = rawRecords[rawDate];
        expect(rawRecord.mfd_occupancy[DYNAMIC_CDF_LABEL]).toBeCloseTo(rawRecord.custom_pms_occupancy[DYNAMIC_CDF_LABEL], 12);
        expect(rawRecord.occupancy[DYNAMIC_CDF_LABEL]).toBeCloseTo(rawRecord.custom_occupancy[DYNAMIC_CDF_LABEL], 12);
        expect(rawRecord.revpar[DYNAMIC_CDF_LABEL]).toEqual(rawRecord.custom_revpar[DYNAMIC_CDF_LABEL]);
        expect(rawRecord.adr[DYNAMIC_CDF_LABEL]).toEqual(rawRecord.custom_adr[DYNAMIC_CDF_LABEL]);
        expect(rawRecord.custom_nested_denominator[DYNAMIC_CDF_LABEL]).toEqual(1);
        expect(rawRecord.custom_nested_numerator_and_denominator[DYNAMIC_CDF_LABEL]).toEqual(0.6666666666666666);
        expect(rawRecord.custom_double_nested_numerator_and_denominator[DYNAMIC_CDF_LABEL]).toEqual(2.4685714285714284);
        expect(rawRecord.custom_divide_by_zero[DYNAMIC_CDF_LABEL]).toEqual('-');
      }
    }

    expect(rawResponse.type).toBe('Summary');

    const formattedResponse = await ReportService.getReportQueryData(report, propertyId, Mode.PREVIEW, NumericFormat.FORMATTED);

    if (Object.keys(formattedResponse.records).length > 0) {
      expect(formattedResponse.headers.length).toBeGreaterThanOrEqual(0);

      expect(formattedResponse.headers[0][0]).toBe('mfd_occupancy');
      expect(formattedResponse.headers[1][0]).toBe('custom_pms_occupancy');
      expect(formattedResponse.headers[2][0]).toBe('occupancy');
      expect(formattedResponse.headers[3][0]).toBe('custom_occupancy');
      expect(formattedResponse.headers[4][0]).toBe('revpar');
      expect(formattedResponse.headers[5][0]).toBe('custom_revpar');
      expect(formattedResponse.headers[6][0]).toBe('adr');
      expect(formattedResponse.headers[7][0]).toBe('custom_adr');
      expect(formattedResponse.headers[8][0]).toBe('custom_nested_denominator');
      expect(formattedResponse.headers[9][0]).toBe('custom_nested_numerator_and_denominator');
      expect(formattedResponse.headers[10][0]).toBe('custom_double_nested_numerator_and_denominator');
      expect(formattedResponse.headers[11][0]).toBe('custom_divide_by_zero');

      const formattedRecords = formattedResponse.records;

      const formattedDates = Object.keys(formattedRecords);
      formattedDates.forEach((formattedDate: any) => {
        const formattedRecord = formattedRecords[formattedDate];
        expect(formattedRecord.mfd_occupancy[DYNAMIC_CDF_LABEL]).toEqual(formattedRecord.custom_pms_occupancy[DYNAMIC_CDF_LABEL]);
        expect(formattedRecord.occupancy[DYNAMIC_CDF_LABEL]).toEqual(formattedRecord.custom_occupancy[DYNAMIC_CDF_LABEL]);
        expect(formattedRecord.revpar[DYNAMIC_CDF_LABEL]).toEqual(formattedRecord.custom_revpar[DYNAMIC_CDF_LABEL]);
        if (formattedRecord.adr[DYNAMIC_CDF_LABEL] === '-') {
          expect(formattedRecord.custom_adr[DYNAMIC_CDF_LABEL]).toEqual('-');
        } else {
          expect(Number(formattedRecord.adr[DYNAMIC_CDF_LABEL].replace(/,/g, ''))).toBeCloseTo(Number(formattedRecord.custom_adr[DYNAMIC_CDF_LABEL].replace(/,/g, '')), 3);
        }
        expect(formattedRecord.custom_nested_denominator[DYNAMIC_CDF_LABEL]).toEqual('1.00');
        expect(formattedRecord.custom_nested_numerator_and_denominator[DYNAMIC_CDF_LABEL]).toEqual('0.67');
        expect(formattedRecord.custom_double_nested_numerator_and_denominator[DYNAMIC_CDF_LABEL]).toEqual('2.47');
        expect(formattedRecord.custom_divide_by_zero[DYNAMIC_CDF_LABEL]).toEqual('-');
      });
    }

    expect(formattedResponse.type).toBe('Summary');
  });
});
