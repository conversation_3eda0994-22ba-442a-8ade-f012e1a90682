import { CdfKind } from '@/entities/report/enums/cdf.type';
import { CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { MetricType } from '@/entities/report/enums/metric.type';
import { Mode } from '@/entities/report/enums/mode.type';
import { NumericFormat } from '@/entities/report/enums/numeric.format.type';
import { SortType } from '@/entities/report/enums/sort.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { ReportQueryData } from '@/entities/report/reportQueryData.model';
import { Config, ReportService } from '@/index';

describe('- Query Pivot Report Grouped by Custom Dynamic CDF', () => {
  test('Query Pivot Report Grouped by Custom Dynamic CDF', async () => {
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;

    const jsonBody = new ReportQueryData(
      [propertyId],
      3,
      [
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'reservation_number',
            multi_level_id: undefined,
          },
          metrics: undefined,
          multi_level_id: undefined,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'primary_guest_full_name',
            multi_level_id: undefined,
          },
          metrics: undefined,
          multi_level_id: undefined,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'room_nights_count',
            multi_level_id: undefined,
          },
          metrics: [MetricType.SUM],
          multi_level_id: undefined,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'guest_count',
            multi_level_id: undefined,
          },
          metrics: [MetricType.SUM],
          multi_level_id: undefined,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'checkin_date',
            multi_level_id: undefined,
          },
          metrics: undefined,
          multi_level_id: undefined,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'checkout_date',
            multi_level_id: undefined,
          },
          metrics: undefined,
          multi_level_id: undefined,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'reservation_status',
            multi_level_id: undefined,
          },
          metrics: undefined,
          multi_level_id: undefined,
        },
      ],
      [
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'booking_datetime_property_timezone',
            multi_level_id: undefined,
          },
          modifier: Modifier.MONTH_WITHOUT_YEAR,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'cancellation_datetime',
            multi_level_id: undefined,
          },
          modifier: Modifier.WEEK,
        },
      ],
      [
        {
          cdf: {
            type: CdfKind.CUSTOM,
            column: 'custom_dyn',
            multi_level_id: undefined,
          },
          modifier: undefined,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'primary_guest_document_issuing_country',
            multi_level_id: undefined,
          },
          modifier: undefined,
        },
      ],
      {
        details: false,
        totals: false,
        transpose: false,
      },
      [
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'booking_datetime_property_timezone',
            multi_level_id: undefined,
          },
          direction: SortType.ASC,
        },
        {
          cdf: {
            type: CdfKind.DEFAULT,
            column: 'cancellation_datetime',
            multi_level_id: undefined,
          },
          direction: SortType.ASC,
        },
      ],

      null,
      [
        {
          description: '',
          formula: [
            {
              kind: CustomCdfFormulaKind.CDF,
              value: 'adults_count',
              metric: MetricType.SUM,
            },
            {
              kind: CustomCdfFormulaKind.OPERATOR,
              value: '+',
              metric: undefined,
            },
            {
              kind: CustomCdfFormulaKind.OPERAND,
              value: '10',
              metric: undefined,
            },
          ],
          kind: CustomCdfKind.DYNAMIC,
          name: 'Dyn',
        },
        {
          description: 'test',
          formula: [
            {
              kind: CustomCdfFormulaKind.CDF,
              value: 'adults_count',
              metric: MetricType.SUM,
            },
            {
              kind: CustomCdfFormulaKind.OPERATOR,
              value: '+',
              metric: undefined,
            },
            {
              kind: CustomCdfFormulaKind.CDF,
              value: 'adults_count',
              metric: 'mean',
            },
          ],
          kind: CustomCdfKind.DYNAMIC,
          name: 'test',
        },
      ],
      null,
      {
        date: 'MM-DD-YYYY',
        link: false,
      },
      [],
      null,
    );

    const data = ReportService.getReportQueryData(jsonBody, propertyId, Mode.PAGE, NumericFormat.FORMATTED, undefined, 0, 10);
    expect(data).toBeDefined();
  });
});
