import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config, XLSXService, PropertyService } from '@/index';
import { ReportQueryData } from '@/entities/report/reportQueryData.model';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Mode } from '@/entities/report/enums/mode.type';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { OccupancyV1CDF } from '@/entities/datasets/occupancy_v1.cdf';
import { NumericFormat } from '@/entities/report/enums/numeric.format.type';
import { getDefaultFilter, getTwoThirdsCustomCdf } from 'tests/common/data/occupancy_v1';
import { MetricType } from '@/entities/report/enums/metric.type';
import { CustomCdfFormat, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { ReportQueryExport } from '@/entities/report/reportQueryExport.model';
import { ExportFormatType } from '@/entities/report/enums/export.format.type';
import { DYNAMIC_CDF_LABEL } from 'tests/common/constants/dynamic-cdf-label';
import { ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE } from 'tests/common/constants/all-records-displayed-success-message';
import { Sort } from '@/entities/report/sort/sort.model';
import { SortType } from '@/entities/report/enums/sort.type';

jest.retryTimes(2);
describe('Report Query Export Data', () => {
  test('Query Export Occupancy V1 With different kinds of number cdfs, validate that the proper decimals are in the export group by week', async () => {
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.OCCUPANCY_V1;

    const settingsResponse = await PropertyService.getPropertySettings(propertyId);
    const currencyRoundingPrecision = settingsResponse.find((setting: any) => setting.name === 'currency_cdf_rounding_precision').value;

    let roundedTwoThirds: number;

    switch (currencyRoundingPrecision) {
      case '0':
        roundedTwoThirds = 67;
        break;
      case '1':
        roundedTwoThirds = 66.7;
        break;
      case '2':
        roundedTwoThirds = 66.67;
        break;
      case '3':
        roundedTwoThirds = 66.667;
        break;
      case '4':
        roundedTwoThirds = 66.6667;
        break;
    }

    const report = new ReportQueryData(
      propertyIds,
      datasetId,
      [
        new Column(new Cdf(OccupancyV1CDF.ROOM_GUEST_COUNT, CdfKind.DEFAULT), Object.values(MetricType)),
        new Column(new Cdf(OccupancyV1CDF.ROOM_REVENUE, CdfKind.DEFAULT), Object.values(MetricType)),
        new Column(new Cdf(OccupancyV1CDF.OCCUPANCY, CdfKind.DEFAULT)),
        new Column(new Cdf(OccupancyV1CDF.ADR, CdfKind.DEFAULT)),
        new Column(new Cdf(OccupancyV1CDF.REVPAR, CdfKind.DEFAULT)),
        new Column(new Cdf('custom_number_number', CdfKind.CUSTOM), Object.values(MetricType)),
        new Column(new Cdf('custom_number_percentage', CdfKind.CUSTOM), Object.values(MetricType)),
        new Column(new Cdf('custom_number_currency', CdfKind.CUSTOM), Object.values(MetricType)),
        new Column(new Cdf('custom_dynamic_number', CdfKind.CUSTOM)),
        new Column(new Cdf('custom_dynamic_percentage', CdfKind.CUSTOM)),
        new Column(new Cdf('custom_dynamic_currency', CdfKind.CUSTOM)),

      ],
      [
        new GroupRow(new Cdf(OccupancyV1CDF.STAY_DATE), Modifier.WEEK),
      ],
      null,
      { totals: true, details: false },
      [new Sort(new Cdf(OccupancyV1CDF.STAY_DATE), SortType.ASC)],
      getDefaultFilter(),
      [
        getTwoThirdsCustomCdf(CustomCdfKind.NUMBER, CustomCdfFormat.NUMBER),
        getTwoThirdsCustomCdf(CustomCdfKind.NUMBER, CustomCdfFormat.PERCENTAGE),
        getTwoThirdsCustomCdf(CustomCdfKind.NUMBER, CustomCdfFormat.CURRENCY),
        getTwoThirdsCustomCdf(CustomCdfKind.DYNAMIC, CustomCdfFormat.NUMBER),
        getTwoThirdsCustomCdf(CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE),
        getTwoThirdsCustomCdf(CustomCdfKind.DYNAMIC, CustomCdfFormat.CURRENCY),
      ],
    );

    const formattedResponse = await ReportService.getReportQueryData(report, propertyId, Mode.PREVIEW, NumericFormat.FORMATTED);

    if (Object.keys(formattedResponse.records).length > 0) {
      expect(formattedResponse.headers.length).toBeGreaterThanOrEqual(0);

      const formattedRecords = formattedResponse.records;

      const formattedDates = Object.keys(formattedRecords);
      formattedDates.forEach((formattedDate: any) => {
        const formattedRecord = formattedRecords[formattedDate];
        expect(formattedRecord.custom_dynamic_currency[DYNAMIC_CDF_LABEL]).toEqual(String(roundedTwoThirds));
        expect(formattedRecord.custom_dynamic_number[DYNAMIC_CDF_LABEL]).toEqual('66.67');
        expect(formattedRecord.custom_dynamic_percentage[DYNAMIC_CDF_LABEL]).toEqual('66.67');
      });

      // const exportReport = {...report, title: 'Test Export'};
      const exportReport = new ReportQueryExport(
        'test',
        propertyIds,
        report.dataset_id,
        report.columns,
        report.group_rows,
        report.group_columns,
        report.settings,
        report.sort,
        report.filters,
        report.custom_cdfs,
        report.periods,
        report.formats,

      );
      const exportedReport = await ReportService.exportByQuery(exportReport, true, ExportFormatType.XLSX, propertyId);

      expect(exportedReport);
      const workbookJson = await XLSXService.readExcelFromUrl(exportedReport.url);
      expect(workbookJson[2].__EMPTY).toBe(ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE);
      expect(workbookJson[workbookJson.length - 1].__EMPTY).toBe('Filters');
      expect(workbookJson[workbookJson.length - 1].__EMPTY_1).toBe('Stay Date >= Start of Last Month AND Stay Date < Start of Next Week');

      // helper function to put the right column names on the data, our excel files can be disordered when formatted
      function mapColumnNames(data: any[], columnNames: any): any[] {
        // Use slice to avoid modifying the column names entry
        return data.slice(4, -1).map(entry => {
          const mappedEntry: { [key: string]: any } = {};
          Object.keys(entry).forEach(key => {
            // Use the column name from columnNames for the key if available
            const newKey = columnNames[key] || key;
            mappedEntry[newKey] = entry[key];
          });
          return mappedEntry;
        });
      }

      function countDecimalPlaces(value: number): number {
        if (Number.isInteger(value)) {
          return 0;
        }

        const valueStr = value.toString();

        // Check if the number is in scientific notation
        if (valueStr.includes('e-')) {
          const [, exponent] = valueStr.split('e-');
          const exponentValue = parseInt(exponent);
          // The exponent in scientific notation directly indicates the number of zeros
          return exponentValue;
        } if (valueStr.includes('e')) {
          // Handle positive exponent in scientific notation (rare for counting decimals but for completeness)
          const [base, exponent] = valueStr.split('e');
          const decimalPlacesInBase = base.includes('.') ? base.split('.')[1].length : 0;
          const exponentValue = parseInt(exponent);
          return Math.max(0, decimalPlacesInBase - exponentValue);
        }

        // Standard decimal handling for non-scientific notation numbers
        const parts = valueStr.split('.');
        return parts[1] ? parts[1].length : 0;
      }

      // Getting the column names from workbookJson[3]
      const columnNames = workbookJson[3];

      // Applying the mapping function to the workbookJson
      const processedData = mapColumnNames(workbookJson, columnNames);

      function parseStartDate(stayDateString: string) {
        if (stayDateString == '-') {
          return Number(-1);
        }
        const truncatedDateStr: string = stayDateString.substring(0, 12);
        const date = new Date(truncatedDateStr);
        return Number(date.getTime());
      }

      let previousTimestamp = 0;

      processedData.forEach((entry: any) => {
        const startTimestamp = parseStartDate(entry['Stay Date']);
        expect(startTimestamp).not.toBeNull();
        if (startTimestamp > 0) {
          expect(startTimestamp).toBeGreaterThanOrEqual(previousTimestamp);
        }
        previousTimestamp = startTimestamp;
        expect(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`]).toEqual(roundedTwoThirds);
        expect(entry[`Dynamic_number - ${DYNAMIC_CDF_LABEL}`]).toEqual(66.67);
        expect(entry[`Dynamic_percentage - ${DYNAMIC_CDF_LABEL}`]).toEqual(66.67);
        expect(entry['Number_currency - max']).toEqual(roundedTwoThirds);
        expect(entry['Number_currency - mean']).toEqual(roundedTwoThirds);
        expect(entry['Number_currency - min']).toEqual(roundedTwoThirds);
        expect(countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry[`Dynamic_number - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry[`Dynamic_percentage - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_currency - count'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Number_currency - max'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Number_currency - mean'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Number_currency - min'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Number_currency - sum'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Number_currency - std'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Number_currency - var'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));

        expect(countDecimalPlaces(entry['Total Room Revenue - count'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Total Room Revenue - max'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Total Room Revenue - mean'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Total Room Revenue - min'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Total Room Revenue - sum'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Total Room Revenue - std'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry['Total Room Revenue - var'])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));

        expect(countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));

        expect(countDecimalPlaces(entry['Number_number - count'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_number - max'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_number - mean'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_number - min'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_number - sum'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_number - std'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_number - var'])).toBeLessThanOrEqual(2);

        expect(countDecimalPlaces(entry['Number_percentage - count'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_percentage - max'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_percentage - mean'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_percentage - min'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_percentage - sum'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_percentage - std'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Number_percentage - var'])).toBeLessThanOrEqual(2);

        expect(countDecimalPlaces(entry['Room Guest Count - count'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Room Guest Count - max'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Room Guest Count - mean'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Room Guest Count - min'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Room Guest Count - sum'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Room Guest Count - std'])).toBeLessThanOrEqual(2);
        expect(countDecimalPlaces(entry['Room Guest Count - var'])).toBeLessThanOrEqual(2);

        expect(countDecimalPlaces(entry[`ADR - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry[`RevPAR - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(countDecimalPlaces(entry[`Occupancy - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(2);
      });
    }
    expect(formattedResponse.type).toBe('Summary');
  });
});
