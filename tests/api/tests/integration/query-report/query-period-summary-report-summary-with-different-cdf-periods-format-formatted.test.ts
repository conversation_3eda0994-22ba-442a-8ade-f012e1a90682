import { getDefaultFilter, getPeriodSummaryReportDifferentCdfs } from 'tests/common/data/financial';
import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportQueryData } from '@/entities/report/reportQueryData.model';
import { Mode } from '@/entities/report/enums/mode.type';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';

jest.retryTimes(2);
describe('Report Query Period Summary Report Data Different CDF Periods', () => {
  test('Query a Period Sumary Report that contains metrics', async () => {
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.FINANCIAL;

    const { columns, groupRows, periods, sorts } = getPeriodSummaryReportDifferentCdfs();

    const report = new ReportQueryData(
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      { totals: false, details: false },
      sorts,
      getDefaultFilter(),
      null,
      periods,
    );

    const response = await ReportService.getReportQueryData(report, propertyId, Mode.RUN);

    expect(response.headers.length).toBeGreaterThanOrEqual(0);
    expect(response.index.length).toBeGreaterThanOrEqual(0);
    expect(response.type).toBe('PeriodSummary');
    if (isNotEmptyObject(response.records)) {
      const expectedPeriods = periods.map((period: any) => period.name);
      const expectedHeaders: string[][] = [];

      // Build the expected headers dynamically
      expectedPeriods.forEach((periodName: string) => {
        columns.forEach((col: any) => {
          col.metrics.forEach((metric: string) => {
            expectedHeaders.push([periodName, col.cdf.column, metric]);
          });
        });
      });

      // Now assert each expected header matches the response
      expect(response.headers.length).toEqual(expectedHeaders.length);

      expectedHeaders.forEach((expectedHeader, index) => {
        expect(response.headers[index]).toEqual(expectedHeader);
      });
    }
  });
});
