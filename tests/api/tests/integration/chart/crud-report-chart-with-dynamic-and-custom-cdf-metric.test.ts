import { Guid } from 'guid-typescript';

import { Report } from '@/entities/report/report.model';
import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { Chart } from '@/entities/chart/chart.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { ChartKind } from '@/entities/report/enums/chartKind.type';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { Column } from '@/entities/report/columns/column.model';
import { ChartMetric } from '@/entities/chart/chartMetric.model';
import { OccupancyCDF } from '@/entities/datasets/occupancy.cdf';
import { ChartAxis } from '@/entities/chart/enums/chartAxis';
import { ChartCategory } from '@/entities/chart/chartCategory.model';
import { OccupancyV1CDF } from '@/entities/datasets/occupancy_v1.cdf';
import { Modifier } from '@/entities/report/enums/time.type';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { getPmsOccupancyDynamicCustomCdf } from 'tests/common/data/occupancy_v1';

jest.retryTimes(2);
describe('- Create a report with 1 group row and a custom cdf and dynamic cdf, then make a chart, delete the chart and the report,', () => {
  test('CRUD Report Charts v1.1', async () => {
    const title = `Auto-Reservation-${Guid.create()}`;
    const description = 'Reservation Report Created by SDK';
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.OCCUPANCY_V1;

    const columns = [
      new Column(new Cdf(OccupancyV1CDF.ADR, CdfKind.DEFAULT)),
    ];
    const groupRows = [
      new GroupRow(new Cdf(OccupancyV1CDF.STAY_DATE, CdfKind.DEFAULT), Modifier.MONTH),
    ];
    const settings: ReportSettings = {
      totals: false,
      details: false,
    };

    const myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      settings,
      null,
      null,
      null,
    );

    const report = await ReportService.createReport(myCustomReport, propertyId);
    const reportId = report.id;

    // Add Custom CDF
    const customCdf: CustomCdf = getPmsOccupancyDynamicCustomCdf();
    const customCdfName = customCdf.name;

    const customCdfResponse = await ReportService.createCustomCdf(reportId, propertyId, customCdf);
    expect(customCdfResponse).toBeTruthy();
    expect(customCdfResponse.name).toBe(customCdf.name);
    expect(customCdfResponse.kind).toBe(customCdf.kind);
    expect(customCdfResponse.formula.length).toBe(customCdf.formula.length);

    // Update Report to pull created Custom CDF
    const newColumns = [...columns, new Column(new Cdf(`custom_${customCdfName}`, CdfKind.CUSTOM))];
    const newReport = { ...myCustomReport, columns: newColumns };

    const updatedReport = await ReportService.updateReport(reportId, newReport, propertyId);
    expect(updatedReport.columns.length).toBe(newColumns.length);
    expect(updatedReport.custom_cdfs.length).toBe(1);

    // Get Report Data
    const data = await ReportService.getReportData(reportId, propertyId);
    expect(data.type).toBe('Summary');

    // Verify Group Rows if Data is Returned
    if (isNotEmptyObject(data.records)) {
      expect(data.group_rows.length).toEqual(Object.keys(groupRows).length);
      expect(data.headers[0][0]).toEqual(OccupancyV1CDF.ADR);
      expect(data.headers[1][0]).toEqual(`custom_${customCdfName}`);
    }

    // Create chart
    const chartCategories = [
      new ChartCategory(new Cdf(OccupancyV1CDF.STAY_DATE), Modifier.MONTH),
    ];
    const chartSettings = {
      toolbox: false,
      legend: false,
      metrics: [
        {
          cdf: new Cdf(OccupancyCDF.ADR),
          kind: ChartKind.Line,
          axis: ChartAxis.Left,
        },
        {
          cdf: new Cdf(`custom_${customCdfName}`, CdfKind.CUSTOM),
          kind: ChartKind.Bar,
          axis: ChartAxis.Right,
        },
      ],
    };

    const chartMetrics = [
      new ChartMetric(new Cdf(OccupancyV1CDF.ADR)),
      new ChartMetric(new Cdf(`custom_${customCdfName}`, CdfKind.CUSTOM)),
    ];

    const comboChart = new Chart(
      'My Cool Combo Chart',
      ChartKind.Combo,
      chartSettings,
      chartCategories,
      chartMetrics,
    );

    const chart = await ReportService.createReportChart(comboChart, reportId, propertyId);
    const chartId = chart.id;

    expect(chart.kind).toBe(ChartKind.Combo);

    // Delete created Report
    await ReportService.deleteReportChart(chartId, reportId, propertyId);
    const charts = await ReportService.getReportCharts(reportId, propertyId);
    expect(charts.length).toBe(0);
    await ReportService.deleteReport(reportId, propertyId);
  });
});
