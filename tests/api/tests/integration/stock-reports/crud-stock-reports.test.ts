import { log } from 'console';

import { Guid } from 'guid-typescript';

import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config, MeService, StockReportFolderService } from '@/index';
import { Rules, StockReportPostBody } from '@/entities/report/settings/rules';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { FinancialCDF } from '@/entities/datasets/financial.cdf';
import { MetricType } from '@/entities/report/enums/metric.type';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Report } from '@/entities/report/report.model';
import { StockReportQuery } from '@/entities/report/stockReportQuery.model';
import { getDefaultPeriods } from 'tests/common/data/financial';
import { delay } from 'tests/common/utils/time';
import { CustomCdfFormula } from '@/entities/report/customCdf/customCdfFormula.model';
import { CustomCdfFormat, CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { OperatorType } from '@/entities/report/enums/operator.type';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { ChartSettings } from '@/entities/chart/chartSettings';
import { Chart } from '@/entities/chart/chart.model';
import { ChartMetric } from '@/entities/chart/chartMetric.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Modifier } from '@/entities/report/enums/time.type';
import { ChartCategory } from '@/entities/chart/chartCategory.model';
import { Sort } from '@/entities/report/sort/sort.model';
import { SortType } from '@/entities/report/enums/sort.type';
import { ReportClone } from '@/entities/report/reportClone.model';
import { ChartKind } from '@/entities/report/enums/chartKind.type';
import { ReportKind } from '@/entities/report/enums/report_kind.type';
import { ChartSearchColumns } from '@/entities/report/enums/chartSearchColumns.type';
import { MAX_STOCK_REPORT_REVISION } from 'tests/common/constants/limits';

jest.retryTimes(2);
describe('- Stock Reports are reports provided by Cloudbeds', () => {
  test('Create Get Update and Delete Stock Reports', async () => {
    const crudActions = ['create', 'delete', 'read', 'update'];
    const description = 'Financial Stock Report Created by SDK';
    const datasetId = Dataset.FINANCIAL;
    const originalName = `Auto-StockReport-OriginalName-${Guid.create()}`;
    const updatedName = `Auto-StockReport-UpdatedName-${Guid.create()}`;
    const lineChartTitle = `Auto-Line-Chart-${Guid.create()}`;
    const barChartTitle = `Auto-Bar-Chart-${Guid.create()}`;

    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const settings: ReportSettings = {
      totals: false,
      details: false,
    };

    const propertyIds = [propertyId];
    const associationId = Config.ASSOCIATIONS[propertyId];
    if (associationId !== undefined) {
      propertyIds.push(associationId);
    }
    const meProperties = await MeService.getCurrentUserProperties(propertyId);
    const countryCode = meProperties.find((property: any) => property.id === propertyId).country_code;
    const countryCodes = ['IT', 'TH'];
    if (!countryCodes.includes(countryCode)) {
      countryCodes.push(countryCode);
    }
    const features = ['report_builder', 'cb_websites', 'mybookings_enabled', 'cb_amplify_dashboard', 'availability_enabled',
      'cashiering_system_enabled', 'cb_payments_stripe_enabled', 'hardware_terminal', 'house_keeping_enabled', 'invoicing_enabled',
      'settings_multi_currency', 'myallocator_enabled', 'pie_enabled', 'revenue_allocation', 'shared_inventory_enabled',
      'split_inventory', 'whistle_enabled'];
    const propertyTypes = ['hotel', 'resort', 'bed_and_breakfast', 'ranch', 'flat_hotel', 'hostel', 'motel', 'boutique', 'guest_house',
      'vacation_rental', 'chain', 'outdoor_lodge', 'rv_park', 'campground'];
    const rules: Rules = {
      feature_ids: features,
      property_ids: propertyIds,
      country_codes: countryCodes,
      property_types: propertyTypes,
    };
    // check logged in user has crud access if they dont, skip crud tests
    const userPolicies = await MeService.getCurrentUserPolicies(propertyId);

    if (userPolicies.filter((policy: any) => policy.resource === ReportKind.StockReport).length > 0
      && JSON.stringify(userPolicies.filter((policy: any) => policy.resource === ReportKind.StockReport)[0].actions) === JSON.stringify(crudActions)) {
    // Build a list of reports
      const { periods } = getDefaultPeriods();
      const reportColumns = [
        new Column(new Cdf(FinancialCDF.CREDIT_AMOUNT), [MetricType.SUM]),
        new Column(new Cdf(FinancialCDF.DEBIT_AMOUNT), [MetricType.SUM]),
      ];

      const reportGroupRows = [
        new GroupRow(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), Modifier.MONTH),
      ];

      const sorts = [new Sort(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), SortType.DESC)];

      const report = new Report(
        originalName,
        description,
        propertyId,
        propertyIds,
        datasetId,
        reportColumns,
        reportGroupRows,
        null,
        settings,
        sorts,
        null,
        periods,
      );

      // Create a report
      await ReportService.authenticateService();
      const createdReport = await ReportService.createReport(report, propertyId);
      const reportId = createdReport.id;

      // Create Custom CDF
      const customCdfName = 'balance';
      const customCdf: CustomCdf = new CustomCdf(
        customCdfName,
        'Balance math formula',
        [
          new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.CREDIT_AMOUNT),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, OperatorType.sub),
          new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.CREDIT_AMOUNT),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, OperatorType.add),
          new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '10'),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, OperatorType.mul),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '0.2'),
          new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
        ],
        CustomCdfKind.NUMBER,
        CustomCdfFormat.NUMBER,
      );

      const customCdfResponse = await ReportService.createCustomCdf(reportId, propertyId, customCdf);
      expect(customCdfResponse).toBeTruthy();
      expect(customCdfResponse.name).toBe(customCdf.name);
      expect(customCdfResponse.kind).toBe(customCdf.kind);
      expect(customCdfResponse.formula.length).toBe(customCdf.formula.length);

      // Update Report to pull created Custom CDF
      const newColumns = [...reportColumns, new Column(new Cdf(`custom_${customCdfName}`, CdfKind.CUSTOM))];
      const newReport = { ...report, columns: newColumns };

      const updatedReport = await ReportService.updateReport(reportId, newReport, propertyId);
      expect(updatedReport.columns.length).toBe(newColumns.length);
      expect(updatedReport.custom_cdfs.length).toBe(1);

      const postBody: StockReportPostBody = {
        report_id: reportId,
        rules,
      };

      await delay(1000);
      const createdStockReport = await ReportService.createStockReport(propertyId, postBody);
      const updatedAt1 = createdStockReport.updated_at;

      expect(createdStockReport.title).toBe(originalName);
      const stockReportId = createdStockReport.id;

      // Add Folder to Stock Report
      const folderName = `Auto-Stock-Report-Folder-${Guid.create()}`;
      const folder = await StockReportFolderService.createFolder(folderName, propertyId);

      // Get a Random Stock Report ID
      const stockReports = await ReportService.getStockReports(propertyId);
      const stockReport = stockReports.stock_reports[0];
      expect(stockReport.folder_id).not.toBe(folder.id);

      // Assign new Folder to Stock Report
      await ReportService.assignFolderToStockReport(folder.id, stockReportId, propertyId);

      const chartSettings: ChartSettings = {
        legend: false,
        toolbox: false,
      };

      const chartMetrics = [
        new ChartMetric(new Cdf(FinancialCDF.CREDIT_AMOUNT), [MetricType.SUM]),
        new ChartMetric(new Cdf(FinancialCDF.DEBIT_AMOUNT), [MetricType.SUM]),
      ];

      const chartCategories = [
        new ChartCategory(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), Modifier.MONTH),
      ];

      // Create a chart
      const barChart = new Chart(
        barChartTitle,
        ChartKind.Bar,
        chartSettings,
        chartCategories,
        chartMetrics,
      );

      let charts = await ReportService.getStockReportCharts(stockReportId, propertyId);
      expect(charts.length).toBe(0);
      let chart = await ReportService.createStockReportChart(barChart, stockReportId, propertyId);
      const chartId = chart.id;

      chart = await ReportService.getStockReportChart(chartId, stockReportId, propertyId);
      expect(chart.kind).toBe(ChartKind.Bar);

      charts = await ReportService.getStockReportCharts(stockReportId, propertyId);
      expect(charts.length).toBe(1);

      const lineChart = new Chart(
        lineChartTitle,
        ChartKind.Line,
        chartSettings,
        chartCategories,
        chartMetrics,
      );

      chart = await ReportService.updateStockReportChart(chartId, stockReportId, propertyId, lineChart);

      chart = await ReportService.getStockReportChart(chartId, stockReportId, propertyId);
      expect(chart.kind).toBe(ChartKind.Line);

      charts = await ReportService.getStockReportCharts(stockReportId, propertyId);
      expect(charts.length).toBe(1);

      const chartSearchResponse = await ReportService.searchCharts(propertyId, originalName, ChartSearchColumns.DatasourceTitle);
      expect(chartSearchResponse.total).toBe(1);
      const chartSearchResponseChartTitle = await ReportService.searchCharts(propertyId, lineChartTitle, ChartSearchColumns.Title);
      expect(chartSearchResponseChartTitle.total).toBe(1);
      const chartSearchResponseWrongTitleColumn = await ReportService.searchCharts(propertyId, originalName, ChartSearchColumns.Title);
      expect(chartSearchResponseWrongTitleColumn.total).toBe(0);
      const chartSearchResponseDatasourceKind = await ReportService.searchCharts(propertyId, originalName, ChartSearchColumns.DatasourceTitle, ReportKind.StockReport);
      expect(chartSearchResponseDatasourceKind.total).toBe(1);
      const chartSearchResponseWrongDatasourceKind = await ReportService.searchCharts(propertyId, originalName, ChartSearchColumns.DatasourceTitle, ReportKind.Report);
      expect(chartSearchResponseWrongDatasourceKind.total).toBe(0);
      const chartSearchReponseChartKind = await ReportService.searchCharts(propertyId, originalName, ChartSearchColumns.DatasourceTitle, undefined, ChartKind.Line);
      expect(chartSearchReponseChartKind.total).toBe(1);
      const chartSearchReponseWrongChartKind = await ReportService.searchCharts(propertyId, originalName, ChartSearchColumns.DatasourceTitle, undefined, ChartKind.Bar);
      expect(chartSearchReponseWrongChartKind.total).toBe(0);
      const chartsByIds = await ReportService.getAllCharts(propertyId, ReportKind.StockReport, undefined, [chartId]);
      expect(chartsByIds.total).toBe(1);

      const filteredStockReportsCompound = await ReportService.getStockReports(propertyId, {
        dataset_ids: '1',
        ids: createdStockReport.id,
        title: createdStockReport.title,
        include_cloudbeds: true });
      const filteredStockReportsCompoundDescription = await ReportService.getStockReports(propertyId, {
        dataset_ids: '1',
        ids: createdStockReport.id,
        description: createdStockReport.description,
        include_cloudbeds: true });

      expect(filteredStockReportsCompound.stock_reports[0].title).toBe(originalName);
      expect(filteredStockReportsCompoundDescription.stock_reports[0].title).toBe(originalName);

      const { id, type, columns, updated_at, user_id, custom_cdfs, ...putBody } = createdStockReport;

      expect(custom_cdfs).toBeTruthy();
      columns.pop();
      putBody.title = updatedName;
      putBody.rules = rules;
      putBody.columns = columns;
      putBody.periods = periods;

      delete putBody.folder_id;
      delete putBody.tags;

      await delay(1000);
      let updatedStockReport = await ReportService.updateStockReport(stockReportId, propertyId, putBody);

      // Update the stock report MAX_STOCK_REPORT_REVISION + 1
      for (let revision = 0; revision < MAX_STOCK_REPORT_REVISION + 1; revision++) {
        updatedStockReport = await ReportService.updateStockReport(stockReportId, propertyId, putBody);
      }

      const updatedAt2 = updatedStockReport.updated_at;

      expect(updatedStockReport.title).toBe(updatedName);

      await delay(1000);
      // assign report to country code this user doesn't have
      rules.country_codes = ['ZZ'];
      rules.feature_ids = [
        'report_builder',
        'cb_websites',
        'mybookings_enabled',
        'cb_amplify_dashboard', 'availability_enabled',
        'cashiering_system_enabled', 'cb_payments_stripe_enabled', 'hardware_terminal', 'house_keeping_enabled', 'invoicing_enabled',
        'settings_multi_currency', 'myallocator_enabled', 'pie_enabled', 'revenue_allocation', 'shared_inventory_enabled',
        'split_inventory', 'whistle_enabled'];

      const updatedRules = await ReportService.updateStockReportRules(stockReportId, propertyId, rules);
      expect(updatedRules.property_ids.length).toBe(rules.property_ids.length);
      expect(updatedRules.country_codes.length).toBe(rules.country_codes.length);

      const updatedStockReport2 = await ReportService.getStockReportById(stockReportId, propertyId);

      const updatedAt3 = updatedStockReport2.updated_at;

      expect(updatedAt1).not.toBe(updatedAt2);
      expect(updatedAt2).not.toBe(updatedAt3);
      expect(updatedAt3).not.toBe(updatedAt1);

      const privilegedUserGetStockReportsFiltered = await ReportService.getStockReports(propertyId, { ids: stockReportId, include_cloudbeds: true });
      const userGetStockReportsFiltered = await ReportService.getStockReports(propertyId, { ids: stockReportId });

      expect(privilegedUserGetStockReportsFiltered.total).toBeGreaterThan(userGetStockReportsFiltered.total);

      const otherProperties = [1, 2, 3];
      const stockReportQuery = new StockReportQuery(otherProperties, updatedStockReport2.columns, undefined, undefined, undefined, undefined, undefined, undefined, periods);
      const summaryResponse = await ReportService.getStockReportQuerySummaryById(updatedStockReport2.id, propertyId, stockReportQuery);

      Object.keys(summaryResponse.metrics[periods[0].name].metrics.credit_amount).forEach((key: string) => {
      // Verify count metric is a valid integer
        if (key === MetricType.COUNT) {
          expect(summaryResponse.metrics[periods[0].name].metrics.credit_amount[key] % 1).toEqual(0);
        } else {
          const value = summaryResponse.metrics[periods[0].name].metrics.credit_amount[key];
          // value may be null or numeric
          try {
            expect(typeof value).toBe('number');
          } catch (error) {
            expect(value).toEqual(null);
          }
        }
      });

      const dataResponse = await ReportService.getStockReportQueryDataById(updatedStockReport2.id, propertyId, stockReportQuery);

      expect(summaryResponse.total).toBeGreaterThanOrEqual(0);
      expect(dataResponse.headers).toBeDefined();

      await ReportService.removeFolderFromStockReport(folder.id, stockReportId, propertyId);

      // Delete reports
      await Promise.all([
        ReportService.deleteStockReport(stockReportId, propertyId),
        ReportService.deleteReport(reportId, propertyId),
      ]);

      // Delete Stock Report Folder
      StockReportFolderService.deleteFolder(folder.id, propertyId);

      // get stock report revisions for this
      const revisions = await ReportService.getStockReportRevisions(stockReportId, propertyId);
      const revisionId = revisions[0].revision_id;
      const revision = await ReportService.getStockReportRevision(stockReportId, revisionId, propertyId);

      delete revision.id;
      delete revision.type;
      delete revision.user_id;
      delete revision.revision_id;
      delete revision.rules;
      delete revision.updated_at;
      delete revision.stock_report_id;
      delete revision.custom_cdfs;
      revision.periods = periods;

      const republishedStockReport = await ReportService.updateStockReport(stockReportId, propertyId, revision);
      const cloneReportData = new ReportClone(undefined, undefined, undefined, propertyIds);
      const clonedStockReport = await ReportService.cloneStockReport(stockReportId, propertyId, cloneReportData);
      expect(clonedStockReport.periods).toHaveLength(periods.length);
      expect(republishedStockReport.published).toBeTruthy();
      expect(republishedStockReport.type).toBe('PeriodSummary');

      await ReportService.deleteStockReportChart(chartId, stockReportId, propertyId);
      charts = await ReportService.getStockReportCharts(stockReportId, propertyId);
      expect(charts.length).toBe(0);
      await ReportService.deleteStockReport(stockReportId, propertyId);
      charts = await ReportService.getReportCharts(clonedStockReport.id, propertyId);
      await ReportService.deleteReportChart(charts[0].id, clonedStockReport.id, propertyId);
      await ReportService.deleteReport(clonedStockReport.id, propertyId);
    } else {
      expect(true).toBe(true);
      log('User successfully does not have stock report crud access');
    }
  });
});
