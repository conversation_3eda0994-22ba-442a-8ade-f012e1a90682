import { log } from 'console';

import { Guid } from 'guid-typescript';

import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config, MeService } from '@/index';
import { Rules, StockReportPostBody } from '@/entities/report/settings/rules';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { FinancialCDF } from '@/entities/datasets/financial.cdf';
import { MetricType } from '@/entities/report/enums/metric.type';
import { Chart } from '@/entities/chart/chart.model';
import { ChartMetric } from '@/entities/chart/chartMetric.model';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Report } from '@/entities/report/report.model';
import { StockReportQuery } from '@/entities/report/stockReportQuery.model';
import { delay } from 'tests/common/utils/time';
import { CustomCdfFormula } from '@/entities/report/customCdf/customCdfFormula.model';
import { CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { CustomCdfValidate } from '@/entities/report/customCdf/customCdfValidate.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Modifier } from '@/entities/report/enums/time.type';
import { ReportKind } from '@/entities/report/enums/report_kind.type';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { ChartKind } from '@/entities/report/enums/chartKind.type';
import { getDefaultPeriods } from 'tests/common/data/financial';
import { StockReportQueryExport } from '@/entities/report/stockReportQueryExport.model';
import { ExportFormatType } from '@/entities/report/enums/export.format.type';
import { Sort } from '@/entities/report/sort/sort.model';
import { SortType } from '@/entities/report/enums/sort.type';
import { ChartAxis } from '@/entities/chart/enums/chartAxis';

jest.retryTimes(2);
describe('- Stock Reports are reports provided by Cloudbeds', () => {
  test('Create Get Update Delete and validate Custom CDFs for Stock Reports', async () => {
    const crudActions = ['create', 'delete', 'read', 'update'];
    const description = 'Financial Stock Report Created by SDK';
    const datasetId = Dataset.FINANCIAL;
    const originalName = `Auto-StockReport-OriginalName-${Guid.create()}`;

    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const settings: ReportSettings = {
      totals: false,
      details: false,
    };
    const sorts: Sort[] = [
      new Sort(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), SortType.DESC),
    ];

    const propertyIds = [propertyId];
    const associationId = Config.ASSOCIATIONS[propertyId];
    if (associationId !== undefined) {
      propertyIds.push(associationId);
    }
    const meProperties = await MeService.getCurrentUserProperties(propertyId);
    const countryCode = meProperties.find((property: any) => property.id === propertyId).country_code;
    const countryCodes = ['IT', 'TH'];
    if (!countryCodes.includes(countryCode) && countryCode !== null && countryCode !== undefined) {
      countryCodes.push(countryCode);
    }
    const features = ['report_builder', 'cb_websites', 'mybookings_enabled', 'cb_amplify_dashboard', 'availability_enabled',
      'cashiering_system_enabled', 'cb_payments_stripe_enabled', 'hardware_terminal', 'house_keeping_enabled', 'invoicing_enabled',
      'settings_multi_currency', 'myallocator_enabled', 'pie_enabled', 'revenue_allocation', 'shared_inventory_enabled',
      'split_inventory', 'whistle_enabled'];

    const rules: Rules = {
      feature_ids: features,
      property_ids: propertyIds,
      country_codes: countryCodes,
    };
    // check logged in user has crud access if they don't, skip crud tests
    const userPolicies = await MeService.getCurrentUserPolicies(propertyId);
    if (userPolicies.filter((policy: any) => policy.resource === ReportKind.StockReport).length > 0) {
      log('found');
    }
    if (userPolicies.filter((policy: any) => policy.resource === ReportKind.StockReport).length > 0
      && JSON.stringify(userPolicies.filter((policy: any) => policy.resource === ReportKind.StockReport)[0].actions) === JSON.stringify(crudActions)) {
      // Build a list of reports
      const reportColumns = [
        new Column(new Cdf(FinancialCDF.CREDIT_AMOUNT), [MetricType.SUM]),
        new Column(new Cdf(FinancialCDF.DEBIT_AMOUNT), [MetricType.SUM]),
      ];

      const reportGroupRows = [
        new GroupRow(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), Modifier.MONTH),
      ];
      const report = new Report(
        originalName,
        description,
        propertyId,
        propertyIds,
        datasetId,
        reportColumns,
        reportGroupRows,
        null,
        settings,
        sorts,
        null,
        getDefaultPeriods().periods,
      );

      // Create a report
      await ReportService.authenticateService();
      const createdReport = await ReportService.createReport(report, propertyId);
      const reportId = createdReport.id;

      // Publish the report
      const postBody: StockReportPostBody = {
        report_id: reportId,
        rules,
      };

      // Verify published reports
      await delay(1000);
      const createdStockReport = await ReportService.createStockReport(propertyId, postBody);
      expect(createdStockReport.title).toBe(originalName);
      const stockReportId = createdStockReport.id;

      // Build and Validate Custom CDF
      const customCdfName = 'Some Text';
      const customCdfValidate: CustomCdfValidate = new CustomCdfValidate(
        customCdfName,
        [
          new CustomCdfFormula(CustomCdfFormulaKind.SEPARATOR, 'Just some text'),
        ],
        CustomCdfKind.STRING,
      );

      // Build and Validate Custom CDF
      const customCdfNumberName = 'Some Number';
      const customCdfNumberValidate: CustomCdfValidate = new CustomCdfValidate(
        customCdfNumberName,
        [
          new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '+'),
          new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
        ],
        CustomCdfKind.NUMBER,
      );
      const validateCustomCdfResponse = await ReportService.validateCustomCdf(stockReportId, propertyId, customCdfValidate, ReportKind.StockReport);
      expect(validateCustomCdfResponse).toBeTruthy();
      expect(validateCustomCdfResponse.name).toBe(customCdfValidate.name);
      expect(validateCustomCdfResponse.kind).toBe(customCdfValidate.kind);
      expect(validateCustomCdfResponse.formula.length).toBe(customCdfValidate.formula.length);

      // Create Custom CDF
      const customCdf: CustomCdf = new CustomCdf(
        customCdfValidate.name,
        'Just a string',
        customCdfValidate.formula,
        customCdfValidate.kind,
      );
      const customCdfNumber: CustomCdf = new CustomCdf(
        customCdfNumberValidate.name,
        'Just a string',
        customCdfNumberValidate.formula,
        customCdfNumberValidate.kind,
      );
      const customCdfResponse = await ReportService.createCustomCdf(stockReportId, propertyId, customCdf, ReportKind.StockReport);
      expect(customCdfResponse).toBeTruthy();
      expect(customCdfResponse.name).toBe(customCdf.name);
      expect(customCdfResponse.kind).toBe(customCdf.kind);
      expect(customCdfResponse.formula.length).toBe(customCdf.formula.length);

      // Get Custom CDF
      const getCustomCdfResponse = await ReportService.getCustomCdf(stockReportId, propertyId, customCdfResponse.id, ReportKind.StockReport);
      expect(getCustomCdfResponse).toBeTruthy();
      expect(getCustomCdfResponse.name).toBe(customCdf.name);
      expect(getCustomCdfResponse.kind).toBe(customCdf.kind);
      expect(getCustomCdfResponse.formula.length).toBe(customCdf.formula.length);

      // Get Custom CDFs
      const getCustomCdfsResponse = await ReportService.getCustomCdfs(stockReportId, propertyId, ReportKind.StockReport);
      expect(getCustomCdfsResponse).toBeTruthy();
      expect(getCustomCdfsResponse.length).toBeGreaterThan(0);

      // Update Custom CDF
      await delay(1000);
      const updatedCustomCdf: CustomCdf = new CustomCdf(
        `${customCdfName} Updated`,
        'Just an updated string',
        [
          new CustomCdfFormula(CustomCdfFormulaKind.SEPARATOR, 'Just some updated text'),
        ],
        CustomCdfKind.STRING,
      );
      const updateCustomCdfResponse = await ReportService.updateCustomCdf(stockReportId, getCustomCdfResponse.id, propertyId, updatedCustomCdf, ReportKind.StockReport);
      expect(updateCustomCdfResponse).toBeTruthy();
      expect(updateCustomCdfResponse.name).toBe(updatedCustomCdf.name);
      expect(updateCustomCdfResponse.kind).toBe(updatedCustomCdf.kind);
      expect(updateCustomCdfResponse.formula.length).toBe(updatedCustomCdf.formula.length);

      // Delete Custom CDF
      const deleteCustomCdfResponse = await ReportService.deleteCustomCdf(stockReportId, customCdfResponse.id, propertyId, ReportKind.StockReport);
      expect(deleteCustomCdfResponse).toBe('');

      // Recreate Custom CDF and Number Custom CDF
      const customCdfResponse2 = await ReportService.createCustomCdf(stockReportId, propertyId, customCdf, ReportKind.StockReport);
      expect(customCdfResponse2).toBeTruthy();
      expect(customCdfResponse2.name).toBe(customCdf.name);
      expect(customCdfResponse2.kind).toBe(customCdf.kind);
      expect(customCdfResponse2.formula.length).toBe(customCdf.formula.length);

      // Recreate Custom CDF and Number Custom CDF
      const customCdfNumberResponse = await ReportService.createCustomCdf(stockReportId, propertyId, customCdfNumber, ReportKind.StockReport);
      expect(customCdfNumberResponse).toBeTruthy();
      expect(customCdfNumberResponse.name).toBe(customCdfNumber.name);
      expect(customCdfNumberResponse.kind).toBe(customCdfNumber.kind);
      expect(customCdfNumberResponse.formula.length).toBe(customCdfNumber.formula.length);

      // Add Number Custom CDF to columns

      // Update stock report to group by custom cdf
      const { id, type, columns, updated_at, user_id, custom_cdfs, ...putBody } = createdStockReport;
      const updatedName = `Auto-StockReport-UpdatedName-${Guid.create()}`;
      const reportGroupRowsCustomCdf = [
        new GroupRow(new Cdf(customCdfResponse2.column, CdfKind.CUSTOM)),
      ];
      const reportGroupRowsCustomCdfSort = [
        new Sort(new Cdf(customCdfResponse2.column, CdfKind.CUSTOM), SortType.ASC),
      ];

      expect(custom_cdfs).toBeTruthy();
      columns.pop();
      putBody.title = updatedName;
      putBody.rules = rules;
      putBody.columns = [new Column(new Cdf(customCdfNumberResponse.column, CdfKind.CUSTOM), [MetricType.SUM]), ...columns];
      putBody.group_rows = reportGroupRowsCustomCdf;
      putBody.sort = reportGroupRowsCustomCdfSort;
      delete putBody.custom_cdfs;
      delete putBody.folder_id;
      delete putBody.updated_at;
      delete putBody.generated_at;
      delete putBody.tags;

      await delay(1000);
      const updatedStockReport = await ReportService.updateStockReport(stockReportId, propertyId, putBody);
      const otherProperties = [1, 2, 3];
      const stockReportQuery = new StockReportQuery(otherProperties, updatedStockReport.columns);
      const stockReportQueryExport = new StockReportQueryExport('Test Title', otherProperties, updatedStockReport.dataset_id, updatedStockReport.columns);
      const summaryResponse = await ReportService.getStockReportQuerySummaryById(updatedStockReport.id, propertyId, stockReportQuery);
      const dataResponse = await ReportService.getStockReportQueryDataById(updatedStockReport.id, propertyId, stockReportQuery);
      const exportResponse = await ReportService.exportStockReportByQuery(updatedStockReport.id, stockReportQueryExport, true, ExportFormatType.XLSX, propertyId);

      expect(summaryResponse.total).toBeGreaterThanOrEqual(0);
      expect(dataResponse.headers).toBeDefined();
      expect(exportResponse).toBeDefined();

      const chartSettings = {
        toolbox: false,
        legend: false,
        metrics: [
          {
            cdf: new Cdf(customCdfNumberResponse.column, CdfKind.CUSTOM),
            metric: MetricType.SUM,
            kind: ChartKind.Bar,
            axis: ChartAxis.Right,
          },
        ],
      };

      // Add a chart to the report
      const chart = new Chart(
        'Chart Title',
        ChartKind.Combo,
        chartSettings,
        reportGroupRowsCustomCdf,
        [new ChartMetric(new Cdf(customCdfNumberResponse.column, CdfKind.CUSTOM), [MetricType.SUM])],
      );

      const chartResponse = await ReportService.createStockReportChart(chart, stockReportId, propertyId);
      const chartId = chartResponse.id;

      expect(chartResponse.kind).toBe(ChartKind.Combo);
      expect(chartId).toBeDefined();

      // Update Custom CDF and verify Chart and Report are updated
      await delay(1000);
      const finalUpdateName = `${customCdfName} Updated Finally`;
      const updatedCustomCdfCategory: CustomCdf = new CustomCdf(
        finalUpdateName,
        'Just an updated string',
        customCdfNumber.formula,
        CustomCdfKind.NUMBER,
      );
      const response = await ReportService.updateCustomCdf(stockReportId, customCdfNumberResponse.id, propertyId, updatedCustomCdfCategory, ReportKind.StockReport);

      expect(response).toBeTruthy();
      expect(response.column).toBe('custom_some_text_updated_finally');
      expect(response.name).toBe(updatedCustomCdfCategory.name);
      expect(response.kind).toBe(updatedCustomCdfCategory.kind);
      expect(response.formula.length).toBe(updatedCustomCdfCategory.formula.length);

      const updatedChart = await ReportService.getStockReportChart(chartId, stockReportId, propertyId);
      expect(updatedChart.metrics[0].cdf.column).toBe('custom_some_text_updated_finally');
      expect(updatedChart.settings.metrics[0].cdf.column).toBe('custom_some_text_updated_finally');

      expect(updatedChart);

      // Delete reports
      await Promise.all([
        ReportService.deleteStockReport(stockReportId, propertyId),
        ReportService.deleteReport(reportId, propertyId),
      ]);
    } else {
      expect(true).toBe(true);
      log('User successfully does not have stock report crud access');
    }
  });
});
