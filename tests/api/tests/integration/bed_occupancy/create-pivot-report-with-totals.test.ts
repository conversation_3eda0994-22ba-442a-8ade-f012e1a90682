import { Guid } from 'guid-typescript';

import { Report } from '@/entities/report/report.model';
import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';

import {
  getPivotReport,
  getDefaultFilter,
} from '../../common/data/bed_occupancy';

jest.retryTimes(2);
describe(`- A pivot report is a summary report but grouped by Columns.
- If Group Rows has 3 CDF, it can not be grouped by columns
- It should be 1 or 2 CDF selected as Group Rows in order to select 1 or 2 CDF to be Grouped by Column
- The Pivot Report will show the selected columns that contains metrics. For example Sum Credit based on
  the selected group rows, for example Check-in Date and Check-out date and Grouped Columns by Transaction Type
  So a Report divided in the 3 columns Credit, Debit and Quantity will be displayed by the selected metric and
  divided by the Transaction Type, for example Payment, Product, Tax and then the selected rows will be displayed`, () => {
  test('Create Bed Occupancy Pivot Report v1.1', async () => {
    const title = `Auto-Bed-Occupancy-${Guid.create()}`;
    const description = 'Bed Occupancy Grouped Report Created by SDK';
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.BED_OCCUPANCY;
    const { columns, groupRows, groupColumns } = getPivotReport();

    let settings: ReportSettings = {
      totals: true,
      details: false,
    };

    let myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      groupColumns,
      settings,
      null,
      getDefaultFilter(),
    );

    let report = await ReportService.createReport(
      myCustomReport,
      propertyId,
    );
    const reportId = report.id;
    let response = await ReportService.getReport(reportId, propertyId);

    // Verify Columns with Metrics
    expect(response.columns).toEqual(myCustomReport.columns);

    // Verify Group By Rows
    expect(response.group_rows).toEqual(myCustomReport.group_rows);

    // Verify Group By Columns
    expect(response.group_columns).toEqual(myCustomReport.group_columns);

    // Verify Report Metadata
    expect(response.title).toEqual(myCustomReport.title);
    expect(response.description).toEqual(myCustomReport.description);
    expect(response.dataset_id).toEqual(datasetId);
    expect(response.settings.totals).toEqual(
      myCustomReport.settings.totals,
    );
    expect(response.settings.details).toEqual(
      myCustomReport.settings.details,
    );
    expect(response.property_id).toEqual(propertyId);
    expect(response.property_ids).toEqual(myCustomReport.property_ids);

    // Get Report Data
    let data = await ReportService.getReportData(reportId, propertyId);
    expect(data.type).toBe('Pivot');
    if (isNotEmptyObject(data.records)) {
      expect(data.headers.length).toBeGreaterThanOrEqual(0);
      expect(data.group_rows.length).toEqual(Object.keys(groupRows).length);
      expect(data.group_columns.length).toEqual(
        Object.keys(groupColumns).length,
      );

      expect(Object.keys(data.totals).length).toBeGreaterThanOrEqual(0);
      expect(Object.keys(data.subtotals).length).toBeGreaterThanOrEqual(0);
    }

    // transpose the report
    settings = {
      totals: true,
      details: false,
      transpose: true,
    };

    myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      groupColumns,
      settings,
      null,
      getDefaultFilter(),
    );
    report = await ReportService.updateReport(
      reportId,
      myCustomReport,
      propertyId,
    );
    response = await ReportService.getReport(reportId, propertyId);

    // Verify Columns with Metrics
    expect(response.columns).toEqual(myCustomReport.columns);

    // Verify Group By Rows
    expect(response.group_rows).toEqual(myCustomReport.group_rows);

    // Verify Group By Columns
    expect(response.group_columns).toEqual(myCustomReport.group_columns);

    // Verify Report Metadata
    expect(response.title).toEqual(myCustomReport.title);
    expect(response.description).toEqual(myCustomReport.description);
    expect(response.dataset_id).toEqual(datasetId);
    expect(response.settings.totals).toEqual(
      myCustomReport.settings.totals,
    );
    expect(response.settings.details).toEqual(
      myCustomReport.settings.details,
    );
    expect(response.property_id).toEqual(propertyId);
    expect(response.property_ids).toEqual(myCustomReport.property_ids);

    // Get Report Data
    data = await ReportService.getReportData(reportId, propertyId);
    if (isNotEmptyObject(data.records)) {
      expect(data.type).toBe('Pivot');
      if (isNotEmptyObject(data.records)) {
        expect(data.headers.length).toBeGreaterThanOrEqual(0);
        expect(data.group_rows.length).toEqual(
          Object.keys(groupColumns).length,
        );
        expect(data.group_columns.length).toEqual(
          Object.keys(groupRows).length,
        );

        expect(Object.keys(data.totals).length).toBeGreaterThanOrEqual(0);
        expect(Object.keys(data.subtotals).length).toBeGreaterThanOrEqual(0);
      }
    }
    // Delete created Report
    await ReportService.deleteReport(reportId, propertyId);
  });
});
