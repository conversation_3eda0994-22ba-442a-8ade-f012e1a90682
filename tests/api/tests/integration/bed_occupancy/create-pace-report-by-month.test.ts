import { Guid } from 'guid-typescript';

import { Report } from '@/entities/report/report.model';
import { Dataset } from '@/entities/datasets/dataset.type';
import { ReportService, Config } from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { CdfKind } from '@/entities/report/enums/cdf.type';

import { getCustomCdf, getPaceReport } from '../../common/data/bed_occupancy';

jest.retryTimes(2);
describe(`- A summary details report is a report based in data grouped by columns
- It is a report which allows users to group rows data
- A max of 3 Group Rows can be selected
- The columns don't need to be of type numeric and have metrics associated
- The Summary Report will show the data grouped by the selected columns`, () => {
  test('Create Bed Occupancy Pace Report By Month v1.1', async () => {
    const title = `Auto-Bed-Occupancy-${Guid.create()}`;
    const description = 'Bed Occupancy Grouped Report Created by SDK';
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.BED_OCCUPANCY;
    const { columns, groupRows, sorts, periods } = getPaceReport();

    const settings: ReportSettings = {
      totals: true,
      details: false,
    };

    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Nov', 'Dec', '-'];

    const myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      settings,
      sorts,
      null,
      periods,

    );

    let report = await ReportService.createReport(myCustomReport, propertyId);
    const reportId = report.id;
    const response = await ReportService.getReport(reportId, propertyId);

    // Verify Columns with Metrics
    expect(response.columns).toEqual(myCustomReport.columns);

    // Verify Group By Rows
    expect(response.group_rows).toEqual(myCustomReport.group_rows);

    // Verify Group By Columns
    expect(response.group_columns).toEqual(myCustomReport.group_columns);

    // Verify Report Metadata
    expect(response.title).toEqual(myCustomReport.title);
    expect(response.description).toEqual(myCustomReport.description);
    expect(response.dataset_id).toEqual(datasetId);
    expect(response.settings.totals).toEqual(myCustomReport.settings.totals);
    expect(response.settings.details).toEqual(myCustomReport.settings.details);
    expect(response.property_id).toEqual(propertyId);
    expect(response.property_ids).toEqual(myCustomReport.property_ids);

    const customCdf = await ReportService.createCustomCdf(reportId, propertyId, getCustomCdf());

    const newColumns = [
      ...report.columns,
      new Column(new Cdf(customCdf.column, CdfKind.CUSTOM)),
    ];

    myCustomReport.columns = newColumns;
    report = await ReportService.updateReport(reportId, myCustomReport, propertyId);
    expect(report.columns).toEqual(myCustomReport.columns);

    // Get Report Data
    const data = await ReportService.getReportData(reportId, propertyId);
    expect(data.type).toBe('PeriodSummary');
    if (isNotEmptyObject(data.records)) {
      expect(data.group_rows.length).toEqual(Object.keys(groupRows).length);
      data.index.every((month: string, index: number, dataArray: Array<string>) => {
        expect(months.indexOf(dataArray[index + 1])).toBeLessThanOrEqual(months.indexOf(month));
        return true;
      });
    }

    // Delete created Report
    await ReportService.deleteReport(reportId, propertyId);
  });
});
