import { Guid } from 'guid-typescript';

import {
  descendingSorts,
  getDefaultFilterRoomTypeNotNull,
  getSummaryReportGroupByMonthAndWeek,
} from 'tests/common/data/bed_occupancy';
import { Report } from '@/entities/report/report.model';
import { Dataset } from '@/entities/datasets/dataset.type';
import {
  ReportService,
  Config,
  XLSXService,
} from '@/index';
import { ReportSettings } from '@/entities/report/settings/reportSettings';
import { isNotEmptyObject } from 'tests/common/utils/emptyObject';
import { ExportFormatType } from '@/entities/report/enums/export.format.type';
import { MetricType } from '@/entities/report/enums/metric.type';
import { METRIC_ORDER } from 'tests/common/constants/metric-order';
import { MONTH_ORDER } from 'tests/common/constants/month-order';
import { DYNAMIC_CDF_LABEL } from 'tests/common/constants/dynamic-cdf-label';
import { ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE } from 'tests/common/constants/all-records-displayed-success-message';

jest.retryTimes(2);
describe('- A Bed Occupancy Summary Report', () => {
  test('Create and export Bed Occupancy Report with three group rows, details and totals, validating sorts', async () => {
    const title = `Auto-Bed-Occupancy-${Guid.create()}`;
    const description = 'Bed Occupancy Report Created by SDK';
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];
    const datasetId = Dataset.BED_OCCUPANCY;

    const settings: ReportSettings = {
      totals: true,
      details: true,
    };
    const { columns, groupRows, sorts } = getSummaryReportGroupByMonthAndWeek();
    let myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      settings,
      sorts,
      getDefaultFilterRoomTypeNotNull(),
      null,
    );

    const customReport = await ReportService.createReport(
      myCustomReport,
      propertyId,
    );
    const reportId = customReport.id;

    const response = await ReportService.getReport(reportId, propertyId);

    // Verify Columns with Metrics
    expect(response.columns).toEqual(myCustomReport.columns);

    // Verify Group By Rows
    expect(response.group_rows).toEqual(myCustomReport.group_rows);

    // Verify Group By Columns
    expect(response.group_columns).toEqual(myCustomReport.group_columns);

    // Verify Report Metadata
    expect(response.title).toEqual(myCustomReport.title);
    expect(response.description).toEqual(myCustomReport.description);
    expect(response.dataset_id).toEqual(datasetId);
    expect(response.settings.totals).toEqual(myCustomReport.settings.totals);
    expect(response.settings.details).toEqual(
      myCustomReport.settings.details,
    );
    expect(response.property_id).toEqual(propertyId);
    expect(response.property_ids).toEqual(myCustomReport.property_ids);

    // Get Report Data
    let data = await ReportService.getReportData(reportId, propertyId);
    expect(data.headers.length).toBeGreaterThanOrEqual(0);
    expect(data.index.length).toBeGreaterThanOrEqual(0);
    expect(data.type).toBe('Summary');

    // export the report
    let exportedReport = await ReportService.exportById(
      reportId,
      true,
      ExportFormatType.XLSX,
      propertyId,
    );

    let workbookJson = await XLSXService.readExcelFromUrl(exportedReport.url);
    expect(workbookJson.length).toBeGreaterThan(0);
    expect(workbookJson[2].__EMPTY).toBe(ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE);
    expect(workbookJson[workbookJson.length - 1].__EMPTY).toBe('Filters');
    expect(workbookJson[workbookJson.length - 1].__EMPTY_1).toBe(
      'Stay Date >= Start of Last Month AND Stay Date < Start of Next Week AND Room Type ID Is Not Null ',
    );

    expect(workbookJson.slice(workbookJson.length - 9)[0].__EMPTY).toBe(
      'Total sum',
    );
    expect(workbookJson.slice(workbookJson.length - 9)[1].__EMPTY).toBe(
      'Total mean',
    );
    expect(workbookJson.slice(workbookJson.length - 9)[2].__EMPTY).toBe(
      'Total max',
    );
    expect(workbookJson.slice(workbookJson.length - 9)[3].__EMPTY).toBe(
      'Total min',
    );
    expect(workbookJson.slice(workbookJson.length - 9)[4].__EMPTY).toBe(
      'Total count',
    );
    expect(workbookJson.slice(workbookJson.length - 9)[5].__EMPTY).toBe(
      'Total std',
    );
    expect(workbookJson.slice(workbookJson.length - 9)[6].__EMPTY).toBe(
      'Total var',
    );
    expect(workbookJson.slice(workbookJson.length - 9)[7].__EMPTY).toBe(
      `Total ${DYNAMIC_CDF_LABEL}`,
    );

    // validate the group rows in the right order: group by status, then month, then week
    // each column in a formatted  summary report is going to have a weird name beginning with __EMPTY, then __EMPTY_2, __EMPTY_2, etc
    // it is recommended to export the actual file this test is generating if debugging is needed
    // comparing workbookJson to the actual file is the best way to understand the structure of the data
    // the data we will validate is beginning on the 3rd index, we can skip the last 7 rows as they are totals we already validated
    // first lets validate the group rows,
    // the first level is sorted by status
    // the second level is sorted by month
    // the third level is sorted by week
    // each time a value without metric changes, reset its metric index
    // each time a level changes reset the next level downs' index
    // looking at the sheet, our data for group rows starts at row with index 3, in three columns: __EMPTY_1, __EMPTY_2, and __EMPTY_3
    // grab the first groups and start iterating in the next line
    // iterate to the end
    const group1column = '__EMPTY';
    const group2column = '__EMPTY_1';
    const group3column = '__EMPTY_2';
    let group1Index = workbookJson[4][group1column];
    let group2Index = workbookJson[4][group2column];
    let group3index = workbookJson[4][group3column];
    expect(group1Index).toBeDefined();
    expect(group2Index).toBeDefined();
    expect(group3index).toBeDefined();

    let group1MetricIndex = -1;
    let group2MetricIndex = -1;
    let group3MetricIndex = -1;
    let group1Value = METRIC_ORDER.includes(
      workbookJson[4][group1column].split(' ').pop() as MetricType,
    )
      ? workbookJson[4][group1column]
        .split(' ')
        .slice(0, -1)
        .join(' ')
      : workbookJson[4][group1column];
    let lastGroup1Value = workbookJson[4][group1column];
    let lastGroup2Value = workbookJson[4][group2column];
    let lastgroup3value = workbookJson[4][group3column];
    let metricIndex: any;

    workbookJson.slice(5, -9).forEach((item: any) => {
      if (item[group1column] !== undefined) {
        group1Value = METRIC_ORDER.includes(
          item[group1column].split(' ').pop() as MetricType,
        )
          ? item[group1column]
            .split(' ')
            .slice(0, -1)
            .join(' ')
          : item[group1column];
        expect(
          group1Value.toLowerCase() >= lastGroup1Value.toLowerCase(),
        ).toBe(true);
        if (lastGroup1Value != group1Value) {
          group1MetricIndex = -1;
        }
        group1Index = item[group1column];
        group3MetricIndex = -1;
        group2MetricIndex = -1;
        group2Index = item[group2column];
        lastGroup1Value = group1Value;

        metricIndex = METRIC_ORDER.indexOf(
          item[group1column].split(' ').pop() as MetricType,
        );
        // if no metric, it will be -1
        if (metricIndex >= 0) {
          expect(metricIndex).toBeGreaterThan(group1MetricIndex);
          group1MetricIndex = metricIndex;
        }
      }
      if (
        item[group2column] !== undefined
        && item[group2column].length >= 3
      ) {
        expect(
          MONTH_ORDER.indexOf(item[group2column].slice(0, 3))
          >= MONTH_ORDER.indexOf(group2Index.slice(0, 3)),
        ).toBe(true);
        if (lastGroup2Value.slice(0, 3) != item[group2column].slice(0, 3)) {
          group2MetricIndex = -1;
        }
        lastGroup2Value = item[group2column];
        metricIndex = METRIC_ORDER.indexOf(
          item[group2column].split(' ').pop() as MetricType,
        );
        // if no metric, it will be -1
        if (metricIndex >= 0) {
          expect(metricIndex).toBeGreaterThan(group2MetricIndex);
          group2MetricIndex = metricIndex;
        }

        group2Index = item[group2column];

        // whenever group2 updates, group3 should reset
        group3MetricIndex = -1;
        group3index = item[group3column].slice(0, 12);
      }

      if (
        item[group3column] !== undefined
        && item[group3column].length >= 40
      ) {
        if (lastgroup3value.slice(0, 12) != item[group3column].slice(0, 12)) {
          group3MetricIndex = -1;
        }
        lastgroup3value = item[group3column].slice(0, 12);

        expect(
          new Date(item[group3column].slice(0, 12))
          >= new Date(group3index.slice(0, 12)),
        ).toBe(true);
        group3index = item[group3column];
        metricIndex = METRIC_ORDER.indexOf(
          item[group3column].split(' ').pop() as MetricType,
        );
        // if no metric, it will be -1
        if (metricIndex >= 0) {
          expect(metricIndex).toBeGreaterThan(group3MetricIndex);
          group3MetricIndex = metricIndex;
        }
      }
    });

    myCustomReport = new Report(
      title,
      description,
      propertyId,
      propertyIds,
      datasetId,
      columns,
      groupRows,
      null,
      settings,
      descendingSorts(),
      getDefaultFilterRoomTypeNotNull(),
      null,
    );

    const updatedReport = await ReportService.updateReport(
      reportId,
      myCustomReport,
      propertyId,
    );
    expect(updatedReport);
    data = await ReportService.getReportData(reportId, propertyId);

    if (isNotEmptyObject(data.records)) {
      exportedReport = await ReportService.exportById(
        reportId,
        true,
        ExportFormatType.XLSX,
        propertyId,
      );

      workbookJson = await XLSXService.readExcelFromUrl(exportedReport.url);
      expect(workbookJson.length).toBeGreaterThan(0);
      expect(workbookJson[2].__EMPTY).toBe(ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE);
      expect(workbookJson[workbookJson.length - 1].__EMPTY).toBe('Filters');
      expect(workbookJson[workbookJson.length - 1].__EMPTY_1).toBe(
        'Stay Date >= Start of Last Month AND Stay Date < Start of Next Week AND Room Type ID Is Not Null ',
      );

      expect(workbookJson.slice(workbookJson.length - 9)[0].__EMPTY).toBe(
        'Total sum',
      );
      expect(workbookJson.slice(workbookJson.length - 9)[1].__EMPTY).toBe(
        'Total mean',
      );
      expect(workbookJson.slice(workbookJson.length - 9)[2].__EMPTY).toBe(
        'Total max',
      );
      expect(workbookJson.slice(workbookJson.length - 9)[3].__EMPTY).toBe(
        'Total min',
      );
      expect(workbookJson.slice(workbookJson.length - 9)[4].__EMPTY).toBe(
        'Total count',
      );
      expect(workbookJson.slice(workbookJson.length - 9)[5].__EMPTY).toBe(
        'Total std',
      );
      expect(workbookJson.slice(workbookJson.length - 9)[6].__EMPTY).toBe(
        'Total var',
      );
      expect(workbookJson.slice(workbookJson.length - 9)[7].__EMPTY).toBe(
        `Total ${DYNAMIC_CDF_LABEL}`,
      );
      group1Index = workbookJson[4][group1column];
      group2Index = workbookJson[4][group2column];
      group3index = workbookJson[4][group3column];

      group1MetricIndex = -1;
      group2MetricIndex = -1;
      group3MetricIndex = -1;
      group1Value = METRIC_ORDER.includes(
        workbookJson[4][group1column].split(' ').pop() as MetricType,
      )
        ? workbookJson[4][group1column]
          .split(' ')
          .slice(0, -1)
          .join(' ')
        : workbookJson[4][group1column];
      lastGroup1Value = workbookJson[4][group1column];
      lastGroup2Value = workbookJson[4][group2column];
      lastgroup3value = workbookJson[4][group3column];

      workbookJson.slice(5, -9).forEach((item: any) => {
        if (item[group1column] !== undefined) {
          group1Value = METRIC_ORDER.reduce((val, metric) => (val.endsWith(` ${metric}`) ? val.replace(new RegExp(` ${metric}$`), '') : val), item[group1column]);

          expect(
            group1Value.toLowerCase() <= lastGroup1Value.toLowerCase(),
          ).toBe(true);
          if (lastGroup1Value != group1Value) {
            group1MetricIndex = -1;
          }
          group1Index = item[group1column];
          group3MetricIndex = -1;
          group2MetricIndex = -1;
          group2Index = item[group2column];
          lastGroup1Value = group1Value;

          metricIndex = METRIC_ORDER.indexOf(
            item[group1column].split(' ').pop() as MetricType,
          );
          // if no metric, it will be -1
          if (metricIndex >= 0) {
            expect(metricIndex).toBeGreaterThan(group1MetricIndex);
            group1MetricIndex = metricIndex;
          }
        }
        if (
          item[group2column] !== undefined
          && item[group2column].length >= 3
        ) {
          expect(
            MONTH_ORDER.indexOf(item[group2column].slice(0, 3))
            <= MONTH_ORDER.indexOf(group2Index.slice(0, 3)),
          ).toBe(true);
          if (lastGroup2Value.slice(0, 3) != item[group2column].slice(0, 3)) {
            group2MetricIndex = -1;
          }
          lastGroup2Value = item[group2column];
          metricIndex = METRIC_ORDER.indexOf(
            item[group2column].split(' ').pop() as MetricType,
          );
          // if no metric, it will be -1
          if (metricIndex >= 0) {
            expect(metricIndex).toBeGreaterThan(group2MetricIndex);
            group2MetricIndex = metricIndex;
          }

          group2Index = item[group2column];

          // whenever group2 updates, group3 should reset
          group3MetricIndex = -1;
          group3index = item[group3column].slice(0, 12);
        }

        if (
          item[group3column] !== undefined
          && item[group3column].length >= 40
        ) {
          if (
            group3index === undefined
            || group3index == null
            || group3index.length < 40
          ) {
            group3index = item[group3column];
          }
          if (
            lastgroup3value.slice(0, 12) != item[group3column].slice(0, 12)
          ) {
            group3MetricIndex = -1;
          }
          lastgroup3value = item[group3column].slice(0, 12);

          expect(
            new Date(item[group3column].slice(0, 12))
            <= new Date(group3index.slice(0, 12)),
          ).toBe(true);
          group3index = item[group3column];
          metricIndex = METRIC_ORDER.indexOf(
            item[group3column].split(' ').pop() as MetricType,
          );
          // if no metric, it will be -1
          if (metricIndex >= 0) {
            expect(metricIndex).toBeGreaterThan(group3MetricIndex);
            group3MetricIndex = metricIndex;
          }
        }
      });
      // Delete created Report
      await ReportService.deleteReport(reportId, propertyId);
    }
  });
});
