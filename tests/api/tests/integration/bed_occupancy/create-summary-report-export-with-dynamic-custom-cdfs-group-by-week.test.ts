import { Guid } from 'guid-typescript';

import {
  ReportService,
  Config,
  XLSXService,
  PropertyService,
  MeService,
} from '@/index';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import {
  getDefaultFilter,
  getTwoThirdsCustomCdf,
} from 'tests/common/data/bed_occupancy';
import { MetricType } from '@/entities/report/enums/metric.type';
import {
  CustomCdfFormat,
  CustomCdfKind,
} from '@/entities/report/enums/customCdf.type';
import { ExportFormatType } from '@/entities/report/enums/export.format.type';
import { Report } from '@/entities/report/report.model';
import { ReportKind } from '@/entities/report/enums/report_kind.type';
import { Dataset } from '@/entities/datasets/dataset.type';
import { Rules, StockReportPostBody } from '@/entities/report/settings/rules';
import { ExportViewType } from '@/entities/report/enums/export.view.type';
import { DYNAMIC_CDF_LABEL } from 'tests/common/constants/dynamic-cdf-label';
import { ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE } from 'tests/common/constants/all-records-displayed-success-message';
import { Sort } from '@/entities/report/sort/sort.model';
import { SortType } from '@/entities/report/enums/sort.type';
import { BedOccupancyCDF } from '@/entities/datasets/bed_occupancy';

jest.retryTimes(2);
describe('Report Create Export Data', () => {
  test('Create Export Bed Occupancy With different kinds of number cdfs, validate that the proper decimals are in the export, group by week', async () => {
    const propertyId = Config.USERS.DI_FULL_ACCESS.PROPERTY_ID;
    const propertyIds = [propertyId];

    const settingsResponse = await PropertyService.getPropertySettings(
      propertyId,
    );
    const currencyRoundingPrecision = settingsResponse.find(
      (setting: any) => setting.name === 'currency_cdf_rounding_precision',
    ).value;

    let roundedTwoThirds: number;

    switch (currencyRoundingPrecision) {
      case '0':
        roundedTwoThirds = 67;
        break;
      case '1':
        roundedTwoThirds = 66.7;
        break;
      case '2':
        roundedTwoThirds = 66.67;
        break;
      case '3':
        roundedTwoThirds = 66.667;
        break;
      case '4':
        roundedTwoThirds = 66.6667;
        break;
    }

    const title = `Auto-Bed-Occupancy-${Guid.create()}`;

    let columns = [
      new Column(
        new Cdf(BedOccupancyCDF.GUEST_COUNT, CdfKind.DEFAULT),
        Object.values(MetricType),
      ),
      new Column(
        new Cdf(BedOccupancyCDF.ROOM_REVENUE, CdfKind.DEFAULT),
        Object.values(MetricType),
      ),
      new Column(new Cdf(BedOccupancyCDF.BED_OCCUPANCY, CdfKind.DEFAULT)),
      new Column(new Cdf(BedOccupancyCDF.AVERAGE_BED_RATE, CdfKind.DEFAULT)),
      new Column(new Cdf(BedOccupancyCDF.ADJUSTED_BED_OCCUPANCY, CdfKind.DEFAULT)),
    ];

    let customReport = new Report(
      title,
      '',
      propertyId,
      propertyIds,
      Dataset.BED_OCCUPANCY,
      columns,
      [new GroupRow(new Cdf(BedOccupancyCDF.STAY_DATE), Modifier.WEEK)],
      null,
      { totals: true, details: false },
      null,
      getDefaultFilter(),
    );

    let report = await ReportService.createReport(customReport, propertyId);

    const customCdf1 = await ReportService.createCustomCdf(
      report.id,
      propertyId,
      getTwoThirdsCustomCdf(CustomCdfKind.NUMBER, CustomCdfFormat.NUMBER),
      ReportKind.Report,
    );
    const customCdf2 = await ReportService.createCustomCdf(
      report.id,
      propertyId,
      getTwoThirdsCustomCdf(CustomCdfKind.NUMBER, CustomCdfFormat.PERCENTAGE),
      ReportKind.Report,
    );
    const customCdf3 = await ReportService.createCustomCdf(
      report.id,
      propertyId,
      getTwoThirdsCustomCdf(CustomCdfKind.NUMBER, CustomCdfFormat.CURRENCY),
      ReportKind.Report,
    );
    const customCdf4 = await ReportService.createCustomCdf(
      report.id,
      propertyId,
      getTwoThirdsCustomCdf(CustomCdfKind.DYNAMIC, CustomCdfFormat.NUMBER),
      ReportKind.Report,
    );
    const customCdf5 = await ReportService.createCustomCdf(
      report.id,
      propertyId,
      getTwoThirdsCustomCdf(CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE),
      ReportKind.Report,
    );
    const customCdf6 = await ReportService.createCustomCdf(
      report.id,
      propertyId,
      getTwoThirdsCustomCdf(CustomCdfKind.DYNAMIC, CustomCdfFormat.CURRENCY),
      ReportKind.Report,
    );

    columns = [
      new Column(
        new Cdf(BedOccupancyCDF.GUEST_COUNT, CdfKind.DEFAULT),
        Object.values(MetricType),
      ),
      new Column(
        new Cdf(BedOccupancyCDF.ROOM_REVENUE, CdfKind.DEFAULT),
        Object.values(MetricType),
      ),
      new Column(new Cdf(BedOccupancyCDF.BED_OCCUPANCY, CdfKind.DEFAULT)),
      new Column(new Cdf(BedOccupancyCDF.AVERAGE_BED_RATE, CdfKind.DEFAULT)),
      new Column(new Cdf(BedOccupancyCDF.ADJUSTED_BED_OCCUPANCY, CdfKind.DEFAULT)),
      new Column(
        new Cdf('custom_number_number', CdfKind.CUSTOM),
        Object.values(MetricType),
      ),
      new Column(
        new Cdf('custom_number_percentage', CdfKind.CUSTOM),
        Object.values(MetricType),
      ),
      new Column(
        new Cdf('custom_number_currency', CdfKind.CUSTOM),
        Object.values(MetricType),
      ),
      new Column(new Cdf('custom_dynamic_number', CdfKind.CUSTOM)),
      new Column(new Cdf('custom_dynamic_percentage', CdfKind.CUSTOM)),
      new Column(new Cdf('custom_dynamic_currency', CdfKind.CUSTOM)),
    ];

    customReport = new Report(
      title,
      '',
      propertyId,
      propertyIds,
      Dataset.BED_OCCUPANCY,
      columns,
      [new GroupRow(new Cdf(BedOccupancyCDF.STAY_DATE), Modifier.WEEK)],
      null,
      { totals: true, details: false },
      [new Sort(new Cdf(BedOccupancyCDF.STAY_DATE), SortType.ASC)],
      getDefaultFilter(),
    );

    report = await ReportService.updateReport(
      report.id,
      customReport,
      propertyId,
    );

    let exportedReport = await ReportService.exportById(
      report.id,
      true,
      ExportFormatType.XLSX,
      propertyId,
    );

    expect(exportedReport);
    let workbookJson = await XLSXService.readExcelFromUrl(exportedReport.url);
    expect(workbookJson[2].__EMPTY).toBe(ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE);
    expect(workbookJson[workbookJson.length - 1].__EMPTY).toBe('Filters');
    expect(workbookJson[workbookJson.length - 1].__EMPTY_1).toBe('Stay Date >= Start of Last Month AND Stay Date < Start of Next Week');

    // helper function to put the right column names on the data, our excel files can be disordered when formatted
    function mapColumnNames(data: any[], columnNames: any): any[] {
      // Use slice to avoid modifying the column names entry
      return data.slice(4, -1).map(entry => {
        const mappedEntry: { [key: string]: any } = {};
        Object.keys(entry).forEach(key => {
          // Use the column name from columnNames for the key if available
          const newKey = columnNames[key] || key;
          mappedEntry[newKey] = entry[key];
        });
        return mappedEntry;
      });
    }

    function countDecimalPlaces(value: number): number {
      if (Number.isInteger(value)) {
        return 0;
      }

      const valueStr = value.toString();

      // Check if the number is in scientific notation
      if (valueStr.includes('e-')) {
        const [, exponent] = valueStr.split('e-');
        const exponentValue = parseInt(exponent);
        // The exponent in scientific notation directly indicates the number of zeros
        return exponentValue;
      }
      if (valueStr.includes('e')) {
        // Handle positive exponent in scientific notation (rare for counting decimals but for completeness)
        const [base, exponent] = valueStr.split('e');
        const decimalPlacesInBase = base.includes('.')
          ? base.split('.')[1].length
          : 0;
        const exponentValue = parseInt(exponent);
        return Math.max(0, decimalPlacesInBase - exponentValue);
      }

      // Standard decimal handling for non-scientific notation numbers
      const parts = valueStr.split('.');
      return parts[1] ? parts[1].length : 0;
    }

    // Getting the column names from workbookJson[4]
    let columnNames = workbookJson[3];

    // Applying the mapping function to the workbookJson
    const processedData = mapColumnNames(workbookJson, columnNames);
    expect(processedData);
    processedData.forEach((entry: any) => {
      expect(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`]).toEqual(roundedTwoThirds);
      expect(entry[`Dynamic_number - ${DYNAMIC_CDF_LABEL}`]).toEqual(66.67);
      expect(entry[`Dynamic_percentage - ${DYNAMIC_CDF_LABEL}`]).toEqual(66.67);
      expect(entry['Number_currency - max']).toEqual(roundedTwoThirds);
      expect(entry['Number_currency - mean']).toEqual(roundedTwoThirds);
      expect(entry['Number_currency - min']).toEqual(roundedTwoThirds);
      expect(
        countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`]),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry[`Dynamic_number - ${DYNAMIC_CDF_LABEL}`]),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry[`Dynamic_percentage - ${DYNAMIC_CDF_LABEL}`]),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_currency - count']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Number_currency - max']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Number_currency - mean']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Number_currency - min']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Number_currency - sum']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Number_currency - std']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Number_currency - var']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));

      expect(
        countDecimalPlaces(entry['Total Room Revenue - count']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Total Room Revenue - max']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Total Room Revenue - mean']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Total Room Revenue - min']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Total Room Revenue - sum']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Total Room Revenue - std']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Total Room Revenue - var']),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));

      expect(
        countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`]),
      ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
      expect(
        countDecimalPlaces(entry['Number_number - count']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_number - max']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_number - mean']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_number - min']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_number - sum']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_number - std']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_number - var']),
      ).toBeLessThanOrEqual(2);

      expect(
        countDecimalPlaces(entry['Number_percentage - count']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_percentage - max']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_percentage - mean']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_percentage - min']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_percentage - sum']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_percentage - std']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Number_percentage - var']),
      ).toBeLessThanOrEqual(2);

      expect(
        countDecimalPlaces(entry['Room Guest Count - count']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Room Guest Count - max']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Room Guest Count - mean']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Room Guest Count - min']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Room Guest Count - sum']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Room Guest Count - std']),
      ).toBeLessThanOrEqual(2);
      expect(
        countDecimalPlaces(entry['Room Guest Count - var']),
      ).toBeLessThanOrEqual(2);

      expect(countDecimalPlaces(entry[`Average Bed Rate - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(
        parseInt(currencyRoundingPrecision),
      );
      expect(countDecimalPlaces(entry[`Adjusted Bed Occupancy - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(
        parseInt(currencyRoundingPrecision),
      );
      expect(
        countDecimalPlaces(entry[`Bed Occupancy - ${DYNAMIC_CDF_LABEL}`]),
      ).toBeLessThanOrEqual(2);
    });

    const crudActions = ['create', 'delete', 'read', 'update'];
    const userPolicies = await MeService.getCurrentUserPolicies(propertyId);
    if (
      userPolicies.filter(
        (policy: any) => policy.resource === ReportKind.StockReport,
      ).length > 0
      && JSON.stringify(
        userPolicies.filter(
          (policy: any) => policy.resource === ReportKind.StockReport,
        )[0].actions,
      ) === JSON.stringify(crudActions)
    ) {
      // publish to stock report

      const rules: Rules = {
        feature_ids: null,
        property_ids: null,
        country_codes: null,
        property_types: null,
      };
      const postBody: StockReportPostBody = {
        report_id: report.id,
        rules,
      };

      const stockReport = await ReportService.createStockReport(
        propertyId,
        postBody,
      );
      expect(stockReport);

      exportedReport = await ReportService.getStockReportExportById(
        stockReport.id,
        propertyId,
        propertyIds,
        ExportViewType.FORMATTED,
        ExportFormatType.XLSX,
      );

      expect(exportedReport);
      workbookJson = await XLSXService.readExcelFromUrl(exportedReport.url);
      expect(workbookJson[2].__EMPTY).toBe(ALL_RECORDS_DISPLAYED_SUCCESS_MESSAGE);
      expect(workbookJson[workbookJson.length - 1].__EMPTY).toBe('Filters');
      expect(workbookJson[workbookJson.length - 1].__EMPTY_1).toBe('Stay Date >= Start of Last Month AND Stay Date < Start of Next Week');

      columnNames = workbookJson[3];

      // Applying the mapping function to the workbookJson
      const processedData = mapColumnNames(workbookJson, columnNames);
      expect(processedData);
      processedData.forEach((entry: any) => {
        expect(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`]).toEqual(roundedTwoThirds);
        expect(entry[`Dynamic_number - ${DYNAMIC_CDF_LABEL}`]).toEqual(66.67);
        expect(entry[`Dynamic_percentage - ${DYNAMIC_CDF_LABEL}`]).toEqual(66.67);
        expect(entry['Number_currency - max']).toEqual(roundedTwoThirds);
        expect(entry['Number_currency - mean']).toEqual(roundedTwoThirds);
        expect(entry['Number_currency - min']).toEqual(roundedTwoThirds);
        expect(
          countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`]),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry[`Dynamic_number - ${DYNAMIC_CDF_LABEL}`]),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry[`Dynamic_percentage - ${DYNAMIC_CDF_LABEL}`]),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_currency - count']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Number_currency - max']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Number_currency - mean']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Number_currency - min']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Number_currency - sum']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Number_currency - std']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Number_currency - var']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));

        expect(
          countDecimalPlaces(entry['Total Room Revenue - count']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Total Room Revenue - max']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Total Room Revenue - mean']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Total Room Revenue - min']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Total Room Revenue - sum']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Total Room Revenue - std']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Total Room Revenue - var']),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));

        expect(
          countDecimalPlaces(entry[`Dynamic_currency - ${DYNAMIC_CDF_LABEL}`]),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry['Number_number - count']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_number - max']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_number - mean']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_number - min']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_number - sum']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_number - std']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_number - var']),
        ).toBeLessThanOrEqual(2);

        expect(
          countDecimalPlaces(entry['Number_percentage - count']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_percentage - max']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_percentage - mean']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_percentage - min']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_percentage - sum']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_percentage - std']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Number_percentage - var']),
        ).toBeLessThanOrEqual(2);

        expect(
          countDecimalPlaces(entry['Room Guest Count - count']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Room Guest Count - max']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Room Guest Count - mean']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Room Guest Count - min']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Room Guest Count - sum']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Room Guest Count - std']),
        ).toBeLessThanOrEqual(2);
        expect(
          countDecimalPlaces(entry['Room Guest Count - var']),
        ).toBeLessThanOrEqual(2);

        expect(countDecimalPlaces(entry[`Average Bed Rate - ${DYNAMIC_CDF_LABEL}`])).toBeLessThanOrEqual(
          parseInt(currencyRoundingPrecision),
        );
        expect(
          countDecimalPlaces(entry[`Adjusted Bed Occupancy - ${DYNAMIC_CDF_LABEL}`]),
        ).toBeLessThanOrEqual(parseInt(currencyRoundingPrecision));
        expect(
          countDecimalPlaces(entry[`Bed Occupancy - ${DYNAMIC_CDF_LABEL}`]),
        ).toBeLessThanOrEqual(2);

        let lastWeekTimestamp = -1; // Initialize with a value that is earlier than any valid timestamp

        processedData.forEach(item => {
          const stayDate = item['Stay Date'];

          if (stayDate === 'Total') {
            // Verify 'Total' is really the last element
            expect(processedData.indexOf(item)).toBe(processedData.length - 1);
          } else {
            // Extract the date part from the first segment of the weekly range
            const datePart = stayDate.split('-')[0].trim(); // Extracts "Feb 15, 2021" from the example
            // Parse the extracted date part directly into a timestamp
            const weekTimestamp = new Date(datePart).getTime(); // Convert to timestamp

            // Expect the week timestamp to be greater than the last week timestamp for proper sequencing
            expect(weekTimestamp).toBeGreaterThan(lastWeekTimestamp);

            lastWeekTimestamp = weekTimestamp; // Update lastWeekTimestamp to the current week timestamp
          }
        });
      });
      await ReportService.deleteStockReport(stockReport.id, propertyId);
    }

    await ReportService.deleteCustomCdf(report.id, customCdf1.id, propertyId);
    await ReportService.deleteCustomCdf(report.id, customCdf2.id, propertyId);
    await ReportService.deleteCustomCdf(report.id, customCdf3.id, propertyId);
    await ReportService.deleteCustomCdf(report.id, customCdf4.id, propertyId);
    await ReportService.deleteCustomCdf(report.id, customCdf5.id, propertyId);
    await ReportService.deleteCustomCdf(report.id, customCdf6.id, propertyId);

    await ReportService.deleteReport(report.id, propertyId);
  });
});
