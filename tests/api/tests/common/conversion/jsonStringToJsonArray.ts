/**
 * Method that was created to format the exported json from reports into a JSON Array readable for Javascript
 * @param json - Json String from exported report
 */
export function jsonStringToJsonArray(json: string) {
  const jsonWithoutJumpSpace = json.replace(/\n/g, ',');
  const jsonWithLatestCommaRemoved = jsonWithoutJumpSpace.slice(0, -1);
  const jsonArray = `[${jsonWithLatestCommaRemoved}]`;
  return JSON.parse(jsonArray);
}
