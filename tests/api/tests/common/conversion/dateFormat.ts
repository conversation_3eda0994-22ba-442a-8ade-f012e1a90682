function formatDate(date: Date, includeTime = false): string {
  let formattedDate = `${date.getFullYear()}-${(`0${date.getMonth()}`).slice(-2)}-${(`0${date.getDate()}`).slice(-2)}`;
  formattedDate = includeTime
    ? `${formattedDate} ${(`0${date.getHours()}`).slice(-2)}:${(`0${date.getMinutes()}`).slice(-2)}:${(`0${date.getSeconds()}`).slice(-2)}`
    : formattedDate;
  return formattedDate;
}

export { formatDate };
