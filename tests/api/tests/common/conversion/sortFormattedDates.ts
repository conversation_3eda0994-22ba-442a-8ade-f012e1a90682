export function sortFormattedWeeks(headers: string[][]): string[][] {
  // Sort the headers based on the datetime value of the first 12 characters of each element
  return headers.slice().sort((a, b) => {
    const dateA: Date = new Date(a[0].slice(0, 12));
    const dateB: Date = new Date(b[0].slice(0, 12));
    return dateA.getTime() - dateB.getTime(); // Using getTime() for better comparison
  });
}

export function sortFormattedMonths(headers: string[][]): string[][] {
  // Define a mapping of month abbreviations to their respective order
  const MONTH_ORDER: { [key: string]: number } = {
    Jan: 1,
    Feb: 2,
    Mar: 3,
    Apr: 4,
    May: 5,
    Jun: 6,
    Jul: 7,
    Aug: 8,
    Sep: 9,
    Oct: 10,
    Nov: 11,
    Dec: 12,
  };

  // Sort the headers based on the datetime value of the first 12 characters of each element
  return headers.slice().sort((a, b) => {
    const monthA: string = a[0]; // Extract month abbreviation
    const monthB: string = b[0];

    // Compare by month order
    const MONTH_ORDERA: number = MONTH_ORDER[monthA];
    const MONTH_ORDERB: number = MONTH_ORDER[monthB];

    if (MONTH_ORDERA !== MONTH_ORDERB) {
      return MONTH_ORDERA - MONTH_ORDERB;
    }

    // If months are the same, compare by the rest of the date
    return a[0].localeCompare(b[0]);
  });
}
