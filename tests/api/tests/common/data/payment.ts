import { PaymentCDF } from '@/entities/datasets/payment.cdf';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import { paymentMetricCdfs, metrics } from '@/entities/report/columns/metrics/metrics';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';

export function getListReportWithAllColumns() {
  return Object.values(PaymentCDF).map(cdf => {
    if (paymentMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(PaymentCDF).forEach((cdf: any) => {
    if (paymentMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getSummaryReport() {
  const columns = paymentMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(PaymentCDF.CHECKIN_DATE), Modifier.MONTH),
    new GroupRow(new Cdf(PaymentCDF.CHECKOUT_DATE), Modifier.MONTH),
  ];

  return { columns, groupRows };
}

export function getPivotReport() {
  const groupColumns = [
    new GroupColumn(new Cdf(PaymentCDF.CARD_TYPE)),
    new GroupColumn(new Cdf(PaymentCDF.PAYMENT_METHOD)),
  ];
  return { ...getSummaryReport(), groupColumns };
}

export function getDefaultFilter() {
  const filterByYear = new Date(`${new Date().getFullYear()}-01-01`);
  const date = filterByYear.toISOString();
  const filters = new Filters(FilterType.AND, [
    new Filter(new Cdf(PaymentCDF.CHECKIN_DATE), FilterByType.GREATER_THAN_OR_EQUAL, date),
  ]);
  return filters;
}

export function getDefaultPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(new Cdf(PaymentCDF.CHECKIN_DATE), periodOne, 'yesterday', 'tomorrow'),
    new Period(new Cdf(PaymentCDF.CHECKIN_DATE), periodTwo, 'start_last_year', 'tomorrow'),
    new Period(new Cdf(PaymentCDF.CHECKIN_DATE), periodThree, 'start_current_year', 'tomorrow'),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}
