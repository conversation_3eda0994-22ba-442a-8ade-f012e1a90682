import { ChartCategory } from '@/entities/chart/chartCategory.model';
import { ChartMetric } from '@/entities/chart/chartMetric.model';
import { ReservationCDF } from '@/entities/datasets/reservation.cdf';
import { Multilevel } from '@/entities/multilevel/multilevel.type';
import { RoomNightsCDF } from '@/entities/multilevel/roomNights.cdf';
import { RoomReservationCDF } from '@/entities/multilevel/roomReservation.cdf';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import {
  reservationMetricCdfs,
  metrics,
  roomnightMetricCdfs,
  roomreservationsMetricCdfs,
} from '@/entities/report/columns/metrics/metrics';
import { Comparison } from '@/entities/report/comparisons/comparison.model';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { MetricType } from '@/entities/report/enums/metric.type';
import { SortType } from '@/entities/report/enums/sort.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';
import { Sort } from '@/entities/report/sort/sort.model';

export function getListReportWithAllColumns() {
  return Object.values(ReservationCDF).map(cdf => {
    if (reservationMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(ReservationCDF).forEach((cdf: any) => {
    if (reservationMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getListReportWithAllColumnsAndMultilevels() {
  const reservationCdfs = Object.values(ReservationCDF).map(cdf => {
    if (reservationMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
  const roomNightsCdfs = Object.values(RoomNightsCDF).map(cdf => {
    if (roomnightMetricCdfs.includes(cdf)) {
      return new Column(
        new Cdf(cdf, CdfKind.DEFAULT, Multilevel.ROOM_NIGHTS),
        metrics,
      );
    }
    return new Column(new Cdf(cdf, CdfKind.DEFAULT, Multilevel.ROOM_NIGHTS));
  });

  const roomReservationCdfs = Object.values(RoomReservationCDF).map(cdf => {
    if (roomreservationsMetricCdfs.includes(cdf)) {
      return new Column(
        new Cdf(cdf, CdfKind.DEFAULT, Multilevel.ROOM_RESERVATION),
        metrics,
      );
    }
    return new Column(
      new Cdf(cdf, CdfKind.DEFAULT, Multilevel.ROOM_RESERVATION),
    );
  });

  return reservationCdfs.concat(roomNightsCdfs, roomReservationCdfs);
}

export function getSummaryReport() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH_WITHOUT_YEAR),
    new GroupRow(new Cdf(ReservationCDF.CANCELLATION_DATETIME), Modifier.WEEK),
  ];

  return { columns, groupRows };
}

export function getChartReport() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH),
  ];

  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.GRAND_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH),
  ];

  return { columns, groupRows, chartCategories, chartMetrics };
}

export function getSunburstChartReport() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH),
    new GroupRow(new Cdf(ReservationCDF.CANCELLATION_DATETIME), Modifier.MONTH),
  ];

  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.ROOM_REVENUE_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH),
    new ChartCategory(
      new Cdf(ReservationCDF.CANCELLATION_DATETIME),
      Modifier.MONTH,
    ),
  ];
  const sorts = [
    new Sort(new Cdf(ReservationCDF.BOOKING_DATETIME), SortType.DESC),
    new Sort(new Cdf(ReservationCDF.CANCELLATION_DATETIME), SortType.DESC),
  ];

  return { columns, groupRows, chartCategories, chartMetrics, sorts };
}

export function getGeomapChartReport() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(ReservationCDF.PRIMARY_GUEST_RESIDENCE_COUNTRY)),
  ];

  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.GRAND_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.PRIMARY_GUEST_RESIDENCE_COUNTRY)),
  ];

  return { columns, groupRows, chartCategories, chartMetrics };
}

export function getHeatmapChartReport() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [new GroupRow(new Cdf(ReservationCDF.CHECKIN_DATE))];

  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.GRAND_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.CHECKIN_DATE)),
  ];

  return { columns, groupRows, chartCategories, chartMetrics };
}

export function getSummaryWithMultilevel() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH),
    new GroupRow(
      new Cdf(RoomNightsCDF.STAY_DATE, CdfKind.DEFAULT, Multilevel.ROOM_NIGHTS),
      Modifier.MONTH,
    ),
  ];

  return { columns, groupRows };
}

export function getPivotReport() {
  const groupColumns = [
    new GroupColumn(new Cdf(ReservationCDF.CARD_TYPE)),
    new GroupColumn(new Cdf(ReservationCDF.IS_REPEAT_GUEST)),
  ];
  return { ...getSummaryReport(), groupColumns };
}

export function getPivotReporLink() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  ); const groupRows = [new GroupRow(new Cdf(ReservationCDF.PRIMARY_GUEST_FULL_NAME))];
  const groupColumns = [
    new GroupColumn(new Cdf(ReservationCDF.RESERVATION_NUMBER)),
  ];
  return { columns, groupRows, groupColumns };
}

export function getPivotReportSunburstChart() {
  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.ROOM_REVENUE_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH_WITHOUT_YEAR),
    new ChartCategory(
      new Cdf(ReservationCDF.CANCELLATION_DATETIME),
      Modifier.WEEK,
    ),
  ];

  return { ...getPivotReport(), chartMetrics, chartCategories };
}

export function getPivotReportChart() {
  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.ROOM_REVENUE_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.BOOKING_DATETIME), Modifier.MONTH_WITHOUT_YEAR),
  ];

  return { ...getPivotReport(), chartMetrics, chartCategories };
}

export function getPivotGeomapChartReport() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(ReservationCDF.PRIMARY_GUEST_RESIDENCE_COUNTRY)),
  ];
  const groupColumns = [
    new GroupColumn(new Cdf(ReservationCDF.CARD_TYPE)),
    new GroupColumn(new Cdf(ReservationCDF.IS_REPEAT_GUEST)),
  ];
  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.ROOM_REVENUE_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.PRIMARY_GUEST_RESIDENCE_COUNTRY)),
  ];

  return { columns, groupRows, groupColumns, chartMetrics, chartCategories };
}

export function getPivotHeatmapChartReport() {
  const columns = reservationMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [new GroupRow(new Cdf(ReservationCDF.CHECKIN_DATE))];
  const groupColumns = [
    new GroupColumn(new Cdf(ReservationCDF.CARD_TYPE)),
    new GroupColumn(new Cdf(ReservationCDF.IS_REPEAT_GUEST)),
  ];
  const chartMetrics = [
    new ChartMetric(new Cdf(ReservationCDF.ROOM_REVENUE_TOTAL_AMOUNT), [
      MetricType.SUM,
    ]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(ReservationCDF.CHECKIN_DATE)),
  ];

  return { columns, groupRows, groupColumns, chartMetrics, chartCategories };
}

export function getPivotReportWithMultilevels() {
  const groupColumns = [
    new GroupColumn(new Cdf(ReservationCDF.CARD_TYPE)),
    new GroupColumn(new Cdf(ReservationCDF.IS_REPEAT_GUEST)),
  ];
  return { ...getSummaryWithMultilevel(), groupColumns };
}

export function getDefaultFilter() {
  const filterByYear = new Date(`${new Date().getFullYear()}-01-01`);
  const date = filterByYear.toISOString();
  const filters = new Filters(FilterType.AND, [
    new Filter(
      new Cdf(ReservationCDF.BOOKING_DATETIME),
      FilterByType.GREATER_THAN_OR_EQUAL,
      date,
    ),
  ]);
  return filters;
}

export function getDefaultComparisons() {
  const comparisonOne = '<=0';
  const comparisonTwo = '1-30';
  const comparisonThree = '31-60';
  const comparisonFour = '61-90';
  const comparisonFive = '>90';

  const comparisons = [
    new Comparison(
      comparisonOne,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.LESS_THAN_OR_EQUAL,
          '0',
        ),
      ]),
    ),
    new Comparison(
      comparisonTwo,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.GREATER_THAN,
          '0',
        ),
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.LESS_THAN_OR_EQUAL,
          '30',
        ),
      ]),
    ),
    new Comparison(
      comparisonThree,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.GREATER_THAN,
          '30',
        ),
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.LESS_THAN_OR_EQUAL,
          '60',
        ),
      ]),
    ),
    new Comparison(
      comparisonFour,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.GREATER_THAN,
          '60',
        ),
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.LESS_THAN_OR_EQUAL,
          '90',
        ),
      ]),
    ),
    new Comparison(
      comparisonFive,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(ReservationCDF.BOOKING_WINDOW),
          FilterByType.GREATER_THAN,
          '90',
        ),
      ]),
    ),
  ];
  return { comparisonOne, comparisonTwo, comparisonThree, comparisons };
}

export function getDefaultPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(
      new Cdf(ReservationCDF.CHECKIN_DATE),
      periodOne,
      'yesterday',
      '2023-05-24T00:00:00.000Z',
      true,
    ),
    new Period(
      new Cdf(ReservationCDF.CHECKIN_DATE),
      periodTwo,
      'start_last_year',
      '2023-05-24',
      true,
    ),
    new Period(
      new Cdf(ReservationCDF.CHECKIN_DATE),
      periodThree,
      'start_current_year',
      'tomorrow',
    ),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getMultiLevelPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(
      new Cdf(RoomNightsCDF.STAY_DATE, CdfKind.DEFAULT, Multilevel.ROOM_NIGHTS),
      periodOne,
      'yesterday',
      '2023-05-24T00:00:00.000Z',
      true,
    ),
    new Period(
      new Cdf(RoomNightsCDF.STAY_DATE, CdfKind.DEFAULT, Multilevel.ROOM_NIGHTS),
      periodTwo,
      'start_last_year',
      '2023-05-24',
      true,
    ),
    new Period(
      new Cdf(RoomNightsCDF.STAY_DATE, CdfKind.DEFAULT, Multilevel.ROOM_NIGHTS),
      periodThree,
      'start_current_year',
      'tomorrow',
    ),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getDefaultPeriodsDifferentCdfs() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(
      new Cdf(ReservationCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE),
      periodOne,
      'yesterday',
      '2023-05-24T00:00:00.000Z',
      true,
    ),
    new Period(
      new Cdf(ReservationCDF.CHECKIN_DATE),
      periodTwo,
      'start_last_year',
      '2023-05-24',
      true,
    ),
    new Period(
      new Cdf(ReservationCDF.CHECKIN_DATE),
      periodThree,
      'start_current_year',
      'tomorrow',
    ),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getPeriodSummaryReport() {
  const { periods } = getDefaultPeriods();
  const sorts = [
    new Sort(new Cdf(ReservationCDF.BOOKING_DATETIME), SortType.DESC),
    new Sort(new Cdf(ReservationCDF.CANCELLATION_DATETIME), SortType.DESC),
  ];

  return { ...getSummaryReport(), periods, sorts };
}

export function getSummaryComparisonReport() {
  const { comparisons } = getDefaultComparisons();
  const sorts = [
    new Sort(new Cdf(ReservationCDF.BOOKING_DATETIME), SortType.ASC),
    new Sort(new Cdf(ReservationCDF.CANCELLATION_DATETIME), SortType.ASC),
  ];

  return { ...getSummaryReport(), comparisons, sorts };
}

export function getMuliLevelPeriodSummaryReport() {
  const { periods } = getMultiLevelPeriods();
  const sorts = [
    new Sort(new Cdf(ReservationCDF.BOOKING_DATETIME), SortType.DESC),
    new Sort(new Cdf(ReservationCDF.CANCELLATION_DATETIME), SortType.DESC),
  ];

  return { ...getSummaryReport(), periods, sorts };
}

export function getBookingDateFilterThisYear() {
  const filterByYearStart = new Date(`${new Date().getFullYear()}-01-01`);
  const startDate = filterByYearStart.toISOString();
  const filterByYearEnd = new Date(`${new Date().getFullYear()}-12-31`);
  const endDate = filterByYearEnd.toISOString();
  const filters = new Filters(FilterType.AND, [
    new Filter(
      new Cdf(ReservationCDF.BOOKING_DATETIME),
      FilterByType.GREATER_THAN_OR_EQUAL,
      startDate,
    ),
    new Filter(
      new Cdf(ReservationCDF.BOOKING_DATETIME),
      FilterByType.LESS_THAN_OR_EQUAL,
      endDate,
    ),
  ]);
  return filters;
}
