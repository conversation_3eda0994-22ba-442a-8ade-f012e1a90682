import { metrics, payoutMetricCdfs } from '@/entities/report/columns/metrics/metrics';
import { Column } from '@/entities/report/columns/column.model';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { PayoutCdf } from '@/entities/datasets/payout.cdf';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Modifier } from '@/entities/report/enums/time.type';

export function getListReportWithAllColumns() {
  return Object.values(PayoutCdf).map(cdf => {
    if (payoutMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getSummaryReport() {
  const columns = payoutMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(PayoutCdf.PAYOUT_ID)),
    new GroupRow(new Cdf(PayoutCdf.PAYOUT_DATE), Modifier.MONTH),
  ];

  return { columns, groupRows };
}

export function getPivotReport() {
  const groupColumns = [
    new GroupColumn(new Cdf(PayoutCdf.TRANSACTION_TYPE)),
    new GroupColumn(new Cdf(PayoutCdf.INVENTORY_OBJECT_TYPE)),
  ];
  return { ...getSummaryReport(), groupColumns };
}
