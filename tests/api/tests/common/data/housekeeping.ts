import { ChartCategory } from '@/entities/chart/chartCategory.model';
import { ChartMetric } from '@/entities/chart/chartMetric.model';
import { HousekeepingCDF } from '@/entities/datasets/housekeeping.cdf';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import {
  housekeepingMetricCdfs,
  metrics,
} from '@/entities/report/columns/metrics/metrics';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { MetricType } from '@/entities/report/enums/metric.type';
import { SortType } from '@/entities/report/enums/sort.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';
import { Sort } from '@/entities/report/sort/sort.model';

export function getListReportWithAllColumns() {
  return Object.values(HousekeepingCDF).map(cdf => {
    if (housekeepingMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(HousekeepingCDF).forEach((cdf: any) => {
    if (housekeepingMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getSummaryReportWithPropertyId() {
  const columns = housekeepingMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(HousekeepingCDF.STAY_DATE), Modifier.MONTH),
    new GroupRow(new Cdf(HousekeepingCDF.ROOM_CONDITION_LAST_UPDATED_DATETIME)),
    new GroupRow(new Cdf(HousekeepingCDF.PROPERTY_ID)),
  ];

  return { columns, groupRows };
}

export function getSummaryReport() {
  const columns = housekeepingMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(
      new Cdf(HousekeepingCDF.ESTIMATED_ARRIVAL_TIME),
      Modifier.TIME_HOUR,
    ),
    new GroupRow(
      new Cdf(HousekeepingCDF.ROOM_CONDITION_LAST_UPDATED_DATETIME),
      Modifier.MONTH,
    ),
  ];

  return { columns, groupRows };
}

export function getChartReport() {
  const columns = housekeepingMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(HousekeepingCDF.STAY_DATE), Modifier.MONTH),
  ];

  const chartMetrics = [
    new ChartMetric(new Cdf(HousekeepingCDF.ROOM_COUNT), [MetricType.SUM]),
  ];
  const chartCategories = [
    new ChartCategory(new Cdf(HousekeepingCDF.STAY_DATE), Modifier.MONTH),
  ];

  return { columns, groupRows, chartCategories, chartMetrics };
}

export function getPivotReport() {
  const groupColumns = [new GroupColumn(new Cdf(HousekeepingCDF.FRONTDESK_STATUS))];
  return { ...getSummaryReport(), groupColumns };
}

export function getDefaultFilter() {
  const filterByYear = new Date(`${new Date().getFullYear()}-01-01`);
  const date = filterByYear.toISOString();
  const filters = new Filters(FilterType.AND, [
    new Filter(
      new Cdf(HousekeepingCDF.STAY_DATE),
      FilterByType.GREATER_THAN_OR_EQUAL,
      date,
    ),
  ]);
  return filters;
}

export function getDefaultPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(
      new Cdf(HousekeepingCDF.STAY_DATE),
      periodOne,
      'yesterday',
      'tomorrow',
    ),
    new Period(
      new Cdf(HousekeepingCDF.STAY_DATE),
      periodTwo,
      'start_last_year',
      'tomorrow',
    ),
    new Period(
      new Cdf(HousekeepingCDF.STAY_DATE),
      periodThree,
      'start_current_year',
      'tomorrow',
    ),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getDefaultPeriodsDifferentCdfs() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(
      new Cdf(
        HousekeepingCDF.ROOM_CONDITION_LAST_UPDATED_DATETIME_PROPERTY_TIMEZONE,
      ),
      periodOne,
      'yesterday',
      'tomorrow',
    ),
    new Period(
      new Cdf(HousekeepingCDF.STAY_DATE),
      periodTwo,
      'start_last_year',
      'tomorrow',
    ),
    new Period(
      new Cdf(HousekeepingCDF.STAY_DATE),
      periodThree,
      'start_current_year',
      'tomorrow',
    ),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getPeriodSummaryReport() {
  const { periods } = getDefaultPeriods();
  const sorts = [
    new Sort(
      new Cdf(HousekeepingCDF.ROOM_CONDITION_LAST_UPDATED_DATETIME),
      SortType.DESC,
    ),
    new Sort(
      new Cdf(
        HousekeepingCDF.ROOM_CONDITION_LAST_UPDATED_DATETIME_PROPERTY_TIMEZONE,
      ),
      SortType.DESC,
    ),
  ];

  return { ...getSummaryReport(), periods, sorts };
}

export function getPeriodSummaryReportDifferentCdfs() {
  const { periods } = getDefaultPeriodsDifferentCdfs();
  const sorts = [
    new Sort(
      new Cdf(HousekeepingCDF.ROOM_CONDITION_LAST_UPDATED_DATETIME),
      SortType.DESC,
    ),
    new Sort(
      new Cdf(
        HousekeepingCDF.ROOM_CONDITION_LAST_UPDATED_DATETIME_PROPERTY_TIMEZONE,
      ),
      SortType.DESC,
    ),
  ];

  return { ...getSummaryReport(), periods, sorts };
}
