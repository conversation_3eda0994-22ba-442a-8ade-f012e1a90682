import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import {
  metrics,
  bedOccupancyMetricCdfs,
} from '@/entities/report/columns/metrics/metrics';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { SortType } from '@/entities/report/enums/sort.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';
import { Sort } from '@/entities/report/sort/sort.model';
import { Multilevel } from '@/entities/multilevel/multilevel.type';
import { OccupancyReservationsCDF } from '@/entities/multilevel/occupancyReservations.cdf';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { CustomCdfFormula } from '@/entities/report/customCdf/customCdfFormula.model';
import { CustomCdfFormat, CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { MetricType } from '@/entities/report/enums/metric.type';
import { BedOccupancyCDF } from '@/entities/datasets/bed_occupancy';

const fixedGroups = [
  new GroupRow(new Cdf(BedOccupancyCDF.STAY_DATE), Modifier.MONTH),
];

export function getListReportWithAllColumns() {
  return Object.values(BedOccupancyCDF).map(cdf => {
    if (bedOccupancyMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(BedOccupancyCDF).forEach((cdf: any) => {
    if (bedOccupancyMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getSummaryReport() {
  const columns = bedOccupancyMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );

  return { columns, groupRows: fixedGroups };
}

export function getPivotReport() {
  const columns = [
    new Column(new Cdf(BedOccupancyCDF.ADULTS_COUNT), [MetricType.MIN]),
    new Column(new Cdf(BedOccupancyCDF.ADJUSTED_BED_OCCUPANCY)),
    new Column(new Cdf(BedOccupancyCDF.AVERAGE_BED_RATE)),
  ];
  const groupColumns = [
    new GroupColumn(new Cdf(BedOccupancyCDF.ROOM_TYPE_ID)),
  ];
  return { columns, groupRows: fixedGroups, groupColumns };
}

export function getSummaryWithMultilevel() {
  const columns = bedOccupancyMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(BedOccupancyCDF.STAY_DATE), Modifier.MONTH),
    new GroupRow(new Cdf(OccupancyReservationsCDF.BOOKING_DATE, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS), Modifier.MONTH),
  ];

  return { columns, groupRows };
}

export function getPivotReportWithMultilevels() {
  const groupColumns = [
    new GroupColumn(new Cdf(OccupancyReservationsCDF.RESERVATION_STATUS, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS)),
  ];
  return { ...getSummaryWithMultilevel(), groupColumns };
}

export function getDefaultFilter() {
  const filters = new Filters(FilterType.AND, [
    new Filter(
      new Cdf(BedOccupancyCDF.STAY_DATE),
      FilterByType.GREATER_THAN_OR_EQUAL,
      'start_last_month',
    ),
    new Filter(
      new Cdf(BedOccupancyCDF.STAY_DATE),
      FilterByType.LESS_THAN,
      'start_next_week',
    ),
  ]);
  return filters;
}

export function getDefaultFilterRoomTypeNotNull() {
  const filters = new Filters(FilterType.AND, [
    new Filter(
      new Cdf(BedOccupancyCDF.STAY_DATE),
      FilterByType.GREATER_THAN_OR_EQUAL,
      'start_last_month',
    ),
    new Filter(
      new Cdf(BedOccupancyCDF.STAY_DATE),
      FilterByType.LESS_THAN,
      'start_next_week',
    ),
    new Filter(new Cdf(BedOccupancyCDF.ROOM_TYPE_ID), FilterByType.IS_NOT_NULL, ''),
  ]);
  return filters;
}

export function getBookingDateFilter() {
  const filters = new Filters(FilterType.AND, [
    new Filter(
      new Cdf(
        OccupancyReservationsCDF.BOOKING_DATE,
        CdfKind.DEFAULT,
        Multilevel.BED_OCCUPANCY_RESERVATIONS,
      ),
      FilterByType.GREATER_THAN_OR_EQUAL,
      'start_last_year',
    ),
    new Filter(
      new Cdf(
        OccupancyReservationsCDF.BOOKING_DATE,
        CdfKind.DEFAULT,
        Multilevel.BED_OCCUPANCY_RESERVATIONS,
      ),
      FilterByType.LESS_THAN,
      'start_next_week',
    ),
  ]);
  return filters;
}

export function getDefaultPeriods() {
  const periodOne = 'Last Year';
  const periodTwo = 'YTD';
  const periods = [
    new Period(
      new Cdf(BedOccupancyCDF.STAY_DATE),
      periodOne,
      'start_last_year',
      'start_current_year',
    ),
    new Period(
      new Cdf(BedOccupancyCDF.STAY_DATE),
      periodTwo,
      'start_current_year',
      'start_next_year',
    ),
  ];
  return { periodOne, periodTwo, periods };
}

export function getPaceReport(modifier: Modifier = Modifier.MONTH_WITHOUT_YEAR) {
  const columns = [
    new Column(new Cdf(bedOccupancyMetricCdfs[0]), metrics),
  ];
  const groupRows = [
    new GroupRow(new Cdf(BedOccupancyCDF.PROPERTY_ID)),
    new GroupRow(new Cdf(BedOccupancyCDF.ROOM_TYPE_ID)),
    new GroupRow(new Cdf(BedOccupancyCDF.STAY_DATE), modifier),
  ];

  const sorts = [
    new Sort(new Cdf(BedOccupancyCDF.PROPERTY_ID), SortType.DESC),
    new Sort(new Cdf(BedOccupancyCDF.ROOM_TYPE_ID), SortType.DESC),
    new Sort(new Cdf(BedOccupancyCDF.STAY_DATE), SortType.DESC),
  ];

  const periodOne = 'Last Year';
  const periodTwo = 'This Year (so far)';
  const periods = [
    new Period(new Cdf(BedOccupancyCDF.STAY_DATE), periodOne, 'start_last_year', 'start_current_year'),
    new Period(new Cdf(BedOccupancyCDF.STAY_DATE), periodTwo, 'start_current_year', 'start_next_year'),
  ];
  return { columns, groupRows, sorts, periods };
}

export function threeDaysPeriods() {
  const periods = [
    new Period(new Cdf(BedOccupancyCDF.STAY_DATE), 'Yesterday', 'yesterday', 'today', false),
    new Period(new Cdf(BedOccupancyCDF.STAY_DATE), 'Today', 'today', 'tomorrow', false),
    new Period(new Cdf(BedOccupancyCDF.STAY_DATE), 'Tomorrow', 'tomorrow', 'days_later;1', false),
  ];
  return { periods };
}

export function threeDaysBookingDatePeriods() {
  const periods = [
    new Period(new Cdf(OccupancyReservationsCDF.BOOKING_DATE, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS), 'Yesterday', 'yesterday', 'today', false),
    new Period(new Cdf(OccupancyReservationsCDF.BOOKING_DATE, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS), 'Today', 'today', 'tomorrow', false),
    new Period(new Cdf(OccupancyReservationsCDF.BOOKING_DATE, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS), 'Tomorrow', 'tomorrow', 'days_later;1', false),
  ];
  return { periods };
}

export function getCustomCdf() {
  return new CustomCdf('pms_occupancy', 'Custom PMS Occupancy', [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, BedOccupancyCDF.ROOM_RATE, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, BedOccupancyCDF.ROOM_FEES, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '-'),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, BedOccupancyCDF.ROOM_TAXES, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '-'),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, BedOccupancyCDF.GUEST_COUNT, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '100'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfNestedDenominator() {
  return new CustomCdf('nested_denominator', 'Nested Denominator', [
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '9'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '9'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),

  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfNestedNumeratorAndDenominator() {
  return new CustomCdf('nested_numerator_and_denominator', 'Nested Numerator and Denominator', [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '2'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '3'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '4'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfDoubleNestedNumeratorAndDenominator() {
  return new CustomCdf('double_nested_numerator_and_denominator', 'Double Nested Numerator and Denominator', [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '9'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '8'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '7'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '6'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '5'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '4'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '+'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '3'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '+'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '2'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfDivideByZero() {
  return new CustomCdf('divide_by_zero', 'Divide by Zero', [
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '0'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getTwoThirdsCustomCdf(kind: CustomCdfKind, format: CustomCdfFormat) {
  return new CustomCdf(`${kind}_${format}`, `${kind}_${format}`, [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '2'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '3'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '100'),

  ], kind, format);
}

export function getSummaryReportGroupByMonthAndWeek() {
  const columns = [
    new Column(new Cdf(BedOccupancyCDF.ROOM_REVENUE), metrics),
    new Column(new Cdf(BedOccupancyCDF.BED_OCCUPANCY)),
  ];

  const groupRows = [
    new GroupRow(new Cdf(BedOccupancyCDF.ROOM_TYPE_ID)),
    new GroupRow(new Cdf(BedOccupancyCDF.STAY_DATE), Modifier.MONTH_WITHOUT_YEAR),
    new GroupRow(new Cdf(OccupancyReservationsCDF.BOOKING_DATE, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS), Modifier.WEEK),
  ];

  const sorts = [
    new Sort(new Cdf(BedOccupancyCDF.ROOM_TYPE_ID), SortType.ASC),
    new Sort(new Cdf(BedOccupancyCDF.STAY_DATE), SortType.ASC),
    new Sort(new Cdf(OccupancyReservationsCDF.BOOKING_DATE, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS), SortType.ASC),
  ];

  return { columns, groupRows, sorts };
}

export function descendingSorts() {
  const sorts = [
    new Sort(new Cdf(BedOccupancyCDF.ROOM_TYPE_ID), SortType.DESC),
    new Sort(new Cdf(BedOccupancyCDF.STAY_DATE), SortType.DESC),
    new Sort(new Cdf(OccupancyReservationsCDF.BOOKING_DATE, CdfKind.DEFAULT, Multilevel.BED_OCCUPANCY_RESERVATIONS), SortType.DESC),
  ];

  return sorts;
}
