import { InvoiceCDF } from '@/entities/datasets/invoice.cdf';
import { InvoiceItemsCDF } from '@/entities/multilevel/invoiceItems.cdf';
import { Multilevel } from '@/entities/multilevel/multilevel.type';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import { invoiceMetricCdfs, invoiceItemsMetricCdfs, metrics } from '@/entities/report/columns/metrics/metrics';
import { CdfKind } from '@/entities/report/enums/cdf.type';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';

export function getListReportWithAllColumns() {
  return Object.values(InvoiceCDF).map(cdf => {
    if (invoiceMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(InvoiceCDF).forEach((cdf: any) => {
    if (invoiceItemsMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getListReportWithAllColumnsAndMultilevels() {
  const invoiceCdfs = Object.values(InvoiceCDF).map(cdf => {
    if (invoiceMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });

  const invoiceItemsCdfs = Object.values(InvoiceItemsCDF).map(cdf => {
    if (invoiceItemsMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf, CdfKind.DEFAULT, Multilevel.INVOICE_ITEMS), metrics);
    }
    return new Column(new Cdf(cdf, CdfKind.DEFAULT, Multilevel.INVOICE_ITEMS));
  });

  return invoiceCdfs.concat(invoiceItemsCdfs);
}

export function getSummaryReport() {
  const columns = invoiceMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(InvoiceCDF.RESERVATION_DATETIME), Modifier.MONTH),
    new GroupRow(new Cdf(InvoiceCDF.INVOICE_GENERATE_DATETIME), Modifier.MONTH),
  ];

  return { columns, groupRows };
}

export function getSummaryWithMultilevel() {
  const columns = invoiceMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(InvoiceCDF.INVOICE_GENERATE_DATETIME), Modifier.MONTH),
    new GroupRow(new Cdf(InvoiceItemsCDF.DATE_POSTED, CdfKind.DEFAULT, Multilevel.INVOICE_ITEMS), Modifier.MONTH),
  ];

  return { columns, groupRows };
}

export function getPivotReport() {
  const groupColumns = [
    new GroupColumn(new Cdf(InvoiceCDF.INVOICE_STATUS)),
    new GroupColumn(new Cdf(InvoiceCDF.INVOICE_CURRENCY_CODE)),
  ];
  return { ...getSummaryReport(), groupColumns };
}

export function getPivotReportWithMultilevels() {
  const groupColumns = [
    new GroupColumn(new Cdf(InvoiceCDF.INVOICE_STATUS)),
  ];
  return { ...getSummaryWithMultilevel(), groupColumns };
}

export function getDefaultFilter() {
  const filterByYear = new Date(`${new Date().getFullYear()}-01-01`);
  const date = filterByYear.toISOString();
  const filters = new Filters(FilterType.AND, [
    new Filter(new Cdf(InvoiceCDF.INVOICE_GENERATE_DATETIME), FilterByType.GREATER_THAN_OR_EQUAL, date),
  ]);
  return filters;
}

export function getDefaultPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(new Cdf(InvoiceCDF.RESERVATION_DATETIME), periodOne, 'yesterday', 'tomorrow'),
    new Period(new Cdf(InvoiceCDF.RESERVATION_DATETIME), periodTwo, 'start_last_year', 'tomorrow'),
    new Period(new Cdf(InvoiceCDF.RESERVATION_DATETIME), periodThree, 'start_current_year', 'tomorrow'),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}
