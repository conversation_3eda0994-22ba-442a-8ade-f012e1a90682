import { GuestCDF } from '@/entities/datasets/guest.cdf';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import { guestMetricCdfs, metrics } from '@/entities/report/columns/metrics/metrics';
import { Comparison } from '@/entities/report/comparisons/comparison.model';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { SortType } from '@/entities/report/enums/sort.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';
import { Sort } from '@/entities/report/sort/sort.model';

export function getListReportWithAllColumns() {
  return Object.values(GuestCDF).map(cdf => new Column(new Cdf(cdf)));
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(GuestCDF).forEach((cdf: any) => {
    if (guestMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getSummaryReport() {
  const columns = guestMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(GuestCDF.GUEST_RESIDENCE_COUNTRY)),
    new GroupRow(new Cdf(GuestCDF.RESERVATION_STATUS)),
  ];

  return { columns, groupRows };
}

export function getPivotReport() {
  const columns = guestMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(GuestCDF.GUEST_RESIDENCE_COUNTRY)),
    new GroupRow(new Cdf(GuestCDF.RESERVATION_STATUS)),
  ];
  const groupColumns = [
    new GroupColumn(new Cdf(GuestCDF.GUEST_BIRTH_DATE), Modifier.YEAR),
  ];
  return { columns, groupRows, groupColumns };
}

export function getDefaultFilter() {
  const filterByYear = new Date(`${new Date().getFullYear()}-01-01`);
  const date = filterByYear.toISOString();
  const filters = new Filters(FilterType.AND, [
    new Filter(new Cdf(GuestCDF.BOOKING_DATETIME), FilterByType.GREATER_THAN_OR_EQUAL, date),
  ]);
  return filters;
}

export function getDefaultPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(new Cdf(GuestCDF.BOOKING_DATETIME), periodOne, 'yesterday', 'tomorrow'),
    new Period(new Cdf(GuestCDF.BOOKING_DATETIME), periodTwo, 'start_last_year', 'tomorrow'),
    new Period(new Cdf(GuestCDF.BOOKING_DATETIME), periodThree, 'start_current_year', 'tomorrow'),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getDefaultPeriodsDifferentCdfs() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(new Cdf(GuestCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), periodOne, 'yesterday', 'tomorrow'),
    new Period(new Cdf(GuestCDF.BOOKING_DATETIME), periodTwo, 'start_last_year', 'tomorrow'),
    new Period(new Cdf(GuestCDF.BOOKING_DATETIME), periodThree, 'start_current_year', 'tomorrow'),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getPeriodSummaryReport() {
  const { periods } = getDefaultPeriods();
  const sorts = [new Sort(new Cdf(GuestCDF.GUEST_RESIDENCE_COUNTRY), SortType.DESC), new Sort(new Cdf(GuestCDF.RESERVATION_STATUS), SortType.DESC)];

  return { ...getSummaryReport(), periods, sorts };
}

export function getPeriodSummaryReportDifferentCdfs() {
  const { periods } = getDefaultPeriodsDifferentCdfs();
  const sorts = [new Sort(new Cdf(GuestCDF.GUEST_RESIDENCE_COUNTRY), SortType.DESC), new Sort(new Cdf(GuestCDF.RESERVATION_STATUS), SortType.DESC)];

  return { ...getSummaryReport(), periods, sorts };
}

export function getDefaultComparisons() {
  const comparisonOne = '<=0';
  const comparisonTwo = '1-30';
  const comparisonThree = '31-60';
  const comparisonFour = '61-90';
  const comparisonFive = '>90';

  const comparisons = [
    new Comparison(
      comparisonOne,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.LESS_THAN_OR_EQUAL,
          '0',
        ),
      ]),
    ),
    new Comparison(
      comparisonTwo,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.GREATER_THAN,
          '0',
        ),
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.LESS_THAN_OR_EQUAL,
          '30',
        ),
      ]),
    ),
    new Comparison(
      comparisonThree,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.GREATER_THAN,
          '30',
        ),
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.LESS_THAN_OR_EQUAL,
          '60',
        ),
      ]),
    ),
    new Comparison(
      comparisonFour,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.GREATER_THAN,
          '60',
        ),
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.LESS_THAN_OR_EQUAL,
          '90',
        ),
      ]),
    ),
    new Comparison(
      comparisonFive,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(GuestCDF.DURATION_OF_STAY),
          FilterByType.GREATER_THAN,
          '90',
        ),
      ]),
    ),
  ];
  return { comparisonOne, comparisonTwo, comparisonThree, comparisons };
}
