import { ChartCategory } from '@/entities/chart/chartCategory.model';
import { ChartMetric } from '@/entities/chart/chartMetric.model';
import { FinancialCDF } from '@/entities/datasets/financial.cdf';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import { financialMetricCdfs, metrics } from '@/entities/report/columns/metrics/metrics';
import { Comparison } from '@/entities/report/comparisons/comparison.model';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { CustomCdfFormula } from '@/entities/report/customCdf/customCdfFormula.model';
import { CustomCdfFormat, CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { MetricType } from '@/entities/report/enums/metric.type';
import { SortType } from '@/entities/report/enums/sort.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';
import { Sort } from '@/entities/report/sort/sort.model';

export function getListReportWithAllColumns() {
  return Object.values(FinancialCDF).map(cdf => {
    if (financialMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(FinancialCDF).forEach((cdf: any) => {
    if (financialMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getSummaryReportWithPropertyId() {
  const columns = financialMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(FinancialCDF.TRANSACTION_DATETIME), Modifier.MONTH),
    new GroupRow(new Cdf(FinancialCDF.IS_REFUND)),
    new GroupRow(new Cdf(FinancialCDF.PROPERTY_ID)),
  ];

  return { columns, groupRows };
}

export function getSummaryReport() {
  const columns = financialMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(FinancialCDF.TRANSACTION_DATETIME), Modifier.MONTH),
    new GroupRow(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), Modifier.MONTH),
  ];

  return { columns, groupRows };
}

export function getChartReport() {
  const columns = financialMetricCdfs.map(cdf => new Column(new Cdf(cdf), metrics));
  const groupRows = [
    new GroupRow(new Cdf(FinancialCDF.TRANSACTION_DATETIME), Modifier.MONTH),
  ];

  const chartMetrics = [new ChartMetric(new Cdf(FinancialCDF.DEBIT_AMOUNT), [MetricType.SUM])];
  const chartCategories = [
    new ChartCategory(new Cdf(FinancialCDF.TRANSACTION_DATETIME), Modifier.MONTH),
  ];

  return { columns, groupRows, chartCategories, chartMetrics };
}

export function getPivotReport() {
  const groupColumns = [
    new GroupColumn(new Cdf(FinancialCDF.ROOM_NUMBER)),
    new GroupColumn(new Cdf(FinancialCDF.FEE_TYPE)),
  ];
  return { ...getSummaryReport(), groupColumns };
}

export function getPivotReportGroupByMonthAndWeek() {
  const columns = [
    new Column(new Cdf(FinancialCDF.DEBIT_AMOUNT), metrics),
    new Column(new Cdf(FinancialCDF.CREDIT_AMOUNT), metrics),
  ];

  const groupRows = [
    new GroupRow(new Cdf(FinancialCDF.TRANSACTION_DATETIME), Modifier.MONTH_WITHOUT_YEAR),
    new GroupRow(new Cdf(FinancialCDF.TRANSACTION_DATETIME_PROPERTY_TIMEZONE), Modifier.WEEK),
  ];
  const groupColumns = [
    new GroupColumn(new Cdf(FinancialCDF.BOOKING_DATETIME), Modifier.WEEK),
    new GroupColumn(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), Modifier.MONTH_WITHOUT_YEAR),
  ];

  const sorts = [
    new Sort(new Cdf(FinancialCDF.TRANSACTION_DATETIME), SortType.ASC),
    new Sort(new Cdf(FinancialCDF.TRANSACTION_DATETIME_PROPERTY_TIMEZONE), SortType.ASC),
  ];
  return { columns, groupRows, groupColumns, sorts };
}

export function getDefaultFilter() {
  const filterByYear = new Date(`${new Date().getFullYear()}-01-01`);
  const date = filterByYear.toISOString();
  const filters = new Filters(FilterType.AND, [
    new Filter(new Cdf(FinancialCDF.BOOKING_DATETIME), FilterByType.GREATER_THAN_OR_EQUAL, date),
  ]);
  return filters;
}

export function getDefaultPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(new Cdf(FinancialCDF.BOOKING_DATETIME), periodOne, 'yesterday', 'tomorrow'),
    new Period(new Cdf(FinancialCDF.BOOKING_DATETIME), periodTwo, 'start_last_year', 'tomorrow'),
    new Period(new Cdf(FinancialCDF.BOOKING_DATETIME), periodThree, 'start_current_year', 'tomorrow'),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getDefaultPeriodsDifferentCdfs() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(new Cdf(FinancialCDF.TRANSACTION_DATETIME_PROPERTY_TIMEZONE), periodOne, 'yesterday', 'tomorrow'),
    new Period(new Cdf(FinancialCDF.BOOKING_DATETIME), periodTwo, 'start_last_year', 'tomorrow'),
    new Period(new Cdf(FinancialCDF.BOOKING_DATETIME), periodThree, 'start_current_year', 'tomorrow'),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getPeriodSummaryReport() {
  const { periods } = getDefaultPeriods();
  const sorts = [new Sort(new Cdf(FinancialCDF.TRANSACTION_DATETIME), SortType.DESC), new Sort(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), SortType.DESC)];

  return { ...getSummaryReport(), periods, sorts };
}

export function getSummaryReportGroupByMonthAndWeek() {
  const columns = [
    new Column(new Cdf(FinancialCDF.DEBIT_AMOUNT), metrics),
    new Column(new Cdf(FinancialCDF.CREDIT_AMOUNT), metrics),
  ];

  const groupRows = [
    new GroupRow(new Cdf(FinancialCDF.TRANSACTION_STATUS)),
    new GroupRow(new Cdf(FinancialCDF.TRANSACTION_DATETIME), Modifier.MONTH_WITHOUT_YEAR),
    new GroupRow(new Cdf(FinancialCDF.BOOKING_DATETIME), Modifier.WEEK),
  ];

  const sorts = [
    new Sort(new Cdf(FinancialCDF.TRANSACTION_STATUS), SortType.ASC),
    new Sort(new Cdf(FinancialCDF.TRANSACTION_DATETIME), SortType.ASC),
    new Sort(new Cdf(FinancialCDF.BOOKING_DATETIME), SortType.ASC),
  ];

  return { columns, groupRows, sorts };
}

export function getPeriodSummaryReportGroupByMonthAndWeek() {
  const { periods } = getDefaultPeriods();

  const { columns, groupRows, sorts } = getSummaryReportGroupByMonthAndWeek();

  return { columns, groupRows, periods, sorts };
}

export function getPeriodSummaryReportDifferentCdfs() {
  const { periods } = getDefaultPeriodsDifferentCdfs();
  const sorts = [new Sort(new Cdf(FinancialCDF.TRANSACTION_DATETIME), SortType.DESC), new Sort(new Cdf(FinancialCDF.BOOKING_DATETIME_PROPERTY_TIMEZONE), SortType.DESC)];

  return { ...getSummaryReport(), periods, sorts };
}

export function getCustomDynamicCdfDivideByZero() {
  return new CustomCdf('divide_by_zero', 'Divide by Zero', [
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '0'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomNumberCdf() {
  return new CustomCdf('number', 'Number', [
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '10'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '+'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '20'),
  ], CustomCdfKind.NUMBER, CustomCdfFormat.NUMBER);
}

export function getCustomDynamicCdf() {
  return new CustomCdf('dynamic', 'Dynamic', [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.DEBIT_AMOUNT, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, FinancialCDF.CREDIT_AMOUNT, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '100'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function descendingSorts() {
  const sorts = [
    new Sort(new Cdf(FinancialCDF.TRANSACTION_STATUS), SortType.DESC),
    new Sort(new Cdf(FinancialCDF.TRANSACTION_DATETIME), SortType.DESC),
    new Sort(new Cdf(FinancialCDF.BOOKING_DATETIME), SortType.DESC),
  ];

  return sorts;
}

export function getDefaultComparisons() {
  const comparisonOne = '<=0';
  const comparisonTwo = '1-30';
  const comparisonThree = '31-60';
  const comparisonFour = '61-90';
  const comparisonFive = '>90';

  const comparisons = [
    new Comparison(
      comparisonOne,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.LESS_THAN_OR_EQUAL,
          '0',
        ),
      ]),
    ),
    new Comparison(
      comparisonTwo,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.GREATER_THAN,
          '0',
        ),
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.LESS_THAN_OR_EQUAL,
          '30',
        ),
      ]),
    ),
    new Comparison(
      comparisonThree,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.GREATER_THAN,
          '30',
        ),
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.LESS_THAN_OR_EQUAL,
          '60',
        ),
      ]),
    ),
    new Comparison(
      comparisonFour,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.GREATER_THAN,
          '60',
        ),
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.LESS_THAN_OR_EQUAL,
          '90',
        ),
      ]),
    ),
    new Comparison(
      comparisonFive,
      new Filters(FilterType.AND, [
        new Filter(
          new Cdf(FinancialCDF.DEBIT_AMOUNT),
          FilterByType.GREATER_THAN,
          '90',
        ),
      ]),
    ),
  ];
  return { comparisonOne, comparisonTwo, comparisonThree, comparisonFour, comparisonFive, comparisons };
}
