import { OccupancyCDF } from '@/entities/datasets/occupancy.cdf';
import { Cdf } from '@/entities/report/cdf/cdf.model';
import { Column } from '@/entities/report/columns/column.model';
import {
  occupancyMetricCdfs,
  metrics,
} from '@/entities/report/columns/metrics/metrics';
import { CustomCdf } from '@/entities/report/customCdf/customCdf.model';
import { CustomCdfFormula } from '@/entities/report/customCdf/customCdfFormula.model';
import { CustomCdfFormat, CustomCdfFormulaKind, CustomCdfKind } from '@/entities/report/enums/customCdf.type';
import { FilterType } from '@/entities/report/enums/filter.type';
import { FilterByType } from '@/entities/report/enums/filterBy.type';
import { MetricType } from '@/entities/report/enums/metric.type';
import { Modifier } from '@/entities/report/enums/time.type';
import { Filter } from '@/entities/report/filters/filter.model';
import { Filters } from '@/entities/report/filters/filters';
import { GroupColumn } from '@/entities/report/groupColumn/groupColumn.model';
import { GroupRow } from '@/entities/report/groupRows/groupRow.model';
import { Period } from '@/entities/report/periods/period.model';

export function getListReportWithAllColumns() {
  return Object.values(OccupancyCDF).map(cdf => {
    if (occupancyMetricCdfs.includes(cdf)) {
      return new Column(new Cdf(cdf), metrics);
    }
    return new Column(new Cdf(cdf));
  });
}

export function getListReportWithAllMetrics() {
  const metricCdfs: any = [];
  Object.values(OccupancyCDF).forEach((cdf: any) => {
    if (occupancyMetricCdfs.includes(cdf)) {
      metricCdfs.push(new Column(new Cdf(cdf), metrics));
    }
  });
  return metricCdfs;
}

export function getSummaryReport() {
  const columns = occupancyMetricCdfs.map(
    cdf => new Column(new Cdf(cdf), metrics),
  );
  const groupRows = [
    new GroupRow(new Cdf(OccupancyCDF.STAY_DATE), Modifier.MONTH),
    new GroupRow(new Cdf(OccupancyCDF.BOOKING_DATE), Modifier.MONTH),
  ];

  return { columns, groupRows };
}

export function getPivotReport() {
  const columns = [
    new Column(new Cdf(OccupancyCDF.OCCUPANCY)),
    new Column(new Cdf(OccupancyCDF.ADR)),
  ];
  const groupRows = [
    new GroupRow(new Cdf(OccupancyCDF.STAY_DATE), Modifier.MONTH),
  ];
  const groupColumns = [
    new GroupColumn(new Cdf(OccupancyCDF.BOOKING_DATE), Modifier.YEAR),
  ];
  return { columns, groupRows, groupColumns };
}

export function getDefaultFilter() {
  const filters = new Filters(FilterType.AND, [
    new Filter(
      new Cdf(OccupancyCDF.STAY_DATE),
      FilterByType.GREATER_THAN_OR_EQUAL,
      'start_last_week',
    ),
  ]);
  return filters;
}

export function getDefaultPeriods() {
  const periodOne = 'Today';
  const periodTwo = 'Last year to now';
  const periodThree = 'This year so far';
  const periods = [
    new Period(
      new Cdf(OccupancyCDF.BOOKING_DATE),
      periodOne,
      'yesterday',
      'tomorrow',
    ),
    new Period(
      new Cdf(OccupancyCDF.BOOKING_DATE),
      periodTwo,
      'start_last_year',
      'tomorrow',
    ),
    new Period(
      new Cdf(OccupancyCDF.BOOKING_DATE),
      periodThree,
      'start_current_year',
      'tomorrow',
    ),
  ];
  return { periodOne, periodTwo, periodThree, periods };
}

export function getOccupancyDynamicCustomCdf() {
  return new CustomCdf('occupancy', 'Custom Occupancy', [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.BOOKING_QTY_TYPE_A, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.CAPACITY_TYPE_A, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '-'),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.OUT_OF_SERVICE_TYPE_A, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '100'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getAdrDynamicCustomCdf() {
  return new CustomCdf('adr', 'Custom ADR', [
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.ROOM_RATE_AMOUNT, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.BOOKING_QTY_TYPE_A, MetricType.SUM),

  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.CURRENCY);
}

export function getRevParDynamicCustomCdf() {
  return new CustomCdf('revpar', 'Custom RevPar', [
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.ROOM_RATE_AMOUNT, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.CAPACITY_TYPE_A, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '-'),
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.OUT_OF_SERVICE_TYPE_A, MetricType.SUM),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.CURRENCY);
}

export function getCustomDynamicCdfNestedDenominator() {
  return new CustomCdf('nested_denominator', 'Nested Denominator', [
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '9'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '9'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),

  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfNestedNumeratorAndDenominator() {
  return new CustomCdf('nested_numerator_and_denominator', 'Nested Numerator and Denominator', [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '2'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '3'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '4'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfDoubleNestedNumeratorAndDenominator() {
  return new CustomCdf('double_nested_numerator_and_denominator', 'Double Nested Numerator and Denominator', [
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '9'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '8'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '7'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '6'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '5'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '*'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '4'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '+'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, '('),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '3'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '+'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '2'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
    new CustomCdfFormula(CustomCdfFormulaKind.PARENTHESIS, ')'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfDivideByZero() {
  return new CustomCdf('divide_by_zero', 'Divide by Zero', [
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '1'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERATOR, '/'),
    new CustomCdfFormula(CustomCdfFormulaKind.OPERAND, '0'),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}

export function getCustomDynamicCdfSingleMetric() {
  return new CustomCdf('single_metric', 'Single Metric', [
    new CustomCdfFormula(CustomCdfFormulaKind.CDF, OccupancyCDF.ROOM_RATE_AMOUNT, MetricType.SUM),
  ], CustomCdfKind.DYNAMIC, CustomCdfFormat.PERCENTAGE);
}
