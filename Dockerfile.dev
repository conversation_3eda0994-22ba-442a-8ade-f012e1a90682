###############################################################################
# Base image
###############################################################################
FROM public.ecr.aws/docker/library/python:3.10-slim@sha256:06f6d69d229bb55fab83dded514e54eede977e33e92d855ba3f97ce0e3234abc AS base

# Build time args
ARG USERNAME=reporting
ARG USER_UID=1000
ARG USER_GID=${USER_UID}
ARG APP_DIR=/usr/src

WORKDIR ${APP_DIR}

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

ENV VIRTUAL_ENV=${APP_DIR}/.venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Install the runtime dependencies. libcairo2 is needed to convert svg to png
RUN apt-get update && apt-get -y install libcairo2



###############################################################################
## PIP dependencies install
###############################################################################
FROM base AS pip

# Create a virtual environment
RUN python3 -m venv $VIRTUAL_ENV

# Upgrade pip
RUN pip install --upgrade pip

# Install the build dependencies
RUN apt-get update && apt-get -y install git

# Install the pip dependencies
COPY ./dependencies/requirements.txt ${APP_DIR}/dependencies/requirements.txt
COPY ./dependencies/requirements-dev.txt ${APP_DIR}/dependencies/requirements-dev.txt
RUN --mount=type=secret,id=token,env=GITHUB_TOKEN \
    git config --global url."https://x-access-token:${GITHUB_TOKEN}@github.com/".insteadOf 'https://github.com/' && \
    pip install -r dependencies/requirements.txt && \
    pip install -r dependencies/requirements-dev.txt


###############################################################################
## Common source copy
###############################################################################
FROM base AS src-common

# Copy common project files
COPY ./dependencies/sqlalchemy_filters/ dependencies/sqlalchemy_filters
COPY app/ app
COPY celery_app/ celery_app
COPY manage.py .



###############################################################################
## Final image
###############################################################################
FROM base AS final

# Add user and group
RUN groupadd --gid ${USER_GID} ${USERNAME} \
    && useradd --uid ${USER_UID} --gid ${USER_GID} -m ${USERNAME}

USER ${USERNAME}

# Set some Environment Variables
ENV FLASK_APP=manage.py
ENV FLASK_DEBUG=TRUE
ENV DEBUG=TRUE
ENV ENV="LOCAL"


# Copy the virtual environment
COPY --from=pip ${VIRTUAL_ENV} ${VIRTUAL_ENV}

# Copy common project files
COPY --from=src-common ${APP_DIR} ${APP_DIR}

# Copy files needed by this image
COPY wsgi_conf/ wsgi_conf/
COPY babel.cfg .
COPY crowdin.yaml .

# Command to run with gunicorn
CMD ["gunicorn", "manage:app", "--workers=5", "-b", "0.0.0.0:5000"]
