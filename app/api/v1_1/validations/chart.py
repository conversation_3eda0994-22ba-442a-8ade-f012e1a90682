from flask import g

from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.enums.chart import ChartKind
from app.common.exceptions import InvalidUsage
from app.enums import Cdf, CdfKind, Dataset
from app.enums.multi_level import MultiLevel
from app.enums.report import ReportKind
from app.models.chart import Chart
from app.models.report import Report
from app.models.stock_report import StockReport


def validate_chart(data: dict, report: Report = None) -> None | InvalidUsage:
    if not report:
        report = (
            Report.get_by_id(g.report_id)
            if g.report_kind == ReportKind.Report
            else StockReport.get_by_id(g.report_id)
        )
    validate_categories(data, report)
    validate_metrics(data, report)
    validate_settings(data, report)


def validate_categories(data: dict, report: Report) -> None | InvalidUsage:
    report_categories = []
    if report.group_rows:
        report_categories += report.group_rows
    if report.group_columns:
        report_categories += report.group_columns
    if report.periods:
        report_categories += ["Periods"]

    chart_categories = data.get("categories")
    chart_kind = data.get("kind")

    if not report_categories or not all(
        [cdf in report_categories for cdf in chart_categories]
    ):
        raise InvalidUsage.bad_request(
            "All categories must appear in report group rows, group columns or periods"
        )
    match (chart_kind):
        case (
            ChartKind.Line.value
            | ChartKind.Bar.value
            | ChartKind.Area.value
            | ChartKind.Combo.value
        ):
            if len(chart_categories) != 1:
                raise InvalidUsage.bad_request(
                    f"{chart_kind} charts requires one category"
                )
        case ChartKind.Pie.value | ChartKind.Donut.value:
            if not (1 <= len(chart_categories) <= 2):
                raise InvalidUsage.bad_request(
                    f"{chart_kind} charts requires one or two categories"
                )
        case ChartKind.Sunburst.value:
            if len(chart_categories) < 2:
                raise InvalidUsage.bad_request(
                    f"{chart_kind} charts requires at least two categories"
                )
        case ChartKind.Treemap.value:
            if not (1 <= len(chart_categories) <= 3):
                raise InvalidUsage.bad_request(
                    f"{chart_kind} charts requires 1-3 categories"
                )
        case ChartKind.Geomap.value:
            if (
                len(chart_categories) != 1
                or CDF(
                    Dataset(report.dataset_id),
                    chart_categories[0]["cdf"]["column"],
                    chart_categories[0]["cdf"]["type"] == Cdf.Custom.value,
                    chart_categories[0]["cdf"]["type"] == Cdf.CustomField.value,
                    (
                        MultiLevel(chart_categories[0]["cdf"]["multi_level_id"])
                        if chart_categories[0]["cdf"].get("multi_level_id")
                        else None
                    ),
                ).kind
                != CdfKind.Country
            ):
                raise InvalidUsage.bad_request(
                    f"{chart_kind} charts require only one category based on country cdf"
                )
        case ChartKind.Heatmap.value:
            if len(chart_categories) != 1 or CDF(
                Dataset(report.dataset_id),
                chart_categories[0]["cdf"]["column"],
                chart_categories[0]["cdf"]["type"] == Cdf.Custom.value,
                chart_categories[0]["cdf"]["type"] == Cdf.CustomField.value,
                (
                    MultiLevel(chart_categories[0]["cdf"]["multi_level_id"])
                    if chart_categories[0]["cdf"].get("multi_level_id")
                    else None
                ),
            ).kind not in (CdfKind.Date, CdfKind.Timestamp):
                raise InvalidUsage.bad_request(
                    f"{chart_kind} charts require only one category based on date, datetime cdf"
                )
        case _:
            raise InvalidUsage.bad_request("unsupported chart type")


def validate_metrics(data: dict, report: Report) -> None | InvalidUsage:
    dynamic_custom_cdfs = [
        custom_cdf.column
        for custom_cdf in report.custom_cdfs
        if custom_cdf.kind == CdfKind.Dynamic.name
    ]
    report_metrics = CDFs.flatten_metrics(report.columns)
    chart_metrics = CDFs.flatten_metrics(data.get("metrics"))

    if not all([metric in report_metrics for metric in chart_metrics]):
        raise InvalidUsage.bad_request("All Metrics must appear in report columns")

    metrics_without_dynamic_custom_cdfs = [
        metric
        for metric in data.get("metrics")
        if metric["cdf"]["column"] not in dynamic_custom_cdfs
    ]
    if not all(
        [
            bool(cdf.get("metrics"))
            for cdf in metrics_without_dynamic_custom_cdfs
            if cdf["cdf"]["type"] == Cdf.Custom.value
            or not CDF(
                Dataset(report.dataset_id),
                cdf["cdf"]["column"],
                cdf["cdf"]["type"] == Cdf.Custom.value,
                cdf["cdf"]["type"] == Cdf.CustomField.value,
                (
                    MultiLevel(cdf["cdf"]["multi_level_id"])
                    if cdf["cdf"].get("multi_level_id")
                    else None
                ),
            ).is_dynamic_cdf()
        ]
    ):
        raise InvalidUsage.bad_request(
            "All metric columns need at least one metric, unless they are dynamic"
        )


def validate_settings(data: dict, report: Report) -> None | InvalidUsage:
    if "settings" in data and data["settings"] and data["settings"].get("stack"):
        if data.get("kind") != ChartKind.Bar.value:
            raise InvalidUsage.bad_request(
                f"{ChartKind.Bar.value} only can have defined stack options"
            )

    if data.get("kind") == ChartKind.Combo.value:
        if not data.get("settings") or not data.get("settings").get("metrics"):
            raise InvalidUsage.bad_request(
                f"{ChartKind.Combo.value} charts require settings and metrics inside to be defined"
            )

        report_metrics = CDFs.flatten_metrics(report.columns)
        chart_metrics = CDFs.flatten_metrics(data.get("metrics"))

        metrics = []

        dynamic_custom_cdfs = [
            custom_cdf.column
            for custom_cdf in report.custom_cdfs
            if custom_cdf.kind == CdfKind.Dynamic.name
        ]

        for metric in data.get("settings").get("metrics"):
            if (
                not CDF(
                    Dataset(report.dataset_id),
                    metric["cdf"]["column"],
                    metric["cdf"]["type"] == Cdf.Custom.value,
                    metric["cdf"]["type"] == Cdf.CustomField.value,
                    metric["cdf"].get("multi_level_id"),
                ).is_dynamic_cdf()
                and metric["cdf"]["type"] == Cdf.Default.value
            ):
                metrics.append({"cdf": metric["cdf"], "metrics": [metric["metric"]]})
            elif (
                metric["cdf"]["type"] == Cdf.Custom.value
                and metric["cdf"]["column"] not in dynamic_custom_cdfs
            ):
                metrics.append({"cdf": metric["cdf"], "metrics": [metric["metric"]]})

        chart_metrics += CDFs.flatten_metrics(metrics)

        if not all([metric in report_metrics for metric in chart_metrics]):
            raise InvalidUsage.bad_request(
                "All settings metrics must appear in report columns"
            )


def validate_chart_custom_cdf(
    chart: Chart, report_custom_cdf: dict, new_report_custom_cdf: dict
):
    """
    Function that will check if the updated custom cdf is breaking the chart by changing the kind
    when the custom cdf is used on the chart
    """
    if report_custom_cdf.kind != new_report_custom_cdf["kind"]:
        chart_columns = [category["cdf"]["column"] for category in chart.categories] + [
            metric["cdf"]["column"] for metric in chart.metrics
        ]

        if report_custom_cdf.column in chart_columns:
            raise InvalidUsage.bad_request(
                "Can not update the custom cdf kind, since is being used in a chart."
            )
