from datetime import datetime
from typing import Optional

from flask import g, request

from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.constants.dataset import DATASET_LABELS
from app.common.exceptions import InvalidUsage
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums import (
    Cdf,
    CdfKind,
    Dataset as DatasetEnum,
    MultiLevel as MultiLevelEnum,
    RelativeDate as RelativeDateEnum,
)
from app.enums.custom_fields import CustomFields
from app.enums.mode import Mode
from app.enums.report import ReportKind
from app.filters.relative_date import RelativeDate
from app.models.report import Report
from app.services.custom_cdf_service import CustomCdfService
from app.services.custom_field_service import CustomFieldService
from app.services.dataset_service import DatasetService
from app.services.property_service import PropertyService


def validate_property_id(property_id: int):
    PropertyService.are_valid_property_ids([property_id])


def validate_property_ids(property_ids: list[int]):
    PropertyService.are_valid_property_ids(property_ids)


has_custom_cdfs = lambda cdfs: bool(CDFs.get_custom_cdfs(cdfs))


def validate_custom_cdfs_report_creation(data):
    if (
        "columns" in data
        and data["columns"] is not None
        and has_custom_cdfs(data["columns"])
    ):
        raise InvalidUsage.bad_request(
            "Custom columns can't be defined when creating a report"
        )

    if (
        "group_rows" in data
        and data["group_rows"] is not None
        and has_custom_cdfs(data["group_rows"])
    ):
        raise InvalidUsage.bad_request(
            "Custom group rows can't be defined when creating a report"
        )

    if (
        "group_columns" in data
        and data["group_columns"] is not None
        and has_custom_cdfs(data["group_columns"])
    ):
        raise InvalidUsage.bad_request(
            "Custom group columns can't be defined when creating a report"
        )

    if (
        "filters" in data
        and data["filters"] is not None
        and has_custom_cdfs(CDFs.get_cdfs_from_filters(data["filters"], []))
    ):
        raise InvalidUsage.bad_request(
            "Custom filters can't be defined when creating a report"
        )

    if "sort" in data and data["sort"] is not None and has_custom_cdfs(data["sort"]):
        raise InvalidUsage.bad_request(
            "Custom sorts can't be defined when creating a report"
        )


def validate_report_has_custom_cdfs(report_id, custom_cdfs, report_kind: ReportKind):
    for custom_cdf in custom_cdfs:
        if not CustomCdfService.is_custom_cdf_in_report(
            report_kind, report_id, custom_cdf["cdf"]["column"]
        ):
            raise InvalidUsage.not_found(
                "Provided Custom CDF does not exist on current report. Please create it before updating the report"
            )


def validate_custom_cdfs(data, report_kind: ReportKind):
    if request and request.method == "POST" and "clone" not in request.path:
        validate_custom_cdfs_report_creation(data)
    else:
        # Validate if the custom cdf is stored in the stock reports
        if request and all(
            value in request.path for value in ["clone", "stock_reports"]
        ):
            report_kind = ReportKind.StockReport

        if (
            "columns" in data
            and data["columns"] is not None
            and has_custom_cdfs(data["columns"])
        ):
            validate_report_has_custom_cdfs(
                g.report_id, CDFs.get_custom_cdfs(data["columns"]), report_kind
            )

            custom_columns_metrics = [
                custom_column_metric["cdf"]["column"]
                for custom_column_metric in CDFs.get_custom_cdfs(data["columns"])
                if "metrics" in custom_column_metric
            ]

            if len(custom_columns_metrics):
                number_custom_cdfs = [
                    number_custom_cdf.column
                    for number_custom_cdf in CustomCdfService.get_by_report_id(
                        report_kind, g.report_id
                    )
                    if number_custom_cdf.kind == CdfKind.Number.name
                ]

                if not all(
                    number_custom_cdf in number_custom_cdfs
                    for number_custom_cdf in custom_columns_metrics
                ):
                    raise InvalidUsage.bad_request(
                        "Custom columns with metrics only can be defined on number CDFs"
                    )

        if (
            "group_rows" in data
            and data["group_rows"] is not None
            and has_custom_cdfs(data["group_rows"])
        ):
            validate_report_has_custom_cdfs(
                g.report_id, CDFs.get_custom_cdfs(data["group_rows"]), report_kind
            )

        if (
            "group_columns" in data
            and data["group_columns"] is not None
            and has_custom_cdfs(data["group_columns"])
        ):
            validate_report_has_custom_cdfs(
                g.report_id, CDFs.get_custom_cdfs(data["group_columns"]), report_kind
            )

        if (
            "filters" in data
            and data["filters"] is not None
            and has_custom_cdfs(CDFs.get_cdfs_from_filters(data["filters"], []))
        ):
            validate_report_has_custom_cdfs(
                g.report_id,
                CDFs.get_custom_cdfs(CDFs.get_cdfs_from_filters(data["filters"], [])),
                report_kind,
            )

        if (
            "sort" in data
            and data["sort"] is not None
            and has_custom_cdfs(data["sort"])
        ):
            validate_report_has_custom_cdfs(
                g.report_id, CDFs.get_custom_cdfs(data["sort"]), report_kind
            )


def validate_custom_cdfs_query(data):
    custom_cdfs = (
        [custom_cdf["column"] for custom_cdf in data["custom_cdfs"]]
        if "custom_cdfs" in data and data["custom_cdfs"] is not None
        else []
    )
    dynamic_custom_cdfs = (
        [
            custom_cdf["column"]
            for custom_cdf in data["custom_cdfs"]
            if custom_cdf["kind"] == "Dynamic"
        ]
        if "custom_cdfs" in data and data["custom_cdfs"] is not None
        else []
    )

    if any(
        [
            column
            for column in data["columns"]
            if column["cdf"]["column"] in dynamic_custom_cdfs and column.get("metrics")
        ]
    ):
        raise InvalidUsage.bad_request("Can't use metrics on dynamic custom cdfs")
    for key in ["columns", "group_rows", "group_columns", "sort"]:
        if key in data and data[key] is not None and has_custom_cdfs(data[key]):
            key_custom_cdfs = [
                key_custom_cdf["cdf"]["column"]
                for key_custom_cdf in CDFs.get_custom_cdfs(data[key])
            ]

            if not any(
                column_custom_cdf in custom_cdfs
                for column_custom_cdf in key_custom_cdfs
            ):
                raise InvalidUsage.bad_request(
                    f"Custom {key} needs to be defined in the custom cdfs list"
                )

    if len(custom_cdfs):
        custom_columns_metrics = [
            custom_column_metric["cdf"]["column"]
            for custom_column_metric in data["columns"]
            if "metrics" in custom_column_metric
            and custom_column_metric["cdf"]["type"] == Cdf.Custom.value
        ]

        if len(custom_columns_metrics):
            number_custom_cdfs = [
                number_custom_cdf["column"]
                for number_custom_cdf in data["custom_cdfs"]
                if number_custom_cdf["kind"]
                in (CdfKind.Number.name, CdfKind.Dynamic.name)
            ]

            if not all(
                number_custom_cdf in number_custom_cdfs
                for number_custom_cdf in custom_columns_metrics
            ):
                raise InvalidUsage.bad_request(
                    "Custom cdfs with metrics need to be defined in the custom cdfs as numbers"
                )

    if (
        "filters" in data
        and data["filters"] is not None
        and has_custom_cdfs(CDFs.get_cdfs_from_filters(data["filters"], []))
    ):
        filters_custom_cdfs = [
            filters_custom_cdf["cdf"]["column"]
            for filters_custom_cdf in CDFs.get_custom_cdfs(
                CDFs.get_cdfs_from_filters(data["filters"], [])
            )
        ]

        if not any(
            column_custom_cdf in custom_cdfs
            for column_custom_cdf in filters_custom_cdfs
        ):
            raise InvalidUsage.bad_request(
                "Custom filters needs to be defined in the custom cdfs list"
            )


def validate_feature_cdfs(data):
    """Validates is feature cdfs are selected in a report"""
    if data:
        dataset = Dataset(DatasetEnum(data["dataset_id"]))
        feature_cdfs = [
            cdf["column"]
            for cdf in CDFs.get_cdfs_from_categories(
                dataset.cdfs,
                [
                    feature_category["category"]
                    for feature_category in dataset.feature_categories
                ],
            )
        ]

        country_cdfs = [
            cdf["column"]
            for cdf in CDFs.get_cdfs_from_categories(
                dataset.cdfs,
                [
                    country_category["category"]
                    for country_category in dataset.country_categories
                ],
            )
        ]

        feature_and_country_cdfs = feature_cdfs + country_cdfs

        for key in ["columns", "group_rows", "group_columns", "sort"]:
            cdfs = (
                CDFs.get_default_cdfs(data[key])
                if key in data and data[key] is not None
                else []
            )
            if any(
                [
                    True
                    for cdf in cdfs
                    if cdf["cdf"]["column"] in feature_and_country_cdfs
                ]
            ):
                raise InvalidUsage.bad_request(f"Invalid cdf in {key} for this dataset")

        filters = (
            CDFs.get_default_cdfs(CDFs.get_cdfs_from_filters(data["filters"], []))
            if "filters" in data and data["filters"] is not None
            else []
        )
        if any(
            [
                True
                for cdf in filters
                if cdf["cdf"]["column"] in feature_and_country_cdfs
            ]
        ):
            raise InvalidUsage.bad_request("Invalid cdf in filters for this dataset")


def validate_duplicated_cdfs_in_column_and_groups(data):
    columns = data["columns"] if "columns" in data else []
    group_rows = (
        data["group_rows"]
        if "group_rows" in data and data["group_rows"] is not None
        else []
    )
    group_columns = (
        data["group_columns"]
        if "group_columns" in data and data["group_columns"] is not None
        else []
    )

    period_cdf_columns = []
    periods = []
    if "periods" in data and data["periods"] is not None:
        for period in data["periods"]:
            period_cdf_column = period["cdf"]["column"]
            if period_cdf_column not in period_cdf_columns:
                period_cdf_columns.append(period_cdf_column)
                periods.append(period)

    if CDFs.has_duplicates(columns + group_rows + group_columns):
        raise InvalidUsage.bad_request(
            "Columns, Group Rows, and Group Columns must not contain duplicates"
        )

    if CDFs.has_duplicates(columns + group_columns + periods):
        raise InvalidUsage.bad_request(
            "Columns, Group Columns and Periods must not contain duplicates"
        )


def validate_columns(data):
    if "columns" in data and "dataset_id" in data:
        dataset = Dataset(DatasetEnum(data["dataset_id"]))
        validate_cdfs(data["columns"], dataset.flatten_cdfs)
        validate_multi_levels(data["columns"], dataset)
        validate_metrics(data["columns"], dataset.flatten_cdfs)


def validate_groups(data, report_kind: Optional[ReportKind] = None):
    if "group_rows" in data and data["group_rows"] is not None and "dataset_id" in data:
        dataset = Dataset(DatasetEnum(data["dataset_id"]))
        validate_cdfs(data["group_rows"], dataset.flatten_cdfs)
        validate_multi_levels(data["group_rows"], dataset)
        validate_modifiers(data["group_rows"], dataset.flatten_cdfs)

        if (
            "settings" not in data
            or data["settings"] is None
            or not data["settings"].get("details")
        ):
            default_columns = [
                column
                for column in data["columns"]
                if column["cdf"]["type"] == Cdf.Default.value
            ]
            custom_columns = CDFs.get_custom_cdfs(data["columns"])

            cdf_metrics = validate_metrics(default_columns, dataset.flatten_cdfs)
            custom_cdf_metrics = validate_custom_cdf_metrics(
                custom_columns, data.get("custom_cdfs")
            )
            dynamic_custom_cdfs = []
            if custom_columns:
                custom_cdfs = (
                    [
                        dict(column=custom_cdf["column"], kind=custom_cdf["kind"])
                        for custom_cdf in data.get("custom_cdfs")
                    ]
                    if data.get("custom_cdfs")
                    else [
                        dict(column=custom_cdf.column, kind=custom_cdf.kind)
                        for custom_cdf in CustomCdfService.get_by_report_id(
                            report_kind, g.report_id
                        )
                        if custom_cdf.column
                        in [column["cdf"]["column"] for column in custom_columns]
                    ]
                )
                dynamic_custom_cdfs = (
                    [
                        custom_cdf["column"]
                        for custom_cdf in custom_cdfs
                        if custom_cdf["kind"] == CdfKind.Dynamic.name
                    ]
                    if custom_cdfs
                    else []
                )

            if (
                len(cdf_metrics) + len(custom_cdf_metrics) + len(dynamic_custom_cdfs)
                == 0
            ):
                raise InvalidUsage.bad_request(
                    "Columns with metrics are necessary to group rows"
                )

    if (
        "group_columns" in data
        and data["group_columns"] is not None
        and "dataset_id" in data
    ):
        dataset = Dataset(DatasetEnum(data["dataset_id"]))
        validate_cdfs(data["group_columns"], dataset.flatten_cdfs)
        validate_multi_levels(data["group_columns"], dataset)
        validate_modifiers(data["group_columns"], dataset.flatten_cdfs)

        if "group_rows" not in data or data["group_rows"] is None:
            raise InvalidUsage.bad_request(
                "Group rows needs to be defined in order to define group columns"
            )

        if len(data["group_rows"]) > 2:
            raise InvalidUsage.bad_request(
                "Group rows can't have more than 2 groups when there's group columns defined"
            )

        if (
            "settings" in data
            and data["settings"] is not None
            and data["settings"].get("details")
        ):
            raise InvalidUsage.bad_request("A pivot report must not have details=true")


def validate_filters(data):
    if "filters" in data and data["filters"] is not None and "dataset_id" in data:
        dataset = Dataset(DatasetEnum(data["dataset_id"]))
        cdf_columns = [cdf["column"] for cdf in dataset.flatten_cdfs]
        rules = CDFs.get_cdfs_from_filters(data["filters"], [])

        if any(
            [
                True
                for rule in rules
                if rule["cdf"]["column"] not in cdf_columns
                and "multi_level_id" not in rule["cdf"]
                and rule["cdf"]["type"] == Cdf.Default.value
            ]
        ):
            error = "Invalid column in filters for this dataset"
            errors = {
                **{
                    rule["cdf"]["column"]: error
                    for rule in rules
                    if rule["cdf"]["column"] not in cdf_columns
                }
            }
            raise InvalidUsage.bad_request(errors)

        validate_multi_levels(
            [rule for rule in rules if "multi_level_id" in rule["cdf"]], dataset
        )

        invalid_date_or_time_filters = [
            rule for rule in rules if is_invalid_date_or_time(rule, dataset)
        ]
        if len(invalid_date_or_time_filters) > 0:
            errors = {
                **{
                    (
                        rule["value"]
                        if isinstance(rule["value"], str) or not rule["value"]
                        else rule["value"][0]
                    ): f"Invalid date in {rule['cdf']['column']} filters"
                    for rule in invalid_date_or_time_filters
                }
            }
            raise InvalidUsage.bad_request(errors)


def validate_sort(data):
    if "sort" in data and data["sort"] is not None and "dataset_id" in data:
        dataset = Dataset(DatasetEnum(data["dataset_id"]))
        validate_cdfs(data["sort"], dataset.flatten_cdfs)

        sort_cdfs = (
            [sort["cdf"]["column"] for sort in data["sort"]] if "sort" in data else []
        )

        if len(sort_cdfs) != len(set(sort_cdfs)):
            raise InvalidUsage.bad_request("Sort cdfs must not contain duplicates")

        group_rows = (
            [group_rows["cdf"]["column"] for group_rows in data["group_rows"]]
            if "group_rows" in data and data["group_rows"] is not None
            else []
        )

        if (
            group_rows
            and "settings" in data
            and data["settings"].get("details") is False
        ):
            if not all([sort_cdf in group_rows for sort_cdf in sort_cdfs]):
                raise InvalidUsage.bad_request(
                    "Is only possible to sort by Columns when report has Group Rows and details"
                )

        # Validate Sort includes Columns and Group Rows CDF's only
        columns = [columns["cdf"]["column"] for columns in data["columns"]]

        if not all([sort_cdf in columns + group_rows for sort_cdf in sort_cdfs]):
            raise InvalidUsage.bad_request(
                "Is only possible to sort by CDF's that are selected in the Report"
            )

        validate_multi_levels(data["sort"], dataset)


def validate_settings(data):
    if (
        data.get("settings")
        and data["settings"].get("transpose")
        and not data["group_rows"]
        and not data["periods"]
        and not data["comparisons"]
    ):
        raise InvalidUsage.bad_request("Transpose not supported for list reports")

    if data.get("settings") and data.get("periods") and data["settings"].get("details"):
        raise InvalidUsage.bad_request(
            "Details are not supported for period comparison report"
        )

    if data.get("periods") and data.get("group_columns"):
        raise InvalidUsage.bad_request(
            "Pivot report not supported by period comparison report"
        )

    if data.get("periods") and data.get("comparisons"):
        raise InvalidUsage.bad_request(
            "Cannot have both periods and comparisons in the same report"
        )

    dynamic_custom_cdfs = (
        [
            custom_cdf["column"]
            for custom_cdf in data["custom_cdfs"]
            if custom_cdf["kind"] == CdfKind.Dynamic.name
        ]
        if data.get("custom_cdfs")
        else []
    )
    dynamic_cdfs = [
        cdf
        for cdf in Dataset(DatasetEnum(data["dataset_id"])).flatten_cdfs
        if cdf["kind"]
        in [CdfKind.Dynamic, CdfKind.DynamicPercentage, CdfKind.DynamicCurrency]
        and cdf["column"] in [column["cdf"]["column"] for column in data.get("columns")]
    ]
    if (
        (data.get("comparisons") or data.get("periods"))
        and data.get("group_rows")
        and not (
            next(
                (True for column in data.get("columns", []) if column.get("metrics")),
                False,
            )
            or dynamic_custom_cdfs
            or dynamic_cdfs
        )
    ):
        raise InvalidUsage.bad_request(
            "At least one column with a metric is required for SummaryComparison"
        )


def validate_cdfs(data_cdfs, dataset_cdfs):
    cdfs = CDFs.get_default_cdfs(data_cdfs)
    report_type_cdfs = [cdf["column"] for cdf in dataset_cdfs]

    missing_cdfs = [
        cdf["cdf"]["column"]
        for cdf in cdfs
        if cdf["cdf"]["column"] not in report_type_cdfs
        and "multi_level_id" not in cdf["cdf"]
    ]
    if any(missing_cdfs):
        error = "Invalid cdf for this dataset"
        invalid_groups = {cdf: error for cdf in missing_cdfs}
        raise InvalidUsage.bad_request(invalid_groups)


def validate_modifiers(data_cdfs, cdf_list):
    default_cdfs = [
        group_row for group_row in data_cdfs if group_row["cdf"]["type"] == "default"
    ]

    validate_level_modifiers(CDFs.get_default_cdfs(default_cdfs), cdf_list)

    multi_levels = set(
        [
            column["cdf"]["multi_level_id"]
            for column in CDFs.get_multi_level_cdfs(default_cdfs)
        ]
    )

    for multi_level in multi_levels:
        validate_level_modifiers(
            [
                column
                for column in CDFs.get_multi_level_cdfs(default_cdfs)
                if column["cdf"]["multi_level_id"] == multi_level
            ],
            MultiLevel(MultiLevelEnum(multi_level)).cdfs,
        )


def validate_level_modifiers(columns, cdf_list):
    cdf_modifiers = [
        cdf["column"]
        for cdf in cdf_list
        if cdf["kind"] == CdfKind.Date
        or cdf["kind"] == CdfKind.Timestamp
        or cdf["kind"] == CdfKind.Time
        and cdf["column"] in [group["cdf"]["column"] for group in columns]
    ]

    if any(
        [
            True
            for cdf in columns
            if "modifier" in cdf and cdf["cdf"]["column"] not in cdf_modifiers
        ]
    ):
        error = "Cdf doesn't support modifier"
        invalid_modifiers = {
            cdf["cdf"]["column"]: error
            for cdf in columns
            if "modifier" in cdf and cdf["cdf"]["column"] not in cdf_list
        }
        raise InvalidUsage.bad_request(invalid_modifiers)


def validate_iso8601(str_val):
    try:
        datetime.fromisoformat(str_val)
    except (ValueError, Exception):
        try:
            datetime.fromisoformat(str_val.replace("Z", "+00:00"))
        except (ValueError, Exception):
            return False
        return True
    return True


def validate_relative_date(value: str | None):
    if not value:
        return False

    value, duration = RelativeDate.get_duration_from_value(value)
    return value in [relative_date.value for relative_date in RelativeDateEnum] and (
        not duration or isinstance(duration, int) and duration >= 0
    )


def validate_date_or_relative_date(value: str | None):
    if not validate_relative_date(value):
        return validate_iso8601(value)
    return True


def is_invalid_date_or_time(filter: dict, dataset: Dataset):
    """Check that the filter value of dates is s a relative date, has a valid duration,
    or is a valid iso8601 timestamp
    """
    if "type" not in filter["cdf"] or filter["cdf"]["type"] not in [
        Cdf.Custom.value,
        Cdf.CustomField.value,
    ]:
        if CDF(
            dataset.kind,
            filter["cdf"]["column"],
            False,
            False,
            multi_level=(
                None
                if "multi_level_id" not in filter["cdf"]
                else MultiLevelEnum(filter["cdf"]["multi_level_id"])
            ),
        ).is_kind(CdfKind.Date) or CDF(
            dataset.kind,
            filter["cdf"]["column"],
            False,
            False,
            multi_level=(
                None
                if "multi_level_id" not in filter["cdf"]
                else MultiLevelEnum(filter["cdf"]["multi_level_id"])
            ),
        ).is_kind(
            CdfKind.Timestamp
        ):
            return not validate_date_or_relative_date(filter.get("value"))


def validate_folder_ids(folder_ids: str):
    try:
        folder_ids = [int(folder_id) for folder_id in folder_ids.split(",")]
    except Exception:
        return InvalidUsage.bad_request("Provide a valid comma-separated list")

    if len(folder_ids) > 50:
        raise InvalidUsage.bad_request("The maximum of Id's are 50")


def validate_multi_levels(fields, dataset):
    invalid_multi_levels = set(
        [
            str(field["cdf"]["multi_level_id"])
            for field in fields
            if "multi_level_id" in field["cdf"]
            and field["cdf"]["multi_level_id"]
            not in [multi_level.value for multi_level in MultiLevelEnum]
        ]
    )

    if any(invalid_multi_levels):
        raise InvalidUsage.bad_request(
            "MultiLevel ID(s) [" + ", ".join(invalid_multi_levels) + "] is invalid"
        )

    unavailable_multi_levels = set(
        [
            str(field["cdf"]["multi_level_id"])
            for field in fields
            if "multi_level_id" in field["cdf"]
            and field["cdf"]["multi_level_id"] not in dataset.multi_levels
        ]
    )

    if any(unavailable_multi_levels):
        raise InvalidUsage.bad_request(
            f"MultiLevel ID(s) [{', '.join(unavailable_multi_levels)}] not available for this Dataset"
        )

    cdfs_not_in_multi_level = set(
        [
            field["cdf"]["column"]
            for field in CDFs.get_multi_level_cdfs(fields)
            if field["cdf"]["column"]
            not in [
                cdf["column"]
                for cdf in MultiLevel(
                    MultiLevelEnum(field["cdf"]["multi_level_id"])
                ).cdfs
            ]
        ]
    )

    if any(cdfs_not_in_multi_level):
        raise InvalidUsage.bad_request(
            "CDF(s) ["
            + ", ".join(cdfs_not_in_multi_level)
            + "] not available for this MultiLevel"
        )


def validate_metrics(columns, dataset_cdfs):
    """Function that will return a list of cdfs that are Metrics"""
    cdf_metrics = validate_level_metrics(CDFs.get_default_cdfs(columns), dataset_cdfs)

    multi_levels = set(
        [
            column["cdf"]["multi_level_id"]
            for column in CDFs.get_multi_level_cdfs(columns)
        ]
    )
    for multi_level in multi_levels:
        cdf_metrics += validate_level_metrics(
            [
                column
                for column in CDFs.get_multi_level_cdfs(columns)
                if column["cdf"]["multi_level_id"] == multi_level
            ],
            MultiLevel(MultiLevelEnum(multi_level)).cdfs,
        )

    return cdf_metrics


def validate_custom_cdf_metrics(columns: list, custom_cdfs: list | None) -> list[str]:
    """Take the custom cdf columns from the report and return a list of custom cdf's column names
    that have metrics and are Numeric"""

    cdf_metrics = [column["cdf"]["column"] for column in columns if "metrics" in column]

    if custom_cdfs:
        dynamic_custom_cdfs = [
            custom_cdf
            for custom_cdf in custom_cdfs
            if custom_cdf["kind"] == CdfKind.Dynamic.name
        ]
        cdf_metrics += dynamic_custom_cdfs
    return cdf_metrics


def validate_level_metrics(columns, cdf_list):
    cdf_metrics = [
        cdf["column"]
        for cdf in cdf_list
        if cdf["kind"]
        in (
            CdfKind.Number,
            CdfKind.Currency,
            CdfKind.DynamicCurrency,
            CdfKind.DynamicPercentage,
        )
        and cdf["column"]
        in [default_column["cdf"]["column"] for default_column in columns]
    ]

    if any(
        [
            True
            for column in columns
            if "metrics" in column and column["cdf"]["column"] not in cdf_metrics
        ]
    ):
        error = "Cdf doesn't support metrics"
        invalid_metrics = {
            column["cdf"]["column"]: error
            for column in columns
            if "metrics" in column and column["cdf"]["column"] not in cdf_metrics
        }
        raise InvalidUsage.bad_request(invalid_metrics)

    return cdf_metrics


def validate_period_summary_sorts(data):
    if data.get("periods") and data.get("group_rows"):
        if not data.get("sort"):
            raise InvalidUsage.bad_request("Sort is required for period summary")
        group_rows = [group_row["cdf"]["column"] for group_row in data["group_rows"]]
        sorts = [sort["cdf"]["column"] for sort in data["sort"]]
        if group_rows != sorts:
            raise InvalidUsage.bad_request(
                "Period Summary sort must provide sort for each group row in order"
            )


def validate_period_names(periods):
    if periods:
        names = [period["name"] for period in periods if period.get("name")]

        if len(set(names)) != len(names):
            raise InvalidUsage.bad_request("Period cannot have same name")


def validate_comparison_names(comparisons):
    if comparisons:
        names = [
            comparison["name"] for comparison in comparisons if comparison.get("name")
        ]

        if len(set(names)) != len(names):
            raise InvalidUsage.bad_request("Comparisons cannot have same name")


def validate_dataset_id_not_read_only(data):
    dataset_id = data.get("dataset_id")
    property_id = data.get("property_id")
    read_only_dataset_ids = DatasetService.get_read_only_dataset_ids(
        property_id, g.user.email
    )
    if dataset_id in read_only_dataset_ids:
        raise InvalidUsage.bad_request(
            f"{DATASET_LABELS[DatasetEnum(dataset_id)]} dataset is read-only."
        )


def validate_dataset_requirements(data):
    group_rows_fixed_cdfs = [
        Dataset(DatasetEnum.OccupancyV1).model.stay_date.key,
    ]

    # only validate stay date group row for occupancy v1 when using occupancy cdf
    if data["dataset_id"] == DatasetEnum.OccupancyV1.value and "occupancy" in [
        column["cdf"]["column"] for column in data["columns"]
    ]:
        # Validate Group Rows are the fixed ones
        group_rows = data["group_rows"] if data["group_rows"] is not None else []

        if not all(
            cdf in [cdf["cdf"]["column"] for cdf in group_rows]
            for cdf in group_rows_fixed_cdfs
        ):
            raise InvalidUsage.bad_request(
                "Occupancy V1 report must have the following group row: Stay Date"
            )


def get_custom_field_cdfs(data):
    cdfs = []
    cdfs += data["columns"]
    if data.get("group_rows"):
        cdfs += data["group_rows"]
    if data.get("group_columns"):
        cdfs += data["group_columns"]
    if data.get("filters"):
        cdfs += CDFs.get_cdfs_from_filters(data["filters"], [])
    if data.get("sort"):
        cdfs += data["sort"]
    if data.get("periods"):
        cdfs += data["periods"]
    if data.get("comparisons"):
        for comparison in data["comparisons"]:
            cdfs += CDFs.get_cdfs_from_filters(comparison["filters"], [])
    return [cdf for cdf in cdfs if cdf["cdf"]["type"] == Cdf.CustomField.value]


def validate_no_custom_field_cdfs(data):
    if get_custom_field_cdfs(data):
        raise InvalidUsage.bad_request("Custom Fields are not allowed in stock reports")


def validate_custom_field_cdfs(data):
    custom_field_cdfs = get_custom_field_cdfs(data)
    if custom_field_cdfs:
        if data.get("dataset_id") not in CustomFields.values():
            raise InvalidUsage.bad_request(
                "Custom Fields are not available for this dataset"
            )

        custom_fields = (
            [
                custom_field_cdf.__dict__
                for custom_field_cdf in Report.get_by_id(g.report_id).custom_field_cdfs
            ]
            if hasattr(g, "report_id")
            else []
        )
        custom_fields += data.get("custom_field_cdfs", [])

        custom_field_columns = [data["column"] for data in custom_fields]
        custom_field_cdf_columns = list(
            set(
                [
                    custom_field_cdf["cdf"]["column"]
                    for custom_field_cdf in custom_field_cdfs
                ]
            )
        )
        if (
            custom_field_cdfs
            and not custom_field_columns
            or not all(
                custom_field_cdf_column in custom_field_columns
                for custom_field_cdf_column in custom_field_cdf_columns
            )
        ):
            raise InvalidUsage.bad_request(
                "Report has Custom Fields that are not defined"
            )


def validate_report_custom_field_cdf(
    custom_field_cdf: dict, dataset_id: int, property_ids: list[int]
):
    if not all(
        id in property_ids
        for id in [
            property["property_id"] for property in custom_field_cdf["properties"]
        ]
    ):
        raise InvalidUsage.bad_request("Property id does not exist in the report")

    for property in custom_field_cdf["properties"]:
        custom_field = (
            CustomFieldService.get_custom_field_by_dataset_id_and_internal_name(
                dataset_id,
                property["internal_name"],
                property["property_id"],
            )
        )
        if not custom_field:
            raise InvalidUsage.bad_request(
                f"Custom Field with internal name {property['internal_name']} for property id {property['property_id']} does not exist in the dataset"
            )


def validate_no_custom_cdfs_custom_field_cdfs_duplicates(data):
    custom_columns = []
    if data.get("custom_cdfs"):
        custom_columns += [custom_cdf["column"] for custom_cdf in data["custom_cdfs"]]
    if data.get("custom_field_cdfs"):
        custom_columns += [
            custom_field_cdf["column"] for custom_field_cdf in data["custom_field_cdfs"]
        ]

    if len(custom_columns) != len(set(custom_columns)):
        raise InvalidUsage.bad_request(
            "Custom Fields and Custom CDFs must not contain duplicate columns"
        )


def validate_custom_field_cdfs_in_custom_cdfs_exist(data):
    custom_cdfs = data.get("custom_cdfs", [])
    if custom_cdfs:
        custom_field_cdfs_in_custom_cdfs = []
        for custom_cdf in data.get("custom_cdfs", []):
            custom_field_cdfs_in_custom_cdfs += [
                formula["value"]
                for formula in custom_cdf["formula"]
                if formula["kind"] == "custom_field_cdf"
            ]

        if custom_field_cdfs_in_custom_cdfs:
            custom_field_cdfs = data.get("custom_field_cdfs", [])
            if not custom_field_cdfs:
                raise InvalidUsage.bad_request(
                    "Custom CDF contains Custom Field CDF, none are defined"
                )
            custom_field_cdf_columns = [
                custom_field_cdf["column"] for custom_field_cdf in custom_field_cdfs
            ]

            if not all(
                [
                    custom_field_cdf_in_custom_cdf in custom_field_cdf_columns
                    for custom_field_cdf_in_custom_cdf in custom_field_cdfs_in_custom_cdfs
                ]
            ):
                raise InvalidUsage.bad_request(
                    "Custom CDF contains undefined Custom Field CDF"
                )


def validate_page_mode(data):
    if request and request.args.get("mode") == Mode.Page.name:
        # if not one of the two valid report kinds, raise exception
        if data["periods"] or data["comparisons"]:
            raise InvalidUsage.bad_request(
                "Page mode is not supported for comparison reports"
            )
