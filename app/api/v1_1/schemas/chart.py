from marshmallow import (
    Schema,
    fields,
    pre_dump,
    pre_load,
    validate,
    validates_schema,
)

from webargs.fields import DelimitedList

from app.api.v1_1.schemas.pagination import PaginationSchema
from app.api.v1_1.schemas.report import PathReportIdSchema
from app.api.v1_1.schemas.report import ReportColumnsSchema, ReportGroupSchema
from app.api.v1_1.schemas.report.report import ReportCloneSchema
from app.api.v1_1.schemas.report.report_cdf import ReportCDFSchema
from app.api.v1_1.schemas.stock_report import PathStockReportIdSchema
from app.api.v1_1.schemas.stock_report.stock_report import StockReportCloneSchema
from app.api.v1_1.validations.chart import validate_chart
from app.common.constants.chart import CATEGORY_STRINGS
from app.common.enums.chart import ChartKind, ChartKindBarStackingOption
from app.common.enums.search import ChartSearchColumns
from app.common.exceptions import InvalidUsage
from app.datasets.financial import FinancialView
from app.enums.cdf import Cdf
from app.enums.chart_axis import ChartAxis
from app.enums.dataset import Dataset
from app.enums.metric import Metric
from app.enums.report import ReportKind
from app.enums.sort import Sort
from app.models.chart import Chart
from app.schemas.datetime import CustomDateTimeUtc


class ChartSettingsMetricsSchema(Schema):
    cdf = fields.Nested(
        ReportCDFSchema,
        required=True,
        metadata={
            "description": "Report CDF",
            "example": dict(
                type=Cdf.Default.value, column=FinancialView.credit_amount.key
            ),
        },
    )
    metric = fields.String(
        required=False,
        validate=validate.OneOf([metric.value for metric in Metric]),
        metadata={
            "description": "Chart metric. Not required for dynamic cdfs",
            "example": Metric.sum.value,
        },
    )
    kind = fields.String(
        required=True,
        validate=validate.OneOf(
            [ChartKind.Line.value, ChartKind.Bar.value, ChartKind.Area.value]
        ),
        metadata={
            "description": "Kind of chart",
            "example": ChartKind.Line.value,
        },
    )
    axis = fields.String(
        required=True,
        validate=validate.OneOf([axis.value for axis in ChartAxis]),
        metadata={
            "description": "Left or right vertical axis",
            "example": ChartAxis.Left.value,
        },
    )


class ChartSettingsSchema(Schema):
    # all settings specific to our chart library
    legend = fields.Boolean(
        required=False,
        dump_default=False,
        metadata={
            "description": "Toggle to show/hide legend",
            "example": False,
            "nullable": False,
        },
    )
    toolbox = fields.Boolean(
        required=False,
        dump_default=False,
        metadata={
            "description": "Toggle to show/hide the toolbox",
            "example": False,
            "nullable": False,
        },
    )
    metrics = fields.Nested(
        ChartSettingsMetricsSchema,
        required=False,
        many=True,
        metadata={
            "description": "List of CDFs with metrics and vertical axis to represent data.",
            "example": dict(
                cdf=dict(
                    type=Cdf.Default.value, column=FinancialView.credit_amount.key
                ),
                metric=Metric.sum.value,
                kind=ChartKind.Line.value,
                axis=ChartAxis.Left.value,
            ),
        },
    )
    stack = fields.String(
        required=False,
        validate=validate.OneOf(
            [option.value for option in ChartKindBarStackingOption]
        ),
        metadata={
            "description": "Stack option of Chart kind bar",
            "example": ChartKindBarStackingOption.Normalized.value,
        },
    )


class ChartSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Chart Id", "example": "1"},
    )

    datasource_id = fields.Integer(
        dump_only=True,
        required=True,
        metadata={"description": "ID of the datasource", "example": "1"},
    )

    datasource_kind = fields.String(
        dump_only=True,
        required=True,
        validate=validate.OneOf([kind.value for kind in ReportKind]),
        metadata={
            "description": "Datasource Kind",
            "example": ReportKind.StockReport.value,
        },
    )
    datasource_title = fields.String(
        dump_only=True,
        metadata={"description": "Title of the datasource", "example": "My Report"},
    )
    datasource_description = fields.String(
        dump_only=True,
        metadata={
            "description": "Description of the datasource",
            "example": "Detailed Report Description",
        },
    )
    title = fields.String(
        required=True,
        metadata={"description": "Chart title", "example": "My cool new chart"},
    )
    kind = fields.String(
        required=True,
        validate=validate.OneOf([kind.value for kind in ChartKind]),
        metadata={
            "description": "Kind of chart",
            "example": ChartKind.Bar.value,
        },
    )

    settings = fields.Nested(ChartSettingsSchema, load_default={})
    categories = fields.Raw(
        required=False,
        many=True,
        metadata={
            "description": 'List of CDFs in group rows or group columns (or the text string "Periods" for period comparison reports) to '
            "categorize data.  Categories are represented horizontally (x-axis) on most charts",
            "example": '"Periods"',
        },
    )

    metrics = fields.Nested(
        ReportColumnsSchema,
        required=False,
        many=True,
        metadata={
            "description": "List of CDFs with metrics to represent data. Metrics will be represented vertically (y-axis) on most charts."
        },
    )

    user_id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "User id", "example": "1"},
    )
    created_at = CustomDateTimeUtc(
        dump_only=True,
        description="Created At Time",
    )

    updated_at = CustomDateTimeUtc(dump_only=True, description="Updated At Time")

    @pre_load
    def format_categories(self, data, **kwargs):
        categories = data.get("categories")
        try:
            categories = [
                (
                    categories[i]
                    if categories[i] in CATEGORY_STRINGS
                    else ReportGroupSchema().load(categories[i])
                )
                for i in range(len(categories))
            ]
        except:
            raise InvalidUsage.bad_request("Invalid Categories")
        data["categories"] = categories
        return data

    @pre_dump
    def load_report_id_based_on_kind(self, data, **kwargs):
        """Pre dump function to check if chart is on stock report id to return it in the report id"""
        if isinstance(data, Chart):
            data.datasource_id = (
                data.stock_report_id if data.stock_report_id else data.report_id
            )
            data.datasource_kind = (
                ReportKind.StockReport.value
                if data.stock_report_id
                else ReportKind.Report.value
            )
            # Dump the categories in the correct format
            categories = data.categories
            try:
                categories = [
                    (
                        categories[i]
                        if categories[i] in CATEGORY_STRINGS
                        else ReportGroupSchema().load(categories[i])
                    )
                    for i in range(len(categories))
                ]
            except:
                raise InvalidUsage.bad_request("Invalid Categories")
            data.categories = categories

        return data

    @validates_schema
    def validates_schema(self, data, **kwargs):
        validate_chart(data)


class PathReportIdChartIdSchema(PathReportIdSchema):
    chart_id = fields.Integer(
        as_string=True,
        required=True,
        metadata={"description": "Id For the chart", "example": "1"},
    )


class PathStockReportIdChartIdSchema(PathStockReportIdSchema):
    chart_id = fields.Integer(
        as_string=True,
        required=True,
        metadata={"description": "Id For the chart", "example": "1"},
    )


class ChartCloneSchema(ChartSchema):
    fields = ("title", "kind", "settings", "categories", "metrics")


class ReportWithChartsCloneSchema(ReportCloneSchema):
    charts = fields.Nested(
        ChartCloneSchema,
        many=True,
        required=False,
        metadata={"description": "List of charts to clone"},
    )


class StockReportWithChartsCloneSchema(StockReportCloneSchema):
    charts = fields.Nested(
        ChartCloneSchema,
        many=True,
        required=False,
        metadata={"description": "List of charts to clone"},
    )


class ChartsSchema(PaginationSchema):
    class Meta:
        ordered = True

    charts = fields.Nested(ChartSchema, many=True)


class QuerySortChartSchema(Schema):
    class Meta:
        ordered = True

    sort = fields.String(
        metadata={"description": "Sort Charts"},
        load_default="updated_at,desc",
        validate=validate.OneOf(
            [
                f"{field},{sort.name}"
                for field in ChartSchema().fields.keys()
                if field
                in (
                    "created_at",
                    "updated_at",
                    "id",
                    "title",
                    "kind",
                )
                for sort in Sort
            ]
        ),
    )


class QueryFilterChartSchema(Schema):
    class Meta:
        ordered = True

    ids = DelimitedList(
        fields.Integer(
            as_string=True,
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Chart IDs , delimited. For example ?ids=1,2,3"},
    )
    datasource_kind = fields.String(
        validate=validate.OneOf([kind.value for kind in ReportKind]),
        metadata={
            "description": "Datasource Kind",
            "example": ReportKind.StockReport.value,
        },
    )
    tag_ids = DelimitedList(
        fields.Integer(
            as_string=True,
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Tag IDs , delimited. For example ?tag_ids=1,2,3"},
    )

    chart_kind = DelimitedList(
        fields.String(
            validate=validate.OneOf([kind.value for kind in ChartKind]),
            metadata={
                "description": "Kind of chart",
                "example": ChartKind.Bar.value,
            },
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Chart Kinds, delimited", "example": "bar,line"},
    )
    dataset_ids = DelimitedList(
        fields.Integer(validate=validate.OneOf([kind.value for kind in Dataset])),
        validate=validate.Length(min=1),
        metadata={"description": "Dataset IDs, delimited", "example": "1,2,3"},
    )

    @validates_schema
    def validate_schema(self, data, **kwargs):
        # tag_ids can only be used when datasource_kind = report
        tag_ids = data.get("tag_ids")
        datasource_kind = data.get("datasource_kind")

        if tag_ids and datasource_kind != ReportKind.Report.value:
            raise InvalidUsage.bad_request(
                f"Tag Ids may only be used with datasource kind {ReportKind.Report.value}"
            )


class QueryFilterSearchChartSchema(QueryFilterChartSchema):
    class Meta:
        ordered = True

    search_term = fields.String(
        metadata={
            "description": "A text string to search for in any of the search column fields",
            "example": "in-house",
        }
    )
    search_columns = DelimitedList(
        fields.String(
            validate=validate.OneOf(
                [
                    report_search_column.value
                    for report_search_column in ChartSearchColumns
                ]
            )
        )
    )

    @validates_schema
    def validate_search_term_and_columns(self, data, **kwargs):
        # need both search term and search columns if using either
        search_term = data.get("search_term")
        search_columns = data.get("search_columns")

        if (search_term and not search_columns) or (search_columns and not search_term):
            raise InvalidUsage.bad_request(
                "Must use both search term and search columns"
            )
