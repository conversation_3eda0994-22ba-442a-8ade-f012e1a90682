from marshmallow import Schema, fields


class PropertySchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Property id", "example": "1"},
    )
    name = fields.String(
        dump_only=True,
        required=True,
        metadata={"description": "Property name", "example": "Property name"},
    )
    timezone = fields.String(
        dump_only=True,
        required=True,
        metadata={
            "description": "Property Timezone",
            "example": "UTC",
        },
    )
    country_code = fields.String(
        dump_only=True,
        required=False,
        metadata={"description": "Property country code", "example": "US"},
    )
    currency = fields.String(
        dump_only=True,
        required=False,
        metadata={"description": "Property currency code", "example": "USD"},
    )
