from marshmallow import Schema, fields, validate

# from app.enums.cdf import Cdf
from app.enums.cdf_kind import CdfKind


class CDFSchema(Schema):
    class Meta:
        ordered = True

    column = fields.String(
        metadata={
            "description": "Column name reference for description",
            "example": "reservation_status",
        },
    )
    name = fields.String(
        metadata={"description": "Name of the CDF", "example": "Reservation Status"}
    )
    description = fields.String(
        metadata={
            "description": "Description of the CDF",
            "example": "Status of the Reservation",
        }
    )
    kind = fields.String(
        metadata={
            "description": "CDF kind",
            "example": CdfKind.String.name,
            "enum": [kind.name for kind in CdfKind],
        },
        validate=validate.OneOf(kind.name for kind in CdfKind),
    )
    type = fields.String(
        metadata={"description": "Type of the CDF", "example": "default"},
        dump_default="default",
        dump_only=True,
    )


class CDFSSchema(Schema):
    class Meta:
        ordered = True

    category = fields.String(
        metadata={"description": "Category of the CDF", "example": "Booking"}
    )
    cdfs = fields.Nested(
        CDFSchema, required=False, many=True, metadata={"description": "List of CDFs"}
    )


class CDFOptionsSchema(CDFSchema):
    class Meta:
        ordered = True

    options = fields.List(
        fields.String(),
        dump_only=True,
        metadata={
            "description": "List of picklist options available for CDF",
            "example": ["Confirmed", "Cancelled"],
        },
    )
