import re

from marshmallow import Schema, fields, validate, validates_schema

from webargs.fields import DelimitedList

from app.api.v1_1.schemas.report import ReportsSearchSchema
from app.api.v1_1.schemas.stock_report.stock_report import StockReportsSearchSchema
from app.common.constants.search import SEARCH_COLUMNS
from app.common.enums.search import ReportSearchColumns, SearchResources
from app.common.exceptions import InvalidUsage


get_filters_regex = (
    lambda: f"^({'|'.join([str(filter) for filter in set([filter for filters in SEARCH_COLUMNS.values() for filter in filters])])})+:[^:]+$"
)


class SearchSchema(Schema):
    class Meta:
        ordered = True

    reports = fields.Nested(ReportsSearchSchema, required=False)
    stock_reports = fields.Nested(StockReportsSearchSchema, required=False)


class SearchFiltersSchema(Schema):
    filters = DelimitedList(
        fields.String(
            validate=validate.Regexp(get_filters_regex()),
            metadata={"example": "title:report title"},
        ),
        required=True,
        metadata={
            "description": (
                "List of comma separated filters to partial match, column:value. Right now is only possible to filter by "
                f"{set([filter for filters in SEARCH_COLUMNS.values() for filter in filters])}"
            )
        },
    )

    def validate_filters(self, kind, data):
        filters = data["filters"]
        for filter in filters:
            column = re.split("[-:]", filter)[0]
            if column not in SEARCH_COLUMNS[kind]:
                raise InvalidUsage.bad_request(
                    f"{column} is invalid for resource {kind}"
                )


class SearchParamSchema(SearchFiltersSchema):
    class Meta:
        ordered = True

    resources = DelimitedList(
        fields.String(
            validate=validate.OneOf([format.value for format in SearchResources]),
            load_default=None,
            metadata={"example": SearchResources.Report.value},
        ),
        metadata={"description": ("Which resources to search")},
        required=True,
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        resources = data["resources"]
        for kind in resources:
            self.validate_filters(kind, data)


class QuerySearchSchema(Schema):
    class Meta:
        ordered = True

    search_term = fields.String(
        metadata={
            "description": "A text string to search for in any of the search column fields",
            "example": "in-house",
        }
    )
    search_columns = DelimitedList(
        fields.String(
            validate=validate.OneOf(
                [
                    report_search_column.value
                    for report_search_column in ReportSearchColumns
                ]
            )
        )
    )

    @validates_schema
    def validate_search_term_and_columns(self, data, **kwargs):
        # need both search term and search columns if using either
        search_term = data.get("search_term")
        search_columns = data.get("search_columns")

        if (search_term and not search_columns) or (search_columns and not search_term):
            raise InvalidUsage.bad_request(
                "Must use both search term and search columns"
            )
