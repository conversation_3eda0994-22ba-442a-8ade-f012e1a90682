from marshmallow import Schema, fields, validate, validates

from webargs.fields import DelimitedList


from app.common.exceptions import InvalidUsage


class QueryReportExportByIdsSchema(Schema):
    class Meta:
        ordered = True

    name = fields.String(
        required=False,
        load_default="Workbook",
        metadata={
            "description": "Name of the workbook to generate",
            "example": "Nightly report",
        },
        validate=validate.Length(min=1, max=32),
    )
    report_ids = DelimitedList(
        fields.Integer(
            as_string=True,
            required=True,
            validate=lambda x: int(x) > 0
            or InvalidUsage.bad_request("Value should be greater than 0."),
            metadata={"description": "Report Id", "example": "1"},
        ),
        validate=validate.Length(min=1, max=15),
        required=True,
        metadata={"description": "Report IDs"},
    )

    include_charts = fields.Bool(
        required=False,
        load_default=True,
        metadata={
            "description": "Flag to include charts in the export, defaults to True",
            "example": True,
        },
    )

    @validates("report_ids")
    def no_duplicate_report_ids(self, value):
        if len(value) != len(set(value)):
            raise InvalidUsage.bad_request(
                "Report IDs must not contain duplicate values"
            )
