from marshmallow import Schema, fields


class HTTPHeadersSchema(Schema):
    class Meta:
        ordered = True

    date = fields.String(metadata={"description": "Date of the response"})
    content_type = fields.String(
        attribute="content-type", metadata={"description": "Content type"}
    )
    content_length = fields.Integer(
        attribute="content-length", metadata={"description": "Length of the content"}
    )
    connection = fields.String(metadata={"description": "Connection type"})
    x_amzn_requestid = fields.String(
        attribute="x-amzn-requestid", metadata={"description": "Amazon request ID"}
    )


class ResponseMetadataSchema(Schema):
    class Meta:
        ordered = True

    RequestId = fields.String(
        attribute="RequestId", metadata={"description": "Request ID"}
    )
    HTTPStatusCode = fields.Integer(
        attribute="HTTPStatusCode", metadata={"description": "HTTP status code"}
    )
    HTTPHeaders = fields.Nested(
        HTTPHeadersSchema,
        attribute="HTTPHeaders",
        metadata={"description": "HTTP headers"},
    )
    RetryAttempts = fields.Integer(
        attribute="RetryAttempts", metadata={"description": "Number of retry attempts"}
    )


class EmailSchema(Schema):
    class Meta:
        ordered = True

    MessageId = fields.String(
        attribute="MessageId", metadata={"description": "ID of the email sent"}
    )
    ResponseMetadata = fields.Nested(ResponseMetadataSchema)


class QueueResultSchema(Schema):
    class Meta:
        ordered = True

    url = fields.URL(
        required=True, metadata={"description": "Download URL of the generated report"}
    )
    format = fields.String(
        required=True,
        metadata={"description": "Format of the report", "example": "xlsx"},
    )
    view = fields.String(
        required=True, metadata={"description": "View of the report", "example": "raw"}
    )
    filename = fields.String(
        required=True,
        metadata={"description": "Name of the report", "example": "Report Name"},
    )
    generated_at = fields.String(metadata={"description": "Timestamp of the report"})
    email = fields.Nested(
        EmailSchema,
        required=False,
        metadata={
            "description": "Information about the email sent to the user, if one was sent."
        },
    )
    emails = fields.Nested(
        EmailSchema,
        many=True,
        required=False,
        metadata={
            "description": "Information about the emails sent to the user, if there were multiple emails sent."
        },
    )


class QueueResponseSchema(Schema):
    class Meta:
        ordered = True

    id = fields.String(
        metadata={
            "desription": "UUID of the task",
            "example": "53cce75e-eed6-4040-8a98-8accf372947a",
        }
    )
    status = fields.String(
        metadata={"description": "Status of the task", "example": "PENDING"}
    )
    result = fields.Nested(QueueResultSchema, required=False)
    error = fields.String(
        metadata={"description": "Error, if the task failed"},
        required=False,
    )
