from marshmallow import Schema, fields, validate

from app.api.v1_1.schemas.pagination import PaginationSchema
from app.enums.task import TaskNames


class TaskSchema(Schema):
    class Meta:
        ordered = True

    id = fields.String(
        metadata={
            "description": "UUID of the task",
            "example": "53cce75e-eed6-4040-8a98-8accf372947a",
        }
    )
    name = fields.String(
        metadata={
            "description": "Name of which task is applied",
            "example": "export_stock_report_by_id",
        },
        validate=validate.OneOf(TaskNames.values()),
    )
    property_id = fields.Integer(
        metadata={"description": "Property ID of the task", "example": 79}
    )
    property_ids = fields.List(
        fields.Integer(
            metadata={
                "description": "Property IDs the task will touch during processing",
                "example": [79, 6],
            }
        )
    )
    user_id = fields.Integer(
        metadata={"description": "User id that submitted the task", "example": 7513}
    )
    created_at = fields.DateTime(
        metadata={
            "description": "Created at timestamp of the task",
            "example": "2023-08-24T17:08:04.094590",
        }
    )
    updated_at = fields.DateTime(
        metadata={
            "description": "Updated at timestamp of the task",
            "example": "2023-08-24T17:08:04.094590s",
        }
    )


class TasksSchema(PaginationSchema):
    class Meta:
        ordered = True

    tasks = fields.Nested(TaskSchema, many=True)
