from marshmallow import Schema, fields, validate

from app.enums import Limit, Offset, Total


class PaginationSchema(Schema):
    """Pagination options"""

    offset = fields.Integer(
        metadata={
            "description": "The difference between start of the data and the start of the response data",
            "example": 0,
        },
        load_default=Offset.Min.value,
        required=False,
        validate=validate.Range(min=Offset.Min.value),
    )
    limit = fields.Integer(
        metadata={
            "description": "The maximum number of results in the response",
            "example": 5,
        },
        load_default=Limit.Default.value,
        required=False,
        validate=validate.Range(min=Limit.Min.value, max=Limit.Max.value),
    )
    total = fields.Integer(
        metadata={"description": "The total number of records", "example": 42},
        load_default=Total.Min.value,
        required=False,
        validate=validate.Range(min=Total.Min.value),
    )


class QueryPaginationSchema(PaginationSchema):
    """Parameters to enable pagination"""

    class Meta:
        fields = ("offset", "limit")
