from marshmallow import Schema, fields, pre_dump, validate

from app.api.v1_1.schemas.property import PropertySchema
from app.common.enums.permissions import (
    PermissionAction,
    PermissionResource,
    PermissionRole,
    Resources,
)


class MeSchema(Schema):
    class Meta:
        ordered = True

    user_id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        data_key="id",
        metadata={"description": "User id", "example": "1"},
    )
    email = fields.Email(
        dump_only=True,
        required=True,
        metadata={"description": "User email", "example": "<EMAIL>"},
    )


class PropertyFeatureSchema(Schema):
    name = fields.String(
        required=True,
        metadata={
            "description": "A feature enabled for the property",
            "example": "report_builder",
        },
    )
    enabled = fields.Boolean(
        required=True,
        metadata={
            "description": "Whether the feature is enabled for the property",
            "example": True,
        },
    )


class MePropertySchema(PropertySchema):
    features = fields.List(
        fields.Nested(PropertyFeatureSchema),
        metadata={
            "description": "Features enabled for the property",
            "example": [
                {"name": "report_builder", "enabled": True},
                {"name": "billing_portal", "enabled": False},
            ],
        },
    )


class MePolicySchema(Schema):
    class Meta:
        ordered = True

    resource = fields.String(
        required=True,
        metadata={
            "description": "An access-controlled resource",
            "example": Resources.StockReport.value,
        },
    )
    actions = fields.List(
        fields.String(
            metadata={"description": "An action a user may perform", "example": "read"}
        ),
        required=True,
        validate=validate.Length(min=1),
    )

    @pre_dump
    def set_policies(self, data, **kwargs):
        data["actions"] = [action.get("name") for action in data.get("actions")]
        return data


class MePermissionSchema(Schema):
    role = fields.String(
        required=True,
        validate=validate.OneOf(PermissionRole.list()),
        metadata={
            "description": "An access-controlled resource",
            "example": PermissionRole.VIEWER.value,
        },
    )
    resource = fields.String(
        dump_only=True,
        required=True,
        validate=validate.OneOf(PermissionResource.list()),
        metadata={
            "description": "An access-controlled resource",
            "example": PermissionResource.FINANCIAL_REPORTS.value,
        },
    )
    actions = fields.List(
        fields.String(
            metadata={"description": "An action a user may perform", "example": "read"}
        ),
        dump_only=True,
        validate=validate.OneOf(PermissionAction.list()),
        required=True,
    )


class PermissionResourceSchema(Schema):
    class Meta:
        ordered = True

    id = fields.String(
        dump_only=True,
        required=True,
        metadata={"description": "Resource id", "example": "1"},
    )
    name = fields.String(
        dump_only=True,
        required=False,
        metadata={"description": "Resource Name", "example": "Financial"},
    )


class PermissionSchema(Schema):
    role = fields.String(
        required=True,
        validate=validate.OneOf(
            [
                PermissionRole.VIEWER.value,
                PermissionRole.AUTHOR.value,
                PermissionRole.PUBLISHER.value,
            ]
        ),
        metadata={"description": "Role assigned to the resource"},
    )
    resource = fields.Nested(PermissionResourceSchema, required=True)
    actions = fields.List(
        fields.String(), required=True, metadata={"description": "Allowed actions"}
    )


class PermissionsSchema(Schema):
    reports = fields.List(fields.Nested(PermissionSchema), required=True)
    stock_reports = fields.List(fields.Nested(PermissionSchema), required=True)
    explore = fields.List(fields.Nested(PermissionSchema), required=True)
    settings = fields.List(fields.Nested(PermissionSchema), required=True)


class MePermissionsSchema(Schema):
    permissions = fields.Nested(PermissionsSchema, required=True)


class MeCurrenciesSchema(Schema):
    currencies = fields.List(fields.String, required=True)
