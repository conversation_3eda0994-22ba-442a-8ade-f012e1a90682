from marshmallow import Schema, fields

from app.api.v1_1.schemas.cdf import CDFSchema


class DatasetMultiLevelSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        dump_only=True,
        required=True,
        metadata={"description": "Multi Level ID", "example": "1"},
    )
    name = fields.String(
        dump_only=True,
        required=True,
        metadata={"description": "Multi Level Name", "example": "Room Nights"},
    )


class MultiLevelSchema(DatasetMultiLevelSchema):
    class Meta:
        ordered = True

    cdfs = fields.Nested(
        CDFSchema, required=True, many=True, metadata={"description": "List of CDFs"}
    )
