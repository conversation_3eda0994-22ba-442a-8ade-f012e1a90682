from marshmallow import fields, pre_dump, validate, validates_schema
from marshmallow.schema import Schema

from app.common.constants.favorite_limits import MAX_FAVORITES
from app.common.exceptions import InvalidUsage
from app.enums.favorite import FavoriteKind
from app.models.favorite import Favorite


class FavoriteSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        dump_only=True,
        required=True,
        metadata={"description": "Favorite Id", "example": 1},
    )
    rank = fields.Integer(
        required=True,
        validate=validate.Range(min=1, max=MAX_FAVORITES),
        metadata={
            "description": "Rank associated to the favorite in order to display the reports",
            "example": "1",
        },
    )
    report_id = fields.Integer(
        as_string=True,
        required=False,
        metadata={
            "description": "Report, Stock Report or Hub Id favorited",
            "example": "1",
        },
    )
    entity_id = fields.Integer(
        as_string=True,
        required=False,
        metadata={
            "description": "Report, Stock Report or Hub Id favorited",
            "example": "1",
        },
    )
    report_kind = fields.String(
        required=False,
        validate=validate.OneOf([kind.value for kind in FavoriteKind]),
        metadata={
            "description": "Report Kind",
            "example": FavoriteKind.StockReport.value,
        },
    )
    kind = fields.String(
        required=False,
        validate=validate.OneOf([kind.value for kind in FavoriteKind]),
        metadata={
            "description": "Favorite Kind",
            "example": FavoriteKind.StockReport.value,
        },
    )

    @validates_schema
    def validate_legacy_schema(self, data, **kwargs):
        # This is to support the legacy schema as well as the new one
        # this can be removed when the legacy schema is no longer used
        # this will validate a user sends one of report_id or entity_id and one of kind or report_kind
        # and put the values for report_id and report_kind into entity_id and entity_kind
        report_id = bool(data.get("report_id"))
        entity_id = bool(data.get("entity_id"))
        if report_id == entity_id:
            raise InvalidUsage.bad_request(
                "Must set only one of report id or entity id"
            )
        kind = bool(data.get("kind"))
        report_kind = bool(data.get("report_kind"))
        if kind == report_kind:
            raise InvalidUsage.bad_request("Must set only one of kind or report_kind")

        if report_id:
            data["entity_id"] = data["report_id"]
        if report_kind:
            data["kind"] = data["report_kind"]

    @pre_dump
    def load_entity_id_based_on_kind(self, data, **kwargs):
        """Pre dump function to check if Favorite is on stock report or hub_id id to return it in the report id"""
        if isinstance(data, Favorite):
            if data.stock_report_id:
                data.entity_id = data.stock_report_id
                data.kind = FavoriteKind.StockReport.value
            elif data.hub_id:
                data.kind = FavoriteKind.Hub.value
                data.entity_id = data.hub_id
            else:
                data.kind = FavoriteKind.Report.value
                data.entity_id = data.report_id

            data.report_id = data.entity_id
            data.report_kind = data.kind

        return data


class FavoriteReportTitleSchema(FavoriteSchema):
    class Meta:
        ordered = True

    report_title = fields.String(
        required=True,
        dump_only=True,
        metadata={
            "description": "Report title",
            "example": "Report title",
        },
    )

    title = fields.String(
        required=True,
        dump_only=True,
        metadata={
            "description": "Entity title",
            "example": "Entity title",
        },
    )


class FavoritesSchema(Schema):
    class Meta:
        ordered = True

    favorites = fields.Nested(
        FavoriteReportTitleSchema,
        many=True,
    )
    total = fields.Integer(
        metadata={
            "description": "Number of favorites for this user on the current property",
            "example": 12,
        }
    )


class RankSchema(FavoriteSchema):
    class Meta:
        ordered = True
        fields = ("rank",)

    @validates_schema
    def validate_legacy_schema(self, data, **kwargs):
        pass


class PathFavoriteIdSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        required=True,
        validate=validate.Range(min=1),
        metadata={"description": "Favorite Id of the resource"},
    )
