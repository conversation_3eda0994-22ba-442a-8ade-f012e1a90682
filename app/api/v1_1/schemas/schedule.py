import calendar
import re
from datetime import datetime, timezone

from croniter import croniter

from flask import g

from marshmallow import (
    Schema,
    ValidationError,
    fields,
    post_load,
    pre_dump,
    validate,
    validates,
    validates_schema,
)

from app.api.v1_1.schemas.pagination import PaginationSchema
from app.common.exceptions import InvalidUsage
from app.enums import Format, ScheduleView, Sort
from app.schemas.datetime import CustomDateTimeUtc


PATTERN = r"""
(0)\                                                                        # minutes ~ 0
(\*|([0-9]|1[0-9]|2[0-3])|\*)\                                              # hours ~ * OR 0-23
(\*|([1-9]|[1-2][0-9]?|3[0-1]))?(,(\*|([1-9]|[1-2][0-9]?|3[0-1]))){0,30}\   # day of month ~ * OR 1-31
(\*|([1-9]|1[0-2]?))?(,(\*|([1-9]|1[0-2]?))){0,12}\                         # month ~ * OR 1-12
(\*|[0-6])(,(\*|[0-6])){0,7}                                                # day of week ~ * OR 0-6
$                                                                           # matches the end of the string
"""


def FREQUENCY(required: bool) -> fields.String:
    FREQUENCY_DESCRIPTION = f"""Schedule frequency in [cron format](https://en.wikipedia.org/wiki/Cron).

Needs to match the following pattern:
```{PATTERN}```

* __0 10 * * 0,1,2,4,5__ - At 10:00 on Sunday, Monday, Tuesday, Thursday, and Friday
* __0 10 * * 2__ - At 10:00 on Tuesday
* __0 23 * 12 *__ - At 23:00 in December
* __0 23 1,31 1,2,5,6,7,10,11 1,3__ - At 08:00 on day-of-month 1 and 31 and on Monday and Wednesday in January, February, May, June, July, October, and November"""
    return fields.String(
        required=required,
        metadata={
            "example": "0 10 15 * *" if required else None,
            "description": FREQUENCY_DESCRIPTION,
        },
    )


def get_upcoming_at(frequency: str, now: datetime = None) -> str:
    now = now or datetime.now(timezone.utc)
    cron_parts = frequency.split()
    day_of_month = cron_parts[2]
    cron_hour = int(cron_parts[1]) if cron_parts[1] != "*" else (int(now.hour) + 1) % 24

    iter_ = croniter(frequency, now)
    next_time = iter_.get_next(datetime)

    # If cron is for day 31, and current month has < 31 days, simulate
    if day_of_month == "31":
        year, month = now.year, now.month
        last_day = calendar.monthrange(year, month)[1]

        # Only simulate if we're still before or on the last day of the month
        simulated_time = datetime(year, month, last_day, cron_hour, tzinfo=now.tzinfo)

        if last_day < 31 and now <= simulated_time:
            return simulated_time.strftime("%Y-%m-%dT%H:00:00Z")

    return next_time.strftime("%Y-%m-%dT%H:00:00Z")


class ScheduleSettingsSchema(Schema):
    class Meta:
        ordered = True

    require_login = fields.Boolean(
        metadata={"description": "Require login to view the file", "example": False},
    )


class ScheduleSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        metadata={"example": 1, "description": "Schedule ID"},
    )
    frequency = FREQUENCY(True)
    view = fields.String(
        load_default=ScheduleView.Formatted.value,
        validate=validate.OneOf([view.value for view in ScheduleView]),
        metadata={
            "example": ScheduleView.Formatted.value,
            "description": "Scheduled report view. Should be either 'formatted' or 'details'",
        },
    )
    format = fields.String(
        load_default=Format.XLSX.value,
        validate=validate.OneOf([format.value for format in Format]),
        metadata={
            "example": Format.XLSX.value,
            "description": "Scheduled report format. Should be either 'xlsx', 'csv' or 'json'",
        },
    )
    subject = fields.String(metadata={"description": "Subject of the email"})
    recipients = fields.List(
        fields.Email(),
        required=True,
        validate=validate.Length(max=15, min=1),
        metadata={"description": "List of emails that will receive the report"},
    )
    created_at = CustomDateTimeUtc(
        dump_only=True,
        description="Schedule created at",
    )
    updated_at = CustomDateTimeUtc(
        dump_only=True,
        description="Schedule updated at",
    )
    upcoming_at = fields.Function(
        lambda value: get_upcoming_at(value.frequency),
        dump_only=True,
        metadata={
            "format": "%Y-%m-%dT%H:00:00Z",
            "example": "2022-01-02T18:00:00Z",
            "description": "Schedule upcoming at",
        },
    )
    reports = fields.List(
        fields.Pluck(
            "ReportSchema",
            "id",
            metadata={"description": "Report id", "example": "1"},
        ),
        data_key="report_ids",
        dump_only=True,
        required=True,
        metadata={"description": "Report schedule ids"},
    )
    property_id = fields.Integer(
        as_string=True,
        dump_only=True,
        metadata={"example": 1, "description": "Property ID"},
    )
    user_id = fields.Integer(
        as_string=True,
        dump_only=True,
        metadata={"example": "1", "description": "User ID"},
    )
    settings = fields.Nested(ScheduleSettingsSchema)

    @post_load
    def post_load_default_settings(self, data, **kwargs):
        if bool(data) and not data.get("settings"):
            data["settings"] = {"require_login": False}
        return data

    @pre_dump
    def pre_dump_default_settings(self, data, **kwargs):
        if bool(data) and not data.settings:
            data.settings = {"require_login": False}
        return data

    @validates("frequency")
    def validate_frequency(self, frequency):
        match = re.match(PATTERN, frequency, re.VERBOSE)

        if not match:
            raise ValidationError(frequency + " is not a valid frequency")


class CreateScheduleSchema(ScheduleSchema):
    class Meta:
        fields = (
            "frequency",
            "view",
            "format",
            "subject",
            "recipients",
            "report_ids",
            "settings",
        )

    report_ids = fields.List(
        fields.Integer(
            as_string=True,
            validate=lambda x: int(x) > 0
            or InvalidUsage.bad_request("Value should be greater than 0."),
            metadata={"description": "Report id", "example": "1"},
        ),
        metadata={"description": "Report schedule ids"},
        validate=validate.Length(max=15),
    )

    @validates_schema
    def validate_reports_length(self, data, **kwargs):
        """Validate that if reports length is greater than 1, the report is formatted xlsx"""
        number_of_reports = len(data["report_ids"])
        if number_of_reports > 1 and (
            data["view"] != ScheduleView.Formatted.value
            or data["format"] != Format.XLSX.value
        ):
            raise ValidationError(
                f"To export multiple reports, use formatted xlsx. "
                f"Can not export {number_of_reports} as {data['format']}, {data['view']}",
                "reports",
            )

    @validates_schema
    def validate_view_format_combination(self, data, **kwargs):
        """Ensure valid format selection based on the selected view type."""
        if data["view"] == ScheduleView.Formatted.value and data["format"] not in (
            Format.XLSX.value,
            Format.PDF.value,
        ):
            raise ValidationError(
                "Formatted view only supports XLSX or PDF format", "format"
            )

        if (
            data["view"] == ScheduleView.Details.value
            and data["format"] == Format.PDF.value
        ):
            raise ValidationError(
                "Details view is not supported for PDF format", "format"
            )

        if data["view"] == ScheduleView.Table.value and data["format"] not in (
            Format.CSV.value,
            Format.JSON.value,
        ):
            raise ValidationError(
                "Table view only supports CSV or JSON format", "format"
            )


class SchedulePreviewSchema(ScheduleSchema):
    fields = (
        "id",
        "frequency",
        "view",
        "format",
        "subject",
        "upcoming_at",
        "updated_at",
        "reports",
        "recipients",
    )


class SchedulesSchema(PaginationSchema):
    class Meta:
        ordered = True

    schedules = fields.Nested(SchedulePreviewSchema, many=True)


class QueryScheduleSchema(ScheduleSchema):
    class Meta:
        fields = ("id", "subject", "frequency", "match")

    frequency = FREQUENCY(False)
    match = fields.String(
        validate=validate.Regexp(
            r"(19|20)[0-9][0-9]-(0[0-9]|1[0-2])-(0[1-9]|([12][0-9]|3[01]))T([01][0-9]|2[0-3]):00:00Z$"
        ),
        metadata={
            "description": "Match schedule frequency based on date (2022-01-02T18:00:00Z)"
        },
    )
    id = fields.Integer(
        as_string=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Filter by Schedule ID"},
    )

    @post_load
    def post_load_default_settings(self, data, **kwargs):
        return data

    @pre_dump
    def pre_dump_default_settings(self, data, **kwargs):
        return data


class QuerySortScheduleSchema(Schema):
    sort = fields.String(
        metadata={"description": "Sort schedules"},
        load_default="updated_at,desc",
        validate=validate.OneOf(
            [
                f"{field},{sort.name}"
                for field in (
                    "subject",
                    "view",
                    "format",
                    "created_at",
                    "updated_at",
                    "id",
                )
                for sort in Sort
            ]
        ),
    )


class PathScheduleIdSchema(Schema):
    schedule_id = fields.Integer(
        as_string=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        required=True,
        metadata={"description": "Schedule id of the resource"},
    )

    @post_load
    def set_schedule_id(self, data, **kwargs):
        g.schedule_id = (
            data["schedule_id"] if data is not None else self.context["schedule_id"]
        )
