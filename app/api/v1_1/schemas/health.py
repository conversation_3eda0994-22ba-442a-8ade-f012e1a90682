from marshmallow import Schema, fields


class HealthSchema(Schema):
    class Meta:
        ordered = True

    s3 = fields.Bo<PERSON>an(
        dump_only=True,
        metadata={"example": True, "description": "Object storage service"},
    )
    elasticache = fields.Boolean(
        dump_only=True, metadata={"description": "In-memory caching service"}
    )
    aurora = fields.<PERSON><PERSON>an(
        dump_only=True, example=True, description="Real-time data warehousing service"
    )
    rds = fields.Boolean(
        dump_only=True,
        metadata={"example": True, "description": "Relational database service"},
    )
    maintenance = fields.Bo<PERSON>an(
        dump_only=True,
        metadata={
            "example": True,
            "description": "Maintenance currently being performed",
        },
    )
