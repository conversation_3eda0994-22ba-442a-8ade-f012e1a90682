import re

from flask import g, request

from marshmallow import Schema, fields, validate
from marshmallow.decorators import post_dump, post_load, validates_schema

from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.exceptions import InvalidUsage
from app.enums import Cdf<PERSON>ind, CustomCdfFormulaKind, Dataset, Operator
from app.enums.custom_cdf_numeric_formats import CustomCdfNumericFormats
from app.enums.metric import Metric
from app.enums.report import ReportKind
from app.models.report import Report
from app.models.stock_report import StockReport
from app.schemas.datetime import CustomDateTimeUtc
from app.services.custom_cdf_service import CustomCdfService


class CustomCdfCaseValuesSchema(Schema):
    class Meta:
        ordered = True

    kind = fields.String(
        required=True,
        validate=validate.OneOf(
            ["string", "null", "list", "formula", "cases", "cdf", "number"]
        ),
    )

    value = fields.Raw(
        required=True,
    )


class CustomCdfCaseWhenConditionSchema(Schema):
    class Meta:
        ordered = True

    """
    Represents a single logical condition or a nested set of conditions.

    E.g.:
    {
      "field": "some_field",
      "operator": "equals",
      "value": "XYZ"
    }

    or a nested structure:
    {
      "and": [
        { "field": "some_field4", "operator": "is_null" },
        ...
      ]
    }
    """

    kind = fields.String(
        metadata={"description": "Field kind", "example": "cdf"}, required=False
    )
    field = fields.String(required=False)
    operator = fields.Str(required=False)
    case_value = fields.Nested(CustomCdfCaseValuesSchema, required=False)

    # Nested conditions
    and_ = fields.List(
        fields.Nested(lambda: CustomCdfCaseWhenConditionSchema()),
        data_key="and",
        required=False,
    )
    or_ = fields.List(
        fields.Nested(lambda: CustomCdfCaseWhenConditionSchema()),
        data_key="or",
        required=False,
    )


class CustomCdfCaseSchema(Schema):
    class Meta:
        ordered = True

    when = fields.Nested(CustomCdfCaseWhenConditionSchema, required=True)
    then = fields.Nested(CustomCdfCaseValuesSchema, required=True)


class CustomCdfFormulaSchema(Schema):
    DESCRIPTION = f"""- If the kind is *cdf* the value must be a valid cdf.
    - If the kind is *separator* the supported values are any.
    - If the kind is *operator* the supported values are: {[operator.value for operator in Operator]}.
    - If the kind is *operand* the supported values are numbers.
    - If the kind is *parenthesis* the supported values are: ( or )."""

    kind = fields.String(
        required=True,
        validate=validate.OneOf([kind.value for kind in CustomCdfFormulaKind]),
    )

    value = fields.String(
        required=True,
        metadata={"description": DESCRIPTION},
    )
    metric = fields.String(
        metadata={"description": "Metric to be used in the formula", "nullable": True},
        required=False,
        load_default=None,
        validate=validate.OneOf([metric.name for metric in Metric]),
    )

    cases = fields.Nested(CustomCdfCaseSchema, many=True, required=False)
    default_case = fields.Nested(
        CustomCdfCaseValuesSchema, required=False, nullable=True
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        if data.get("metric") and data["kind"] != CustomCdfFormulaKind.Cdf.value:
            raise InvalidUsage.bad_request("Metric is only supported for cdf kind")

        if data["kind"] == CustomCdfFormulaKind.Cdf.value:
            try:
                dataset_id = (
                    self.context["dataset_id"]
                    if "dataset_id" in self.context
                    else g.dataset_id
                )
                CDF(Dataset(dataset_id), data["value"])
            except ValueError:
                raise InvalidUsage.bad_request(
                    f"Invalid cdf value for this dataset: {data['value']}"
                )

            if CDF(Dataset(dataset_id), data["value"]).cdf["kind"].value in (
                CdfKind.DynamicCurrency.value,
                CdfKind.DynamicPercentage.value,
            ):
                raise InvalidUsage.bad_request(
                    f"Dynamic CDF {data['value']} is not supported on Custom CDF"
                )

        if data["kind"] == CustomCdfFormulaKind.Operator.value:
            if data["value"] not in [operator.value for operator in Operator]:
                raise InvalidUsage.bad_request(
                    f"Invalid operator for this kind: {data['value']}"
                )

        if data["kind"] == CustomCdfFormulaKind.Operand.value:
            if not re.match(r"^[0-9]+(\.[0-9]+)?$", data["value"]):
                raise InvalidUsage.bad_request(
                    f"Invalid operand for this kind: {data['value']}. It must be a number."
                )

        if data["kind"] == CustomCdfFormulaKind.Parenthesis.value:
            if data["value"] not in ["(", ")"]:
                raise InvalidUsage.bad_request(
                    "Invalid parenthesis: supported values are ( or )"
                )
        if data["kind"] == CustomCdfFormulaKind.CustomFieldCdf.value:
            # this validation should only run when creating or updating a custom cdf, the report must be loaded
            if hasattr(g, "report_id"):
                report = Report.get_by_id(g.report_id)
                custom_field_cdf_columns = [
                    custom_field_cdf.column
                    for custom_field_cdf in report.custom_field_cdfs
                ]

                if data["value"] not in custom_field_cdf_columns:
                    raise InvalidUsage.bad_request(
                        f"Invalid custom field cdf value for this dataset: {data['value']}"
                    )
        if len(data["value"]) < 1 and data["kind"] != CustomCdfFormulaKind.Case.value:
            raise InvalidUsage.bad_request("Value is required")
        if (data.get("cases") or data.get("default_case")) and not data[
            "kind"
        ] == CustomCdfFormulaKind.Case.value:
            raise InvalidUsage.bad_request(
                "Cases and default case are only supported for case kind"
            )


class CustomCdfSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        metadata={"description": "Report custom cdf id", "example": "1"},
    )
    column = fields.String(
        dump_only=True,
        required=False,
        metadata={"description": "Report custom cdf column", "example": "full_address"},
    )
    name = fields.String(
        required=True,
        validate=validate.Length(min=1),
        metadata={"description": "Report custom cdf name", "example": "Full address"},
    )
    description = fields.String(
        required=False,
        dump_default=None,
        load_default=None,
        metadata={
            "description": "Report custom cdf description",
            "example": "CDF with the full address information",
        },
    )
    formula = fields.Nested(
        CustomCdfFormulaSchema,
        many=True,
        required=True,
        validate=validate.Length(min=1),
    )
    kind = fields.String(
        required=True,
        metadata={
            "description": "Report custom cdf kind",
            "example": CdfKind.String.name,
        },
        validate=validate.OneOf(
            [kind.name for kind in [CdfKind.String, CdfKind.Number, CdfKind.Dynamic]]
        ),
    )
    created_at = CustomDateTimeUtc(
        dump_only=True,
        description="Report custom cdf created at",
    )
    updated_at = CustomDateTimeUtc(
        dump_only=True,
        description="Report custom cdf updated at",
    )
    format = fields.String(
        required=False,
        allow_none=True,
        validate=validate.OneOf(CustomCdfNumericFormats.values()),
        metadata={
            "description": "Custom Cdf Numeric format (valid for Number and Dynamic only)",
            "example": "currency",
        },
    )

    def name_to_column(self, name):
        return "custom_{}".format(
            re.sub(r"\W", "_", re.sub(r"^\W+|\W+$", "", name))
        ).lower()

    def validate_schema(self, data: dict, report_kind: ReportKind):

        if request and request.method == "PUT" and "validate" not in request.path:
            current_custom_cdf_column = CustomCdfService.get_by_id(
                report_kind, g.custom_cdf_id
            ).column

            if current_custom_cdf_column != self.name_to_column(data["name"]):
                match (report_kind.value):
                    case ReportKind.Report.value:
                        report = Report.get_by_id(g.report_id)
                    case ReportKind.StockReport.value:
                        report = StockReport.get_by_id(g.report_id)
                    case _:
                        raise InvalidUsage.bad_request("unsupported report kind")

                if (
                    report.filters is not None
                    and bool(
                        CDFs.get_custom_cdfs(
                            CDFs.get_cdfs_from_filters(report.filters, [])
                        )
                    )
                    and CDF(
                        dataset=Dataset(self.context["dataset_id"]),
                        name=current_custom_cdf_column,
                        is_custom_cdf=True,
                    ).get_custom_cdf_in_custom_cdfs(
                        CDFs.get_custom_cdfs(
                            CDFs.get_cdfs_from_filters(report.filters, [])
                        )
                    )
                ):
                    return InvalidUsage.conflict(
                        "This custom cdf is used in report filters, please remove the custom cdf from the report before editing the name"
                    )

        self.__validate_cdf(data)

    def __validate_cdf(self, data):
        if data["kind"] in [CdfKind.String.name, CdfKind.Number.name] and any(
            [bool(formula.get("metric")) for formula in data["formula"]]
        ):
            raise InvalidUsage.bad_request(
                "Metrics are not supported for String and Number cdfs"
            )
        if data["kind"] == CdfKind.Dynamic.name:
            if not all(
                [
                    bool(formula["metric"])
                    for formula in data["formula"]
                    if formula["kind"] == CustomCdfFormulaKind.Cdf.value
                ]
            ):
                raise InvalidUsage.bad_request("Metrics are required for Dynamic cdfs")
        if data["kind"] == CdfKind.String.name:
            if any(
                [
                    formula["kind"]
                    in [
                        CustomCdfFormulaKind.Operator.value,
                        CustomCdfFormulaKind.Operand.value,
                        CustomCdfFormulaKind.Parenthesis.value,
                    ]
                    for formula in data["formula"]
                ]
            ):
                raise InvalidUsage.bad_request(
                    "String cdfs cannot have operators, operands or parentheses"
                )

        if data["kind"] in (CdfKind.Number.name, CdfKind.Dynamic.name):
            if (
                len(data["formula"]) == 1
                and data["formula"][0]["kind"] == CustomCdfFormulaKind.Operand.value
            ):
                return True

            if any(
                [
                    formula["kind"] == CustomCdfFormulaKind.Separator.value
                    for formula in data["formula"]
                ]
            ):
                raise InvalidUsage.bad_request(
                    "Number and Dynamic custom cdfs cannot have separators"
                )

            error = "Invalid math formula"

            # Validate balance parenthesis
            expression = "".join(
                [
                    formula["value"]
                    for formula in data["formula"]
                    if formula["kind"] == CustomCdfFormulaKind.Parenthesis.value
                ]
            )
            stack = []
            for value in expression:
                if value == "(":
                    stack.append(value)
                if value == ")":
                    if not stack:
                        raise InvalidUsage.bad_request(error)
                    elif stack.pop() != "(":
                        raise InvalidUsage.bad_request(error)
                    else:
                        continue
            if stack:
                raise InvalidUsage.bad_request(error)

            # Validate parenthesis have following number or cdf
            if len(expression) > 0:
                expression = "".join(
                    [
                        (
                            formula["value"]
                            if formula["kind"] != CustomCdfFormulaKind.Cdf.value
                            else "$CDF"
                        )
                        for formula in data["formula"]
                    ]
                )
                if (
                    re.match(
                        r"^.*?\((((\d+(?:\.\d+)*|\$CDF)(\*|\/|\+|\-)\s*)+((\d+(?:\.\d+)*)|\$CDF)|((\d+(?:\.\d+)*)|\$CDF))*\).*$",
                        expression,
                    )
                    is None
                ):
                    raise InvalidUsage.bad_request(error)

            # Validate overall formula
            expression = "".join(
                [
                    (
                        formula["value"]
                        if formula["kind"]
                        not in (
                            CustomCdfFormulaKind.Cdf.value,
                            CustomCdfFormulaKind.Case.value,
                        )
                        else "$CDF"
                    )
                    for formula in data["formula"]
                    if formula["kind"] != CustomCdfFormulaKind.Parenthesis.value
                ]
            )

            if (
                re.match(
                    r"^((\d+(?:\.\d+)*|\$CDF)(\*|\/|\+|\-)\s*)*((\d+(?:\.\d+)*)|\$CDF)$",
                    expression,
                )
                is None
            ):
                raise InvalidUsage.bad_request(error)

    @post_load
    def post_load(self, data, **kwargs):
        data["column"] = self.name_to_column(data["name"])
        return data

    @post_dump
    def post_dump(self, data, **kwargs):
        if data.get("kind") in (
            CdfKind.Number.name,
            CdfKind.Dynamic.name,
        ) and not data.get("format"):
            data["format"] = CustomCdfNumericFormats.Number.value
        return data
