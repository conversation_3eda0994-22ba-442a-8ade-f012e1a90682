from marshmallow import fields, validate, validates

from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportPicklistQuerySchema,
    ClassicReportQuerySchema,
)
from app.api.v1_1.schemas.report.report import ReportColumnsSchema, ReportGroupSchema
from app.api.v1_1.schemas.report.report_filters import (
    ReportFiltersSchema,
)
from app.api.v1_1.validations.reports import (
    validate_cdfs,
    validate_multi_levels,
)
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum

FILTER_EXAMPLE = {
    "and": [
        {
            "cdf": {"type": "default", "column": "transaction_status"},
            "operator": "list_contains",
            "value": ["Posted"],
        },
        {
            "or": [
                {
                    "cdf": {"type": "default", "column": "tax_type"},
                    "operator": "is_not_null",
                    "value": "",
                },
                {
                    "cdf": {"type": "default", "column": "fee_type"},
                    "operator": "is_not_null",
                    "value": "",
                },
            ]
        },
        {
            "or": [
                {
                    "cdf": {
                        "type": "default",
                        "column": "is_transaction_adjusted",
                    },
                    "operator": "equals",
                    "value": "Yes",
                },
                {
                    "cdf": {
                        "type": "default",
                        "column": "is_transaction_adjusted",
                    },
                    "operator": "equals",
                    "value": "No",
                },
            ]
        },
    ]
}


class TransactionsReportQuerySchema(ClassicReportQuerySchema):
    class Meta:
        ordered = True

    columns = fields.Nested(
        ReportColumnsSchema,
        many=True,
        allow_none=False,
        required=False,
        validate=validate.Length(min=1),
        metadata={"description": "Report columns"},
    )

    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )
    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=3),
        allow_none=True,
        metadata={"description": "Report group rows"},
    )


class TransactionsReportPicklistQuerySchema(ClassicReportPicklistQuerySchema):
    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )

    @validates("group_rows")
    def validate_group_rows(self, group_rows):
        dataset = Dataset(DatasetEnum.Financial)
        validate_cdfs(group_rows, dataset.flatten_cdfs)
        validate_multi_levels(group_rows, dataset)
