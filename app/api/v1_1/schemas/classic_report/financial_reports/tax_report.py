from marshmallow import fields, validate, validates

from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportPicklistQuerySchema,
    ClassicReportQuerySchema,
)
from app.api.v1_1.schemas.report.report_filters import (
    ReportComparisonSchema,
    ReportFiltersSchema,
)
from app.api.v1_1.validations.reports import (
    validate_cdfs,
    validate_multi_levels,
)
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum

FILTER_EXAMPLE = {
    "and": [
        {
            "cdf": {"type": "default", "column": "transaction_status"},
            "operator": "list_contains",
            "value": ["Posted"],
        },
        {
            "or": [
                {
                    "cdf": {"type": "default", "column": "tax_type"},
                    "operator": "is_not_null",
                    "value": "",
                },
                {
                    "cdf": {"type": "default", "column": "fee_type"},
                    "operator": "is_not_null",
                    "value": "",
                },
            ]
        },
        {
            "or": [
                {
                    "cdf": {
                        "type": "default",
                        "column": "is_transaction_adjusted",
                    },
                    "operator": "equals",
                    "value": "Yes",
                },
                {
                    "cdf": {
                        "type": "default",
                        "column": "is_transaction_adjusted",
                    },
                    "operator": "equals",
                    "value": "No",
                },
            ]
        },
    ]
}


class TaxReportQuerySchema(ClassicReportQuerySchema):
    class Meta:
        ordered = True

    comparisons = fields.Nested(
        ReportComparisonSchema,
        many=True,
        dump_default=None,
        load_default=None,
        validate=validate.Length(max=3),
        metadata={
            "description": "A list of up to 3 Comparisons, each containing 'name' and 'filters' properties. The 'filters' are the same structure as those in report.filters."
        },
    )
    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )


class TaxReportPicklistQuerySchema(ClassicReportPicklistQuerySchema):
    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )

    @validates("group_rows")
    def validate_group_rows(self, group_rows):
        dataset = Dataset(DatasetEnum.Financial)
        validate_cdfs(group_rows, dataset.flatten_cdfs)
        validate_multi_levels(group_rows, dataset)
