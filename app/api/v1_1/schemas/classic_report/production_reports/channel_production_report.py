from marshmallow import fields, validate, validates, validates_schema

from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportPicklistQuerySchema,
    ClassicReportQuerySchema,
)
from app.api.v1_1.schemas.report.report import ReportGroupSchema, ReportSortSchema
from app.api.v1_1.validations.reports import (
    validate_cdfs,
    validate_multi_levels,
)
from app.common.exceptions import InvalidUsage
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum

GROUP_ROW_EXAMPLE = [{"cdf": {"column": "reservation_source", "type": "default"}}]


class ChannelProductionReportQuerySchema(ClassicReportQuerySchema):

    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=1),
        allow_none=True,
        metadata={"description": "Report group rows", "example": GROUP_ROW_EXAMPLE},
    )
    sort = fields.Nested(
        ReportSortSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=1),
        allow_none=True,
        metadata={"description": "Report sort"},
    )

    @validates_schema
    def validate_sort_and_group_rows(self, data, **kwargs):
        if data.get("sort") and not data.get("group_rows"):
            raise InvalidUsage.bad_request("Group rows must be set if sort is set")
        if data.get("group_rows") and not data.get("sort"):
            raise InvalidUsage.bad_request("Sort must be set if group rows is set")
        if data.get("sort") or data.get("group_rows"):
            # if one is set, the other must be set, both must use the same cdf
            sort_column = data["sort"][0]["cdf"]["column"]
            group_column = data["group_rows"][0]["cdf"]["column"]
            if sort_column != group_column:
                raise InvalidUsage.bad_request(
                    "Sort and group rows must use the same cdf"
                )


class ChannelProductionReportPicklistQuerySchema(ClassicReportPicklistQuerySchema):
    @validates("group_rows")
    def validate_group_rows(self, group_rows):
        dataset = Dataset(DatasetEnum.OccupancyV1)
        validate_cdfs(group_rows, dataset.flatten_cdfs)
        validate_multi_levels(group_rows, dataset)
