import datetime

from marshmallow import Schema, fields

from app.common.constants.datetime import DEFAULT_DATETIME_DATE_FORMAT


class DailyStatisticsReportDataSchema(Schema):
    class Meta:
        ordered = True

    items_and_services_revenue = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Items and services revenue"},
    )
    payments = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Payments"},
    )
    comps = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Complimentary services"},
    )
    walkins = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Walk-in guests"},
    )
    number_of_guests = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Total number of guests"},
    )
    checkins = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Number of Check-ins"},
    )
    checkouts = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Number of Checkouts"},
    )
    avg_los_nights_stayed = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Average length of stay"},
    )
    avg_los_total_room_nights = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Average length of stay in total room nights"},
    )
    adr_per_guest = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Average daily rate per guest"},
    )
    no_shows = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "No-shows"},
    )
    blocks = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Blocked rooms"},
    )
    adjustments = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Adjustments"},
    )
    total_revenue = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Total revenue"},
    )


class ClassicComparisonSchema(Schema):
    class Meta:
        ordered = True

    date = fields.String(
        required=True,
        metadata={
            "description": "Input date",
            "example": datetime.datetime.now(datetime.timezone.utc)
            .date()
            .strftime(DEFAULT_DATETIME_DATE_FORMAT),
        },
    )


class DailyStatisticsReportSchema(ClassicComparisonSchema):
    today = fields.Nested(
        DailyStatisticsReportDataSchema,
        dump_only=True,
        required=True,
    )
    month_to_date = fields.Nested(
        DailyStatisticsReportDataSchema,
        dump_only=True,
        required=True,
    )


class DailyFinancialOccupancyRateDataSchema(Schema):
    class Meta:
        ordered = True

    occupancy = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Occupancy rate"},
    )
    availability = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Availability rate"},
    )
    blocks_and_holds = fields.Integer(
        dump_default=0,
        required=True,
        metadata={"description": "Blocked rooms"},
    )
    occupancy_percentage = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Occupancy percentage"},
    )
    availability_percentage = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Availability percentage"},
    )
    blocks_and_holds_percentage = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Blocked percentage"},
    )


class DailyFinancialOccupancyRateSchema(ClassicComparisonSchema):
    today = fields.Nested(
        DailyFinancialOccupancyRateDataSchema,
        required=True,
        metadata={"description": "Occupancy rate for the day"},
    )
    month_to_date = fields.Nested(
        DailyFinancialOccupancyRateDataSchema,
        required=True,
        metadata={"description": "Occupancy rate for the month so far"},
    )


class DailyFinancialRoomRevenueDataSchema(Schema):
    adr = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Average daily rate"},
    )
    revpar = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Revenue per available room"},
    )
    room_revenue = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Total revenue"},
    )


class DailyFinancialRoomRevenueSchema(ClassicComparisonSchema):
    today = fields.Nested(
        DailyFinancialRoomRevenueDataSchema,
        required=True,
        metadata={"description": "Room revenue for the day"},
    )
    month_to_date = fields.Nested(
        DailyFinancialRoomRevenueDataSchema,
        required=True,
        metadata={"description": "Room revenue for the month so far"},
    )


class DailyFinancialForecastDataSchema(DailyFinancialRoomRevenueDataSchema):
    occupancy = fields.Float(
        dump_default=0,
        required=True,
        metadata={"description": "Occupancy rate"},
    )
    date = fields.String(
        required=True,
        metadata={"description": "Date"},
    )


class DailyFinancialForecastSchema(ClassicComparisonSchema):
    results = fields.List(
        fields.Nested(
            DailyFinancialForecastDataSchema,
            required=True,
        ),
    )


class DailyFinancialOnTheBooksForecastDataSchema(DailyFinancialRoomRevenueDataSchema):
    class Meta:
        ordered = True

    total_rooms_sold = fields.Integer(
        required=True,
        dump_default=0,
        metadata={"description": "Total rooms sold"},
    )
    total_rooms_sold_percentage = fields.Float(
        required=True,
        dump_default=0,
        metadata={"description": "Total rooms sold percentage over the total rooms"},
    )


class DailyFinancialOnTheBooksForecastSchema(ClassicComparisonSchema):
    result = fields.Nested(
        DailyFinancialOnTheBooksForecastDataSchema,
        required=True,
        allow_none=False,
    )


class DailyFinancialReportQueryParamsSchema(Schema):
    class Meta:
        ordered = True

    date = fields.Date(
        required=True,
        metadata={
            "description": "Filter date of the report",
            "example": datetime.datetime.now(datetime.timezone.utc)
            .date()
            .strftime(DEFAULT_DATETIME_DATE_FORMAT),
        },
    )
