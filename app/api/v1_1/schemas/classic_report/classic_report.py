from flask import g

from marshmallow import fields, post_load, pre_load, validate, validates_schema

from app.api.v1_1.schemas.custom_cdf import CustomCdfSchema
from app.api.v1_1.schemas.report.report import (
    QueryFormatParamsSchema,
    ReportBaseSchema,
    ReportDataSchema,
    ReportGroupSchema,
    ReportSummarySchema,
)
from app.common.exceptions import InvalidUsage
from app.enums.mode import Mode
from app.enums.pagination import Limit, Offset


class ClassicReportDataSchema(ReportDataSchema):
    class Meta:
        ordered = True

    offset = fields.Integer(
        metadata={"description": "The number of records skipped in the response"}
    )
    limit = fields.Integer(
        metadata={"description": "The maximum number of records in the response"}
    )


class ClassicReportSummarySchema(ReportSummarySchema):
    pass


class ClassicReportQueryParamsSchema(QueryFormatParamsSchema):
    class Meta:
        ordered = True

    mode = fields.String(
        validate=validate.OneOf([Mode.Preview.name, Mode.Run.name, Mode.Page.name]),
        load_default=Mode.Preview.name,
        required=False,
        metadata={
            "description": "Select the mode to limit the amount of records. Preview has a limit of 100 records, and run has a limit 12,000 records",
            "example": Mode.Preview.name,
        },
    )

    offset = fields.Integer(
        metadata={
            "description": "The difference between start of the data and the start of the response data",
            "example": 0,
        },
        validate=validate.Range(min=Offset.Min.value),
    )
    limit = fields.Integer(
        metadata={
            "description": "The maximum number of results in the response",
            "example": 5,
        },
        validate=validate.Range(min=Limit.Min.value, max=None),
    )

    @post_load
    def load_mode(self, data, **kwargs):
        data["mode"] = Mode[data["mode"]]
        return data

    @validates_schema
    def validate_page_offset_limit(self, data, **kwargs):
        if (data.get("offset") or data.get("limit")) and data["mode"] != Mode.Page.name:
            raise InvalidUsage.bad_request(
                "Offset and Limit are exclusive to Page mode"
            )

        if data["mode"] == Mode.Page.name and (
            data.get("offset") is None or data.get("limit") is None
        ):
            raise InvalidUsage.bad_request("Page mode requires both Offset and Limit")


class ClassicReportCustomCdfSchema(CustomCdfSchema):
    class Meta:
        ordered = True

    @pre_load
    def pre_load(self, data, **kwargs):
        self.context["dataset_id"] = g.dataset_id
        return data


class ClassicReportSchema(ReportBaseSchema):
    class Meta:
        ordered = True

    custom_cdfs = fields.Nested(
        ClassicReportCustomCdfSchema,
        many=True,
        dump_default=[],
        dump_only=False,
        metadata={"description": "Report custom cdfs"},
    )

    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        dump_default=None,
        load_default=None,
        allow_none=True,
        validate=validate.Length(min=1, max=5),
        metadata={"description": "Report group rows"},
    )
