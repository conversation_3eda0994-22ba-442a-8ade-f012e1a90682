from collections import OrderedDict

from marshmallow import fields, validate, validates

from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportQuerySchema,
)
from app.api.v1_1.schemas.report.report import (
    ReportGroupSchema,
)
from app.api.v1_1.validations.reports import (
    validate_cdfs,
    validate_multi_levels,
    validate_property_ids,
)
from app.common.exceptions import InvalidUsage
from app.datasets.dataset import Dataset
from app.enums.cdf import Cdf
from app.enums.dataset import Dataset as DatasetEnum


SORT_EXAMPLE = [
    {
        "cdf": {"type": "default", "column": "reservation_number"},
        "direction": "asc",
    }
]

GROUP_ROW_EXAMPLE = [{"cdf": {"column": "reservation_source_category"}}]


class UserReconciliationReportPicklistQuerySchema(ClassicReportQuerySchema):
    class Meta:
        fields = ("property_ids", "filters", "group_rows")

    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=True,
        validate=validate.Length(min=1, max=1),
        metadata={
            "description": "Report group rows",
            "example": GROUP_ROW_EXAMPLE,
        },
    )

    @validates("property_ids")
    def validate_property_ids(self, property_ids):
        validate_property_ids(property_ids)

    @validates("filters")
    def validate_no_custom_field_cdfs(self, filters):
        if filters:
            if isinstance(filters, (OrderedDict, dict)):
                if "and" in filters:
                    self.validate_no_custom_field_cdfs(filters["and"])
                elif "or" in filters:
                    self.validate_no_custom_field_cdfs(filters["or"])
                else:
                    cdf = filters.get("cdf")
                    if cdf and cdf.get("type") == Cdf.CustomField.value:
                        raise InvalidUsage.bad_request(
                            "Custom Field CDFs are not allowed on User Reconciliation Report Filters"
                        )
                    if cdf and cdf.get("type") == Cdf.Custom.value:
                        raise InvalidUsage.bad_request(
                            "Custom CDFs are not allowed on User Reconciliation Report Filters"
                        )
            else:
                for filter in filters:
                    self.validate_no_custom_field_cdfs(filter)

    @validates("group_rows")
    def validate_group_rows(self, group_rows):
        dataset = Dataset(DatasetEnum.Financial)
        validate_cdfs(group_rows, dataset.flatten_cdfs)
        validate_multi_levels(group_rows, dataset)
