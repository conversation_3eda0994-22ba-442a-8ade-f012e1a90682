from marshmallow import fields, validate

from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportQuerySchema,
)
from app.api.v1_1.schemas.report.report_filters import ReportComparisonSchema


class DailyRevenueReportQuerySchema(ClassicReportQuerySchema):
    class Meta:
        ordered = True

    comparisons = fields.Nested(
        ReportComparisonSchema,
        many=True,
        dump_default=None,
        load_default=None,
        validate=validate.Length(max=3),
        metadata={
            "description": "A list of up to 3 Comparisons, each containing 'name' and 'filters' properties. The 'filters' are the same structure as those in report.filters."
        },
    )
