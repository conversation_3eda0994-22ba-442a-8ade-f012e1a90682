from collections import OrderedDict

from marshmallow import Schema, fields, validate, validates

from webargs.fields import DelimitedList

from app.api.v1_1.schemas.report.report import (
    ReportGroupSchema,
    ReportSettingsSchema,
    ReportSortSchema,
)
from app.api.v1_1.schemas.report.report_filters import (
    FILTER_EXAMPLE,
    ReportFiltersSchema,
)
from app.api.v1_1.schemas.report.report_formats import ReportFormatsSchema
from app.api.v1_1.validations.reports import validate_property_ids
from app.common.enums.picklist import PaymentReconciliationReportPicklistCdfs
from app.common.exceptions import InvalidUsage
from app.enums.cdf import Cdf


class PaymentReconciliationReportQuerySchema(Schema):
    class Meta:
        ordered = True

    property_ids = fields.List(
        fields.Integer(
            as_string=True,
            required=True,
            validate=lambda x: int(x) > 0
            or InvalidUsage.bad_request("Value should be greater than 0."),
            metadata={"description": "Property id", "example": "1"},
        ),
        validate=validate.Length(min=1, max=1),
        required=True,
        metadata={"description": "Report property ids"},
    )
    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=3),
        metadata={"description": "Report group rows"},
    )
    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )
    sort = fields.Nested(
        ReportSortSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report sort"},
    )
    settings = fields.Nested(
        ReportSettingsSchema,
        required=False,
        default=None,
        allow_none=True,
        metadata={"description": "Report settings"},
    )

    formats = fields.Nested(
        ReportFormatsSchema,
        required=False,
        allow_none=True,
        metadata=dict(description="Formatting options of the report"),
    )


class PaymentReconciliationReportPicklistQuerySchema(
    PaymentReconciliationReportQuerySchema
):
    class Meta:
        fields = ("property_ids", "filters", "group_rows")

    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=1),
        dump_default=None,
        load_default=None,
        allow_none=True,
        metadata={"description": "Report group rows"},
    )

    @validates("property_ids")
    def validate_property_ids(self, property_ids):
        validate_property_ids(property_ids)

    @validates("filters")
    def validate_no_custom_field_cdfs(self, filters):
        if filters:
            if isinstance(filters, (OrderedDict, dict)):
                if "and" in filters:
                    self.validate_no_custom_field_cdfs(filters["and"])
                elif "or" in filters:
                    self.validate_no_custom_field_cdfs(filters["or"])
                else:
                    cdf = filters.get("cdf")
                    if cdf and cdf.get("type") == Cdf.CustomField.value:
                        raise InvalidUsage.bad_request(
                            "Custom Field CDFs are not allowed on Classic Reports"
                        )
            else:
                for filter in filters:
                    self.validate_no_custom_field_cdfs(filter)


class PaymentReconciliationReportPicklistByCdfNameSchema(Schema):
    cdf = fields.String(
        required=False,
        metadata={
            "description": "Column name reference for cdf",
            "example": "reservation_status",
        },
        validate=validate.OneOf(PaymentReconciliationReportPicklistCdfs.values()),
    )


class QueryParamPaymentReconciliationReportCdfsByNameSchema(Schema):
    children = DelimitedList(
        fields.String(
            required=False,
            metadata={
                "description": "Column name reference for cdf",
                "example": "reservation_status",
            },
            validate=validate.OneOf(PaymentReconciliationReportPicklistCdfs.values()),
        ),
        validate=validate.Length(max=2),
    )
