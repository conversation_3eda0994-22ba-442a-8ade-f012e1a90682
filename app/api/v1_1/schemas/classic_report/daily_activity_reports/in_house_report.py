from marshmallow import fields, validate

from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportQuerySchema,
)
from app.api.v1_1.schemas.report.report_filters import ReportFiltersSchema


FILTER_EXAMPLE = {
    "and": [
        {
            "cdf": {"column": "stay_date", "type": "default", "multi_level_id": 1},
            "operator": "greater_than_or_equal",
            "value": "today",
        },
        {
            "cdf": {"column": "stay_date", "type": "default", "multi_level_id": 1},
            "operator": "less_than",
            "value": "tomorrow",
        },
    ]
}


class InHouseReportQuerySchema(ClassicReportQuerySchema):
    class Meta:
        ordered = True

    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )
