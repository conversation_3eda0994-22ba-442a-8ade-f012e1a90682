from marshmallow import validates

from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportPicklistQuerySchema,
)
from app.api.v1_1.validations.reports import (
    validate_cdfs,
    validate_multi_levels,
)
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum


class ArrivalsReportPicklistQuerySchema(ClassicReportPicklistQuerySchema):
    @validates("group_rows")
    def validate_group_rows(self, group_rows):
        dataset = Dataset(DatasetEnum.Reservations)
        validate_cdfs(group_rows, dataset.flatten_cdfs)
        validate_multi_levels(group_rows, dataset)
