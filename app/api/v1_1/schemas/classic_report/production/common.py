from marshmallow import (
    Schema,
    ValidationError,
    fields,
    validate,
    validates_schema,
)

from app.classic_reports.production_reports.constants import (
    OCCUPANCY_FILTERS,
    OCCUPANCY_MAPPINGS,
)


class OccupancyFilterNameSchema(Schema):
    filter_name = fields.String(validate=validate.OneOf(OCCUPANCY_FILTERS))


class OccupancyMappingNameSchema(Schema):
    mapping_name = fields.String(validate=validate.OneOf(OCCUPANCY_MAPPINGS))


class CompareYearsReportArgSchema(Schema):
    class Meta:
        ordered = True

    report_year = fields.String(
        required=True, metadata={"description": "Year of the report"}
    )
    compare_years = fields.List(
        fields.String(
            required=False,
            metadata={"description": "Comparison years of the report"},
        ),
        many=True,
    )
    period_start = fields.String(
        required=True,
        metadata={"description": "Start of the period of the report in MM-DD format"},
        validate=validate.Regexp(
            r"^(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$",
            error="Invalid date. Must be in MM-DD format with a valid month and day.",
        ),
    )
    grouping = fields.String(
        required=True,
        validate=validate.OneOf(["day", "week", "month"]),
        metadata={"description": "Grouping of the report"},
    )
    period_end = fields.String(
        required=True,
        metadata={"description": "Start of the period of the report in MM-DD format"},
        validate=validate.Regexp(
            r"^(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$",
            error="Invalid date. Must be in MM-DD format with a valid month and day.",
        ),
    )
    room_types = fields.List(
        fields.String,
        required=False,
        metadata={"description": "Room types to include in the report"},
    )
    reservation_sources = fields.List(
        fields.String,
        required=False,
        metadata={"description": "Reservation sources to include in the report"},
    )
    reservation_source_categories = fields.List(
        fields.String,
        required=False,
        metadata={
            "description": "Reservation source categories to include in the report"
        },
    )

    @validates_schema
    def validate_schema(self, data, **kwargs):
        if data["period_start"] > data["period_end"]:
            raise ValidationError("Period start must be before period end")
        if data.get("compare_years") and data["report_year"] in data["compare_years"]:
            raise ValidationError("Report year cannot be in compare years list")


class CompareYearsBaseSchema(Schema):
    date = fields.String(
        required=True,
        alias="date",
        metadata={"description": "Summary date"},
    )


class CompareYearsReportSchema(Schema):
    year = fields.Integer(
        required=True,
        metadata={"description": "Year of the report"},
    )
    compare_year = fields.Integer(
        required=False,
        metadata={"description": "Compare year of the report"},
    )
