from marshmallow import (
    fields,
    validate,
)

from app.api.v1_1.schemas.classic_report.production.common import (
    CompareYearsBaseSchema,
    CompareYearsReportSchema,
)


class RoomsSoldReportDataSchema(CompareYearsBaseSchema):
    occupancy = fields.Float(
        required=True,
        metadata={"description": "Revenue per available room"},
    )
    accommodations_booked = fields.Integer(
        required=True,
        metadata={"description": "Total accommodations booked"},
    )
    revenue = fields.Float(required=True, metadata={"description": "Total revenue"})
    compare_accommodations_booked = fields.Integer(
        required=False,
        metadata={"description": "Comparison total accommodations booked"},
    )
    compare_occupancy = fields.Float(
        required=False,
        metadata={"description": "Comparison total revenue"},
    )
    compare_revenue = fields.Float(
        required=False,
        metadata={"description": "Comparison total revenue"},
    )


class RoomsSoldReportSchema(CompareYearsReportSchema):
    results = fields.List(
        fields.Nested(
            RoomsSoldReportDataSchema,
            required=True,
        ),
        validate=validate.Length(min=1, max=366),
        metadata={"description": "Rooms Sold/Occupancy report for the year"},
    )
