from marshmallow import (
    fields,
    validate,
)

from app.api.v1_1.schemas.classic_report.production.common import (
    CompareYearsBaseSchema,
    CompareYearsReportSchema,
)


class RevParReportDataSchema(CompareYearsBaseSchema):
    revpar = fields.Float(
        required=True,
        metadata={"description": "Revenue per available room"},
    )
    accommodations_booked = fields.Integer(
        required=True,
        metadata={"description": "Total accommodations booked"},
    )
    revenue = fields.Float(required=True, metadata={"description": "Total revenue"})
    compare_revpar = fields.Float(
        required=False,
        metadata={"description": "Comparison revenue per available room"},
    )
    compare_accommodations_booked = fields.Integer(
        required=False,
        metadata={"description": "Comparison total accommodations booked"},
    )
    compare_revenue = fields.Float(
        required=False,
        metadata={"description": "Comparison total revenue"},
    )
    percentage_change = fields.Float(
        required=False,
        allow_none=True,
        metadata={"description": "Percentage change in revenue"},
    )


class RevParReportSchema(CompareYearsReportSchema):
    results = fields.List(
        fields.Nested(
            RevParReportDataSchema,
            required=True,
        ),
        validate=validate.Length(min=1, max=366),
        metadata={"description": "RevPar report for the year"},
    )
