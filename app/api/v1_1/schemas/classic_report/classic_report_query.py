from collections import OrderedDict

from flask import g

from marshmallow import Schema, fields, pre_load, validate, validates, validates_schema

from app.api.v1_1.schemas.report.report import (
    ReportGroupSchema,
    ReportSettingsSchema,
    ReportSortSchema,
)
from app.api.v1_1.schemas.report.report_filters import (
    ReportFiltersSchema,
)
from app.api.v1_1.schemas.report.report_formats import ReportFormatsSchema
from app.api.v1_1.validations.reports import (
    validate_cdfs,
    validate_filters,
    validate_multi_levels,
    validate_property_ids,
)
from app.common.exceptions import InvalidUsage
from app.datasets.dataset import Dataset
from app.enums.cdf import Cdf
from app.enums.dataset import Dataset as DatasetEnum


FILTER_EXAMPLE = {
    "and": [
        {
            "cdf": {"type": "default", "column": "stay_date"},
            "operator": "greater_than_or_equal",
            "value": "today",
        },
        {
            "cdf": {"type": "default", "column": "stay_date"},
            "operator": "less_than",
            "value": "tomorrow",
        },
    ]
}

SORT_EXAMPLE = [
    {
        "cdf": {"type": "default", "column": "user"},
        "direction": "asc",
    }
]

GROUP_ROW_EXAMPLE = [
    {"cdf": {"column": "reservation_source_category", "multi_level_id": 4}}
]


class ClassicReportQueryBaseSchema(Schema):
    class Meta:
        ordered = True

    property_ids = fields.List(
        fields.Integer(
            as_string=True,
            required=True,
            validate=validate.Range(min=1),
            metadata={"description": "Property id", "example": "1"},
        ),
        validate=validate.Length(min=1, max=1),
        required=True,
        metadata={"description": "Report property ids"},
    )
    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )

    @pre_load
    def add_dataset_id(self, data, **kwargs):
        self.context["dataset_id"] = g.dataset_id
        return data

    @validates_schema
    def validates_schema(self, data, **kwargs):
        validate_filters(data)

    @validates("property_ids")
    def validate_property_ids(self, property_ids):
        validate_property_ids(property_ids)

    @validates("filters")
    def validate_no_custom_field_cdfs(self, filters):
        if filters:
            if isinstance(filters, (OrderedDict, dict)):
                if "and" in filters:
                    self.validate_no_custom_field_cdfs(filters["and"])
                elif "or" in filters:
                    self.validate_no_custom_field_cdfs(filters["or"])
                else:
                    cdf = filters.get("cdf")
                    if cdf and cdf.get("type") == Cdf.CustomField.value:
                        raise InvalidUsage.bad_request(
                            "Custom Field CDFs are not allowed on Classic Reports"
                        )
            else:
                for filter in filters:
                    self.validate_no_custom_field_cdfs(filter)


class ClassicReportQuerySchema(ClassicReportQueryBaseSchema):
    sort = fields.Nested(
        ReportSortSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report sort", "example": SORT_EXAMPLE},
    )
    settings = fields.Nested(
        ReportSettingsSchema,
        required=False,
        dump_default=None,
        allow_none=True,
        metadata={"description": "Report settings"},
    )
    formats = fields.Nested(
        ReportFormatsSchema,
        required=False,
        allow_none=True,
        metadata=dict(description="Formatting options of the report"),
    )

    @validates_schema
    def validates_sort(self, data, **kwargs):
        if "sort" in data and data["sort"] is not None and "dataset_id" in self.context:
            dataset = Dataset(DatasetEnum(self.context["dataset_id"]))
            validate_cdfs(data["sort"], dataset.flatten_cdfs)

            sort_cdfs = (
                [sort["cdf"]["column"] for sort in data["sort"]]
                if "sort" in data
                else []
            )

            if len(sort_cdfs) != len(set(sort_cdfs)):
                raise InvalidUsage.bad_request("Sort cdfs must not contain duplicates")

            validate_multi_levels(data["sort"], dataset)


class ClassicReportPicklistQuerySchema(ClassicReportQueryBaseSchema):
    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=True,
        validate=validate.Length(min=1, max=4),
        allow_none=True,
        metadata={"description": "Report group rows", "example": GROUP_ROW_EXAMPLE},
    )

    @validates("property_ids")
    def validate_property_ids(self, property_ids):
        validate_property_ids(property_ids)

    @validates("filters")
    def validate_no_custom_field_cdfs(self, filters):
        if filters:
            if isinstance(filters, (OrderedDict, dict)):
                if "and" in filters:
                    self.validate_no_custom_field_cdfs(filters["and"])
                elif "or" in filters:
                    self.validate_no_custom_field_cdfs(filters["or"])
                else:
                    cdf = filters.get("cdf")
                    if cdf and cdf.get("type") == Cdf.CustomField.value:
                        raise InvalidUsage.bad_request(
                            "Custom Field CDFs are not allowed on Classic Reports"
                        )
            else:
                for filter in filters:
                    self.validate_no_custom_field_cdfs(filter)
