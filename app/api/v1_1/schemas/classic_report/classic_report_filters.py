from marshmallow import Schema, fields


class FilterValuesSchema(Schema):
    class Meta:
        ordered = True

    filter_values = fields.List(fields.String())


class MappingSchema(Schema):
    class Meta:
        ordered = True

    key = fields.String()
    values = fields.List(fields.String())


class MappingsSchema(Schema):
    class Meta:
        ordered = True

    mappings = fields.List(fields.Nested(MappingSchema))
