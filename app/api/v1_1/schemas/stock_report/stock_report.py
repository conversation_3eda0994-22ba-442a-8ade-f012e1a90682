from typing import List

from flask import g, request

from marshmallow import (
    Schema,
    fields,
    missing,
    post_load,
    pre_dump,
    pre_load,
    validate,
    validates,
    validates_schema,
)

from webargs.fields import DelimitedList

from app.api.v1_1.schemas.custom_cdf import CustomCdfSchema
from app.api.v1_1.schemas.pagination import PaginationSchema
from app.api.v1_1.schemas.report import (
    QueryFormatParamsSchema,
    QueryReportExportSchema,
    ReportBaseSchema,
    ReportDataSchema,
    ReportExportSchema,
    ReportSummarySchema,
)
from app.api.v1_1.schemas.stock_report.stock_report_custom_cdf import (
    StockReportCustomCdfSchema,
    StockReportRevisionCustomCdfSchema,
)
from app.api.v1_1.validations.reports import (
    validate_columns,
    validate_custom_cdfs,
    validate_dataset_id_not_read_only,
    validate_dataset_requirements,
    validate_duplicated_cdfs_in_column_and_groups,
    validate_filters,
    validate_groups,
    validate_no_custom_field_cdfs,
    validate_settings,
    validate_sort,
)
from app.common.constants.clone_fields import OPTIONAL_CLONE_FIELDS
from app.common.constants.country_codes import COUNTRY_CODES
from app.common.enums.features import BillingPortalFeatures
from app.common.enums.property_types import PropertyTypes
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.enums.report import ReportKind
from app.models.report import Report
from app.models.stock_report import StockReport
from app.schemas.datetime import CustomDateTimeUtc
from app.schemas.user import UserSchema
from app.services.property_service import PropertyService


class StockReportRulesSchema(Schema):
    feature_ids = fields.List(
        fields.String(
            required=True,
            validate=validate.OneOf(
                [feature.value for feature in BillingPortalFeatures]
            ),
            metadata={"description": "Feature ID", "example": "1"},
        ),
        required=False,
        allow_none=True,
        validate=validate.Length(min=1),
        metadata={
            "description": "Stock Report Feature ids, one to many relationship to associate a stock report with all properties with any number of Feature."
        },
    )

    property_ids = fields.List(
        fields.Integer(
            as_string=True,
            required=True,
            metadata={"description": "Property ID", "example": "1"},
        ),
        required=False,
        allow_none=True,
        validate=validate.Length(min=1),
        metadata={
            "description": "Stock Report Property ids, one to many relationship to associate a stock report with any number of property ID."
        },
    )

    country_codes = fields.List(
        fields.String(
            required=True,
            validate=validate.OneOf(
                [COUNTRY_CODES[country] for country in COUNTRY_CODES]
            ),
            metadata={"description": "Country Code", "example": "US"},
        ),
        required=False,
        allow_none=True,
        validate=validate.Length(min=1),
        metadata={
            "description": "Stock Report Country Codes, one to many relationship to associate a stock report with any number of country codes."
        },
    )
    property_types = fields.List(
        fields.String(
            required=True,
            validate=validate.OneOf(
                [property_type.name for property_type in PropertyTypes]
            ),
        ),
        required=False,
        allow_none=True,
        validate=validate.Length(min=1),
        metadata={"description": "Property Type", "example": "hotel"},
    )

    @validates("feature_ids")
    def no_duplicate_feature_ids(self, value):
        if value and len(value) != len(set(value)):
            raise InvalidUsage.bad_request(
                "Feature IDs must not contain duplicate items"
            )

    @validates("property_ids")
    def no_duplicate_property_ids(self, value):
        if value and len(value) != len(set(value)):
            raise InvalidUsage.bad_request(
                "Property IDs must not contain duplicate items"
            )

    @validates("country_codes")
    def no_duplicate_country_codes(self, value):
        if value and len(value) != len(set(value)):
            raise InvalidUsage.bad_request(
                "Country Codes must not contain duplicate items"
            )

    @validates("property_types")
    def no_duplicate_property_types(self, value):
        if value and len(value) != len(set(value)):
            raise InvalidUsage.bad_request(
                "Property Types must not contain duplicate items"
            )


class StockReportSchema(ReportBaseSchema):
    class Meta:
        ordered = True

    property_ids = fields.List(
        fields.Integer(
            as_string=True,
            required=True,
            allow_none=False,
            metadata={"description": "Property id", "example": "1"},
        ),
        validate=validate.Length(min=1),
        required=False,
        metadata={"description": "Report property ids"},
    )

    user_id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "User id", "example": "1"},
    )
    custom_cdfs = fields.Nested(
        StockReportCustomCdfSchema,
        many=True,
        dump_default=None,
        allow_none=True,
        metadata={"description": "Report custom cdfs"},
    )

    published = fields.Boolean(
        required=False,
        metadata={"description": "Published status of the report", "example": True},
    )

    rules = fields.Nested(StockReportRulesSchema)

    created_by = fields.Nested(
        UserSchema,
        dump_only=True,
        metadata={"description": "Report created by"},
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        validate_duplicated_cdfs_in_column_and_groups(data)
        validate_custom_cdfs(data, ReportKind.StockReport)
        validate_settings(data)
        validate_columns(data)
        validate_groups(data, ReportKind.StockReport)
        validate_filters(data)
        validate_sort(data)
        validate_dataset_id_not_read_only(data)
        validate_dataset_requirements(data)
        validate_no_custom_field_cdfs(data)

    @pre_dump
    def set_rules(self, data, **kwargs):
        data.rules = {
            "property_ids": (
                [property.property_id for property in data.properties]
                if data.properties
                else None
            ),
            "feature_ids": (
                [feature.feature_id for feature in data.features]
                if data.features
                else None
            ),
            "country_codes": (
                [country.country_code for country in data.countries]
                if data.countries
                else None
            ),
            "property_types": (
                [property_type.property_type for property_type in data.property_types]
                if data.property_types
                else None
            ),
        }
        return data

    @pre_load
    def set_context(self, data, **kwargs):
        self.context["dataset_id"] = self.context.get(
            "dataset_id",
            data.get(
                "dataset_id",
                StockReport.get_by_id(
                    g.get("report_id", request.view_args.get("stock_report_id"))
                ).dataset_id,
            ),
        )
        return data


class StockReportPublishSchema(Schema):
    class Meta:
        ordered = True

    report_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Report Id", "example": "1"},
    )

    rules = fields.Nested(StockReportRulesSchema)

    @validates_schema
    def validate_publish_schema(self, data, **kwargs):
        data = dict(dataset_id=Report.get_by_id(data["report_id"]).dataset_id)
        validate_dataset_id_not_read_only(data)


class StockReportImportCustomCdfSchema(CustomCdfSchema):
    id = fields.String(allow_none=True, required=False)
    created_at = fields.String(allow_none=True, required=False)
    updated_at = fields.String(allow_none=True, required=False)
    column = fields.String(allow_none=True, required=False)


class StockReportImportSchema(ReportBaseSchema):
    class Meta:
        ordered = True

    published = fields.Boolean(
        required=True,
        metadata={"description": "Published status of the report", "example": True},
    )
    custom_cdfs = fields.Nested(
        StockReportImportCustomCdfSchema,
        many=True,
        dump_default=None,
        allow_none=True,
        metadata={"description": "Report custom cdfs"},
    )
    rules = fields.Nested(StockReportRulesSchema)

    user_id = fields.Integer(
        as_string=True,
        required=True,
        metadata={"description": "User id", "example": "1"},
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        validate_duplicated_cdfs_in_column_and_groups(data)
        validate_settings(data)
        validate_columns(data)
        validate_groups(data)
        validate_filters(data)
        validate_sort(data)
        validate_dataset_requirements(data)


class StockReportInsertSchema(StockReportImportSchema):
    id = fields.Integer(
        as_string=True,
        dump_only=False,
        required=True,
        metadata={"description": "Stock Report id", "example": "1"},
    )


class StockReportPreviewSchema(StockReportSchema):
    class Meta:
        fields = (
            "id",
            "title",
            "description",
            "dataset_id",
            "rules",
            "updated_at",
            "published",
            "custom_cdfs",
            "folder_id",
            "tags",
            "created_by",
        )


class StockReportsSchema(PaginationSchema):
    class Meta:
        ordered = True

    stock_reports = fields.Nested(StockReportPreviewSchema, many=True)


class StockReportExploreSchema(StockReportImportSchema):
    class Meta:
        fields = (
            "id",
            "title",
            "description",
            "dataset_id",
            "score",
            "argument",
        )

    score = fields.Integer(
        required=True,
        validate=validate.Range(min=1),
        metadata={"description": "Score ..."},
    )

    argument = fields.String(
        required=True,
        metadata={"description": "Reason ..."},
    )


class StockReportsExploreSchema(Schema):
    class Meta:
        ordered = True

    stock_reports = fields.Nested(StockReportExploreSchema, many=True)


class StockReportsSearchSchema(PaginationSchema):
    class Meta:
        ordered = True

    items = fields.Nested(StockReportPreviewSchema, many=True)


class StockReportDataSchema(ReportDataSchema):
    pass


class StockReportSummarySchema(ReportSummarySchema):
    pass


class StockReportExportSchema(ReportExportSchema):
    pass


class PathStockReportIdSchema(Schema):
    stock_report_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Stock report id of the resource"},
    )

    @post_load
    def set_report_id(self, data, **kwargs):
        g.report_id = (
            data["stock_report_id"]
            if data is not None
            else self.context["stock_report_id"]
        )
        g.report_kind = ReportKind.StockReport


class PathStockReportIdCustomCdfIdSchema(PathStockReportIdSchema):
    custom_cdf_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Custom cdf id of the resource"},
    )

    @post_load
    def set_custom_cdf_id_and_context(self, data, **kwargs):
        self.context["stock_report_id"] = data["stock_report_id"]
        g.custom_cdf_id = data["custom_cdf_id"]


class PathStockReportRevisionIdSchema(PathStockReportIdSchema):
    revision_id = fields.Integer(
        required=True,
        validate=validate.Range(min=1),
        metadata={"description": "Revision id of the stock report"},
    )


class QueryStockReportIdSchema(QueryFormatParamsSchema):
    property_ids = DelimitedList(
        fields.Integer(
            as_string=True,
            required=True,
            validate=lambda x: int(x) > 0
            or InvalidUsage.bad_request("Value should be greater than 0."),
            metadata={"description": "Property id", "example": "1"},
        ),
        validate=validate.Length(min=1),
        required=True,
        metadata={"description": "Stock report property ids"},
    )

    @validates("property_ids")
    def validate_property_id(self, property_ids: List[int]):
        PropertyService.are_valid_property_ids(property_ids)


class QueryStockReportExportSchema(QueryReportExportSchema, QueryStockReportIdSchema):
    pass


class StockReportFeatureSchema(Schema):
    class Meta:
        ordered = True

    stock_report_id = fields.Integer(
        as_string=True, metadata={"description": "Stock Report ID", "example": "1"}
    )
    feature_id = fields.String(
        metadata={"description": "Feature ID", "example": "report_builder"}
    )


class StockReportPropertySchema(Schema):
    class Meta:
        ordered = True

    stock_report_id = fields.Integer(
        as_string=True, metadata={"description": "Stock Report ID", "example": "1"}
    )
    property_id = fields.Integer(
        as_string=True, metadata={"description": "Property ID", "example": "1"}
    )


class StockReportCountrySchema(Schema):
    class Meta:
        ordered = True

    stock_report_id = fields.Integer(
        as_string=True, metadata={"description": "Stock Report ID", "example": "1"}
    )
    country_code = fields.String(
        metadata={"description": "Country Code", "example": "TH"}
    )


class StockReportPropertyTypeSchema(Schema):
    stock_report_id = fields.Integer(
        as_string=True, metadata={"description": "Stock Report ID", "example": "1"}
    )
    property_type = fields.String(
        metadata={"description": "Property Type", "example": "hotel"}
    )


class RuleSchema(Schema):
    id = fields.String(metadata={"description": "Rule key", "example": "feature_ids"})
    name = fields.String(metadata={"description": "Rule Label", "example": "Products"})
    kind = fields.String(metadata={"description": "Rule Kind", "example": "PickList"})


class SupportedRulesSchema(Schema):
    class Meta:
        ordered = True

    rules = fields.Nested(RuleSchema, many=True)


class FeatureIdRulesSchema(Schema):
    class Meta:
        ordered = True

    id = fields.String(
        metadata={"description": "Feature ID", "example": "report_builder"}
    )
    name = fields.String(metadata={"description": "Feature Name", "example": "Thai"})


class PropertyIdRulesSchema(Schema):
    pass


class CountryCodeRulesSchema(Schema):
    class Meta:
        ordered = True

    id = fields.String(metadata={"description": "Country Code", "example": "TH"})
    name = fields.String(
        metadata={"description": "Feature Name", "example": "Thailand"}
    )


class StockReportRevisionSchema(StockReportSchema):
    class Meta:
        exclude = ("id",)

    stock_report_id = fields.Integer(
        as_string=True, metadata={"description": "Stock Report ID", "example": "1"}
    )
    revision_id = fields.Integer(
        metadata={"description": "Revision ID", "example": "1"}
    )
    rules = fields.Nested(StockReportRulesSchema)
    custom_cdfs = fields.Nested(
        StockReportRevisionCustomCdfSchema,
        many=True,
        dump_default=None,
        allow_none=True,
        metadata={"description": "Report custom cdfs"},
    )


class StockReportRevisionResponseSchema(StockReportRevisionSchema):
    class Meta:
        exclude = ()

    @pre_dump
    def set_rules(self, data, **kwargs):
        return data


class StockReportRevisionsSchema(Schema):
    class Meta:
        ordered = True

    stock_report_id = fields.Integer(
        as_string=True, metadata={"description": "Stock Report ID", "example": "1"}
    )
    revision_id = fields.Integer(
        metadata={"description": "Revision ID", "example": "1"}
    )
    user_id = fields.Integer(
        as_string=True, metadata={"description": "User ID", "example": "1"}
    )
    updated_at = CustomDateTimeUtc(
        dump_only=True,
        description="Updated At Time",
    )


class StockReportCloneSchema(StockReportSchema):
    class Meta:
        ordered = True
        exclude = ("dataset_id",)

    @pre_load
    def set_dataset_id(self, data, **kwargs):
        report_id = g.get("report_id", request.view_args.get("stock_report_id"))
        self.context["dataset_id"] = (
            StockReport.get_by_id(report_id).dataset_id
            if "dataset_id" not in self.context
            else self.context["dataset_id"]
        )
        logger.debug(
            f"Set dataset ID for clone stock report {report_id}",
            extra=dict(
                context=self.context,
                propery_id=g.property_id,
                request=request.view_args,
            ),
        )

        return data

    @pre_load
    def make_optional(self, data, **kwargs):
        # Make the fields optional by removing the required attribute
        for field in self.fields:
            if field in OPTIONAL_CLONE_FIELDS:
                self.fields[field].required = False
                self.fields[field].load_default = missing
                self.fields[field].dump_default = missing
        return data

    @validates_schema
    def validates_schema(self, data, **kwargs):
        validate_dataset_id_not_read_only(data)


class PathStockReportIdTagIdSchema(PathStockReportIdSchema):
    stock_tag_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Stock Tag Id of the resource"},
    )
