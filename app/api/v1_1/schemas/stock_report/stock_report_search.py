from flask import g

from marshmallow import (
    Schema,
    fields,
    validate,
    validates,
    validates_schema,
)

from webargs.fields import DelimitedList

from app.api.v1_1.schemas.stock_report import StockReportSchema
from app.common.enums.permissions import PermissionAction, Resources
from app.common.enums.search import ReportSearchColumns
from app.common.exceptions import InvalidUsage
from app.enums import Dataset, Sort
from app.services.permission_service import PermissionService


class QueryStockReportFilterSchema(Schema):
    ids = DelimitedList(
        fields.Integer(
            as_string=True,
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Filter by Report IDs"},
    )
    title = fields.String(
        validate=validate.Length(min=1, max=100),
        metadata={
            "description": "Filter by Stock Report title",
            "example": "Stock Report Title",
        },
    )
    description = fields.String(
        validate=validate.Length(max=200),
        metadata={
            "description": "Filter by Report description",
            "example": "Report description",
        },
        allow_none=True,
    )
    dataset_ids = DelimitedList(
        fields.Integer(validate=validate.OneOf([kind.value for kind in Dataset])),
        validate=validate.Length(min=1),
        metadata={"description": "Dataset IDs"},
    )

    include_cloudbeds = fields.Bool(
        load_default=False,
        metadata={
            "description": "Flag to determine if response should include all cloudbeds reports, regardless of property access.  "
            "Restricted by permission.  Bad request exception if used with property_country_only.",
            "example": False,
        },
    )

    property_country_only = fields.Bool(
        load_default=False,
        metadata={
            "description": "Flag to determine if response should only include stock reports that are specified to the property country, "
            "stock reports without a designated country will not be included. Bad request exception if used with include_cloudbeds.",
            "example": False,
        },
    )
    folder_ids = DelimitedList(
        fields.Integer(
            validate=lambda x: int(x) >= 0
            or InvalidUsage.bad_request("Should be greater or equal than zero."),
            as_string=True,
        ),
        validate=validate.Length(min=1),
        as_string=True,
        metadata={"description": "Filter by Folder IDs"},
    )

    stock_tag_ids = DelimitedList(
        fields.Integer(
            validate=lambda x: int(x) >= 0
            or InvalidUsage.bad_request("Should be greater or equal than zero."),
            as_string=True,
        ),
        validate=validate.Length(min=1),
        as_string=True,
        metadata={"description": "Filter by Stock Tag IDs"},
    )

    @validates("include_cloudbeds")
    def validate_include_cloudbeds_for_privileged_user(self, value):
        if value:
            # TODO: Come back here later to refactor this based on permission service
            access = PermissionService.is_whitelisted(g.user.email)

            if not access:
                raise InvalidUsage.forbidden(
                    f"User does not have permission to {PermissionAction.VIEW.value} {Resources.StockReport.value}"
                )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        if data.get("property_country_only") and data.get("include_cloudbeds"):
            raise InvalidUsage.bad_request(
                "Cannot set include_cloudbeds and property_country_only both to True"
            )


class QuerySortStockReportSchema(Schema):
    sort = fields.String(
        metadata={"description": "Sort stock_reports"},
        load_default="title,asc",
        validate=validate.OneOf(
            [
                f"{field},{sort.name}"
                for field in StockReportSchema().fields.keys()
                if field in ("id", "title", "description", "dataset_id", "updated_at")
                for sort in Sort
            ]
        ),
    )


class SearchQueryStockReportFilterSchema(QueryStockReportFilterSchema):
    class Meta:
        ordered = True

    search_term = fields.String(
        metadata={
            "description": "A text string to search for in any of the search column fields",
            "example": "in-house",
        }
    )
    search_columns = DelimitedList(
        fields.String(
            validate=validate.OneOf(
                [
                    report_search_column.value
                    for report_search_column in ReportSearchColumns
                ]
            )
        )
    )
    published = fields.Boolean(
        metadata={"description": "Filter by published status"},
        required=False,
        allow_none=True,
    )


class QueryStockReportExploreSchema(Schema):
    question = fields.String(
        required=True,
        metadata={
            "description": "A text string to question on the report using NLP",
            "example": "Suggest all the reports where I can see my financial performance for the last quarter",
        },
    )
