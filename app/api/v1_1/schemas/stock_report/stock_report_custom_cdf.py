from flask import g, request

from marshmallow import fields, pre_load, validates_schema

from app.api.v1_1.schemas.custom_cdf import CustomCdfSchema
from app.enums.report import ReportKind
from app.models.stock_report import StockReport


class StockReportCustomCdfSchema(CustomCdfSchema):
    class Meta:
        ordered = True

    @pre_load
    def pre_load(self, data, **kwargs):
        report_id = g.get("report_id", request.view_args.get("stock_report_id"))
        self.context["dataset_id"] = (
            StockReport.get_by_id(report_id).dataset_id
            if "dataset_id" not in self.context
            else self.context["dataset_id"]
        )
        return data

    @validates_schema
    def validates_schema(self, data, **kwargs):
        self.validate_schema(data, ReportKind.StockReport)


class StockReportCustomCdfValidateSchema(StockReportCustomCdfSchema):
    class Meta:
        ordered = True
        fields = ("name", "formula", "kind")


class StockReportRevisionCustomCdfSchema(CustomCdfSchema):
    class Meta:
        ordered = True

    created_at = fields.String()
    updated_at = fields.String()
