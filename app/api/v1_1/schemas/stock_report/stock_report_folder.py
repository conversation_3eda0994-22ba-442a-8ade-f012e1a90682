from flask_babel import lazy_gettext as _

from marshmallow import Schema, fields, pre_dump

from app.api.v1_1.schemas.folder import PathFolderIdSchema
from app.api.v1_1.schemas.pagination import PaginationSchema


class StockReportFolderSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        dump_only=True,
        required=True,
        metadata={"description": "Folder Id", "example": "1"},
        as_string=True,
    )
    name = fields.String(
        required=True,
        metadata={"description": "Folder Name", "example": "My Reports"},
    )

    @pre_dump
    def translate_name(self, data, **kwargs):
        return dict(id=data.id, name=_(data.name))


class StockReportFoldersSchema(PaginationSchema):
    class Meta:
        ordered = True

    folders = fields.Nested(StockReportFolderSchema, many=True)


class PathStockReportsFolderIdSchema(Schema):
    class Meta:
        ordered = True

    folder_id = fields.Integer(
        required=True,
        allow_none=False,
        metadata={"description": "Folder Id", "example": "1"},
        as_string=True,
    )


class PathFolderIdStockReportIdSchema(PathFolderIdSchema):
    class Meta:
        ordered = True

    stock_report_id = fields.Integer(
        required=True,
        allow_none=False,
        metadata={"description": "Stock Report Id", "example": "1"},
    )


class StockReportIdSchema(Schema):
    class Meta:
        ordered = True

    stock_report_id = fields.Integer(
        required=True,
        allow_none=False,
        metadata={"description": "Stock Report Id", "example": "1"},
    )


class PathFolderIdAndStockReportIdSchema(
    PathStockReportsFolderIdSchema, StockReportIdSchema
):
    pass
