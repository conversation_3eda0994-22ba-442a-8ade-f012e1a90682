from typing import List

from flask import g

from marshmallow import (
    Schema,
    fields,
    post_load,
    pre_load,
    validate,
    validates,
    validates_schema,
)

from app.api.v1_1.schemas.report import (
    QueryFormatParamsSchema,
    ReportColumnsSchema,
    ReportFormatsSchema,
    ReportGroupSchema,
    ReportSettingsSchema,
    ReportSortSchema,
)
from app.api.v1_1.schemas.report.report_filters import (
    FILTER_EXAMPLE,
    ReportComparisonSchema,
    ReportFiltersSchema,
    ReportPeriodSchema,
)
from app.api.v1_1.schemas.stock_report.stock_report_custom_cdf import (
    StockReportCustomCdfSchema,
)
from app.api.v1_1.validations.reports import (
    validate_comparison_names,
    validate_period_names,
    validate_period_summary_sorts,
)
from app.common.constants.limits import MAX_REPORT_COMPARISONS
from app.common.exceptions import InvalidUsage
from app.enums.dataset import Dataset
from app.enums.mode import Mode
from app.models.stock_report import StockReport
from app.services.property_service import PropertyService


class StockReportQuerySchema(Schema):
    class Meta:
        ordered = True

    property_ids = fields.List(
        fields.Integer(
            as_string=True,
            required=True,
            allow_none=False,
            metadata={"description": "Property id", "example": "1"},
        ),
        validate=validate.Length(min=1),
        required=True,
        metadata={"description": "Report property ids"},
    )
    columns = fields.Nested(
        ReportColumnsSchema,
        many=True,
        allow_none=False,
        required=False,
        validate=validate.Length(min=1),
        metadata={"description": "Report columns"},
    )
    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=3),
        allow_none=True,
        metadata={"description": "Report group rows"},
    )
    group_columns = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=2),
        allow_none=True,
        metadata={"description": "Report group columns"},
    )
    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report filters", "example": FILTER_EXAMPLE},
    )
    sort = fields.Nested(
        ReportSortSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report sort"},
    )
    settings = fields.Nested(
        ReportSettingsSchema,
        required=False,
        dump_default=None,
        allow_none=True,
        metadata={"description": "Report settings"},
    )
    periods = fields.Nested(
        ReportPeriodSchema,
        many=True,
        dump_default=None,
        load_default=None,
        metadata={"description": "Periods to compare"},
    )
    custom_cdfs = fields.Nested(
        StockReportCustomCdfSchema,
        many=True,
        dump_default=None,
        allow_none=True,
        metadata={"description": "Report custom cdfs"},
    )
    formats = fields.Nested(
        ReportFormatsSchema,
        required=False,
        allow_none=True,
        metadata=dict(description="Formatting options of the report"),
    )
    comparisons = fields.Nested(
        ReportComparisonSchema,
        many=True,
        dump_default=None,
        load_default=None,
        validate=validate.Length(max=MAX_REPORT_COMPARISONS),
        metadata={
            "description": "A list of up to 5 Comparisons, each containing 'name' and 'filters' properties. The 'filters' are the same structure as those in report.filters."
        },
    )

    @validates("periods")
    def validate_periods_have_different_names(self, periods):
        validate_period_names(periods)

    @validates("comparisons")
    def validate_comparisons_have_different_names(self, comparisons):
        validate_comparison_names(comparisons)

    @validates_schema
    def validate_period_sorts(self, data, **kwargs):
        validate_period_summary_sorts(data)

    @validates("property_ids")
    def validate_property_id(self, property_ids: List[int]):
        PropertyService.are_valid_property_ids(property_ids)

    @pre_load
    def set_context(self, data, **_):
        self.context["dataset_id"] = self.context.get(
            "dataset_id", data.get("dataset_id")
        )
        if not self.context["dataset_id"] and hasattr(g, "report_id"):
            stock_report = StockReport.get_by_id(g.report_id)
            if not stock_report:
                return InvalidUsage.not_found(
                    message=f"A Stock report with id: {g.report_id} does not exist"
                )
            self.context["dataset_id"] = stock_report.dataset_id
        return data


class QueryStockReportQueryParamsSchema(QueryFormatParamsSchema):
    class Meta:
        ordered = True

    mode = fields.String(
        validate=validate.OneOf([Mode.Preview.name, Mode.Run.name]),
        required=True,
        metadata={
            "description": "Select the mode to limit the amount of records. Preview has a limit of 100 records, and run has a limit 12,000 records",
            "example": Mode.Preview.name,
        },
    )

    @post_load
    def load_mode(self, data, **kwargs):
        if "mode" in data:
            data["mode"] = Mode[data["mode"]]

        return data


class StockReportQueryExportSchema(StockReportQuerySchema):
    title = fields.String(
        required=True,
        validate=validate.Length(min=1, max=100),
        metadata={
            "description": "Report title",
            "example": "Report title",
        },
    )
    dataset_id = fields.Integer(
        required=True,
        validate=validate.OneOf([kind.value for kind in Dataset]),
        metadata={
            "description": "Dataset id",
            "example": 1,
        },
    )
    columns = fields.Nested(
        ReportColumnsSchema,
        many=True,
        allow_none=False,
        required=True,
        validate=validate.Length(min=1),
        metadata={"description": "Report columns"},
    )
