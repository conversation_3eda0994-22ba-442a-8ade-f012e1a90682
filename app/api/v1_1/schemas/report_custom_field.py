import re

from marshmallow import Schema, fields, validate
from marshmallow.decorators import post_load

from app.api.v1_1.schemas.custom_fields import CustomFieldCDFPropertySchema
from app.enums.cdf_kind import CdfKind


class ReportCustomFieldCdfSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        metadata={"description": "Report custom field cdf id", "example": "1"},
    )
    column = fields.String(
        dump_only=True,
        required=False,
        metadata={
            "description": "Report custom field cdf column",
            "example": "full_address",
        },
    )
    name = fields.String(
        required=True,
        validate=validate.Length(min=1),
        metadata={
            "description": "Report custom field cdf name",
            "example": "Full address",
        },
    )
    properties = fields.Nested(
        CustomFieldCDFPropertySchema,
        many=True,
        required=True,
        validate=validate.Length(min=1),
        metadata={"description": "List of properties"},
    )
    kind = fields.Constant(CdfKind.String.name, dump_only=True)

    def name_to_column(self, name):
        return "custom_{}".format(
            re.sub(r"\W", "_", re.sub(r"^\W+|\W+$", "", name))
        ).lower()

    @post_load
    def post_load(self, data, **kwargs):
        data["column"] = self.name_to_column(data["name"])
        return data
