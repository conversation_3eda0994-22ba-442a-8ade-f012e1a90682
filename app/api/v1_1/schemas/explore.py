from marshmallow import Schema, fields


class QuestionExploreSchema(Schema):
    question = fields.String(
        required=True,
        metadata={
            "description": "A text string to question on the report using NLP",
            "example": "I want to see data about my debits and credits grouped by transaction type",
        },
    )


class QueryReportExploreSchema(QuestionExploreSchema):
    context_id = fields.String(
        required=False,
        load_default=None,
        metadata={
            "description": "A unique identifier for the context",
        },
    )
