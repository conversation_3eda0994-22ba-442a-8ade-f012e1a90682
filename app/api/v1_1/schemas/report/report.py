from flask import g

from marshmallow import (
    Schema,
    fields,
    missing,
    pre_load,
    validate,
    validates_schema,
)
from marshmallow.decorators import post_dump, post_load, validates

from app.api.v1_1.schemas.pagination import PaginationSchema
from app.api.v1_1.schemas.property import PropertySchema
from app.api.v1_1.schemas.report.report_cdf import ReportCDFSchema
from app.api.v1_1.schemas.report.report_custom_cdf import ReportCustomCdfSchema
from app.api.v1_1.schemas.report.report_filters import (
    FILTER_EXAMPLE,
    ReportComparisonSchema,
    ReportFiltersSchema,
    ReportPeriodSchema,
)
from app.api.v1_1.schemas.report.report_formats import ReportFormatsSchema
from app.api.v1_1.schemas.report_custom_field import ReportCustomFieldCdfSchema
from app.api.v1_1.schemas.tag import TagSchema
from app.api.v1_1.validations.reports import (
    validate_columns,
    validate_comparison_names,
    validate_custom_cdfs,
    validate_custom_cdfs_query,
    validate_custom_field_cdfs,
    validate_custom_field_cdfs_in_custom_cdfs_exist,
    validate_dataset_id_not_read_only,
    validate_dataset_requirements,
    validate_duplicated_cdfs_in_column_and_groups,
    validate_feature_cdfs,
    validate_filters,
    validate_groups,
    validate_no_custom_cdfs_custom_field_cdfs_duplicates,
    validate_page_mode,
    validate_period_names,
    validate_period_summary_sorts,
    validate_property_id,
    validate_property_ids,
    validate_settings,
    validate_sort,
)
from app.common.constants.clone_fields import OPTIONAL_CLONE_FIELDS
from app.common.constants.limits import (
    MAX_GROUP_COLUMNS,
    MAX_GROUP_ROWS,
    MAX_REPORT_COMPARISONS,
    MAX_REPORT_PERIODS,
)
from app.common.exceptions import InvalidUsage
from app.datasets.financial import FinancialView
from app.enums import (
    Dataset,
    Format,
    GroupModifier,
    Metric,
    Mode,
    NumericFormat,
    ReportView,
    Sort,
    View,
)
from app.enums.cdf import Cdf
from app.enums.pagination import Limit, Offset
from app.enums.report import ReportKind
from app.models.report import Report
from app.reports.report_type import ReportType
from app.schemas.datetime import CustomDateTimeUtc
from app.schemas.user import UserSchema


class ReportColumnsSchema(Schema):
    class Meta:
        ordered = True

    cdf = fields.Nested(
        ReportCDFSchema,
        required=True,
        metadata={
            "example": dict(
                type=Cdf.Default.value, column=FinancialView.credit_amount.key
            )
        },
    )
    metrics = fields.List(
        fields.String(
            validate=validate.OneOf([metric.value for metric in Metric]),
            metadata={"example": Metric.sum.value},
        ),
        required=False,
        validate=validate.Length(min=1),
        metadata={"description": "Report column metrics"},
    )

    @validates("metrics")
    def validate_metrics(self, metrics):
        if len(metrics) != len(set(metrics)):
            raise InvalidUsage.bad_request(
                "Metrics must not contain duplicate elements"
            )


class ReportGroupSchema(Schema):
    class Meta:
        ordered = True

    cdf = fields.Nested(ReportCDFSchema, required=True)
    modifier = fields.String(
        required=False,
        validate=validate.OneOf([group.value for group in GroupModifier]),
        metadata={"description": "Report group modifier"},
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        if data["cdf"]["type"] == Cdf.Custom.value and "modifier" in data:
            raise InvalidUsage.bad_request("Custom CDF's does not support modifier")


class ReportSortSchema(Schema):
    class Meta:
        ordered = True

    cdf = fields.Nested(ReportCDFSchema, required=True)
    direction = fields.String(
        required=True,
        validate=validate.OneOf([direction.value for direction in Sort]),
        metadata={"example": Sort.asc.value, "description": "Report sort direction"},
    )


class ReportSettingsSchema(Schema):
    class Meta:
        ordered = True

    details = fields.Boolean(
        required=True,
        truthy={True},
        falsy={False},
        metadata={
            "description": "Report details",
            "example": False,
        },
    )
    totals = fields.Boolean(
        required=True,
        truthy={True},
        falsy={False},
        metadata={
            "description": "Report totals",
            "example": False,
        },
    )
    transpose = fields.Boolean(
        dump_default=False,
        required=False,
        truthy={True},
        falsy={False},
        metadata={
            "description": "Transpose Report",
            "example": False,
        },
    )

    @validates_schema
    def validate_settings(self, data, **kwargs):
        if data["details"] and data.get("transpose"):
            raise InvalidUsage.bad_request(
                "Transpose not supported for reports with details"
            )


class ReportBaseSchema(Schema):
    id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Report id", "example": "1"},
    )
    title = fields.String(
        required=True,
        validate=validate.Length(min=1, max=100),
        metadata={
            "description": "Report title",
            "example": "Report title",
        },
    )
    description = fields.String(
        required=False,
        validate=validate.Length(max=200),
        allow_none=True,
        metadata={
            "description": "Report description",
            "example": "Report description",
        },
    )
    dataset_id = fields.Integer(
        required=True,
        validate=validate.OneOf([kind.value for kind in Dataset]),
        metadata={
            "description": "Dataset id",
            "example": 1,
        },
    )
    columns = fields.Nested(
        ReportColumnsSchema,
        many=True,
        required=True,
        validate=validate.Length(min=1),
        metadata={"description": "Report columns"},
    )
    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=MAX_GROUP_ROWS),
        dump_default=None,
        load_default=None,
        allow_none=True,
        metadata={"description": "Report group rows"},
    )
    group_columns = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=MAX_GROUP_COLUMNS),
        dump_default=None,
        load_default=None,
        allow_none=True,
        metadata={"description": "Report group columns"},
    )
    periods = fields.Nested(
        ReportPeriodSchema,
        many=True,
        dump_default=None,
        load_default=None,
        validate=validate.Length(max=MAX_REPORT_PERIODS),
        metadata={"description": "Periods to compare"},
    )
    comparisons = fields.Nested(
        ReportComparisonSchema,
        many=True,
        dump_default=None,
        load_default=None,
        validate=validate.Length(max=MAX_REPORT_COMPARISONS),
        metadata={
            "description": "A list of up to 5 Comparisons, each containing 'name' and 'filters' properties. The 'filters' are the same structure as those in report.filters."
        },
    )
    filters = fields.Nested(
        ReportFiltersSchema,
        required=False,
        dump_default=None,
        load_default=None,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={
            "description": "Report filters",
            "example": FILTER_EXAMPLE,
        },
    )
    sort = fields.Nested(
        ReportSortSchema,
        many=True,
        required=False,
        dump_default=None,
        load_default=None,
        validate=validate.Length(min=1),
        allow_none=True,
        metadata={"description": "Report sort"},
    )
    settings = fields.Nested(
        ReportSettingsSchema,
        required=False,
        dump_default=None,
        allow_none=True,
        metadata={"description": "Report settings"},
    )
    formats = fields.Nested(
        ReportFormatsSchema,
        required=False,
        allow_none=True,
        metadata=dict(description="Formatting options of the report"),
    )
    type = fields.String(
        required=True,
        dump_only=True,
        validate=validate.OneOf([view.name for view in ReportView]),
        metadata={"example": ReportView.List.name, "description": "Report type"},
    )
    folder_id = fields.Integer(
        required=True,
        dump_only=True,
        allow_none=True,
        as_string=True,
        metadata={"description": "Folder id", "example": "1"},
    )
    tags = fields.Nested(
        TagSchema,
        many=True,
        dump_only=True,
        required=True,
        metadata={"description": "Report tags"},
    )
    schedules = fields.List(
        fields.Pluck(
            "ScheduleSchema",
            "id",
            metadata={"description": "Schedule id", "example": "1"},
        ),
        data_key="schedule_ids",
        required=True,
        dump_only=True,
        metadata={"description": "Report schedule ids"},
    )
    updated_at = CustomDateTimeUtc(
        dump_only=True,
        description="Report updated at",
    )

    @post_load
    def ensure_grouped_reports_have_sort(self, data, **kwargs):
        if (
            data.get("group_rows")
            and data.get("settings", {}).get("details")
            and not data.get("periods")
            and not data.get("comparisons")
        ):
            user_sorts = (
                {sort["cdf"]["column"]: sort["direction"] for sort in data.get("sort")}
                if data.get("sort")
                else {}
            )
            fixed_sorts = [
                (
                    dict(
                        cdf=dict(
                            type=column["cdf"]["type"],
                            column=column["cdf"]["column"],
                        ),
                        direction=(
                            user_sorts.get(column["cdf"]["column"], Sort.asc.value)
                        ),
                    )
                    if column["cdf"].get("multi_level_id") is None
                    else dict(
                        cdf=dict(
                            type=column["cdf"]["type"],
                            column=column["cdf"]["column"],
                            multi_level_id=column["cdf"]["multi_level_id"],
                        ),
                        direction=(
                            user_sorts.get(column["cdf"]["column"], Sort.asc.value)
                        ),
                    )
                )
                for column in data.get("group_rows")
            ]

            column_sorts = (
                [
                    sort
                    for sort in data.get("sort")
                    if sort["cdf"]["column"]
                    not in [fs["cdf"]["column"] for fs in fixed_sorts]
                ]
                if data.get("sort")
                else []
            )
            fixed_sorts += column_sorts
            data["sort"] = fixed_sorts
        return data

    @pre_load
    def set_context(self, data, **kwargs):
        self.context["dataset_id"] = self.context.get(
            "dataset_id", data.get("dataset_id")
        )
        return data

    @post_dump
    def dump_settings(self, data, **kwargs):
        if "settings" in data and data["settings"] is None:
            data["settings"] = dict(totals=False, details=False, transpose=False)

        return data

    @validates("periods")
    def validate_periods_have_different_names(self, periods):
        validate_period_names(periods)

    @validates("comparisons")
    def validate_comparisons_have_different_names(self, comparisons):
        validate_comparison_names(comparisons)

    @validates_schema
    def validate_period_summary_group_rows_match_sorts(self, data, **kwargs):
        validate_period_summary_sorts(data)


class ReportSchema(ReportBaseSchema):
    class Meta:
        ordered = True

    property_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Report property id", "example": "1"},
    )
    property_ids = fields.List(
        fields.Integer(
            required=True,
            as_string=True,
            validate=lambda x: int(x) > 0
            or InvalidUsage.bad_request("Value should be greater than 0."),
            metadata={"description": "Property id", "example": "1"},
        ),
        validate=validate.Length(min=1),
        required=True,
        metadata={"description": "Report property ids"},
    )
    custom_cdfs = fields.Nested(
        ReportCustomCdfSchema,
        many=True,
        dump_default=[],
        dump_only=True,
        metadata={"description": "Report custom cdfs"},
    )
    custom_field_cdfs = fields.Nested(
        ReportCustomFieldCdfSchema,
        many=True,
    )
    created_by = fields.Nested(
        UserSchema, dump_only=True, metadata={"description": "Report created by"}
    )

    @validates("property_id")
    def validate_property_id(self, property_id):
        validate_property_id(property_id)

    @validates("property_ids")
    def validate_property_ids(self, property_ids):
        validate_property_ids(property_ids)

    @validates_schema
    def validates_schema(self, data, **kwargs):
        validate_settings(data)
        validate_feature_cdfs(data)
        validate_duplicated_cdfs_in_column_and_groups(data)
        validate_custom_cdfs(
            data,
            ReportKind.Report if not g.get("report_kind") else g.get("report_kind"),
        )
        validate_columns(data)
        validate_groups(
            data,
            ReportKind.Report if not g.get("report_kind") else g.get("report_kind"),
        )
        validate_filters(data)
        validate_sort(data)
        validate_dataset_id_not_read_only(data)
        validate_dataset_requirements(data)
        validate_custom_field_cdfs(data)
        validate_no_custom_cdfs_custom_field_cdfs_duplicates(data)
        validate_custom_field_cdfs_in_custom_cdfs_exist(data)


class ReportUpdateSchema(ReportSchema):
    class Meta:
        ordered = True
        exclude = ("custom_field_cdfs",)


class ReportCloneSchema(ReportSchema):
    class Meta:
        ordered = True
        exclude = ("dataset_id",)

    @pre_load
    def set_dataset_id(self, data, **kwargs):
        self.context["dataset_id"] = data.get(
            "dataset_id", Report.get_by_id(g.get("report_id")).dataset_id
        )
        return data

    @pre_load
    def make_optional(self, data, **kwargs):
        # Make the fields optional by removing the required attribute
        for field in self.fields:
            if field in OPTIONAL_CLONE_FIELDS:
                self.fields[field].required = False
                self.fields[field].load_default = missing
                self.fields[field].dump_default = missing
        return data

    @validates_schema
    def validate_dataset_not_read_only(self, data, **kwargs):
        validate_dataset_id_not_read_only(data)

    @validates_schema
    def validates_schema(self, data, **kwargs):
        pass


class ReportQuerySchema(ReportSchema):
    class Meta:
        ordered = True
        fields = (
            "property_ids",
            "dataset_id",
            "columns",
            "group_rows",
            "group_columns",
            "custom_cdfs",
            "filters",
            "sort",
            "settings",
            "periods",
            "formats",
            "comparisons",
            "custom_field_cdfs",
        )

    custom_cdfs = fields.Nested(
        ReportCustomCdfSchema,
        many=True,
        dump_default=None,
        load_default=None,
        allow_none=True,
        metadata={"description": "Report custom cdfs"},
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        validate_settings(data)
        validate_duplicated_cdfs_in_column_and_groups(data)
        validate_custom_cdfs_query(data)
        validate_columns(data)
        validate_groups(data)
        validate_filters(data)
        validate_sort(data)
        validate_dataset_requirements(data)
        validate_no_custom_cdfs_custom_field_cdfs_duplicates(data)
        validate_custom_field_cdfs(data)
        validate_custom_field_cdfs_in_custom_cdfs_exist(data)
        validate_page_mode(data)


class ReportQueryExportSchema(ReportQuerySchema):
    class Meta:
        ordered = True
        fields = (
            "title",
            "property_ids",
            "dataset_id",
            "columns",
            "group_rows",
            "group_columns",
            "custom_cdfs",
            "filters",
            "sort",
            "settings",
            "periods",
            "formats",
            "comparisons",
            "custom_field_cdfs",
        )


class ReportQuerySummarySchema(ReportSchema):
    class Meta:
        ordered = True
        fields = ("property_ids", "dataset_id", "columns", "filters", "formats")


class QueryFormatParamsSchema(Schema):
    class Meta:
        ordered = True

    format = fields.String(
        validate=validate.OneOf([format.value for format in NumericFormat]),
        load_default=NumericFormat.Raw.value,
        metadata={
            "description": 'Select the format of the numbers in the records. The "raw" values are sent unmodified from the database. '
            'The "formatted" records as are cast to a string. Currency type CDFs are defined in the property settings and other '
            "numbers are formatted with commas to separate every third digit and a decimal to separate the fractional portion.",
            "example": NumericFormat.Formatted.value,
        },
    )

    offset = fields.Integer(
        metadata={
            "description": "The difference between start of the data and the start of the response data",
            "example": 0,
        },
        validate=validate.Range(min=Offset.Min.value),
    )
    limit = fields.Integer(
        metadata={
            "description": "The maximum number of results in the response",
            "example": 5,
        },
        validate=validate.Range(min=Limit.Min.value, max=Limit.ClassicReportMax.value),
    )


class QueryReportQueryParamsSchema(QueryFormatParamsSchema):
    class Meta:
        ordered = True

    mode = fields.String(
        validate=validate.OneOf([Mode.Preview.name, Mode.Run.name, Mode.Page.name]),
        required=True,
        metadata={
            "description": "Select the mode to limit the amount of records. Preview has a limit of 100 records, and run has a limit 12,000 records",
            "example": Mode.Preview.name,
        },
    )

    @post_load
    def load_mode(self, data, **kwargs):
        data["mode"] = Mode[data["mode"]]

        return data


class ReportPreviewSchema(ReportSchema):
    fields = (
        "id",
        "title",
        "description",
        "dataset_id",
        "created_by",
        "updated_at",
        "folder_id",
        "tags",
        "schedules",
    )


class ReportsSchema(PaginationSchema):
    class Meta:
        ordered = True

    reports = fields.Nested(ReportPreviewSchema, many=True)


class ReportsSearchSchema(PaginationSchema):
    class Meta:
        ordered = True

    items = fields.Nested(ReportPreviewSchema, many=True)


class PathReportIdSchema(Schema):
    report_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Report id of the resource"},
    )

    @post_load
    def set_report_id(self, data, **kwargs):
        g.report_id = (
            data["report_id"] if data is not None else self.context["report_id"]
        )
        g.report_kind = ReportKind.Report


class PathReportIdCustomCdfIdSchema(PathReportIdSchema):
    custom_cdf_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Custom cdf id of the resource"},
    )

    @post_load
    def set_custom_cdf_id_and_context(self, data, **kwargs):
        self.context["report_id"] = data["report_id"]
        g.custom_cdf_id = data["custom_cdf_id"]


class ReportSummarySchema(Schema):
    class Meta:
        ordered = True

    total = fields.Integer(
        required=True,
        dump_only=True,
        metadata={"example": 100, "description": "Total records on the report"},
    )
    metrics = fields.Dict(
        required=True,
        dump_only=True,
        metadata={"description": "Dictionary of metric columns"},
    )


class ReportDataSchema(Schema):
    class Meta:
        ordered = True

    headers = fields.List(
        fields.Raw(),
        required=True,
        dump_only=True,
        metadata={
            "description": "Column headers that list columns names. The order of headers is the same as those specified in the request through the columns "
            "property. The number of headers is the number of columns."
        },
    )
    index = fields.List(
        fields.List(fields.String()),
        required=True,
        dump_only=True,
        metadata={"description": "List of index distinct names"},
    )
    group_rows = fields.List(
        fields.String(),
        required=True,
        dump_only=True,
        metadata={"description": "List of CDF names of group rows"},
    )
    group_columns = fields.List(
        fields.String(),
        required=True,
        dump_only=True,
        metadata={"description": "List of CDF names of group columns"},
    )
    periods = fields.List(
        fields.String(),
        required=False,
        dump_only=True,
        metadata={"description": "List of names of the periods being compared"},
    )
    records = fields.Dict(
        required=True,
        dump_only=True,
        metadata={
            "description": "Dictionary of records of column value combinations and metric values in the report"
        },
    )
    subtotals = fields.Dict(
        required=True,
        dump_only=True,
        metadata={"description": "Dictionary of subtotals in the report"},
    )
    totals = fields.Dict(
        required=True,
        dump_only=True,
        metadata={"description": "Dictionary of totals in the report"},
    )
    type = fields.String(
        required=True,
        dump_only=True,
        metadata={
            "example": ReportType.List,
            "description": "Type of the report generated",
        },
    )
    comparisons = fields.List(
        fields.String(),
        required=False,
        dump_only=True,
        metadata={"description": "List of names of the comparisons being made"},
    )

    aggregated_count = fields.Integer(
        required=False,
        dump_only=True,
        metadata={
            "description": "Number of rows included in aggregations in the report"
        },
    )


class ReportExportSchema(Schema):
    class Meta:
        ordered = True

    url = fields.Url(
        dump_only=True,
        schemes="https",
        metadata={
            "example": "https://...",
            "description": "Url to get the exported report",
        },
    )
    format = fields.String(
        dump_only=True,
        validate=validate.OneOf([format.value for format in Format]),
        metadata={
            "example": Format.XLSX.value,
            "description": "Format: should be either 'xlsx', 'csv', 'pdf' or 'json'",
        },
    )
    view = fields.String(
        dump_only=True,
        validate=validate.OneOf([view.value for view in View]),
        metadata={
            "example": View.Details.value,
            "description": "View: should be either 'formatted', 'details' or 'table'",
        },
    )
    filename = fields.String(
        dump_only=True, metadata={"description": "Name of the file exported"}
    )
    generated_at = CustomDateTimeUtc(
        dump_only=True,
        description="File generated at",
    )


class ReportPropertySchema(PropertySchema):
    pass


class QueryReportExportSchema(Schema):
    class Meta:
        ordered = True

    view = fields.String(
        required=True,
        validate=validate.OneOf([view.value for view in View]),
        metadata={
            "example": View.Details.value,
            "description": "View: should be either 'formatted' or 'details'",
        },
    )
    format = fields.String(
        required=True,
        validate=validate.OneOf([format.value for format in Format]),
        metadata={
            "example": Format.XLSX.value,
            "description": "Format: should be either 'xlsx', 'csv', 'pdf' or 'json'",
        },
    )
    include_charts = fields.Bool(
        required=False,
        load_default=True,
        metadata={
            "description": "Flag to include charts in the export, defaults to True",
            "example": True,
        },
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        if data["view"] == View.Formatted.value and data["format"] not in (
            Format.XLSX.value,
            Format.PDF.value,
        ):
            raise InvalidUsage.bad_request(
                "Formatted view only support XLSX or PDF format"
            )

        if data["view"] == View.Details.value and data["format"] == Format.PDF.value:
            raise InvalidUsage.bad_request("Details view only is not supported for PDF")

        if data["view"] == View.Table.value and data["format"] in (
            Format.XLSX.value,
            Format.PDF.value,
        ):
            raise InvalidUsage.bad_request("Table view only support CSV or JSON format")


class PathReportIdTagIdSchema(PathReportIdSchema):
    tag_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Tag Id of the resource"},
    )


class ReportRelativeDateSchema(Schema):
    value = fields.String(
        required=True,
        dump_only=True,
        metadata={"description": "Relative Date entry identifier"},
    )


class QuerySortReportSchema(Schema):
    sort = fields.String(
        metadata={"description": "Sort reports"},
        load_default="id,asc",
        validate=validate.OneOf(
            [
                f"{field},{sort.name}"
                for field in (
                    "id",
                    "title",
                    "description",
                    "dataset_id",
                    "folder_id",
                    "user_id",
                    "updated_at",
                )
                for sort in Sort
            ]
        ),
    )


class PathReportIdCustomFieldCdfIdSchema(PathReportIdSchema):
    custom_field_cdf_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Custom cdf id of the resource"},
    )


class ReportExploreChatSchema(Schema):
    actor = fields.String(
        required=True,
        metadata={
            "description": "The actor who sent the message, e.g., 'user' or 'explore'."
        },
    )
    message = fields.String(
        required=True, metadata={"description": "The content of the message."}
    )


class ReportExploreSchema(ReportSchema):
    class Meta:
        ordered = True
        fields = (
            "context_id",
            "title",
            "description",
            "dataset_id",
            "columns",
            "group_rows",
            "filters",
            "sort",
            "settings",
            "chat",
        )

    context_id = fields.String(
        dump_only=True,
        metadata={
            "description": "A unique identifier for the context",
            "example": 123,
        },
    )
    chat = fields.List(
        fields.Nested(ReportExploreChatSchema),
        required=False,
        dump_only=True,
        metadata={"description": "Chat messages"},
    )
