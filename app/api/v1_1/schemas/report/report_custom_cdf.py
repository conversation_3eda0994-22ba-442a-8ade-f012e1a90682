from flask import g

from marshmallow import pre_load, validates_schema

from app.api.v1_1.schemas.custom_cdf import CustomCdfSchema
from app.enums.report import ReportKind
from app.models.report import Report


class ReportCustomCdfSchema(CustomCdfSchema):
    class Meta:
        ordered = True

    @pre_load
    def pre_load(self, data, **kwargs):
        self.context["dataset_id"] = (
            Report.get_by_id(g.report_id).dataset_id
            if "dataset_id" not in self.context
            else self.context["dataset_id"]
        )
        return data

    @validates_schema
    def validates_schema(self, data, **kwargs):
        self.validate_schema(data, ReportKind.Report)


class ReportCustomCdfValidateSchema(ReportCustomCdfSchema):
    class Meta:
        ordered = True
        fields = ("name", "formula", "kind")
