from flask import g, request

from marshmallow import Schema, fields, pre_load, validate, validates_schema

from sqlalchemy import BIGINT, FLOAT, INTEGER

from app.api.v1_1.schemas.report.report_cdf import ReportCDFSchema
from app.api.v1_1.validations.reports import (
    validate_date_or_relative_date,
    validate_relative_date,
)
from app.cdfs.cdf import CDF
from app.common.constants.datetime import DEFAULT_DATETIME_DATE_FORMAT
from app.common.constants.limits import (
    MAX_REPORT_COMPARISON_FILTERS,
    MIN_REPORT_COMPARISON_FILTERS,
)
from app.common.exceptions import InvalidUsage
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums import FilterOperator, RelativeDate as RelativeDateEnum
from app.enums.cdf import Cdf
from app.enums.cdf_kind import CdfKind
from app.enums.dataset import Dataset as DatasetEnum
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.filters.relative_date import RelativeDate


FILTER_EXAMPLE = {
    "and": [
        {
            "cdf": {"type": "default", "column": "checkin_date"},
            "operator": "less_than_or_equal",
            "value": "today",
        },
        {
            "or": [
                {
                    "cdf": {"type": "default", "column": "booking_datetime"},
                    "operator": "less_than_or_equal",
                    "value": "2021-10-19T00:00:00",
                },
                {
                    "cdf": {"type": "default", "column": "booking_datetime"},
                    "operator": "greater_than_or_equal",
                    "value": "2020-10-19T00:00:00",
                },
            ]
        },
    ]
}

LIST_FILTERS = [
    FilterOperator.ListContains.value,
    FilterOperator.NotListContains.value,
]

NUMBER_COMPARISON_FILTERS = [
    FilterOperator.GreaterThan.value,
    FilterOperator.GreaterThanOrEqual.value,
    FilterOperator.LessThan.value,
    FilterOperator.LessThanOrEqual.value,
]

EQUALITY_FILTERS = [
    FilterOperator.Equals.value,
    FilterOperator.NotEquals.value,
]


class ReportFilterValueField(fields.Field):
    """Field that serializes to a list of strings and deserializes to list of strings or
    serializes to a string and deserializes to string based on the Filter Operator
    """

    def _deserialize(self, value, attr, data, **kwargs):
        if not isinstance(value, (list, str)):
            raise InvalidUsage.bad_request(
                "Value from Filters must be list of string for list operators, string or null"
            )

        return value


class ReportFilterSchema(Schema):
    class Meta:
        include = {
            "and": fields.List(
                fields.Nested("ReportFilterSchema"), validate=validate.Length(min=1)
            ),
            "or": fields.List(
                fields.Nested("ReportFilterSchema"), validate=validate.Length(min=1)
            ),
            "cdf": fields.Nested(
                ReportCDFSchema, metadata={"description": "Filter cdf for the rule"}
            ),
            "operator": fields.String(
                validate=validate.OneOf(
                    [operator.value for operator in FilterOperator]
                ),
                metadata={"description": "Filter operator value"},
            ),
            "value": ReportFilterValueField(
                metadata={"description": "Filter value"}, allow_none=True
            ),
        }
        ordered = True

    def validate_rule(self, data):
        if "or" not in data and "and" not in data:
            missing = [
                key for key in ["cdf", "operator", "value"] if key not in data.keys()
            ]
            if missing:
                missing_string = ", ".join(missing)
                raise InvalidUsage.bad_request(
                    f"Missing item from filter rule: {missing_string}"
                )

    def validate_value(self, data):
        if "value" in data:
            if (data["value"] is None or data["value"] == "") and data[
                "operator"
            ] in NUMBER_COMPARISON_FILTERS:
                raise InvalidUsage.bad_request(
                    f"Value can not be empty when operator is {data['operator']}"
                )

            if data["value"] is not None:
                if not any(
                    [
                        isinstance(data["value"], list)
                        and data["operator"] in LIST_FILTERS,
                        isinstance(data["value"], str)
                        and data["operator"] not in LIST_FILTERS,
                    ]
                ):
                    raise InvalidUsage.bad_request(
                        "Value from Filters must be list of string for list operators or string"
                    )

    def validate_value_type(self, data):
        """Validate that the value is the correct type for the column in the CDF."""
        dataset_id = (
            self.context["dataset_id"]
            if self.context.get("dataset_id")
            else g.dataset_id
        )
        dataset = Dataset(DatasetEnum(dataset_id))
        if (
            data.get("cdf")
            and data["cdf"].get("type") not in [Cdf.Custom.value, Cdf.CustomField.value]
            and (data.get("operator") in (NUMBER_COMPARISON_FILTERS + EQUALITY_FILTERS))
        ):
            try:
                if data["cdf"].get("multi_level_id"):
                    cdf = CDF(
                        DatasetEnum(dataset_id),
                        data["cdf"]["column"],
                        False,
                        False,
                        MultiLevelEnum(data["cdf"]["multi_level_id"]),
                    )
                    columns = {
                        column.name: column.type
                        for column in MultiLevel(
                            MultiLevelEnum(data["cdf"]["multi_level_id"])
                        ).model.__table__.columns
                    }
                else:
                    column = data["cdf"]["column"]
                    cdf = CDF(DatasetEnum(dataset_id), column)
                    columns = {
                        column.name: column.type
                        for column in dataset.model.__table__.columns
                    }
            except ValueError as exception:
                raise InvalidUsage.bad_request(exception.args[0])

            match columns.get(cdf.name, None):
                case FLOAT():
                    try:
                        float(data["value"]) if data["value"] is not None else None
                    except ValueError:
                        raise InvalidUsage.bad_request(
                            f"Filter value for {cdf.name} must be a valid number"
                        )
                case BIGINT() | INTEGER():
                    try:
                        int(data["value"]) if data["value"] is not None else None
                    except ValueError:
                        raise InvalidUsage.bad_request(
                            f"Filter value for {cdf.name} must be a valid integer"
                        )
                case _:
                    pass

    @validates_schema
    def validates_schema(self, data, **kwargs):
        self.validate_rule(data)
        self.validate_value(data)
        self.validate_value_type(data)


class ReportFiltersSchema(Schema):
    class Meta:
        include = {
            "and": fields.List(
                fields.Nested(ReportFilterSchema), validate=validate.Length(min=1)
            ),
            "or": fields.List(
                fields.Nested(ReportFilterSchema), validate=validate.Length(min=1)
            ),
        }
        ordered = True

    @validates_schema
    def validate_key(self, data, **kwargs):
        if len(data.keys()) > 1:
            raise InvalidUsage.bad_request("You can only start with one level of group")


class ReportComparisonFiltersSchema(Schema):
    class Meta:
        ordered = True
        include = {
            "and": fields.List(
                fields.Nested(ReportFilterSchema),
                validate=validate.Length(
                    min=MIN_REPORT_COMPARISON_FILTERS, max=MAX_REPORT_COMPARISON_FILTERS
                ),
            )
        }

    @validates_schema
    def validate_comparison_rules(self, data, **kwargs):
        # Check that the comparison filters are not nested
        if any(
            [
                ("and" in filter.keys() or "or" in filter.keys())
                for filter in data["and"]
            ]
        ):
            raise InvalidUsage.bad_request(
                "Nested filters are not allowed in comparison filters"
            )

        # validate only number comparison filters are used in comparisons
        elif any(
            [
                filter["operator"] not in NUMBER_COMPARISON_FILTERS
                for filter in data["and"]
            ]
        ):
            raise InvalidUsage.bad_request(
                "Comparison filters can only use number comparison operators"
            )

        # validate only number comparison filters are used in comparisons
        elif any([filter["cdf"]["type"] == Cdf.Custom.value for filter in data["and"]]):
            raise InvalidUsage.bad_request(
                "Custom CDFs are not allowed in comparison filters"
            )

        # validate that each filter is unique
        filters = [str(filter) for filter in data["and"]]
        unique_filters = set(filters)
        if len(filters) != len(unique_filters):
            raise InvalidUsage.bad_request("Comparison filters must be unique")


class DateOrRelativeDate(fields.String):
    """Field that validates if type is Date or RelativeDate."""

    def _deserialize(self, value, attr, data, **kwargs):
        if not validate_date_or_relative_date(value):
            raise InvalidUsage.bad_request("Field must be date or relative date")
        return value

    def get_converted_value(
        self, value: str, property_timezone: str | None = None, today: str | None = None
    ):
        value, duration = RelativeDate.get_duration_from_value(value)
        if value not in [relative_date.value for relative_date in RelativeDateEnum]:
            return value

        return RelativeDate(
            RelativeDateEnum(value), property_timezone, duration, today
        ).value.strftime(DEFAULT_DATETIME_DATE_FORMAT)


class ReportPeriodSchema(Schema):
    class Meta:
        ordered = True

    cdf = fields.Nested(ReportCDFSchema, required=True)
    name = fields.String(
        required=True,
        metadata={"description": "Label for a period", "example": "Month-To-Date"},
    )

    id = fields.String(
        dump_only=True,
        required=False,
        metadata={
            "description": "machine name of period (auto-generated)",
            "example": "start_of_last_year_to_today",
        },
    )

    start = DateOrRelativeDate(
        required=True,
        metadata={
            "description": "The beginning date or relative date of a period.  When both start and end dates are relative dates, "
            "the difference between today and the end date is subtracted from the start date.",
            "example": "'2014-12-22T03:12:58'|'start_current_year'",
        },
    )
    end = DateOrRelativeDate(
        required=True,
        metadata={
            "description": "The beginning date or relative date of a period",
            "example": "'2014-12-22T03:12:58'|'today'",
        },
    )

    start_relative_to_end = fields.Boolean(
        required=False,
        dump_default=False,
        metadata={
            "description": "If true, relative start date will be relative to the end date instead of today,"
            "e.g. if end date is 2023-01-01, a start date of yesterday would be 2022-12-31",
            "example": False,
        },
    )

    @pre_load
    def set_context(self, data, **kwargs):
        self.context["dataset_id"] = self.context.get(
            "dataset_id",
            request.json.get("dataset_id") if (request and request.is_json) else None,
        )
        return data

    @validates_schema
    def validate_start_relative_to_end(self, data, **kwargs):
        if data.get("start_relative_to_end") and not validate_relative_date(
            data["start"]
        ):
            raise InvalidUsage.bad_request(
                "Start date must be relative date if start_relative_to_end is true"
            )

    @validates_schema
    def validate_end_after_start(self, data, **kwargs):
        end = self.fields["end"].get_converted_value(data["end"])
        if data.get("start_relative_to_end") and validate_relative_date(data["start"]):
            start = self.fields["start"].get_converted_value(data["start"], today=end)
        else:
            start = self.fields["start"].get_converted_value(data["start"])

        if end < start:
            raise InvalidUsage.bad_request("Period end must be after period start")

    @validates_schema
    def validate_cdf_is_datetime(self, data, **kwargs):
        dataset_id = self.context["dataset_id"]
        try:
            if data["cdf"].get("multi_level_id"):
                cdf = CDF(
                    DatasetEnum(dataset_id),
                    data["cdf"]["column"],
                    False,
                    False,
                    MultiLevelEnum(data["cdf"]["multi_level_id"]),
                )
            else:
                column = data["cdf"]["column"]
                cdf = CDF(DatasetEnum(dataset_id), column)
        except ValueError as exception:
            raise InvalidUsage.bad_request(exception.args[0])

        if not (cdf.is_kind(CdfKind.Date) or cdf.is_kind(CdfKind.Timestamp)):
            raise InvalidUsage.bad_request("Period columns must be datetime")


class ReportComparisonSchema(Schema):
    class Meta:
        ordered = True

    name = fields.String(
        required=True,
        metadata={"description": "Label for a Comparison", "example": "Range 1"},
    )
    id = fields.String(
        dump_only=True,
        required=False,
        metadata={
            "description": "machine name of comparison (auto-generated)",
            "example": "range_1",
        },
    )
    filters = fields.Nested(
        ReportComparisonFiltersSchema,
        required=False,
        dump_default=None,
        load_default=None,
        validate=validate.Length(equal=1),
        allow_none=True,
        metadata={
            "description": "Report filters",
            "example": FILTER_EXAMPLE,
        },
    )
