from marshmallow import Schema, fields, validate

from app.enums.cdf import Cdf
from app.enums.multi_level import MultiLevel


class ReportCDFSchema(Schema):
    class Meta:
        ordered = True

    type = fields.String(
        load_default=Cdf.Default.value,
        validate=validate.OneOf([cdf.value for cdf in Cdf]),
        metadata={"description": "Report cdf type", "example": Cdf.Default.value},
    )
    column = fields.String(
        required=True,
        metadata={"description": "Report cdf name", "example": "credit_amount"},
    )
    multi_level_id = fields.Integer(
        required=False,
        validate=validate.OneOf([multi_level.value for multi_level in MultiLevel]),
        metadata={"description": "Multi-level where CDF is located"},
    )
