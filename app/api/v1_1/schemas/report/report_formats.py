from flask import g

from marshmallow import fields, validate, validates_schema
from marshmallow.schema import Schema

from app.common.constants.report_formats import (
    CURRENCIES,
    DATE_FORMATS,
    DEFAULT_DATE_FORMAT,
    DEFAULT_LINK_FORMAT,
)
from app.common.enums.features import LaunchDarklyFeature
from app.common.exceptions import InvalidUsage
from app.enums import FormatsKind
from app.services.launch_darkly_service import LaunchDarklyService


class ReportFormatsSchema(Schema):
    date = fields.String(
        allow_none=True,
        required=False,
        load_default=DEFAULT_DATE_FORMAT,
        validate=validate.OneOf(DATE_FORMATS),
        metadata=dict(
            example="YYYY-MM-DD",
            description="Date Format that will be applied in the export. The default is YYYY-MM-DD",
        ),
    )
    link = fields.Boolean(
        allow_none=True,
        required=False,
        load_default=DEFAULT_LINK_FORMAT,
        metadata=dict(
            example=True,
            description=""""If the link is set to true, all the CDF's that the kind contains Link will
            be converted to links on List and Summary with Details columns""",
        ),
    )
    currency = fields.String(
        allow_none=True,
        required=False,
        validate=validate.OneOf(CURRENCIES),
        metadata=dict(
            example="EUR",
            description="Currency that will be used for the cdfs that has as suffix converted_rate. Only ISO Currencies are accepted",
        ),
    )

    @validates_schema
    def validates_schema(self, data, **kwargs):
        if "currency" in data and data["currency"] is not None:
            if not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ReportingCurrency, g.property_id
            ):
                raise InvalidUsage.bad_request(
                    "Sorry you don't have access to use Reporting Currency"
                )


class FormatOptionsSchema(Schema):
    name = fields.String(
        required=True,
        dump_only=True,
        metadata=dict(description="Display name of the format option"),
    )
    value = fields.String(
        required=True,
        dump_only=True,
        metadata=dict(description="Available report format options by type"),
    )


class PathFormatTypeSchema(Schema):
    format_type_id = fields.String(
        required=True,
        validate=validate.OneOf([format.value for format in FormatsKind]),
        metadata=dict(description="Format type"),
    )
