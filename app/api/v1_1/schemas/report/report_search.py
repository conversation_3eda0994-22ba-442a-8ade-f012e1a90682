from marshmallow import Schema, fields, validate
from marshmallow.decorators import post_load, validates

from webargs.fields import DelimitedList

from app.api.v1_1.validations.reports import (
    validate_folder_ids,
)
from app.common.enums.search import ReportSearchColumns
from app.common.exceptions import InvalidUsage
from app.enums.dataset import Dataset


class GetReportsQueryParamsSchema(Schema):
    class Meta:
        ordered = True

    ids = DelimitedList(
        fields.Integer(
            validate=lambda x: int(x) >= 0
            or InvalidUsage.bad_request("Should be greater or equal than zero."),
            as_string=True,
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Filter by Report IDs"},
    )
    title = fields.String(
        validate=validate.Length(min=1, max=100),
        metadata={"description": "Filter by Report title"},
    )
    description = fields.String(
        validate=validate.Length(max=200),
        metadata={"description": "Filter by Report description"},
        allow_none=True,
    )
    dataset_ids = DelimitedList(
        fields.Integer(validate=validate.OneOf([kind.value for kind in Dataset])),
        validate=validate.Length(min=1),
        metadata={"description": "Filter by Dataset IDs"},
    )
    folder_ids = fields.String(
        validate=validate.Length(min=1),
        metadata={
            "description": "A comma-separated list of the Folder IDs that has reports. Maximum: 50 IDs."
        },
    )
    tag_ids = DelimitedList(
        fields.Integer(
            as_string=True,
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Tag IDs"},
    )
    user_ids = DelimitedList(
        fields.Integer(
            allow_none=False,
            as_string=True,
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Filter reports created by a user ID"},
    )

    @post_load
    def load_folder_ids(self, data, **kwargs):
        if "folder_ids" in data:
            data["folder_ids"] = data["folder_ids"].split(",")

        return data

    @validates("folder_ids")
    def validate_folder_ids(self, folder_ids):
        validate_folder_ids(folder_ids)


class SearchReportsQueryParamsSchema(GetReportsQueryParamsSchema):
    class Meta:
        ordered = True

    search_term = fields.String(
        metadata={
            "description": "A text string to search for in any of the search column fields",
            "example": "in-house",
        }
    )
    search_columns = DelimitedList(
        fields.String(
            validate=validate.OneOf(
                [
                    report_search_column.value
                    for report_search_column in ReportSearchColumns
                ]
            )
        )
    )
