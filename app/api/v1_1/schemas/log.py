from marshmallow import Schema, fields, validate

from app.api.v1_1.schemas.favorite import FavoriteSchema
from app.common.enums.permissions import PermissionAction
from app.enums.dataset import Dataset
from app.enums.export import Format, View
from app.enums.log_actions import EmailAction, FavoriteAction, HubAction, ReportAction
from app.enums.report import ReportKind


class FavoriteLogSchema(FavoriteSchema):
    class Meta:
        ordered = True
        exclude = ("id", "rank")

    action = fields.String(
        required=True,
        validate=validate.OneOf([action.value for action in FavoriteAction]),
        metadata={
            "description": "Favorite or Unfavorite",
            "example": FavoriteAction.Favorite.value,
        },
    )


class ReportLogSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        required=True,
        metadata={"description": "Report Id favorited", "example": "1"},
        data_key="report_id",
    )
    report_kind = fields.String(
        required=True,
        validate=validate.OneOf([kind.value for kind in ReportKind]),
        metadata={
            "description": "Report Kind",
            "example": ReportKind.StockReport.value,
        },
    )
    title = fields.String(
        required=True,
        validate=validate.Length(min=1, max=100),
        metadata={
            "description": "Report title",
            "example": "Report title",
        },
        data_key="report_title",
    )
    dataset_id = fields.Integer(
        required=True,
        validate=validate.OneOf([kind.value for kind in Dataset]),
        metadata={
            "description": "Dataset id",
            "example": 1,
        },
    )
    action = fields.String(
        required=True,
        validate=validate.OneOf([action.value for action in ReportAction]),
        metadata={
            "description": "Favorite or Unfavorite",
            "example": ReportAction.Export.value,
        },
    )
    format = fields.String(
        validate=validate.OneOf([format.value for format in Format]),
        metadata={
            "example": Format.XLSX.value,
            "description": "Format: should be either 'xlsx', 'csv', 'pdf' or 'json'",
        },
        data_key="report_format",
    )
    view = fields.String(
        validate=validate.OneOf([view.value for view in View]),
        metadata={
            "example": View.Details.value,
            "description": "View: should be either 'formatted' or 'details'",
        },
        data_key="report_view",
    )
    property_ids = fields.List(
        fields.Integer(
            metadata={"description": "Property id", "example": "1"},
        ),
        metadata={"description": "Report property ids"},
    )


class PermissionServiceLogSchema(Schema):
    class Meta:
        ordered = True

    role_name = fields.String(
        metadata={"description": "Role Name", "example": "stock_report_publisher"}
    )
    action = fields.String(
        required=True,
        validate=validate.OneOf([action.value for action in PermissionAction]),
        metadata={
            "description": "Create, Read, Update, Delete",
            "example": PermissionAction.CREATE,
        },
    )


class EmailLogSchema(Schema):
    class Meta:
        ordered = True

    recipients = fields.Int(
        metadata={"description": "Count of email recipients", "example": 5},
        required=True,
    )
    subject = fields.String(
        required=True,
        validate=validate.Length(min=0, max=320),
        metadata={
            "description": "Email subject",
            "example": "An email subject",
        },
    )
    action = fields.String(
        required=True,
        validate=validate.OneOf([action.value for action in EmailAction]),
        metadata={
            "description": "Status of email",
            "example": EmailAction.EmailSent.value,
        },
    )


class HubLogSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        required=True,
        metadata={"description": "Hub Id", "example": "1"},
        data_key="hub_id",
    )
    title = fields.String(
        required=False,
        dump_default=None,
        metadata={
            "description": "Hub title",
            "example": "An Example Title",
        },
        data_key="hub_title",
    )
    action = fields.String(
        required=True,
        validate=validate.OneOf([action.value for action in HubAction]),
        metadata={
            "description": "Action performed on hub resource",
            "example": HubAction.Create.value,
        },
    )


class ClassicReportLogSchema(Schema):
    class Meta:
        ordered = True

    id = fields.String(
        required=True,
        metadata={"description": "Classic Report Id", "example": "daily_report"},
    )
    report_kind = fields.String(
        required=True, dump_default=ReportKind.ClassicReport.value
    )
    title = fields.String(
        required=True,
        validate=validate.Length(min=1, max=100),
        metadata={
            "description": "Report title",
            "example": "Report title",
        },
    )
    action = fields.String(required=True, dump_default=ReportAction.DataView.value)
    property_id = fields.Integer(
        metadata={"description": "Property id", "example": "1"}
    )
    organization_id = fields.Integer(
        metadata={"description": "Organization id", "example": "1"}
    )
