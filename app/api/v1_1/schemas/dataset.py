from marshmallow import Schema, fields, validate
from marshmallow.decorators import validates_schema

from webargs.fields import DelimitedList


from app.api.v1_1.schemas.cdf import CDFSSchema
from app.cdfs.cdf import CDF
from app.common.exceptions import InvalidUsage
from app.datasets.dataset import Dataset
from app.enums.dataset import (
    Dataset as DatasetEnum,
)
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.enums.report import ReportKind
from app.schemas.datetime import CustomDateTimeUtc


class DatasetSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        dump_only=True,
        required=True,
        metadata={"description": "Dataset id", "example": "1"},
    )
    name = fields.String(
        dump_only=True,
        required=False,
        metadata={"description": "Dataset Name", "example": "Financial"},
    )
    cdfs = fields.Nested(
        CDFSSchema,
        required=False,
        many=True,
        metadata={"description": "Dataset list of CDFs grouped by Category"},
    )
    multi_levels = fields.List(
        fields.String(),
        required=False,
        metadata={
            "description": "List of multi level dataset(s) associated with Parent dataset"
        },
    )
    read_only = fields.Boolean(
        metadata={
            "description": "Flag to indicate if reports can be saved in this dataset",
            "example": False,
        }
    )
    updated_at = CustomDateTimeUtc(
        dump_only=True,
        description="Dataset updated at",
    )


class DatasetsSchema(DatasetSchema):
    class Meta:
        fields = ("id", "name", "read_only")


class DatasetByIdSchema(DatasetSchema):
    class Meta:
        fields = ("id", "name", "read_only", "cdfs")


class DatasetExploreSchema(DatasetSchema):
    class Meta:
        fields = ("id", "name")


class DatasetByIdUpdatedAtSchema(DatasetSchema):
    class Meta:
        fields = ("updated_at",)


class PathDatasetByIdSchema(Schema):
    dataset_id = fields.Integer(
        required=True,
        validate=validate.OneOf([dataset.value for dataset in DatasetEnum]),
    )


class PathDatasetByIdMultiLevelByIdSchema(PathDatasetByIdSchema):
    multi_level_id = fields.Integer(
        required=True,
        validate=validate.OneOf([multi_level.value for multi_level in MultiLevelEnum]),
    )

    @validates_schema
    def validate_multi_level_id(self, data, **kwargs):
        if (
            data["multi_level_id"]
            not in Dataset(DatasetEnum(data["dataset_id"])).multi_levels
        ):
            raise InvalidUsage.bad_request(
                f"Invalid multi level {data['multi_level_id']} for this dataset: {data['dataset_id']}"
            )


class PathDatasetByIdCdfByNameSchema(PathDatasetByIdSchema):
    cdf = fields.String(
        required=False,
        metadata={
            "description": "Column name reference for cdf",
            "example": "reservation_status",
        },
    )

    @validates_schema
    def validate_cdf_in_dataset(self, data, **kwargs):
        try:
            CDF(DatasetEnum(data["dataset_id"]), data["cdf"])
        except ValueError as error:
            raise InvalidUsage.bad_request(error.args[0])


class PathDatasetByIdMultilevelByIdCdfByNameSchema(PathDatasetByIdMultiLevelByIdSchema):
    cdf = fields.String(
        required=False,
        metadata={
            "description": "Column name reference for cdf",
            "example": "reservation_status",
        },
    )

    @validates_schema
    def validate_cdf_in_multilevel(self, data, **kwargs):
        try:
            CDF(
                DatasetEnum(data["dataset_id"]),
                data["cdf"],
                False,
                False,
                MultiLevelEnum(data["multi_level_id"]),
            )
        except ValueError as error:
            raise InvalidUsage.bad_request(error.args[0])


class GetDatasetByIdQueryParamsSchema(Schema):
    cdfs = fields.String(
        required=False,
        validate=validate.OneOf([cdf.value for cdf in ReportKind]),
        metadata={
            "description": "Whether or not the cdfs should be from stock reports (stock) or reports (default)"
        },
    )


class GetPropertyIdsQueryParamsSchema(Schema):
    class Meta:
        ordered = True

    property_ids = DelimitedList(
        fields.Integer(
            as_string=True,
            required=True,
            validate=lambda x: int(x) > 0
            or InvalidUsage.bad_request("Value should be greater than 0."),
            metadata={"description": "Property Id", "example": "1"},
        ),
        validate=validate.Length(min=1, max=15),
        required=False,
        metadata={"description": "Property IDs"},
    )
