from flask import request

from marshmallow import Schema, fields, validate, validates_schema

from app.common.constants.property_setting import (
    MAX_ROUNDING_PRECISION,
    property_setting_options_type,
)
from app.common.exceptions import InvalidUsage
from app.enums.property_setting import PropertySetting, WeekDays
from app.schemas.datetime import CustomDateTimeUtc


class PropertySettingSchema(Schema):
    class Meta:
        ordered = True

    property_id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Property id", "example": "1"},
    )
    name = fields.String(
        required=True,
        validate=validate.OneOf(
            [property_setting.value for property_setting in PropertySetting]
        ),
        metadata={
            "description": "Property Setting Name",
            "example": PropertySetting.START_OF_WEEK.value,
        },
    )
    value = fields.String(
        required=True,
        metadata={"description": "Value of the setting", "example": "Monday"},
    )
    user_id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "User id", "example": "1"},
    )
    created_at = CustomDateTimeUtc(
        dump_only=True,
        description="Created At Time",
    )
    updated_at = CustomDateTimeUtc(
        dump_only=True,
        description="Updated At Time",
    )


class PropertySettingsSchema(PropertySettingSchema):
    class Meta:
        fields = ("name", "value")


class UpdatePropertySettingByNameSchema(PropertySettingsSchema):
    class Meta:
        fields = ("value",)

    @validates_schema
    def validate_value_for_setting(self, data, **kwargs):
        name = request.view_args.get("name")
        value = data.get("value")
        match (name):
            case PropertySetting.START_OF_WEEK.value:
                if value not in [week_day.name for week_day in WeekDays]:
                    raise InvalidUsage.bad_request(
                        f"Setting for {name} must be one of {[week_day.name for week_day in WeekDays]}"
                    )
            case PropertySetting.CURRENCY_CDF_ROUNDING_PRECISION.value:
                valid_range = [
                    str(value) for value in list(range(0, MAX_ROUNDING_PRECISION + 1))
                ]
                if value not in valid_range:
                    raise InvalidUsage.bad_request(
                        f"Value for setting {name} must be one of {valid_range}"
                    )
            case _:
                raise InvalidUsage.bad_request("unsupported property setting name")


class PathPropertySettingsByNameSchema(PropertySettingsSchema):
    class Meta:
        fields = ("name",)


class OptionsSchema(Schema):
    class Meta:
        ordered = True

    name = fields.String(
        required=True,
        dump_only=True,
        metadata={
            "description": "Name of the property setting. Used for translations and displaying",
            "example": "Monday",
        },
    )
    value = fields.String(
        required=True,
        dump_only=True,
        metadata={
            "description": "Value that needs to be used on the API",
            "example": "Monday",
        },
    )


class PropertySettingOptionsSchema(Schema):
    class Meta:
        ordered = True

    title = fields.String(
        required=True,
        dump_only=True,
        metadata={
            "description": "Title of the property setting",
            "example": "Start of Week",
        },
    )
    type = fields.String(
        validate=validate.OneOf(property_setting_options_type.values()),
        required=True,
        dump_only=True,
        metadata={"description": "Type of the options based on the property setting"},
    )
    options = fields.Nested(
        OptionsSchema,
        many=True,
        allow_none=False,
        required=True,
        validate=validate.Length(min=1),
        metadata={"description": "List of Options associated to the Property Setting"},
    )
