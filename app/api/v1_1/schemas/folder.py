from marshmallow import fields, validate
from marshmallow.schema import Schema

from app.common.exceptions import InvalidUsage


class FolderSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Folder Id", "example": "1"},
    )
    name = fields.String(
        required=True,
        validate=validate.Length(min=1, max=25),
        metadata={"description": "Folder Name", "example": "My Reports"},
    )
    description = fields.String(
        validate=validate.Length(max=200),
        metadata={
            "description": "Folder description",
            "example": "Contains all my reports",
        },
    )
    parent_id = fields.Integer(
        as_string=True,
        required=True,
        allow_none=True,
        metadata={"description": "Id of the Parent Folder", "example": "1"},
    )


class PathFolderIdSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Folder Id", "example": "1"},
    )


class PathFolderIdReportIdSchema(PathFolderIdSchema):
    class Meta:
        ordered = True

    report_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Report Id", "example": "1"},
    )


class ReportIdSchema(Schema):
    class Meta:
        ordered = True

    report_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Report Id", "example": "1"},
    )
