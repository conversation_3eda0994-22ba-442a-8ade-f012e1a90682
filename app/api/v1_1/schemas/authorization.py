from marshmallow import Schema, fields, validate


class AuthorizationTokenSchema(Schema):
    class Meta:
        ordered = True

    grant_type = fields.String(
        required=True,
        load_only=True,
        validate=validate.OneOf(["mfd_session"]),
        metadata={
            "example": "mfd_session",
            "description": "The OAuth framework specifies several grant types for different use cases, as well as a framework for creating new grant types. This "
            "is a custom grant type built specifically for the purposes of mfd sessions.",
        },
    )
    code = fields.String(
        required=True,
        load_only=True,
        metadata={
            "example": "1aBc",
            "description": "The authorization code is a temporary code that the client will exchange for an access token. The code itself is obtained from the "
            "authorization server where the user gets a chance to see what the information the client is requesting, and approve or deny the request.",
        },
    )
    access_token = fields.String(
        dump_only=True,
        metadata={
            "description": "An Access Token that can be provided in subsequent calls, for example to Cloudbeds API services."
        },
    )
    token_type = fields.String(
        dump_only=True,
        metadata={
            "example": "Bearer",
            "description": "How the Access Token may be used: always “Bearer",
        },
    )
    expires_in = fields.Integer(
        dump_only=True,
        metadata={
            "example": 3600,
            "description": "The time period (in seconds) for which the Access Token is valid.",
        },
    )
