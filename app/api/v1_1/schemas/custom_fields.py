from marshmallow import Schema, fields


class CustomFieldCDFPropertySchema(Schema):
    class Meta:
        ordered = True

    internal_name = fields.String(
        required=True,
        metadata={
            "description": "Internal name of the Custom Field, unique per property.",
            "example": "favorite_food",
        },
    )
    property_id = fields.Integer(
        required=True,
        as_string=True,
        metadata={"example": 1, "description": "Property ID"},
    )


class CustomFieldColumnSchema(Schema):
    internal_name = fields.String(
        required=True,
        metadata={
            "description": "Internal name of the Custom Field, unique per property.",
            "example": "favorite_food",
        },
    )
    field_name = fields.String(
        metadata={
            "description": "Field name of the Custom Field",
            "example": "Favorite Food",
        }
    )


class CustomFieldSchema(Schema):
    class Meta:
        ordered = True

    property_id = fields.Integer(metadata={"example": 1, "description": "Property ID"})
    custom_fields = fields.Nested(CustomFieldColumnSchema, many=True)


class CustomFieldsSchema(Schema):
    class Meta:
        ordered = True

    property_custom_fields = fields.Nested(
        CustomFieldSchema,
        required=True,
        many=True,
        metadata={"description": "List of CDFs"},
    )
