from marshmallow import fields, validate
from marshmallow.decorators import validates


from app.api.v1_1.schemas.report.report import (
    ReportDataSchema,
    ReportGroupSchema,
    ReportQuerySchema,
)
from app.api.v1_1.validations.reports import validate_property_ids


class PicklistQuerySchema(ReportQuerySchema):
    class Meta:
        fields = ("property_ids", "filters")

    group_rows = fields.Nested(
        ReportGroupSchema,
        many=True,
        required=False,
        validate=validate.Length(min=1, max=4),
        dump_default=None,
        load_default=None,
        allow_none=True,
        metadata={"description": "Report group rows"},
    )

    @validates("property_ids")
    def validate_property_ids(self, property_ids):
        validate_property_ids(property_ids)


class PicklistValuesDataSchema(ReportDataSchema):
    class Meta:
        ordered = True

    pass
