from marshmallow import fields, validate
from marshmallow.schema import Schema


class TagSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Tag Id", "example": "1"},
    )
    name = fields.String(
        required=True,
        validate=validate.Length(min=1, max=100),
        metadata={"description": "Tag Name", "example": "#Financial"},
    )


class ReportTagSchema(TagSchema):
    pass


class PathTagIdSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        required=True,
        metadata={"description": "Tag Id of the resource"},
    )


class ReportTagIdSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        required=True,
        metadata={"description": "Tag Id of the resource"},
    )
