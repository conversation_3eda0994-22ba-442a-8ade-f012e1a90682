from marshmallow import Schema, fields

from app.common.exceptions import InvalidUsage
from app.schemas.datetime import CustomDateTimeUtc


class StockTagSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(dump_only=True, metadata={"description": "ID of each tag"})
    name = fields.String(required=True, metadata={"description": "Name of each tag"})
    user_id = fields.Integer(
        dump_only=True, metadata={"description": "User that created the tag"}
    )

    created_at = CustomDateTimeUtc(
        dump_only=True,
        description="Created at date of the tag",
    )
    updated_at = CustomDateTimeUtc(
        dump_only=True,
        description="Updated at date of the tag",
    )


class StockReportTagIdSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Stock Tag Id of the resource"},
    )
