from marshmallow import Schema, fields, validate

from webargs.fields import DelimitedList

from app.api.v1_1.schemas.pagination import PaginationSchema
from app.api.v1_1.schemas.search import QuerySearchSchema
from app.common.exceptions import InvalidUsage
from app.enums.sort import Sort
from app.schemas.user import UserSchema


class CardPositioningSchema(Schema):
    i = fields.String(
        metadata={
            "description": "A string corresponding to the component key",
            "example": "my_key",
        },
        required=True,
    )
    x = fields.Number(
        required=False, metadata={"description": "X coordinate", "example": 1}
    )
    y = fields.Number(
        required=False, metadata={"description": "Y coordinate", "example": 1}
    )
    w = fields.Number(required=False, metadata={"description": "Width", "example": 1})
    h = fields.Number(required=False, metadata={"description": "Height", "example": 1})
    minW = fields.Number(
        required=False, metadata={"description": "Minimum width", "example": 1}
    )
    maxW = fields.Number(
        required=False, metadata={"description": "Maximum width", "example": 1}
    )
    minH = fields.Number(
        required=False, metadata={"description": "Minimum height", "example": 1}
    )
    maxH = fields.Number(
        required=False, metadata={"description": "Maximum height", "example": 1}
    )
    moved = fields.Boolean(
        required=False, metadata={"description": "Moved", "example": True}
    )
    static = fields.Boolean(
        required=False, metadata={"description": "Static", "example": True}
    )
    isDraggable = fields.Boolean(
        required=False, metadata={"description": "Is draggable", "example": True}
    )
    isResizable = fields.Boolean(
        required=False, metadata={"description": "Is resizable", "example": True}
    )
    resizeHandles = fields.List(
        fields.String(
            required=False,
            validate=validate.OneOf(["s", "w", "e", "n", "sw", "nw", "se", "ne"]),
        ),
        required=False,
        metadata={
            "description": "Resize handles",
            "example": ["s", "w", "e", "n", "sw", "nw", "se", "ne"],
        },
        validate=validate.Length(min=1),
    )
    isBounded = fields.Boolean(
        required=False, metadata={"description": "Is bounded", "example": True}
    )


class CardSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Card Id", "example": "1"},
    )
    hub_id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Hub id", "example": "1"},
    )
    chart_id = fields.Integer(
        as_string=True,
        dump_only=False,
        required=True,
        metadata={"description": "Chart id", "example": "1"},
    )


class HubSchema(Schema):
    class Meta:
        ordered = True

    id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Hub Id", "example": "1"},
    )
    title = fields.String(
        required=True,
        validate=validate.Length(min=1, max=100),
        metadata={"description": "Hub title", "example": "My hub"},
    )
    description = fields.String(
        allow_none=True,
        required=False,
        validate=validate.Length(max=200),
        metadata={
            "description": "Hub Description",
            "example": "Detailed description about the hub",
        },
    )
    cards = fields.Nested(CardSchema, many=True, dump_only=True)
    property_id = fields.Integer(
        as_string=True,
        dump_only=True,
        required=True,
        metadata={"description": "Property id", "example": "1"},
    )
    settings = fields.Nested(CardPositioningSchema, load_default={}, many=True)
    created_at = fields.DateTime(
        dump_only=True,
        metadata={
            "description": "Created at timestamp of the hub",
            "example": "2023-08-24T17:08:04.094590",
        },
    )
    updated_at = fields.DateTime(
        dump_only=True,
        metadata={
            "description": "Updated at timestamp of the hub",
            "example": "2023-08-24T17:08:04.094590",
        },
    )
    created_by = fields.Nested(
        UserSchema, dump_only=True, metadata={"description": "Report created by"}
    )


class CreateHubSchema(HubSchema):
    class Meta:
        fields = ("title", "description", "settings")


class CreateCardSchema(CardSchema):
    class Meta:
        fields = ("chart_id",)


class HubIdSchema(Schema):
    class Meta:
        ordered = True

    hub_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Hub id of the resource"},
    )


class HubCardIdSchema(HubIdSchema):
    card_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Card id of the resource"},
    )


class HubsSchema(PaginationSchema):
    class Meta:
        ordered = True

    hubs = fields.Nested(HubSchema, many=True)


class QueryFilterHubsSchema(Schema):
    class Meta:
        ordered = True

    ids = DelimitedList(
        fields.Integer(
            as_string=True,
        ),
        validate=validate.Length(min=1),
        metadata={"description": "Chart IDs", "example": "1,2,3"},
    )

    title = fields.String(
        metadata={"description": "Hub title", "example": "My hub"},
    )
    description = fields.String(
        metadata={"description": "Hub description", "example": "My hub description"}
    )


class QuerySearchHubsSchema(QueryFilterHubsSchema, QuerySearchSchema):
    pass


class QuerySortHubsSchema(Schema):
    class Meta:
        ordered = True

    sort = fields.String(
        metadata={"description": "Sort Hubs"},
        load_default="updated_at,desc",
        validate=validate.OneOf(
            [
                f"{field},{sort.name}"
                for field in HubSchema().fields.keys()
                if field in ("created_at", "updated_at", "title", "description")
                for sort in Sort
            ]
        ),
    )
