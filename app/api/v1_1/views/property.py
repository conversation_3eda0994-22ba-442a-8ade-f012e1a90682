from http import HTTPStatus

from flask.views import MethodView

from app.api.v1_1.schemas.property_setting import (
    PathPropertySettingsByNameSchema,
    PropertySettingOptionsSchema,
    PropertySettingSchema,
    PropertySettingsSchema,
    UpdatePropertySettingByNameSchema,
)
from app.common.constants.api_version import API_VERSION
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    authorization,
    has_property_permission,
)
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.property_setting_service import PropertySettingService


blp = Blueprint(
    "Property",
    "property",
    url_prefix=f"{API_VERSION}/property",
    description="Operations on the property",
)


@blp.route("/settings")
class PropertySettings(MethodView):
    @authorization
    @has_property_permission()
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, PropertySettingsSchema(many=True))
    def get(self, header_property_id: HeaderPropertyIdSchema):
        """Get all the settings for an specific property"""
        property_id = header_property_id.get("property_id")
        return PropertySettingService.get_all_by_property_id(property_id)


@blp.route("/settings/<string:name>")
class PropertySettingsByName(MethodView):
    @authorization
    @has_property_permission()
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathPropertySettingsByNameSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, PropertySettingOptionsSchema)
    def get(
        self,
        _header_property_id: HeaderPropertyIdSchema,
        _: PathPropertySettingsByNameSchema,
        name: str,
    ):
        """Get property settings with their options"""
        return PropertySettingService.get_setting_options_by_name(name)

    @authorization
    @has_property_permission()
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathPropertySettingsByNameSchema, location="path")
    @blp.arguments(UpdatePropertySettingByNameSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, PropertySettingSchema)
    def put(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: PathPropertySettingsByNameSchema,
        updated_setting: UpdatePropertySettingByNameSchema,
        name: str,
    ):
        """Update property setting by name"""
        property_id = header_property_id.get("property_id")
        return PropertySettingService.update_by_name_and_property_id(
            property_id, updated_setting["value"], name
        )
