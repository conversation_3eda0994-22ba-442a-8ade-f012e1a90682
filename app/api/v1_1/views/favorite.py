from http import HTTPStatus

from flask import g
from flask.views import MethodView


from app.api.v1_1.schemas.favorite import (
    FavoriteSchema,
    FavoritesSchema,
    PathFavoriteIdSchema,
    RankSchema,
)
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import (
    PermissionAction,
    PermissionRole,
    Resources,
)
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.favorite_service import FavoriteService


blp = Blueprint(
    "Favorites",
    "favorites",
    url_prefix=f"{API_VERSION}/favorites",
    description="Operations on Favorites",
)


@blp.route("")
class Favorites(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Favorite, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FavoritesSchema)
    def get(self, _: HeaderPropertyIdSchema):
        """List of Favorites in a property for the user that is authorized

        Get a list of all the created favorites for a user on a specific property.
        """
        return FavoriteService.get_all_with_report_title_by_property_id_and_user_id(
            g.property_id, g.user.id
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Favorite, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(FavoriteSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, FavoriteSchema)
    def post(self, _: HeaderPropertyIdSchema, favorite: FavoriteSchema):
        """Create a favorite in a property for the user that is authorized

        A favorite gives the ability to quick search their most wanted reports

        **Notes**
        - There is not possible to have 2 ranks with the same value could not be duplicated.
        - The maximum numbers of reports to favorite is 12.
        - If a report is favorited in the rank 1 and there is already one report favorited in
        the rank 1, the latest one will be put in the desired and position and all the rest of
        the elements will be move one position
        """
        return FavoriteService.create(favorite, g.property_id, g.user)


@blp.route("/<int:id>")
class FavoriteById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Favorite, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathFavoriteIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FavoriteSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathFavoriteIdSchema,
        id: int,
    ):
        """Get a Favorite by id

        Get a Favorite by id.
        """
        return FavoriteService.get_by_id_property_id_and_user_id(
            id,
            g.property_id,
            g.user.id,
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Favorite, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathFavoriteIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathFavoriteIdSchema,
        id: int,
    ):
        """Delete favorite

        Delete a favorite that is not used anymore. Unfavorite action for a given report in a property for the user that is authorized
        """
        FavoriteService.delete_by_id_property_id_and_user_id(
            id, g.property_id, g.user.id
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Favorite, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathFavoriteIdSchema, location="path")
    @blp.arguments(RankSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FavoriteSchema)
    def patch(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathFavoriteIdSchema,
        new_rank: dict,
        id: int,
    ):
        """Update a rank

        Update the rank of a favorite. If there is already a favorite in the new position, it will be
        updated with the current rank, swapping the position

        **Notes**
        - There is not possible to have 2 ranks with the same value could not be duplicated.
        - The maximum numbers of reports to favorite is 12.
        - Example: If you want to update a favorite to the rank 1, being your favorite in the position 2. The rank in the
        position 1 will be moved to the 2 and the 2 to the 1
        """
        return FavoriteService.update_rank_by_id_property_id_and_user_id(
            id, new_rank["rank"], g.property_id, g.user.id
        )
