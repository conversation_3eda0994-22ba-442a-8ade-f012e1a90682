from http import HTTPStatus

from flask.views import MethodView

from app.api.v1_1.schemas.chart import (
    ChartsSchema,
    QueryFilterChartSchema,
    QueryFilterSearchChartSchema,
    QuerySortChartSchema,
)
from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.pagination import pagination
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.chart_service import ChartService


blp = Blueprint(
    "Chart",
    "chart",
    url_prefix=f"{API_VERSION}/charts",
    description="Operations on Charts",
)


@blp.route("")
class Chart(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Chart, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryFilterChartSchema, location="query")
    @blp.arguments(QuerySortChartSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartsSchema)
    @pagination("charts")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        filter: QueryFilterChartSchema,
        sort: QuerySortChartSchema,
        _: QueryPaginationSchema,
    ):
        """Get all charts"""
        charts = ChartService.get_all(
            filter, sort, header_property_id.get("property_id")
        )

        return charts


@blp.route("search")
class ChartSearch(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Chart, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryFilterSearchChartSchema, location="query")
    @blp.arguments(QuerySortChartSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartsSchema)
    @pagination("charts")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        chart_query: QueryFilterSearchChartSchema,
        sort_query: QuerySortChartSchema,
        _: QueryPaginationSchema,
    ):
        """Search for a chart"""
        charts = ChartService.get_search_results(
            chart_query, sort_query, header_property_id.get("property_id")
        )

        return charts
