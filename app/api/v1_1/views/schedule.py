from http import HTTPStatus

from flask import g, request
from flask.views import MethodView

from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.api.v1_1.schemas.schedule import (
    CreateScheduleSchema,
    QueryScheduleSchema,
    QuerySortScheduleSchema,
    ScheduleSchema,
    SchedulesSchema,
)
from app.api.v1_1.schemas.task.queue_response import QueueResponseSchema
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.openapi import X_API_KEY
from app.common.smorest import Blueprint
from app.common.user import User
from app.decorators.api_key_or_authorization import api_key_or_authorization
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.pagination import pagination
from app.schemas.authorization import (
    HeaderPropertyIdApiKeySchema,
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.access_control_service import AccessControlService
from app.services.organization_service import OrganizationService
from app.services.schedule_service import ScheduleService
from app.services.task_service import TaskService


blp = Blueprint(
    "Schedules",
    "schedules",
    url_prefix=f"{API_VERSION}/schedules",
    description="Operations on schedules",
)


@blp.route("")
class Schedules(MethodView):
    @api_key_or_authorization
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Schedule, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdApiKeySchema, location="headers")
    @blp.arguments(QuerySortScheduleSchema, location="query")
    @blp.arguments(QueryScheduleSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, SchedulesSchema)
    @pagination("schedules")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query_sort_schedule: QuerySortScheduleSchema,
        query_schedule: QueryScheduleSchema,
        _: QueryPaginationSchema,
    ):
        """Get schedules"""
        if AccessControlService.is_allowed_to_access_properties(
            property_ids=[header_property_id.get("property_id")]
        ):
            return ScheduleService.get_all(
                header_property_id, query_schedule, query_sort_schedule
            )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Schedule, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(CreateScheduleSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ScheduleSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        schedule: ScheduleSchema,
    ):
        """Create a schedule for a report workbook"""
        if AccessControlService.is_allowed_to_access_properties(
            property_ids=[header_property_id.get("property_id")]
        ):
            return ScheduleService.create_schedule(
                schedule=schedule,
                property_id=header_property_id.get("property_id"),
                user_id=g.user.id,
            )


@blp.route("/<int:schedule_id>")
class ScheduleById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Schedule, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ScheduleSchema)
    def get(self, header_property_id: HeaderPropertyIdSchema, schedule_id: int):
        """Get schedule"""
        schedule = ScheduleService.get_by_id_and_property_id(
            header_property_id.get("property_id"), schedule_id
        )
        return schedule

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Schedule, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(CreateScheduleSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ScheduleSchema)
    def put(
        self,
        header_property_id: HeaderPropertyIdSchema,
        update_schedule: CreateScheduleSchema,
        schedule_id: int,
    ):
        """Update a schedule by id"""
        return ScheduleService.update_schedule(
            schedule_id=schedule_id,
            property_id=header_property_id.get("property_id"),
            update_schedule=update_schedule,
            user_id=g.user.id,
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Schedule, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        schedule_id: int,
    ):
        """Delete schedule on a report"""
        return ScheduleService.delete_schedule(
            schedule_id=schedule_id,
            property_id=header_property_id.get("property_id"),
        )


@blp.route("/<int:schedule_id>/run")
class ScheduleByIdReportExportAsync(MethodView):
    @api_key_or_authorization
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Schedule, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdApiKeySchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def post(
        self,
        _header_property_id: HeaderPropertyIdSchema,
        schedule_id: int,
    ):
        """Get report export by id"""
        schedule = ScheduleSchema().dump(ScheduleService.get_by_id(schedule_id))
        if request.headers.get(X_API_KEY):
            g.property_id = schedule["property_id"]
            g.property_ids = []
            g.user = User(
                id=schedule["user_id"],
                email="<EMAIL>",
                admin=False,
            )
            g.organization_id = OrganizationService.get_organization_id_by_property_id(
                g.property_id
            )

        return TaskService.queue_email_report_export_by_id(
            report_ids=[report_id for report_id in schedule["report_ids"]],
            subject=schedule["subject"],
            recipients=schedule["recipients"],
            view=schedule["view"],
            format=schedule["format"],
            schedule_id=schedule_id,
        )
