from http import HTTPStatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.api.v1_1.schemas.search import SearchParamSchema, SearchSchema
from app.common.constants.api_version import API_VERSION
from app.common.smorest import Blueprint
from app.decorators.authorization import authorization
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.search_service import SearchService


blp = Blueprint(
    "Search",
    "search",
    url_prefix=f"{API_VERSION}/search",
    description="Perform Search on reports and stock reports",
)


@blp.route("")
class Search(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(SearchParamSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, SearchSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        params: SearchParamSchema,
        page: QueryPaginationSchema,
    ):
        """Search cross different resources

        Get paginated search result for each requested resource

        **Notes**:
        - Filter values should be formatted column:value, e.g. title:res
        - Valid columns for report resource: title
        - Valid columns for stock_report resource: title
        - API Returns 400 if a column isn't searchable for any requested resource.
        - Rows are returned if column contains values (case-insensitive comparison)
        - The API returns 200 even if no results are found.
        """
        return SearchService.get_search_results(
            user=g.user,
            header_property_id=header_property_id,
            params=params,
            page=page,
        )
