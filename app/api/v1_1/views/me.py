from http import HTTPStatus


from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.me import (
    MeCurrenciesSchema,
    MePermissionsSchema,
    MePolicySchema,
    MePropertySchema,
    MeSchema,
)
from app.common.constants.api_version import API_VERSION
from app.common.constants.permission_service import MFD_ACCESS_REPORT_BUILDER_ACL
from app.common.constants.report_formats import CURRENCIES
from app.common.enums.features import BillingPortalFeatures, LaunchDarklyFeature
from app.common.enums.permissions import Resources
from app.common.smorest import Blueprint
from app.decorators.authorization import authorization
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.acl_service import ACLService
from app.services.launch_darkly_service import LaunchDarklyService
from app.services.permission_service import PermissionService
from app.services.property_feature_service import PropertyFeatureService
from app.services.property_service import PropertyService

blp = Blueprint(
    "Me",
    "me",
    url_prefix=f"{API_VERSION}/me",
    description="Operations on the current user",
)


@blp.route("")
class Me(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, MeSchema)
    def get(self, _: HeaderPropertyIdSchema):
        """Get profile data about the current user"""
        return dict(user_id=g.user.id, email=g.user.email)


@blp.route("properties")
class MeProperties(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, MePropertySchema(many=True))
    def get(self, _: HeaderPropertyIdSchema):
        """Get properties data about the current user"""
        return [
            {
                **property,
                "features": PropertyFeatureService.get_property_features(
                    property_id=property["id"],
                    enabled=True,
                    select_features=[BillingPortalFeatures.ReportBuilder.value],
                ),
            }
            for property in PropertyService.get_all(property_ids=g.property_ids)
        ]


@blp.route("policies")
class MePolicies(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, MePolicySchema(many=True))
    def get(self, _: HeaderPropertyIdSchema):
        """Get policies data about the current user"""
        explore_policy = LaunchDarklyService.get_policy_by_key(
            LaunchDarklyFeature.Explore, g.property_id
        )
        explore_report_policy = LaunchDarklyService.get_policy_by_key(
            LaunchDarklyFeature.ExploreReports, g.property_id
        )
        policies = PermissionService.get_policies(g.user.email)

        acls = [
            dict(
                resource="acls",
                actions=[
                    dict(name=acl)
                    for acl in ACLService.get_all_by_user_id(g.user.id, g.property_id)
                    if acl
                    in [
                        Resources.PaymentReports.value,
                        Resources.FinancialReports.value,
                        Resources.ProductionReports.value,
                        Resources.DailyActivityReports.value,
                        Resources.PoliceReport.value,
                        Resources.HouseKeeping.value,
                        Resources.ReportBuilder.value,
                        MFD_ACCESS_REPORT_BUILDER_ACL,
                    ]
                ],
            )
        ]

        return explore_policy + explore_report_policy + policies + acls


@blp.route("permissions")
class MePermissions(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, MePermissionsSchema)
    def get(self, _: HeaderPropertyIdSchema):
        """Get permissions data about the current user"""
        return PermissionService.get_permissions(g.property_id, g.user)


@blp.route("currencies")
class MeCurrencies(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, MeCurrenciesSchema)
    def get(self, _: HeaderPropertyIdSchema):
        """Get properties data about the current user"""
        return dict(currencies=CURRENCIES)
