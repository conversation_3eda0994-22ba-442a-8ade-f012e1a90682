from http import HTTPStatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.classic_report.classic_report_filters import (
    FilterValuesSchema,
    MappingsSchema,
)
from app.api.v1_1.schemas.classic_report.production.adr import (
    AdrReportSchema,
)
from app.api.v1_1.schemas.classic_report.production.common import (
    CompareYearsReportArgSchema,
    OccupancyFilterNameSchema,
    OccupancyMappingNameSchema,
)
from app.api.v1_1.views.classic_reports.blueprints_config import blp
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.launch_darkly import check_feature_flag
from app.multi_levels.occupancy_reservation import OccupancyReservationsView
from app.schemas.authorization import (
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.classic_reports.mapping_service import MappingService
from app.services.classic_reports.production_reports.adr_service import AdrService


@blp.route("/production_reports/adr")
class Adr(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportADR)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.ProductionReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(CompareYearsReportArgSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, AdrReportSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        json_data: CompareYearsReportArgSchema,
    ):
        """Get ADR Report

        Returns the ADR report. ADR is a performance metric in the hotel industry
        that is calculated by dividing a hotel's total guestroom revenue by the room count
        and the number of days in the period being measured.
        """
        return AdrService.get_adr_data(
            g.organization_id,
            header_property_id["property_id"],
            json_data["report_year"],
            json_data["period_start"],
            json_data["period_end"],
            json_data["grouping"],
            json_data.get("compare_years"),
            json_data.get("room_types"),
            json_data.get("reservation_sources"),
            json_data.get("reservation_source_categories"),
        )


@blp.route("/production_reports/adr/filter_values/<string:filter_name>")
class AdrFilterValues(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportADR)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.ProductionReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(OccupancyFilterNameSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FilterValuesSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: OccupancyFilterNameSchema,
        filter_name: str,
    ):
        """Get Possible Filter Values

        Returns list of possible filter values.
        """
        return AdrService.get_adr_filter_values(
            header_property_id["property_id"], g.organization_id, filter_name
        )


@blp.route("/production_reports/adr/mappings/<string:mapping_name>")
class AdrMappingsByName(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportADR)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.ProductionReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(OccupancyMappingNameSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, MappingsSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: OccupancyMappingNameSchema,
        mapping_name: str,
    ):
        """Get Mappings
        Returns list of mappings for UI.
        """
        return MappingService.get_mappings(
            OccupancyReservationsView,
            mapping_name,
            header_property_id["property_id"],
            g.organization_id,
        )
