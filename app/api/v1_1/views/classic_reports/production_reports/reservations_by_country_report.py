from http import HTTPStatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.classic_report.classic_report import (
    ClassicReportDataSchema,
    ClassicReportQueryParamsSchema,
    ClassicReportSchema,
)
from app.api.v1_1.schemas.classic_report.production.reservations_by_country import (
    ReservationsByCountryReportPicklistQuerySchema,
    ReservationsByCountryReportQuerySchema,
)
from app.api.v1_1.schemas.picklist import PicklistValuesDataSchema
from app.api.v1_1.views.classic_reports.blueprints_config import blp
from app.classic_reports.common.report_titles import ClassicReportTitles
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.launch_darkly import check_feature_flag
from app.enums.dataset import Dataset as DatasetEnum
from app.schemas.authorization import (
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.classic_report_service import ClassicReportService
from app.services.picklist_value_service import PicklistValueService


@blp.route("/production_reports/reservations_by_country_report")
class ReservationsByCountryReport(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportReservationsByCountryReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.ProductionReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
    ):
        """Get Classic Reservations by Country Report

        Get Classic Reservations by Country Report.
        """
        return ClassicReportService.get_report(
            ClassicReportTitles.ReservationsByCountryReport
        )


@blp.route("/production_reports/reservations_by_country_report/data")
class ReservationsByCountryReportData(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportReservationsByCountryReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.ProductionReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ClassicReportQueryParamsSchema, location="query")
    @blp.arguments(ReservationsByCountryReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportDataSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: ClassicReportQueryParamsSchema,
        report: ReservationsByCountryReportQuerySchema,
    ):
        """Query Reservations by Country Report

        Mode:
        - Preview: 100 Records
        - Run: 12000 Records
        - Page: n records

        Offset:
        - User defined offset - how many rows to skip - only for Page mode

        Limit:
        - User defined limit - how many rows to return    - only for Page mode

        Query reservations by country report data from the classic formula
        - Filters Operators and Values
        - Sort
        - Settings

        """
        data = ClassicReportService.query_data(
            ClassicReportTitles.ReservationsByCountryReport,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            report,
        )
        data["offset"] = query.get("offset")
        data["limit"] = query.get("limit")
        return data


@blp.route("/production_reports/reservations_by_country_report/picklist")
class ReservationsByCountryReportPicklist(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportReservationsByCountryReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.ProductionReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReservationsByCountryReportPicklistQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, PicklistValuesDataSchema)
    def post(
        self,
        header: HeaderPropertyIdSchema,
        report: ReservationsByCountryReportPicklistQuerySchema,
    ):
        """Get the CDF (Cloudbeds Data Field) picklist options"""

        reservations_by_country_report = ClassicReportService.get_report(
            ClassicReportTitles.ReservationsByCountryReport
        )

        return PicklistValueService.get_picklist_values_by_dataset_id_and_cdfs(
            DatasetEnum.Reservations.value,
            report["group_rows"],
            g.organization_id,
            [header["property_id"]],
            report.get("filters"),
            reservations_by_country_report.get("custom_cdfs"),
        )
