from http import HTTPStatus

from flask import g
from flask.views import MethodView


from app.api.v1_1.schemas.classic_report.daily_financial_report import (
    DailyFinancialForecastSchema,
    DailyFinancialOccupancyRateSchema,
    DailyFinancialOnTheBooksForecastSchema,
    DailyFinancialReportQueryParamsSchema,
    DailyFinancialRoomRevenueSchema,
    DailyStatisticsReportSchema,
)
from app.api.v1_1.views.classic_reports.blueprints_config import blp
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.launch_darkly import check_feature_flag
from app.schemas.authorization import (
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.classic_reports.financial_reports.daily_financial_report_service import (
    DailyFinancialReportService,
)


@blp.route("/daily_financial_report/daily_statistics")
class DailyFinancialReportDailyStatistics(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportDailyFinancial)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.FinancialReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(DailyFinancialReportQueryParamsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DailyStatisticsReportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query_params: DailyFinancialReportQueryParamsSchema,
    ):
        """Get Daily Financial Statistics Report

        Returns the daily statistics report which represents a section of the Daily Financial Report on the Classic Report.
        """

        return DailyFinancialReportService.get_statistics_report(
            header_property_id["property_id"],
            g.organization_id,
            query_params["date"],
        )


@blp.route("/daily_financial_report/occupancy_rate")
class DailyFinancialReportOccupancyRate(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportDailyFinancial)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.FinancialReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(DailyFinancialReportQueryParamsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DailyFinancialOccupancyRateSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query_params: DailyFinancialReportQueryParamsSchema,
    ):
        """Get Daily Financial Occupancy Rate Report

        Returns the occupancy rate report which represents a section of the Daily Financial Report on the Classic Report.
        """
        return DailyFinancialReportService.get_occupancy_rate(
            header_property_id["property_id"],
            g.organization_id,
            query_params["date"],
        )


@blp.route("/daily_financial_report/room_revenue")
class DailyFinancialReportRoomRevenue(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportDailyFinancial)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.FinancialReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(DailyFinancialReportQueryParamsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DailyFinancialRoomRevenueSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query_params: DailyFinancialReportQueryParamsSchema,
    ):
        """Get Daily Financial Room Revenue Report

        Returns the room revenue report which represents a section of the Daily Financial Report on the Classic Report.
        """
        return DailyFinancialReportService.get_room_revenue(
            header_property_id["property_id"],
            g.organization_id,
            query_params["date"],
        )


@blp.route("/daily_financial_report/room_revenue_14_days_forecast")
class DailyFinancialReportRoomRevenueForecast(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportDailyFinancial)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.FinancialReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(DailyFinancialReportQueryParamsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DailyFinancialForecastSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query_params: DailyFinancialReportQueryParamsSchema,
    ):
        """Get Daily Financial Room Revenue 14 days Forecast Report

        Returns the room revenue 14 days forecast report which represents a section of the Daily Financial Report on the Classic Report.
        """
        return DailyFinancialReportService.get_room_revenue_14_days_forecast(
            header_property_id["property_id"],
            g.organization_id,
            query_params["date"],
        )


@blp.route("/daily_financial_report/on_the_books_forecast")
class DailyFinancialReportOnTheBooksForecast(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportDailyFinancial)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.FinancialReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(DailyFinancialReportQueryParamsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DailyFinancialOnTheBooksForecastSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query_params: DailyFinancialReportQueryParamsSchema,
    ):
        """Get Daily Financial On the books Forecast Report

        Returns the room revenue on the books forecast report which represents a section of the Daily Financial Report on the Classic Report.
        """
        return DailyFinancialReportService.get_on_the_books_forecast(
            header_property_id["property_id"],
            g.organization_id,
            query_params["date"],
        )
