from http import HTTPStatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.classic_report.classic_report import (
    ClassicReportDataSchema,
    ClassicReportQueryParamsSchema,
    ClassicReportSchema,
    ClassicReportSummarySchema,
)
from app.api.v1_1.schemas.classic_report.daily_activity_reports.payment_reconciliation_report import (
    PaymentReconciliationReportPicklistQuerySchema,
    PaymentReconciliationReportQuerySchema,
)
from app.api.v1_1.schemas.picklist import (
    PicklistValuesDataSchema,
)
from app.api.v1_1.views.classic_reports.blueprints_config import blp
from app.classic_reports.common.report_titles import ClassicReportTitles
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.launch_darkly import check_feature_flag
from app.enums.dataset import Dataset as DatasetEnum
from app.schemas.authorization import (
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.classic_report_service import ClassicReportService
from app.services.picklist_value_service import PicklistValueService


@blp.route("/daily_activity_reports/payment_reconciliation_report")
class PaymentReconciliationReport(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportPaymentReconciliationReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.DailyActivityReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
    ):
        """Get Classic PaymentReconciliation Report

        Get Classic PaymentReconciliation Report.
        """
        return ClassicReportService.get_report(
            ClassicReportTitles.PaymentReconciliationReport
        )


@blp.route("/daily_activity_reports/payment_reconciliation_report/data")
class PaymentReconciliationReportData(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportPaymentReconciliationReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.DailyActivityReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ClassicReportQueryParamsSchema, location="query")
    @blp.arguments(PaymentReconciliationReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportDataSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: ClassicReportQueryParamsSchema,
        report: PaymentReconciliationReportQuerySchema,
    ):
        """Query PaymentReconciliation Report

        Mode:
        - Preview: 100 Records
        - Run: 12000 Records
        - Page: n records

        Offset:
        - User defined offset - how many rows to skip - only for Page mode

        Limit:
        - User defined limit - how many rows to return    - only for Page mode

        Query payment reconciliation report data from the classic formula
        - Filters Operators and Values
        - Sort
        - Settings

        """
        data = ClassicReportService.query_data(
            ClassicReportTitles.PaymentReconciliationReport,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            report,
        )
        return data


@blp.route("/daily_activity_reports/payment_reconciliation_report/summary")
class PaymentReconciliationReportSummary(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportPaymentReconciliationReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.DailyActivityReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ClassicReportQueryParamsSchema, location="query")
    @blp.arguments(PaymentReconciliationReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportSummarySchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: ClassicReportQueryParamsSchema,
        report: PaymentReconciliationReportQuerySchema,
    ):
        """Query PaymentReconciliation Report

        Offset:
        - how many rows to skip - only for Page mode

        Limit:
        - how many rows to return    - only for Page mode

        Query payment reconciliation report data from the classic formula
        - Filters Operators and Values
        - Sort
        - Settings

        """
        return ClassicReportService.query_summary(
            ClassicReportTitles.PaymentReconciliationReport,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            report,
        )


@blp.route("/daily_activity_reports/payment_reconciliation_report/picklist")
class PaymentReconciliationReportPicklist(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportPaymentReconciliationReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.DailyActivityReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PaymentReconciliationReportPicklistQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, PicklistValuesDataSchema)
    def post(
        self,
        header: HeaderPropertyIdSchema,
        report: PaymentReconciliationReportPicklistQuerySchema,
    ):
        """Get the CDF (Cloudbeds Data Field) picklist options"""

        payment_reconciliation_report = ClassicReportService.get_report(
            ClassicReportTitles.PaymentReconciliationReport
        )

        return PicklistValueService.get_picklist_values_by_dataset_id_and_cdfs(
            DatasetEnum.Financial.value,
            report["group_rows"],
            g.organization_id,
            [header["property_id"]],
            report.get("filters"),
            payment_reconciliation_report.get("custom_cdfs"),
        )
