from http import HTTPStatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.classic_report.classic_report import (
    ClassicReportDataSchema,
    ClassicReportQueryParamsSchema,
    ClassicReportSchema,
    ClassicReportSummarySchema,
)
from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportQuerySchema,
)
from app.api.v1_1.views.classic_reports.blueprints_config import blp
from app.classic_reports.common.report_titles import ClassicReportTitles
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.launch_darkly import check_feature_flag
from app.schemas.authorization import (
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.classic_report_service import ClassicReportService


@blp.route("/daily_activity_reports/cancellation_report")
class CancellationReport(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportCancellationReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.DailyActivityReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
    ):
        """Get Classic Cancellation Report

        Get Classic Cancellation Report.
        """
        return ClassicReportService.get_report(ClassicReportTitles.CancellationReport)


@blp.route("/daily_activity_reports/cancellation_report/data")
class CancellationReportData(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportCancellationReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.DailyActivityReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ClassicReportQueryParamsSchema, location="query")
    @blp.arguments(ClassicReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportDataSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: ClassicReportQueryParamsSchema,
        report: ClassicReportQuerySchema,
    ):
        """Query Cancellation Report

        Mode:
        - Preview: 100 Records
        - Run: 12000 Records
        - Page: n records

        Offset:
        - User defined offset - how many rows to skip - only for Page mode

        Limit:
        - User defined limit - how many rows to return    - only for Page mode

        Query in-house report data from the classic formula
        - Filters Operators and Values
        - Sort
        - Settings

        """
        data = ClassicReportService.query_data(
            ClassicReportTitles.CancellationReport,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            report,
        )
        data["offset"] = query.get("offset")
        data["limit"] = query.get("limit")
        return data


@blp.route("/daily_activity_reports/cancellation_report/summary")
class CancellationReportSummary(MethodView):
    @check_feature_flag(LaunchDarklyFeature.ClassicReportCancellationReport)
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.DailyActivityReports, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ClassicReportQueryParamsSchema, location="query")
    @blp.arguments(ClassicReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ClassicReportSummarySchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: ClassicReportQueryParamsSchema,
        report: ClassicReportQuerySchema,
    ):
        """Query Cancellation Report

        Offset:
        - how many rows to skip - only for Page mode

        Limit:
        - how many rows to return    - only for Page mode

        Query in-house report data from the classic formula
        - Filters Operators and Values
        - Sort
        - Settings

        """
        return ClassicReportService.query_summary(
            ClassicReportTitles.CancellationReport,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            report,
        )
