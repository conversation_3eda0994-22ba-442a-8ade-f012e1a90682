from http import HTTPStatus

from flask.views import MethodView

from app.api.v1_1.schemas.liveness import LivenessSchema
from app.common.constants.api_version import API_VERSION
from app.common.smorest import Blueprint
from app.schemas.error import ErrorSchema


blp = Blueprint(
    "Liveness",
    "liveness",
    url_prefix=f"{API_VERSION}/liveness",
    description="Endpoint for service liveness",
)


@blp.route("")
class Liveness(MethodView):
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, LivenessSchema)
    def get(self):
        """Get the liveness probe of the service"""
        return {"live": True}
