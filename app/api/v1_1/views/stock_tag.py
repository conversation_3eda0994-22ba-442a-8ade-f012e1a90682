from http import HTTPStatus

from flask.views import MethodView

from app.api.v1_1.schemas.stock_tag.stock_tag import StockTagSchema
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.exceptions import InvalidUsage
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.stock_report_service import StockReportService
from app.services.stock_tag_service import StockTagService


blp = Blueprint(
    "Stock Tags",
    "stock tags",
    url_prefix=f"{API_VERSION}/stock_tags",
    description="Operations on stock tags",
)


@blp.route("")
class StockTags(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.response(HTTPStatus.OK, StockTagSchema(many=True))
    def get(self, _: HeaderPropertyIdSchema):
        """Get all stock tags

        Get all stock tags created by cloudbeds
        """
        stock_tags = StockTagService.get_all()
        return stock_tags

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(StockTagSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, StockTagSchema)
    def post(self, stock_tag: StockTagSchema):
        """Create a stock tag

        Create a stock tag
        """
        stock_tag = StockTagService.create(stock_tag)
        return stock_tag


@blp.route("/<int:stock_tag_id>")
class StockTag(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockTagSchema)
    def get(self, _: HeaderPropertyIdSchema, stock_tag_id: int):
        """Get a stock tag

        Get a stock tag by ID
        """
        stock_tag = StockTagService.get_by_id_or_404(stock_tag_id)
        return stock_tag

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockTagSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockTagSchema)
    def put(
        self, _: HeaderPropertyIdSchema, stock_tag: StockTagSchema, stock_tag_id: int
    ):
        """Update a stock tag

        Update a stock tag by ID
        """
        stock_tag = StockTagService.update(stock_tag_id, stock_tag)
        return stock_tag

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(self, _: HeaderPropertyIdSchema, stock_tag_id: int):
        """Delete a stock tag

        Delete a stock tag by ID
        """

        existing_stock_reports = StockReportService.get_by_stock_tag_id(stock_tag_id)
        if existing_stock_reports:
            raise InvalidUsage.bad_request("Cannot delete a stock tag that is in use")

        StockTagService.delete(stock_tag_id)
