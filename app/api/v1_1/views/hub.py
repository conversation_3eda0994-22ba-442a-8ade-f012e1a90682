from http import HTTPStatus

from flask.views import MethodView

from app.api.v1_1.schemas.hub import (
    CardSchema,
    CreateCardSchema,
    CreateHubSchema,
    HubCardIdSchema,
    HubIdSchema,
    HubSchema,
    HubsSchema,
    QueryFilterHubsSchema,
    QuerySearchHubsSchema,
    QuerySortHubsSchema,
)
from app.api.v1_1.schemas.log import HubLogSchema
from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.logger import logger
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.pagination import pagination
from app.enums.favorite import FavoriteKind
from app.enums.log_actions import HubAction
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.favorite_service import FavoriteService
from app.services.hub_service import HubService


blp = Blueprint(
    "Hub",
    "hub",
    url_prefix=f"{API_VERSION}/hubs",
    description="Operations on Hubs",
)


@blp.route("")
class Hub(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Hub, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(CreateHubSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, HubSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        hub: CreateHubSchema,
    ):
        """Create a hub"""
        hub = HubService.create_hub(hub, header_property_id.get("property_id"))
        logger.info(
            "Create Hub",
            extra=HubLogSchema().dump(
                {
                    **HubSchema().dump(hub),
                    **dict(
                        action=HubAction.Create.value,
                    ),
                }
            ),
        )
        return hub

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Hub, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryFilterHubsSchema, location="query")
    @blp.arguments(QuerySortHubsSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, HubsSchema)
    @pagination("hubs")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        filters: QueryFilterHubsSchema,
        sort: QuerySortHubsSchema,
        _: QueryPaginationSchema,
    ):
        """Get all hubs"""
        hubs = HubService.get_all(header_property_id.get("property_id"), filters, sort)

        return hubs


@blp.route("<int:hub_id>")
class HubById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Hub, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(HubIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, HubSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: HubIdSchema,
        hub_id: int,
    ):
        """Get a hub by id"""
        hub = HubService.get_hub_by_id(hub_id, header_property_id.get("property_id"))
        logger.info(
            "Read Hub",
            extra=HubLogSchema().dump(
                {
                    **HubSchema().dump(hub),
                    **dict(
                        action=HubAction.Read.value,
                    ),
                }
            ),
        )

        return hub

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Hub, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(CreateHubSchema, location="json")
    @blp.arguments(HubIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, HubSchema)
    def put(
        self,
        header_property_id: HeaderPropertyIdSchema,
        hub: CreateHubSchema,
        path: HubIdSchema,
        hub_id: int,
    ):
        """Update a hub by id"""
        hub = HubService.update_hub_by_id(
            hub_id, hub, header_property_id.get("property_id")
        )
        logger.info(
            "Update Hub",
            extra=HubLogSchema().dump(
                {
                    **HubSchema().dump(hub),
                    **dict(
                        action=HubAction.Update.value,
                    ),
                }
            ),
        )
        return hub

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Hub, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(HubIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: HubIdSchema,
        hub_id: int,
    ):
        """Delete a hub by id"""
        favorites_to_clear = FavoriteService.get_all_by_kind_and_entity_id(
            FavoriteKind.Hub.value, hub_id
        )

        logger.info(
            "Delete Hub",
            extra=HubLogSchema().dump(
                {
                    **dict(
                        id=hub_id,
                        action=HubAction.Read.value,
                    ),
                }
            ),
        )
        HubService.delete_hub_by_id(hub_id, header_property_id.get("property_id"))
        FavoriteService.clear_user_caches_for_favorites(favorites_to_clear)


@blp.route("search")
class HubSearch(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Hub, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QuerySearchHubsSchema, location="query")
    @blp.arguments(QuerySortHubsSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, HubsSchema)
    @pagination("hubs")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        filters: QuerySearchHubsSchema,
        sort: QuerySortHubsSchema,
        _: QueryPaginationSchema,
    ):
        """Get all hubs"""
        hubs = HubService.search_hubs(
            header_property_id.get("property_id"), filters, sort
        )

        return hubs


@blp.route("<int:hub_id>/cards")
class Card(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Hub, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(HubIdSchema, location="path")
    @blp.arguments(CreateCardSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, CardSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: HubIdSchema,
        card: CreateCardSchema,
        hub_id: int,
    ):
        """Create a card linking a hub to a chart"""
        card = HubService.create_card(
            card, hub_id, header_property_id.get("property_id")
        )

        return card

    @blp.route("<int:hub_id>/cards/<int:card_id>")
    class CardById(MethodView):
        @has_property_permission()
        @check_user_has_permission(
            PermissionAction.VIEW, Resources.Hub, PermissionRole.VIEWER
        )
        @blp.arguments(HeaderPropertyIdSchema, location="headers")
        @blp.arguments(HubCardIdSchema, location="path")
        @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
        @blp.response(HTTPStatus.OK, CardSchema)
        def get(
            self,
            header_property_id: HeaderPropertyIdSchema,
            path: HubCardIdSchema,
            hub_id: int,
            card_id: int,
        ):
            """Get a card by id"""
            card = HubService.get_card_by_id(
                hub_id, card_id, header_property_id.get("property_id")
            )

            return card

        @has_property_permission()
        @check_user_has_permission(
            PermissionAction.UPDATE, Resources.Hub, PermissionRole.VIEWER
        )
        @blp.arguments(HeaderPropertyIdSchema, location="headers")
        @blp.arguments(HubCardIdSchema, location="path")
        @blp.arguments(CreateCardSchema, location="json")
        @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
        @blp.response(HTTPStatus.OK, CardSchema)
        def put(
            self,
            header_property_id: HeaderPropertyIdSchema,
            path: HubCardIdSchema,
            card: CreateCardSchema,
            hub_id: int,
            card_id: int,
        ):
            """Update a card by id"""
            card = HubService.update_card_by_id(
                hub_id, card_id, card, header_property_id.get("property_id")
            )

            return card

        @has_property_permission()
        @check_user_has_permission(
            PermissionAction.DELETE, Resources.Hub, PermissionRole.VIEWER
        )
        @blp.arguments(HeaderPropertyIdSchema, location="headers")
        @blp.arguments(HubCardIdSchema, location="path")
        @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
        @blp.response(HTTPStatus.NO_CONTENT)
        def delete(
            self,
            header_property_id: HeaderPropertyIdSchema,
            path: HubCardIdSchema,
            hub_id: int,
            card_id: int,
        ):
            """Delete a card by id"""
            card = HubService.delete_card_by_id(
                hub_id, card_id, header_property_id.get("property_id")
            )

            return card
