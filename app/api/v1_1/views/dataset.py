from http import HTT<PERSON>tatus

from flask import g
from flask.views import <PERSON><PERSON>iew

from flask_babel import lazy_gettext as _

from app.api.v1_1.schemas.cdf import CDFOptionsSchema
from app.api.v1_1.schemas.custom_fields import (
    CustomFieldsSchema,
)
from app.api.v1_1.schemas.dataset import (
    DatasetByIdSchema,
    DatasetByIdUpdatedAtSchema,
    DatasetExploreSchema,
    DatasetsSchema,
    GetDatasetByIdQueryParamsSchema,
    GetPropertyIdsQueryParamsSchema,
    PathDatasetByIdCdfByNameSchema,
    PathDatasetByIdMultiLevelByIdSchema,
    PathDatasetByIdMultilevelByIdCdfByNameSchema,
    PathDatasetByIdSchema,
)
from app.api.v1_1.schemas.explore import QuestionExploreSchema
from app.api.v1_1.schemas.multi_level import DatasetMultiLevelSchema, MultiLevelSchema
from app.cdfs.cdf import CDF
from app.common.cache import TIMEOUT_DAY, TIMEOUT_HOUR, cache
from app.common.constants.api_version import API_VERSION
from app.common.exceptions import InvalidUsage
from app.common.keys import dataset_cdf_options, datasets_cdfs_by_property_and_user
from app.common.smorest import Blueprint
from app.datasets.dataset import Dataset
from app.decorators.authorization import (
    authorization,
)
from app.enums.dataset import Dataset as DatasetEnum
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.enums.report import ReportKind
from app.schemas.authorization import HeaderAcceptLanguageSchema, HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.dataset_service import DatasetService
from app.services.explore_service import ExploreService
from app.services.multi_level_service import MultiLevelService
from app.services.property_feature_service import PropertyFeatureService
from app.services.property_service import PropertyService
from app.services.stock_report_service import StockReportService


blp = Blueprint(
    "Datasets",
    "datasets",
    url_prefix=f"{API_VERSION}/datasets",
    description="Operations on Datasets",
)


@blp.route("")
class Datasets(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(HeaderAcceptLanguageSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DatasetsSchema(many=True))
    def get(
        self,
        property_id: HeaderPropertyIdSchema,
        accept_language: HeaderAcceptLanguageSchema,
    ):
        """Get Datasets

        Obtain all the available Cloudbeds Datasets


        | Dataset                                     | Dataset Name                               | Description                                                                                                                                                                                       |
        | ------                                      | ------                                     | ----------                                                                                                                                                                                        |
        | Financial                                   | Financial                                  | Financial transactions that are made, including debits and credit and their attributes                                                                                                            |
        | Guest                                       | Guests                                     | The details on each guest at the hotel, including the history if their stays, their contact information, document information, etc                                                                |
        | Reservations                                | Reservations                               | Booking or Reservation information include who booked, where the booking came from, when it occurs, the price, the rate plans, room types, etc                                                    |
        | Occupancy (Legacy - Unsupported)            | Occupancy (Legacy - Unsupported)           | The productivity information for the hotel, including metrics such as occupancy, room nights, Revpar, ADR, etc.  Used by revenue managers to set prices and distribution strategies               |
        | Payments                                    | Payment                                    | Payment processing information, including charges, fees, chargebacks, reversals, etc.
        | Invoices                                    | Invoices                                   | Invoices information, including reservation level information etc and credit notes.
        | Occupancy                                   | Occupancy                                  | The productivity information for the hotel, including metrics such as occupancy, room nights, Revpar, ADR, etc.  Used by revenue managers to set prices and distribution strategies               |
        | Housekeeping                                | Housekeeping                               | ...
        """

        return DatasetService.get_datasets_by_property_id(
            property_id.get("property_id"),
            g.user.email,
            accept_language.get("ACCEPT-LANGUAGE"),
        )


@blp.route("explore")
class DatasetExplore(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QuestionExploreSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DatasetExploreSchema)
    def get(
        self,
        header: HeaderPropertyIdSchema,
        query: QuestionExploreSchema,
    ):
        """Get Dataset based on natural language question

        ...
        """

        return ExploreService.explore_dataset(
            query["question"], header["property_id"], g.user
        )


@blp.route("/<int:dataset_id>")
class DatasetById(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(GetDatasetByIdQueryParamsSchema, location="query")
    @blp.arguments(PathDatasetByIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DatasetByIdSchema)
    def get(
        self,
        header: HeaderPropertyIdSchema,
        query: GetDatasetByIdQueryParamsSchema,
        _: PathDatasetByIdSchema,
        dataset_id: int,
    ):
        """Get Dataset by id

        Obtain all the CDF information related with each Dataset and the last time the dataset has been refreshed
        If categories is true, CDFs will be grouped by category

        **CDF**: Cloudbeds Data field.

        | Dataset                          | Dataset Id  | Description                                                                                                                                                                                       |
        | ------                           | ------      | ----------                                                                                                                                                                                        |
        | Financial                        | 1           | Financial transactions that are made, including debits and credit and their attributes                                                                                                            |
        | Guest                            | 2           | The details on each guest at the hotel, including the history if their stays, their contact information, document information, etc                                                                |
        | Reservations                     | 3           | Booking or Reservation information include who booked, where the booking came from, when it occurs, the price, the rate plans, room types, etc                                                    |
        | Occupancy (Legacy - Unsupported) | 4           | The productivity information for the hotel, including metrics such as occupancy, room nights, Revpar, ADR, etc.  Used by revenue managers to set prices and distribution strategies               |
        | Payments                         | 5           | Payment processing information, including charges, fees, chargebacks, reversals, etc.
        | Invoices                         | 6           | Invoices information, including reservation level information etc and credit notes.
        | Occupancy                        | 7           | The productivity information for the hotel, including metrics such as occupancy, room nights, Revpar, ADR, etc.  Used by revenue managers to set prices and distribution strategies               |
        | Housekeeping                     | 8           | ...               |
        """
        dataset_cdfs_key = datasets_cdfs_by_property_and_user(
            header["property_id"], g.user.email, dataset_id, query.get("cdfs")
        )
        dataset_by_id = cache.get(dataset_cdfs_key)

        if dataset_by_id is not None:
            return dataset_by_id

        dataset = Dataset(DatasetEnum(dataset_id))

        property_datasets = DatasetService.get_datasets_by_property_id(
            header["property_id"], g.user.email
        )

        dataset_cdfs = None
        for property_dataset in property_datasets:
            if property_dataset["id"] == dataset_id:
                dataset_cdfs = property_dataset

        if not dataset_cdfs:
            raise InvalidUsage.forbidden(
                f"Property does not have access to dataset ID {dataset_id}"
            )

        if query.get("cdfs") == ReportKind.StockReport.value:
            dataset_cdfs = dict(
                **dataset_cdfs,
                cdfs=StockReportService.get_stock_report_cdfs(
                    header["property_id"], dataset
                ),
            )
        else:
            dataset_cdfs = dict(**dataset_cdfs, cdfs=dataset.non_feature_cdfs)

        cache.set(dataset_cdfs_key, dataset_cdfs, timeout=TIMEOUT_HOUR)
        return dataset_cdfs


@blp.route("/<int:dataset_id>/updated_at")
class DatasetByIdUpdatedAt(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathDatasetByIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DatasetByIdUpdatedAtSchema)
    def get(
        self,
        header: HeaderPropertyIdSchema,
        _: PathDatasetByIdSchema,
        dataset_id: int,
    ):
        """Get Dataset by id Updated At

        Obtain the last time your property receive an event based on the Dataset ID.
        For example if the Dataset ID is Reservation it will show the last time DI received a reservation. If you have a lag bigger than 5 minutes please raise a ZD Ticket.

        **CDF**: Cloudbeds Data field.

        | Dataset                           | Dataset Id  | Description                                                                                                                                                                                       |
        | ------                            | ------      | ----------                                                                                                                                                                                        |
        | Financial                         | 1           | Financial transactions that are made, including debits and credit and their attributes                                                                                                            |
        | Guest                             | 2           | The details on each guest at the hotel, including the history if their stays, their contact information, document information, etc                                                                |
        | Reservations                      | 3           | Booking or Reservation information include who booked, where the booking came from, when it occurs, the price, the rate plans, room types, etc                                                    |
        | Occupancy (Legacy - Unsupported)  | 4           | The productivity information for the hotel, including metrics such as occupancy, room nights, Revpar, ADR, etc.  Used by revenue managers to set prices and distribution strategies               |
        | Payments                          | 5           | Payment processing information, including charges, fees, chargebacks, reversals, etc.
        | Invoices                          | 6           | Invoices information, including reservation level information etc and credit notes.
        | Occupancy                         | 7           | The productivity information for the hotel, including metrics such as occupancy, room nights, Revpar, ADR, etc.  Used by revenue managers to set prices and distribution strategies               |
        | Housekeeping                      | 8           | ...               |
        """

        updated_at = DatasetService.get_dataset_updated_at_by_property_id(
            DatasetEnum(dataset_id), header["property_id"], g.organization_id
        )

        return {"updated_at": updated_at}


@blp.route("/<int:dataset_id>/multi_levels")
class DatasetMultiLevelsByDatasetId(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathDatasetByIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, DatasetMultiLevelSchema(many=True))
    def get(self, header: _, path: PathDatasetByIdSchema, dataset_id: int):
        """List of multi-levels per dataset.

        Obtain list of all multi-levels associated with particular datasets.

        Some datasets might live alone without any levels associated with them.

            Reservations (Dataset)
            │
            └───Room Reservation (Multi-level)
                │
                └─── Room Check-in (CDF)
                │
                └─── Room Check-out (CDF)
                │
                └─── Room Reservation Status (CDF)

        """
        property_datasets = DatasetService.get_datasets_by_property_id(
            header["property_id"], g.user.email
        )

        if dataset_id not in [dataset["id"] for dataset in property_datasets]:
            raise InvalidUsage.forbidden(
                f"Property does not have access to dataset ID {dataset_id}"
            )

        return MultiLevelService.get_multi_levels_by_dataset_id(dataset_id)


@blp.route("/<int:dataset_id>/multi_levels/<int:multi_level_id>")
class DatasetMultilevelsByIdByMultiLevelId(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathDatasetByIdMultiLevelByIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, MultiLevelSchema)
    def get(
        self,
        header: _,
        path: PathDatasetByIdMultiLevelByIdSchema,
        dataset_id: int,
        multi_level_id: int,
    ):
        """List of multi-level CDFs associated by dataset.

        Obtain list of all CDFs (Part of multi-level) associated with particular dataset.

        Some datasets might live alone without any levels associated with them.

            Reservations (Dataset)
            │
            └───Room Reservation (Multi-level)
                │
                └─── Room Check-in (CDF)
                │
                └─── Room Check-out (CDF)
                │
                └─── Room Reservation Status (CDF)

        """
        property_datasets = DatasetService.get_datasets_by_property_id(
            header["property_id"], g.user.email
        )

        if dataset_id not in [dataset["id"] for dataset in property_datasets]:
            raise InvalidUsage.forbidden(
                f"Property does not have access to dataset ID {dataset_id}"
            )

        return MultiLevelService.get_multi_level_by_id(multi_level_id)


@blp.route("/<int:dataset_id>/<string:cdf>")
class DatasetByIdCdfByName(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathDatasetByIdCdfByNameSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, CDFOptionsSchema)
    def get(
        self,
        header: HeaderPropertyIdSchema,
        _: PathDatasetByIdCdfByNameSchema,
        dataset_id: int,
        cdf: str,
    ):
        """Get the CDF (Cloudbeds Data Field) picklist options"""
        dataset_cdf = DatasetService.get_cdf_options(
            header["property_id"], dataset_id, cdf
        )
        return dataset_cdf


@blp.route("/<int:dataset_id>/multi_levels/<int:multi_level_id>/<string:cdf>")
class DatasetByIdMultilevelByIdCdfByName(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathDatasetByIdMultilevelByIdCdfByNameSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, CDFOptionsSchema)
    def get(
        self,
        header: HeaderPropertyIdSchema,
        _: PathDatasetByIdMultilevelByIdCdfByNameSchema,
        dataset_id: int,
        multi_level_id: int,
        cdf: str,
    ):
        """Get the CDF (Cloudbeds Data Field) picklist options for Multi level"""
        # TODO: We need to get the cdfs here
        dataset_cdf_options_key = dataset_cdf_options(
            header["property_id"], dataset_id, cdf
        )

        cdf_options = cache.get(dataset_cdf_options_key)
        category_feature = None

        if cdf_options is not None:
            return cdf_options

        country_code = PropertyService.get_property_country_code(header["property_id"])

        cdf = CDF(
            DatasetEnum(dataset_id), cdf, multi_level=MultiLevelEnum(multi_level_id)
        )
        dataset = Dataset(DatasetEnum(dataset_id))

        if bool(dataset.feature_categories):
            property_features = PropertyFeatureService.get_all_by_property_id(
                header["property_id"]
            )

            category_feature = next(
                (
                    category["feature"]
                    for category in dataset.feature_categories
                    if cdf.category == category["category"]
                ),
                None,
            )

        if bool(dataset.country_categories):
            category_country = next(
                (
                    category["country_code"]
                    for category in dataset.country_categories
                    if cdf.category == category["category"]
                ),
                None,
            )
            if category_feature and category_feature not in property_features:
                return InvalidUsage.not_authorized(
                    "Property does not have access to this cdf"
                )

            if category_country and category_country != country_code:
                return InvalidUsage.not_authorized(
                    "CDF is not applicable to property country"
                )

        dataset_cdf = dict(**cdf.cdf, options=cdf.options)
        cache.set(dataset_cdf_options_key, dataset_cdf, timeout=TIMEOUT_DAY)
        return dataset_cdf


@blp.route("/<int:dataset_id>/property_custom_fields")
class DatasetCustomFieldsByDatasetId(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(GetPropertyIdsQueryParamsSchema, location="query")
    @blp.arguments(PathDatasetByIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, CustomFieldsSchema)
    def get(
        self,
        header: _,
        query: GetPropertyIdsQueryParamsSchema,
        path: PathDatasetByIdSchema,
        dataset_id: int,
    ):
        """List of Custom Fields as CDFs per dataset.

        Obtain list of all Custom Fields associated with particular datasets.

        Some datasets might live alone without any Custom Fields associated with them.

        """
        return DatasetService.get_property_custom_fields_by_dataset_id(
            dataset_id,
            g.organization_id,
            g.property_id,
            list(set(query.get("property_ids", []) + g.property_ids)),
        )
