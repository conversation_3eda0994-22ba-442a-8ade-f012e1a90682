from http import HTTPStatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.folder import (
    FolderSchema,
    PathFolderIdReportIdSchema,
    PathFolderIdSchema,
    ReportIdSchema,
)
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import (
    PermissionAction,
    PermissionRole,
    Resources,
)
from app.common.exceptions import InvalidUsage
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.models.folder import Folder
from app.models.report import Report
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema


blp = Blueprint(
    "Folders",
    "folders",
    url_prefix=f"{API_VERSION}/folders",
    description="Operations on folders",
)


@blp.route("")
class Folders(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Folder, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FolderSchema(many=True))
    def get(self, header_property_id: HeaderPropertyIdSchema):
        """List of Folders per property Id

        Obtain a list of all the created folders under a property
        """
        folders = Folder.get_all_by_property_id(header_property_id.get("property_id"))
        return folders

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Folder, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(FolderSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, FolderSchema)
    def post(self, header_property_id: HeaderPropertyIdSchema, folder: FolderSchema):
        """Create a folder for a property

        Folders give the ability to the user to organize and find better their reports

        **Notes**
        - A Folder can not have the same name
        - A Folder may have a parent or may not
        - The max level of nested folders is 3. Below there is an example of the max level allowed

        ```
        Financial
        │
        └───Bar
            │
            └───Food

        ```
        """
        property_id = header_property_id.get("property_id")
        existing_folder = Folder.get_by_name_and_property_id(
            name=folder["name"], property_id=property_id
        )

        if existing_folder is not None:
            return InvalidUsage.conflict(
                message=f"A folder with name: {folder['name']} already exist"
            )

        if folder["parent_id"] is not None:
            existing_parent = Folder.get_by_id_and_property_id(
                folder["parent_id"], property_id
            )

            if existing_parent is None:
                return InvalidUsage.not_found(
                    message=f"A Folder with id: {folder['parent_id']} does not exist"
                )

            if Folder.is_max_level_depth(folder["parent_id"]):
                return InvalidUsage.bad_request(
                    message="Is not possible to create this folder under the provided parent_id because the maximum level depth is 3"
                )

        folder = Folder.create(**folder, user_id=g.user.id, property_id=g.property_id)
        return folder


@blp.route("/<int:id>")
class FolderById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Folder, PermissionRole.VIEWER
    )
    @blp.arguments(PathFolderIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathFolderIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        id: int,
    ):
        """Delete folder by id

        Delete a folder that is not used anymore

        **Notes**
        - To delete a folder, the folder should not have assigned a report
        - To delete a folder, the folder should not have a parent_id
        """

        folder = Folder.get_by_id_and_property_id(
            id, header_property_id.get("property_id")
        )

        if Report.are_reports_assign_to_folder(id):
            return InvalidUsage.bad_request(
                message="This folder cannot be deleted. Please contact your administrator to proceed."
            )

        if Folder.are_folders_assign_to_folder(id):
            return InvalidUsage.bad_request(
                message="All the folders assigned to this folder needs to be reassign before deleting it"
            )

        folder.delete()

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Folder, PermissionRole.VIEWER
    )
    @blp.arguments(PathFolderIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(FolderSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FolderSchema)
    def put(
        self,
        path: PathFolderIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        updated_folder: FolderSchema,
        id: int,
    ):
        """Update Folder

        Folders give the ability to the user to organize and find better their reports.

        **Notes**
        - A Folder can not have the same name.
        - A Folder may have a parent or may not.
        - A Folder can not have as parent_id himself.
        - The max level of nested folders is 3. Below there is an example of the max level allowed.

        ```
        Financial
        │
        └───Bar
            │
            └───Food

        ```
        """

        property_id = header_property_id.get("property_id")

        folder = Folder.get_by_id_and_property_id(id, property_id)

        if updated_folder["name"] != folder.name:
            existing_folder = Folder.get_by_name_and_property_id(
                name=updated_folder["name"],
                property_id=property_id,
            )

            if existing_folder:
                return InvalidUsage.conflict(
                    message=f"A folder with name: {updated_folder['name']} already exist"
                )

        if updated_folder["parent_id"] is not None:
            if id == updated_folder["parent_id"]:
                return InvalidUsage.bad_request(
                    "A folder cannot have itself as a parent."
                )

            existing_parent = Folder.get_by_id_and_property_id(
                updated_folder["parent_id"], property_id
            )

            if existing_parent is None:
                return InvalidUsage.not_found(
                    message=f"A Folder with id: {updated_folder['parent_id']} does not exist"
                )

            if existing_parent.parent_id == folder.id:
                return InvalidUsage.bad_request(
                    message="A folder can not contain as parent id a folder that is already its parent"
                )

            if Folder.is_max_level_depth(
                parent_id=updated_folder["parent_id"], folder_id=folder.id
            ):
                return InvalidUsage.bad_request(
                    message="Is not possible to create this folder under the provided parent_id because the maximum level depth is 3"
                )

        folder.update(**updated_folder)
        return folder

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Folder, PermissionRole.VIEWER
    )
    @blp.arguments(PathFolderIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FolderSchema)
    def get(
        self,
        path: PathFolderIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        id: int,
    ):
        """Get Folder by id

        Obtain a folder created under a property.
        """

        folder = Folder.get_by_id_and_property_id(
            id, header_property_id.get("property_id")
        )
        return folder


@blp.route("/<int:id>/reports")
class FolderByIdReports(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Folder, PermissionRole.VIEWER
    )
    @blp.arguments(PathFolderIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportIdSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, FolderSchema)
    def post(
        self,
        path: PathFolderIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report: ReportIdSchema,
        id: int,
    ):
        """Assign a report to a folder

        Assign a report to a folder in order to organize your them.
        """
        folder = Folder.get_by_id_and_property_id(
            id, header_property_id.get("property_id")
        )
        existing_report = Report.get_by_id(report_id=report["report_id"])

        existing_report.folder_id = id
        existing_report.update()
        return folder


@blp.route("/<int:id>/reports/<int:report_id>")
class FolderByIdReportById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Folder, PermissionRole.VIEWER
    )
    @blp.arguments(PathFolderIdReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathFolderIdReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        id: int,
        report_id: int,
    ):
        property_id = header_property_id.get("property_id")

        """Unassign a report from a folder

        Unassign a report from a folder in order to delete the folder or re assign it later.
        """
        Folder.get_by_id_and_property_id(id, property_id)

        report = Report.get_by_id(report_id=report_id)

        report.folder_id = None
        report.update()
