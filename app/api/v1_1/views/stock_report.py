from datetime import datetime
from http import HTTPStatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.chart import (
    ChartSchema,
    PathStockReportIdChartIdSchema,
    StockReportWithChartsCloneSchema,
)
from app.api.v1_1.schemas.log import ReportLogSchema
from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.api.v1_1.schemas.report import (
    QueryReportExportSchema,
    ReportCustomCdfSchema,
    ReportLimitSchema,
    ReportSchema,
)
from app.api.v1_1.schemas.stock_report import (
    CountryCodeRulesSchema,
    FeatureIdRulesSchema,
    PathStockReportIdCustomCdfIdSchema,
    PathStockReportIdSchema,
    PathStockReportRevisionIdSchema,
    PropertyIdRulesSchema,
    QuerySortStockReportSchema,
    QueryStockReportExploreSchema,
    QueryStockReportExportSchema,
    QueryStockReportFilterSchema,
    QueryStockReportIdSchema,
    QueryStockReportQueryParamsSchema,
    StockReportCustomCdfSchema,
    StockReportCustomCdfValidateSchema,
    StockReportDataSchema,
    StockReportExportSchema,
    StockReportImportSchema,
    StockReportPublishSchema,
    StockReportQueryExportSchema,
    StockReportQuerySchema,
    StockReportRevisionResponseSchema,
    StockReportRevisionsSchema,
    StockReportRulesSchema,
    StockReportSchema,
    StockReportSummarySchema,
    StockReportsExploreSchema,
    StockReportsSchema,
    SupportedRulesSchema,
)
from app.api.v1_1.schemas.stock_report.stock_report import PathStockReportIdTagIdSchema
from app.api.v1_1.schemas.stock_report.stock_report_folder import (
    PathFolderIdAndStockReportIdSchema,
    PathStockReportsFolderIdSchema,
    StockReportFolderSchema,
    StockReportFoldersSchema,
    StockReportIdSchema,
)
from app.api.v1_1.schemas.stock_report.stock_report_search import (
    SearchQueryStockReportFilterSchema,
)
from app.api.v1_1.schemas.stock_tag.stock_tag import (
    StockReportTagIdSchema,
    StockTagSchema,
)
from app.api.v1_1.schemas.task.queue_response import QueueResponseSchema
from app.api.v1_1.validations.chart import validate_chart, validate_chart_custom_cdf
from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.constants.api_version import API_VERSION
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.pagination import pagination
from app.enums import Dataset
from app.enums.export import Format, View
from app.enums.log_actions import ReportAction
from app.enums.report import ReportKind
from app.schemas.authorization import HeaderAcceptLanguageSchema, HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema
from app.services.chart_service import ChartService
from app.services.custom_cdf_service import CustomCdfService
from app.services.explore_service import ExploreService
from app.services.favorite_service import FavoriteService
from app.services.launch_darkly_service import LaunchDarklyService
from app.services.permission_service import PermissionService
from app.services.report_service import ReportService
from app.services.stock_report_folder_service import StockReportFolderService
from app.services.stock_report_service import StockReportService
from app.services.stock_tag_service import StockTagService
from app.services.task_service import TaskService

blp = Blueprint(
    "Stock reports",
    "stock reports",
    url_prefix=f"{API_VERSION}/stock_reports",
    description="Operations on stock reports",
)


@blp.route("")
class StockReports(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockReportPublishSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, StockReportSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        publish_stock_report: StockReportImportSchema,
    ):
        """Publish a new report

        Publish a single report to a stock report with optional rules.

        The API returns a 201 CREATED if the creation was successful.
        """
        stock_report = StockReportService.publish(
            publish_stock_report, g.user, g.property_id
        )

        return stock_report

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryStockReportFilterSchema, location="query")
    @blp.arguments(QuerySortStockReportSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportsSchema)
    @pagination("stock_reports")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        filters: QueryStockReportFilterSchema,
        sort: QuerySortStockReportSchema,
        _: QueryPaginationSchema,
    ):
        include_cloudbeds = filters.get("include_cloudbeds")
        property_country_only = filters.get("property_country_only")
        """List of stock reports

        Get a list of all the stock reports. Stock reports are the reports that Cloudbeds owns.

        **Notes**
        - A stock report can not be edited, but can be copied.
        - A copied stock report is the same as creating a new report where all the features are available.
        """
        return StockReportService.get_all(
            g.user,
            header_property_id.get("property_id"),
            filters,
            sort,
            include_cloudbeds,
            property_country_only,
        )


@blp.route("/<int:stock_report_id>")
class StockReportById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(StockReportSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportSchema)
    def put(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: PathStockReportIdSchema,
        update_stock_report: StockReportSchema,
        stock_report_id: int,
    ):
        """Update report by id

        Update a single report.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 200 OK if the update was successful.
        """
        stock_report = StockReportService.get_by_id(
            stock_report_id,
            g.user,
            header_property_id.get("property_id"),
            True,
        )

        stock_report_title = stock_report.title

        logger.debug(
            f"User {g.user.id} updated stock report {stock_report_id}",
            extra={
                "old_report": dict(StockReportSchema().dump(stock_report)),
                "new_report": dict(update_stock_report),
            },
        )
        StockReportService.save_revision(stock_report)

        rules = update_stock_report.pop("rules", {})
        update_stock_report["updated_at"] = datetime.now()
        update_stock_report["user_id"] = g.user.id
        stock_report.update(**update_stock_report)

        StockReportService.delete_rules(stock_report_id)
        StockReportService.insert_rules(stock_report_id, rules)
        logger.info(
            "Stock Report updated",
            extra=ReportLogSchema().dump(
                {
                    **update_stock_report,
                    **dict(
                        action=ReportAction.Update.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=g.property_id,
                    ),
                }
            ),
        )

        # Clean all favorites cache if stock report title was updated
        if stock_report_title != update_stock_report["title"]:
            FavoriteService.clear_cache(None, None)

        return stock_report

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Unpublish stock report by id

        Unpublish a single stock report.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 204 NO CONTENT if the delete was successful.
        """
        # get stock report to 'delete'
        stock_report = StockReportService.get_by_id(
            stock_report_id, g.user, g.property_id, True
        )

        logger.debug(
            f"User {g.user.id} has unpublished stock report {stock_report_id}",
            extra={"old_report": dict(StockReportSchema().dump(stock_report))},
        )

        # save current state
        StockReportService.save_revision(stock_report)

        # update published status to False
        stock_report.published = False
        stock_report.user_id = g.user.id

        # save unpublished stock report
        stock_report.save()

        logger.info(
            "Stock Report delete",
            extra=ReportLogSchema().dump(
                {
                    **StockReportSchema().dump(stock_report),
                    **dict(
                        action=ReportAction.Delete.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=g.property_id,
                    ),
                }
            ),
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        __: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Get stock report by id

        Get one stock report. Stock reports are the reports that Cloudbeds owns.

        **Notes**
        - A stock report can not be edited, but can be copied.
        - Since a stock report doesn't belong to any property, it's mandatory to define it.
        - A copied stock report is the same as creating a new report where all the features are available.
        """
        include_cloudbeds = PermissionService.is_whitelisted(g.user.email)

        stock_report = StockReportService.get_by_id(
            stock_report_id, g.user, g.property_id, include_cloudbeds
        )

        return stock_report


@blp.route("/<int:stock_report_id>/rules")
class StockReportByIdRules(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockReportRulesSchema, location="json")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportRulesSchema)
    def put(
        self,
        header_property_id: HeaderPropertyIdSchema,
        rules: StockReportRulesSchema,
        _: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Update report rules by id

        Update a single report's rules.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 200 OK if the update was successful.
        """

        stock_report = StockReportService.get_by_id(
            stock_report_id,
            g.user,
            header_property_id.get("property_id"),
            True,
        )

        logger.info(
            f"User {g.user.id} updated stock report rules {stock_report_id}",
            extra={"new_rules": rules},
        )

        StockReportService.save_revision(stock_report)
        StockReportService.update_rules(stock_report, rules, g.user)

        return rules


@blp.route("/<int:stock_report_id>/data")
class StockReportByIdData(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryStockReportIdSchema, location="query")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportDataSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryStockReportIdSchema,
        __: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Get stock report data by id

        Based on the report `type` generated while creating a stock report the data structure returned may have multiple formats.

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """
        return StockReportService.get_data_by_id(
            stock_report_id,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            g.user,
        )


@blp.route("/<int:stock_report_id>/summary")
class StockReportByIdSummary(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryStockReportIdSchema, location="query")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportSummarySchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryStockReportIdSchema,
        __: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Get stock report summary by id"""
        return StockReportService.get_summary_by_id(
            stock_report_id,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            g.user,
        )


@blp.route("/<int:stock_report_id>/export")
class StockReportByIdExport(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryStockReportExportSchema, location="query")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportExportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryStockReportExportSchema,
        __: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Get stock report export by id"""
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return StockReportService.get_export_by_id(
            stock_report_id,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            g.user,
        )


@blp.route("/<int:stock_report_id>/query/summary")
class StockReportByIdQuerySummary(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(QueryStockReportQueryParamsSchema, location="query")
    @blp.arguments(StockReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportSummarySchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: PathStockReportIdSchema,
        query: QueryStockReportQueryParamsSchema,
        report: StockReportQuerySchema,
        stock_report_id: int,
    ):
        """Query stock report summary

        Mode:
        - Preview: 100 Records
        - Run: 12000 Records

        Query stock report summary from a stock report already created being able to change
        - Filters Operators and Values
        - Columns Order
        - Group Row Modifier
        - Group Columns Modifier
        - Sort
        - Settings

        """
        return StockReportService.query_summary(
            stock_report_id,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            report,
            g.user,
        )


@blp.route("/<int:stock_report_id>/query/data")
class StockReportByIdQueryData(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(QueryStockReportQueryParamsSchema, location="query")
    @blp.arguments(StockReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportDataSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: PathStockReportIdSchema,
        query: QueryStockReportQueryParamsSchema,
        report: StockReportQuerySchema,
        stock_report_id: int,
    ):
        """Query stock report data

        Mode:
        - Preview: 100 Records
        - Run: 12000 Records

        Query stock report data from a stock report already created being able to change
        - Filters Operators and Values
        - Columns Order
        - Group Row Modifier
        - Group Columns Modifier
        - Sort
        - Settings

        """
        return StockReportService.query_data(
            stock_report_id,
            header_property_id.get("property_id"),
            g.organization_id,
            query,
            report,
            g.user,
        )


@blp.route("search")
class Search(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(SearchQueryStockReportFilterSchema, location="query")
    @blp.arguments(QuerySortStockReportSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportsSchema)
    @pagination("stock_reports")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        filters: SearchQueryStockReportFilterSchema,
        sort: QuerySortStockReportSchema,
        _: QueryPaginationSchema,
    ):
        """Get stock reports based on search results

        Get stock reports filtered by different search criteria

        **Notes**:
        - Rows are returned if title contains values (case-insensitive comparison)
        - The API returns 200 even if no results are found.
        """

        include_cloudbeds = filters.get("include_cloudbeds")
        property_country_only = filters.get("property_country_only")
        return StockReportService.get_all_by_partial_search(
            g.user,
            header_property_id.get("property_id"),
            filters,
            sort,
            include_cloudbeds,
            property_country_only,
        )


@blp.route("explore")
class Explore(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryStockReportExploreSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportsExploreSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryStockReportExploreSchema,
    ):
        """Get stock reports based on natural language question

        **Notes**:
        - The API returns 200 even if no results are found.
        """
        question = query.get("question")
        stock_reports = ExploreService.explore_stock_reports(question)
        return stock_reports


@blp.route("rules")
class StockReportRules(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, SupportedRulesSchema)
    def get(
        self,
        _: HeaderPropertyIdSchema,
    ):
        """Get keys of supported rules"""

        return StockReportService.get_supported_rules()


@blp.route("rules/property_ids")
class PropertyIdRulesDetails(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, PropertyIdRulesSchema(many=True))
    def get(
        self,
        _header_property_id: HeaderPropertyIdSchema,
    ):
        """Get keys of supported rules"""

        return StockReportService.get_rule_by_key("property_ids")


@blp.route("rules/feature_ids")
class FeatureIdRulesDetails(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FeatureIdRulesSchema(many=True))
    def get(
        self,
        _header_property_id: HeaderPropertyIdSchema,
    ):
        """Get keys of supported rules"""

        return StockReportService.get_rule_by_key("feature_ids")


@blp.route("rules/country_codes")
class CountryCodeRulesDetails(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, CountryCodeRulesSchema(many=True))
    def get(
        self,
        _header_property_id: HeaderPropertyIdSchema,
    ):
        """Get keys of supported rules"""

        return StockReportService.get_rule_by_key("country_codes")


@blp.route("rules/property_types")
class PropertyTypesRulesDetails(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, CountryCodeRulesSchema(many=True))
    def get(
        self,
        _header_property_id: HeaderPropertyIdSchema,
    ):
        """Get keys of supported rules"""

        return StockReportService.get_rule_by_key("property_types")


@blp.route("/<int:stock_report_id>/revisions")
class StockReportIdRevisions(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReportRevision, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportRevisionsSchema(many=True))
    def get(
        self,
        _: HeaderPropertyIdSchema,
        __: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        return StockReportService.get_revisions_by_stock_report_id(stock_report_id)


@blp.route("/<int:stock_report_id>/revisions/<int:revision_id>")
class StockReportsByIdRevisionsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReportRevision, PermissionRole.VIEWER
    )
    @blp.arguments(PathStockReportRevisionIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportRevisionResponseSchema)
    def get(
        self,
        __: PathStockReportRevisionIdSchema,
        stock_report_id: int,
        revision_id: int,
    ):
        return StockReportService.get_revision(stock_report_id, revision_id)


@blp.route("/<int:stock_report_id>/query/export")
class StockReportQueryExport(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExportSchema, location="query")
    @blp.arguments(StockReportQueryExportSchema, location="json")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportExportSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportExportSchema,
        report_query: StockReportQueryExportSchema,
        _: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Query stock report data and export to file

        Request a file export of data from any stock report json that is not created

        Query stock report data from a stock report that is not created

        Mode:
        - Export: 100000 Records

        Based on the report `type` sent in the request, the data structure returned may have multiple formats.

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        property_id = header_property_id.get("property_id")
        report_query = StockReportService.get_query_report_by_id(
            report_query,
            stock_report_id,
            g.user,
            property_id,
            not PermissionService.is_whitelisted(
                g.user.email,
            ),
        )

        if (
            query.get("view") == View.Formatted.value
            and query.get("format") == Format.XLSX.value
        ):
            if query.get("include_charts"):
                charts = ChartService.get_all_by_datasource_kind_and_id(
                    ReportKind.StockReport.value, stock_report_id
                )
            else:
                charts = []

            return ReportService.get_report_export(
                report_query,
                query,
                header_property_id.get("property_id"),
                g.organization_id,
                g.user,
                charts,
                ReportKind.StockReport.value,
            )

        return ReportService.get_report_export(
            report_query,
            query,
            header_property_id.get("property_id"),
            g.organization_id,
            g.user,
            None,
            ReportKind.StockReport.value,
        )


@blp.route("/<int:stock_report_id>/custom_cdfs")
class StockReportsByIdCustomCdfs(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportCustomCdfSchema(many=True))
    def get(
        self,
        path: PathStockReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_id: int,
    ):
        """Get custom cdfs of a stock report

        Get a list of all the custom cdfs created of a stock report.
        """
        StockReportService.get_by_id(
            stock_report_id, g.user, g.property_id, include_cloudbeds=g.user.admin
        )
        return CustomCdfService.get_by_report_id(
            ReportKind.StockReport, stock_report_id
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockReportCustomCdfSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, StockReportCustomCdfSchema)
    def post(
        self,
        path: PathStockReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_custom_cdf: StockReportCustomCdfSchema,
        stock_report_id: int,
    ):
        """Create a custom cdf for a stock report

        - A custom cdf gives the user the ability to create a new cdf based on existing cdf.
        - Column is generated over the name, only with alphanumerical values and lower case. Special characters are replaced with underscore(_)

        **Note**:
        - A custom cdf should have a validate formula.
        """
        stock_report_custom_cdf_check = CustomCdfService.get_by_report_id_and_column(
            ReportKind.StockReport, stock_report_id, stock_report_custom_cdf["column"]
        )
        if stock_report_custom_cdf_check is not None:
            return InvalidUsage.conflict(
                "A stock report custom cdf with this name already exists in this report"
            )

        CustomCdfService.create(
            report_kind=ReportKind.StockReport,
            report_schema=StockReportCustomCdfSchema,
            custom_cdfs=[stock_report_custom_cdf],
            report_id=stock_report_id,
            user_id=g.user.id,
        )

        return CustomCdfService.get_by_report_id_and_column(
            ReportKind.StockReport, stock_report_id, stock_report_custom_cdf["column"]
        )


@blp.route("/<int:stock_report_id>/custom_cdfs/<int:custom_cdf_id>")
class StockReportsByIdCustomCdfsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(PathStockReportIdCustomCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportCustomCdfSchema)
    def get(
        self,
        path: PathStockReportIdCustomCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_id: int,
        custom_cdf_id: int,
    ):
        """Get custom cdf of a stock report

        Get a created custom cdf of a stock report.
        """
        StockReportService.get_by_id(stock_report_id, g.user, g.property_id)
        report_custom_cdf = CustomCdfService.get_by_id_and_report_id(
            ReportKind.StockReport, custom_cdf_id, stock_report_id
        )

        if report_custom_cdf is None:
            return InvalidUsage.not_found(
                "A custom cdf with this id doesn't exist or doesn't belong to the stock report"
            )

        return report_custom_cdf

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdCustomCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathStockReportIdCustomCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_id: int,
        custom_cdf_id: int,
    ):
        """Delete a custom cdf of a stock report

        Delete a custom cdf of a stock report.
        """
        StockReportService.get_by_id(stock_report_id, g.user, g.property_id)
        stock_report_custom_cdf = CustomCdfService.get_by_id_and_report_id(
            ReportKind.StockReport, custom_cdf_id, stock_report_id
        )

        if stock_report_custom_cdf is None:
            return InvalidUsage.not_found(
                "A stock report custom cdf with this id doesn't exist or doesn't belong to the report"
            )

        stock_report_custom_cdf.delete()

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdCustomCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockReportCustomCdfSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportCustomCdfSchema)
    def put(
        self,
        path: PathStockReportIdCustomCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        new_stock_report_custom_cdf: StockReportCustomCdfSchema,
        stock_report_id: int,
        custom_cdf_id: int,
    ):
        """Update a custom cdf on a report"""
        stock_report = StockReportService.get_by_id(
            stock_report_id, g.user, g.property_id
        )
        stock_report_custom_cdf = CustomCdfService.get_by_id_and_report_id(
            ReportKind.StockReport, custom_cdf_id, stock_report_id
        )
        if stock_report_custom_cdf is None:
            return InvalidUsage.conflict(
                "A stock report custom cdf with this id doesn't exists or doesn't belong to the report"
            )

        report_custom_cdf_duplicate = CustomCdfService.get_by_report_id_and_column(
            ReportKind.StockReport,
            stock_report_id,
            new_stock_report_custom_cdf["column"],
        )

        if (
            report_custom_cdf_duplicate is not None
            and hasattr(report_custom_cdf_duplicate, "id")
            and report_custom_cdf_duplicate.id != custom_cdf_id
        ):
            return InvalidUsage.conflict(
                "A stock report custom cdf with this name already exists in this stock report"
            )

        new_stock_report_custom_cdf = StockReportCustomCdfSchema().dump(
            new_stock_report_custom_cdf
        )
        # Update the Custom CDFs in the report if the column name or kind is changed
        if (
            stock_report_custom_cdf.column != new_stock_report_custom_cdf["column"]
        ) or (stock_report_custom_cdf.kind != new_stock_report_custom_cdf["kind"]):
            # Validate custom cdf on charts
            charts = ChartService.get_all_by_datasource_kind_and_id(
                ReportKind.StockReport.value, stock_report_id
            )

            if charts:
                for chart in charts:
                    validate_chart_custom_cdf(
                        chart, stock_report_custom_cdf, new_stock_report_custom_cdf
                    )

            new_stock_report = dict()
            cdf = lambda custom_cdfs: CDF(
                dataset=Dataset(stock_report.dataset_id),
                name=stock_report_custom_cdf.column,
                is_custom_cdf=True,
            ).get_custom_cdf_in_custom_cdfs(custom_cdfs)
            if (
                stock_report.columns is not None
                and bool(CDFs.get_custom_cdfs(stock_report.columns))
                and cdf(stock_report.columns)
            ):
                new_stock_report["columns"] = [
                    (
                        {
                            **column,
                            "cdf": {
                                **column["cdf"],
                                "column": new_stock_report_custom_cdf["column"],
                            },
                        }
                        if column["cdf"]["column"] == stock_report_custom_cdf.column
                        else column
                    )
                    for column in stock_report.columns
                ]

            if (
                stock_report.group_rows is not None
                and bool(CDFs.get_custom_cdfs(stock_report.group_rows))
                and cdf(stock_report.group_rows)
            ):
                new_stock_report["group_rows"] = [
                    (
                        {
                            **group_row,
                            "cdf": {
                                **group_row["cdf"],
                                "column": new_stock_report_custom_cdf["column"],
                            },
                        }
                        if group_row["cdf"]["column"] == stock_report_custom_cdf.column
                        else group_row
                    )
                    for group_row in stock_report.group_rows
                ]

            if (
                stock_report.group_columns is not None
                and bool(CDFs.get_custom_cdfs(stock_report.group_columns))
                and cdf(stock_report.group_columns)
            ):
                new_stock_report["group_columns"] = [
                    (
                        {
                            **group_column,
                            "cdf": {
                                **group_column["cdf"],
                                "column": new_stock_report_custom_cdf["column"],
                            },
                        }
                        if group_column["cdf"]["column"]
                        == stock_report_custom_cdf.column
                        else group_column
                    )
                    for group_column in stock_report.group_columns
                ]

            if (
                stock_report.sort is not None
                and bool(CDFs.get_custom_cdfs(stock_report.sort))
                and cdf(stock_report.sort)
            ):
                new_stock_report["sort"] = [
                    (
                        {
                            **sort,
                            "cdf": {
                                **sort["cdf"],
                                "column": new_stock_report_custom_cdf["column"],
                            },
                        }
                        if sort["cdf"]["column"] == stock_report_custom_cdf.column
                        else sort
                    )
                    for sort in stock_report.sort
                ]

            stock_report.update(**new_stock_report)

            # Update Custom CDFs in Chart
            ChartService.update_custom_cdfs(
                stock_report_id,
                ReportKind.StockReport.value,
                stock_report_custom_cdf.column,
                new_stock_report_custom_cdf,
            )

        return stock_report_custom_cdf.update(**new_stock_report_custom_cdf)


@blp.route("/<int:stock_report_id>/custom_cdfs/validate")
class ReportsByIdCustomCdfsValidate(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockReportCustomCdfValidateSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportCustomCdfValidateSchema)
    def put(
        self,
        path: PathStockReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        custom_cdf: StockReportCustomCdfValidateSchema,
        stock_report_id: int,
    ):
        """Validate a custom cdf for a stock report

        In order to create a custom cdf the formula needs to be validated.

        **Notes**:
        - Column is generated over the name, only with alphanumerical values and lower case. Special characters are replaced with underscore(_)
        - A valid formula could be a string concatenation.
        - A valid formula could be a math operation.
        - A valid formula is a list of objects that contains kind and value.
        - `kind`: cdf | separator | operator | operand | parenthesis.
        - `value`: It's the value of the cdf, separator, operator, operand or parenthesis.
        - Separator: Any value.
        - Operator: Valid operators.
        - Operand: Numeric values.
        - Parenthesis: ( or ).
        """
        return custom_cdf


@blp.route("/<int:stock_report_id>/clone")
class StockReportByIdClone(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(
        StockReportWithChartsCloneSchema,
        location="json",
    )
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ReportSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathStockReportIdSchema,
        clone_report: StockReportWithChartsCloneSchema,
        stock_report_id: int,
    ):
        """Clone stock report by id

        Create a stock report cloning another report.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 201 CREATED if the cloning was successful.
        """
        stock_report = StockReportService.get_by_id(
            stock_report_id, g.user, g.property_id
        )
        custom_cdfs = stock_report.custom_cdfs
        report = ReportSchema(
            exclude=(
                "id",
                "custom_cdfs",
                "type",
                "created_by",
                "updated_at",
                "folder_id",
                "tags",
                "schedules",
            )
        ).dump(stock_report)

        new_charts = clone_report.pop("charts") if "charts" in clone_report else None
        charts = (
            new_charts
            if new_charts
            else ChartService.get_all_by_datasource_kind_and_id(
                ReportKind.StockReport.value, stock_report_id
            )
        )

        report.update(**clone_report, property_id=g.property_id)

        ReportSchema().load(report)
        report = ReportService.create_report(
            report, user=g.user, property_id=header_property_id
        )

        try:
            if charts:
                for chart in charts:
                    validate_chart(ChartSchema().dump(chart), report)

                for chart in charts:
                    ChartService.create(
                        ChartSchema(
                            exclude=["id", "user_id", "created_at", "updated_at"]
                        ).dump(chart),
                        ReportKind.Report.value,
                        report.id,
                    )

            if custom_cdfs:
                CustomCdfService.create(
                    ReportKind.Report,
                    ReportCustomCdfSchema,
                    custom_cdfs,
                    report.id,
                    g.user.id,
                )
        except Exception:
            report.delete()
            raise InvalidUsage.server_error("There was a problem saving the report")

        logger.info(
            "Save As Report",
            extra=ReportLogSchema().dump(
                {
                    **ReportSchema().dump(report),
                    **dict(
                        action=ReportAction.SaveAs.value,
                        report_kind=ReportKind.StockReport.value,
                        user_id=g.user.id,
                    ),
                }
            ),
        )
        return report


@blp.route("/<int:stock_report_id>/charts")
class StockReportCharts(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartSchema(many=True))
    def get(
        self,
        path: PathStockReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_id: int,
    ):
        charts = ChartService.get_all_by_datasource_kind_and_id(
            ReportKind.StockReport.value, stock_report_id
        )
        return charts

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ChartSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ChartSchema)
    def post(
        self,
        path: PathStockReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        chart_schema: ChartSchema,
        stock_report_id: int,
    ):
        return ChartService.create(
            chart_schema, ReportKind.StockReport.value, stock_report_id
        )


@blp.route("/<int:stock_report_id>/charts/<int:chart_id>")
class StockReportChartsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdChartIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathStockReportIdSchema,
        stock_report_id: int,
        chart_id: int,
    ):
        chart = ChartService.get_by_id_datasource_kind_and_id(
            chart_id, ReportKind.StockReport.value, stock_report_id
        )
        return chart

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdChartIdSchema, location="path")
    @blp.arguments(ChartSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartSchema)
    def put(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathStockReportIdChartIdSchema,
        chart_schema: ChartSchema,
        stock_report_id: int,
        chart_id: int,
    ):
        chart = ChartService.update(
            chart_id, ReportKind.StockReport.value, stock_report_id, chart_schema
        )
        return chart

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportIdChartIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathStockReportIdChartIdSchema,
        stock_report_id: int,
        chart_id: int,
    ):
        ChartService.delete(chart_id, ReportKind.StockReport.value, stock_report_id)


@blp.route("/folders")
class StockReportsFolders(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(HeaderAcceptLanguageSchema, location="headers")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportFoldersSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: HeaderAcceptLanguageSchema,
        query_params: QueryPaginationSchema,
    ):
        """List of stock report folders

        Get a list of all the stock report folders. This folders are managed by Cloudbeds
        """
        return StockReportFolderService.get_all(**query_params)

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockReportFolderSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, StockReportFolderSchema)
    def post(
        self,
        _: HeaderPropertyIdSchema,
        stock_report_folder_schema: StockReportFolderSchema,
    ):
        """Create a new stock report folder

        Create a new Stock Report folder in order to organize them.
        """
        return StockReportFolderService.create(stock_report_folder_schema, g.user.id)


@blp.route("/folders/<int:folder_id>")
class StockReportsFoldersById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(HeaderAcceptLanguageSchema, location="headers")
    @blp.arguments(PathStockReportsFolderIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockReportFolderSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        accept_language: HeaderAcceptLanguageSchema,
        _: PathStockReportsFolderIdSchema,
        folder_id: int,
    ):
        """Get Stock report folder by Id

        Get a stock report folder by id

        """
        return StockReportFolderService.get_by_id(folder_id)

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportsFolderIdSchema, location="path")
    @blp.arguments(StockReportIdSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: PathStockReportsFolderIdSchema,
        stock_report: StockReportIdSchema,
        folder_id: int,
    ):
        """Assign a stock report to a folder

        Assign a stock report to a folder in order to organize your them.

        """
        return StockReportService.assign_folder(
            stock_report["stock_report_id"], folder_id
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathStockReportsFolderIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: PathStockReportsFolderIdSchema,
        folder_id: int,
    ):
        StockReportFolderService.delete(folder_id)


@blp.route("/folders/<int:folder_id>/<int:stock_report_id>")
class StockReportsFoldersByIdStockReportId(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(HeaderAcceptLanguageSchema, location="headers")
    @blp.arguments(PathFolderIdAndStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        accept_language: HeaderAcceptLanguageSchema,
        _: PathFolderIdAndStockReportIdSchema,
        folder_id: int,
        stock_report_id: int,
    ):
        """Remove a stock report from a folder"""
        return StockReportService.remove_stock_report_from_folder(
            stock_report_id, folder_id
        )


@blp.route("limits")
class StockReportLimits(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportLimitSchema)
    def get(self):
        return ReportService.get_limits()


@blp.route("/<int:stock_report_id>/export/async")
class StockReportByIdExportAsync(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryStockReportExportSchema, location="query")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryStockReportExportSchema,
        __: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Get stock report export by id"""
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return TaskService.queue_stock_report_export_by_id(
            stock_report_id,
            header_property_id.get("property_id"),
            query,
        )


@blp.route("/<int:stock_report_id>/query/export/async")
class StockReportQueryExportAsync(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExportSchema, location="query")
    @blp.arguments(StockReportQueryExportSchema, location="json")
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportExportSchema,
        report_query: StockReportQueryExportSchema,
        _: PathStockReportIdSchema,
        stock_report_id: int,
    ):
        """Query stock report data and export to file

        Request a file export of data from any stock report json that is not created

        Query stock report data from a stock report that is not created

        Mode:
        - Export: 100000 Records

        Based on the report `type` sent in the request, the data structure returned may have multiple formats.

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return TaskService.queue_stock_report_export_by_query(
            stock_report_id, header_property_id, query, report_query
        )


@blp.route("/<int:stock_report_id>/stock_tags")
class StockReportsByIdTags(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.StockReport, PermissionRole.VIEWER
    )
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockTagSchema(many=True))
    def get(
        self,
        path: PathStockReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_id: int,
    ):
        """Get tags on stock report

        Get a list of tags that a stock report is associated with.
        """
        stock_report = StockReportService.get_by_id(
            stock_report_id, g.user, header_property_id.get("property_id"), g.user.admin
        )
        return stock_report.tags

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(StockReportTagIdSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, StockTagSchema)
    def post(
        self,
        path: PathStockReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_tag: StockReportTagIdSchema,
        stock_report_id: int,
    ):
        """Create an association between a Report and a tag

        It associates a created tag with a created report.

        """

        stock_report = StockReportService.get_by_id(
            stock_report_id, g.user, header_property_id.get("property_id"), g.user.admin
        )
        tag_to_link = StockTagService.get_by_id_or_404(**stock_tag)

        if StockReportService.is_stock_report_tagged(stock_report_id, stock_tag["id"]):
            return InvalidUsage.conflict(
                f'This stock tag id: {stock_tag["id"]} is already associated with this stock report id: {stock_report_id}'
            )

        stock_report.tags.append(tag_to_link)
        stock_report.save()

        return tag_to_link


@blp.route("/<int:stock_report_id>/stock_tags/<int:stock_tag_id>")
class StockReportsByIdTagsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdTagIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, StockTagSchema)
    def get(
        self,
        path: PathStockReportIdTagIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_id: int,
        stock_tag_id: int,
    ):
        """Get an association between a Report and a tag

        It gets a created tag from a created report.

        """
        StockReportService.get_by_id(
            stock_report_id, g.user, header_property_id.get("property_id"), g.user.admin
        )
        tag = StockTagService.get_by_id_or_404(stock_tag_id)

        if (
            StockReportService.is_stock_report_tagged(stock_report_id, stock_tag_id)
            is False
        ):
            return InvalidUsage.not_found(
                f"This tag id: {stock_tag_id} is not associated with this report id: {stock_report_id}"
            )
        return tag

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.StockReport, PermissionRole.AUTHOR
    )
    @blp.arguments(PathStockReportIdTagIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathStockReportIdTagIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        stock_report_id: int,
        stock_tag_id: int,
    ):
        """Delete an association between a Report and a tag

        It unassigns a created tag from a created report.

        """
        stock_report = StockReportService.get_by_id(
            stock_report_id, g.user, header_property_id.get("property_id"), g.user.admin
        )
        tag_to_unlink = StockTagService.get_by_id_or_404(stock_tag_id)

        if (
            StockReportService.is_stock_report_tagged(stock_report_id, stock_tag_id)
            is False
        ):
            return InvalidUsage.not_found(
                f"This tag id: {stock_tag_id} is not associated with this report id: {stock_report_id}"
            )

        stock_report.tags.remove(tag_to_unlink)
        stock_report.save()
