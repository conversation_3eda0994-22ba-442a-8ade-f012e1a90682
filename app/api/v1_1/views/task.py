from http import HTT<PERSON>tatus

from flask import current_app, g
from flask.views import MethodView

from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.api.v1_1.schemas.task.path_task_id import (
    PathTaskIdSchema,
    TaskTokenSchema,
)
from app.api.v1_1.schemas.task.queue_response import QueueResponseSchema
from app.api.v1_1.schemas.task.task import TasksSchema
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.exceptions import InvalidUsage
from app.common.smorest import Blueprint
from app.decorators.api_key_or_authorization import api_key_or_authorization
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.pagination import pagination
from app.schemas.authorization import (
    HeaderPropertyIdApiKeySchema,
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.di_token_service import DITokenService
from app.services.okta_service import OktaService
from app.services.schedule_service import ScheduleService
from app.services.task_service import TaskService
from app.services.token_service import TokenService


blp = Blueprint(
    "Task",
    "task",
    url_prefix=f"{API_VERSION}/tasks",
    description="Operations on tasks",
)


@blp.route("")
class Tasks(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, TasksSchema)
    @pagination("tasks")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        _: QueryPaginationSchema,
    ):
        """Get tasks

        Returns all the tasks for a single property.
        """
        results = TaskService.get_all(g.user.id, header_property_id.get("property_id"))
        return results


@blp.route("<string:id>")
class GetTaskByTaskId(MethodView):
    @api_key_or_authorization
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdApiKeySchema, location="headers")
    @blp.arguments(PathTaskIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathTaskIdSchema,
        id: str,
    ):
        TaskService.get_task_by_id_and_g(id)
        celery_app = current_app.extensions["celery"]
        result = celery_app.AsyncResult(id)

        if issubclass(type(result.result), Exception):

            result = dict(
                id=result.id,
                status=result.status,
                error=result.result.args[0] if len(result.result.args) > 0 else "",
            )

        elif result.result:
            result.result.pop("charts", None)

        return result


@blp.route("token/<string:token>")
class GetTaskByToken(MethodView):
    @blp.arguments(TaskTokenSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def get(self, path: TaskTokenSchema, token: str):
        try:
            token = DITokenService(token)
        except:
            raise InvalidUsage.not_found()

        id = token.get("sub")

        # validate if mfd login
        auth_header = g.access_token
        if token.get("require_login"):
            try:
                if not OktaService.is_valid_access_token(auth_header):
                    raise InvalidUsage.not_authorized("Invalid access token")
            except:
                raise InvalidUsage.not_authorized("Access token could not be validated")

            # validate if user is still able to access the download
            schedule_id = token.get("schedule_id")
            if schedule_id:
                if (
                    TokenService(auth_header).get_email()
                    not in ScheduleService.get_by_id(schedule_id).recipients
                ):
                    raise InvalidUsage.forbidden(
                        "User not authorized to access download, check with subscription owner."
                    )

        TaskService.get_task_by_id_or_404(id)
        celery_app = current_app.extensions["celery"]
        result = celery_app.AsyncResult(id)

        if issubclass(type(result.result), Exception):
            result = dict(id=result.id, status=result.status, result=None)

        elif result.result:
            result.result.pop("charts", None)

        return result
