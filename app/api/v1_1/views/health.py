from http import HTTPStatus

from flask.views import MethodView

from app.api.v1_1.schemas.health import HealthSchema
from app.common.cache import TIMEOUT_5_MINUTES, cache
from app.common.constants.api_version import API_VERSION
from app.common.smorest import Blueprint
from app.schemas.error import ErrorSchema
from app.services.health_service import HealthService


blp = Blueprint(
    "Health",
    "health",
    url_prefix=f"{API_VERSION}/health",
    description="Operations on the health of the service",
)


@blp.route("")
class Health(MethodView):
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, HealthSchema)
    @cache.cached(timeout=TIMEOUT_5_MINUTES)
    def get(self):
        """Get the health of the service

        This method performs various checks, such as:

        - The status of the connections to the infrastructure services used by the service instance
        - The status of the host, e.g. storage,
        - Application specific logic and direct dependencies
        """
        health = dict(
            s3=HealthService.s3_available(),
            elasticache=HealthService.redis_available(),
            aurora=HealthService.aurora_available(),
            rds=HealthService.database_available(),
            maintenance=HealthService.check_maintenance_mode(),
        )
        return health
