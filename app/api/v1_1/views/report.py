from http import HTT<PERSON>tatus

from flask import g
from flask.views import MethodView

from app.api.v1_1.schemas.chart import (
    ChartSchema,
    PathReportIdChartIdSchema,
    ReportWithChartsCloneSchema,
)
from app.api.v1_1.schemas.explore import QueryReportExploreSchema
from app.api.v1_1.schemas.log import ReportLogSchema
from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.api.v1_1.schemas.report import (
    FormatOptionsSchema,
    PathFormatTypeSchema,
    PathReportIdCustomCdfIdSchema,
    PathReportIdSchema,
    PathReportIdTagIdSchema,
    QueryFormatParamsSchema,
    QueryReportExportSchema,
    QueryReportQueryParamsSchema,
    QuerySortReportSchema,
    ReportCustomCdfSchema,
    ReportCustomCdfValidateSchema,
    ReportDataSchema,
    ReportExploreSchema,
    ReportExportSchema,
    ReportLimitSchema,
    ReportPropertySchema,
    ReportQueryExportSchema,
    ReportQuerySchema,
    ReportRelativeDateSchema,
    ReportSchema,
    ReportSummarySchema,
    ReportsSchema,
)
from app.api.v1_1.schemas.report.report import (
    PathReportIdCustomFieldCdfIdSchema,
    ReportUpdateSchema,
)
from app.api.v1_1.schemas.report.report_search import (
    GetReportsQueryParamsSchema,
    SearchReportsQueryParamsSchema,
)
from app.api.v1_1.schemas.report_custom_field import (
    ReportCustomFieldCdfSchema,
)
from app.api.v1_1.schemas.tag import ReportTagIdSchema, ReportTagSchema, TagSchema
from app.api.v1_1.schemas.task.query_exports_by_report_ids import (
    QueryReportExportByIdsSchema,
)
from app.api.v1_1.schemas.task.queue_response import QueueResponseSchema
from app.api.v1_1.validations.chart import validate_chart, validate_chart_custom_cdf
from app.api.v1_1.validations.reports import (
    get_custom_field_cdfs,
    validate_report_custom_field_cdf,
)
from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.constants.api_version import API_VERSION
from app.common.enums.features import BillingPortalFeatures, LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    authorization,
    check_user_has_permission,
    has_property_permission,
)
from app.decorators.pagination import pagination
from app.enums import (
    Dataset,
    FormatsKind,
    RelativeDate as RelativeDateEnum,
)
from app.enums.export import Format
from app.enums.favorite import FavoriteKind
from app.enums.log_actions import ReportAction
from app.enums.report import ReportKind
from app.models.report import (
    Report,
    ReportCustomCdf,
    ReportCustomFieldCdf,
    ReportCustomFieldCdfProperty,
)
from app.models.tag import Tag
from app.schemas.authorization import (
    HeaderPropertyIdSchema,
)
from app.schemas.error import ErrorSchema
from app.services.chart_service import ChartService
from app.services.custom_cdf_service import CustomCdfService
from app.services.custom_field_service import CustomFieldService
from app.services.explore_service import ExploreService
from app.services.favorite_service import FavoriteService
from app.services.formats_service import FormatsService
from app.services.launch_darkly_service import LaunchDarklyService
from app.services.property_feature_service import PropertyFeatureService
from app.services.property_service import PropertyService
from app.services.report_service import ReportService
from app.services.task_service import TaskService


blp = Blueprint(
    "Reports",
    "reports",
    url_prefix=f"{API_VERSION}/reports",
    description="Operations on reports",
)


@blp.route("")
class Reports(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ReportSchema)
    def post(self, header_property_id: HeaderPropertyIdSchema, report: ReportSchema):
        """Create a new report

        Create a single report.

        *Note: Based on the *properties* defined while creating a report the `type` may have multiple formats.*

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |

        The API returns a 201 CREATED if the creation was successful.
        """

        logger.info(
            "Create Report",
            extra=ReportLogSchema().dump(
                {
                    **ReportSchema().dump(report),
                    **dict(
                        action=ReportAction.Create.value,
                        report_kind=ReportKind.Report.value,
                        user_id=g.user.id,
                    ),
                }
            ),
        )

        if report.get("custom_field_cdfs"):
            report["custom_field_cdfs"] = [
                ReportCustomFieldCdf(
                    **dict(
                        column=custom_field_cdf["column"],
                        name=custom_field_cdf["name"],
                        properties=[
                            ReportCustomFieldCdfProperty(**property)
                            for property in custom_field_cdf["properties"]
                        ],
                    )
                )
                for custom_field_cdf in report["custom_field_cdfs"]
            ]
        report = ReportService.create_report(
            report, user=g.user, property_id=header_property_id
        )
        return report

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(GetReportsQueryParamsSchema, location="query")
    @blp.arguments(QuerySortReportSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportsSchema)
    @pagination("reports")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: GetReportsQueryParamsSchema,
        sort: QuerySortReportSchema,
        _: QueryPaginationSchema,
    ):
        """Get reports

        Returns all the reports for a single property.
        """
        return ReportService.get_all(
            header_property_id.get("property_id"), query, sort, g.user
        )


@blp.route("explore")
class ReportsExplore(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExploreSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNPROCESSABLE_ENTITY, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportExploreSchema)
    def get(self, header: HeaderPropertyIdSchema, query: QueryReportExploreSchema):
        """Get a report by exploring a question using natural language processing

        Receive a report by exploring a question using natural language processing.

        *Note: Based on the *question* defined while creating a report the `type` may have multiple formats.*
        """

        return ExploreService.explore_report(
            query["question"], query["context_id"], header["property_id"], g.user
        )


@blp.route("/<int:report_id>")
class ReportById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathReportIdSchema,
        report_id: int,
    ):
        """Get report by id

        Returns a single report.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 200 OK if there is a report with given id.
        """
        report = ReportService.get_by_id(report_id, g.user)
        return report

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportUpdateSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportSchema)
    def put(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        update_report: ReportSchema,
        report_id: int,
    ):
        """Update report by id

        Update a single report.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 200 OK if the update was successful.
        """
        report = ReportService.get_by_id(report_id, g.user)
        if report.property_id != header_property_id.get("property_id"):
            raise InvalidUsage.bad_request("Report does not belong to property")
        report_title = report.title

        report.update(**update_report)

        logger.info(
            "Update Report",
            extra=ReportLogSchema().dump(
                {
                    **ReportSchema().dump(report),
                    **dict(
                        action=ReportAction.Update.value,
                        report_kind=ReportKind.Report.value,
                    ),
                }
            ),
        )

        # Clean user favorites when title is updated
        if report_title != update_report["title"]:
            property_id = header_property_id.get("property_id")
            FavoriteService.clear_cache(property_id, None)

        return report

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
    ):
        """Delete report by id

        Delete a single report.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 204 NO CONTENT if the delete was successful.
        """
        if ChartService.get_all_by_datasource_kind_and_id(
            ReportKind.Report.value, report_id
        ):
            raise InvalidUsage.bad_request("Report has charts, cannot delete")

        favorites_to_clear = FavoriteService.get_all_by_kind_and_entity_id(
            FavoriteKind.Report.value, report_id
        )
        report = ReportService.get_by_id(report_id, g.user)
        report.delete()
        FavoriteService.clear_user_caches_for_favorites(favorites_to_clear)
        for schedule in report.schedules:
            if not len(schedule.reports):
                schedule.delete()
        logger.info(
            "Delete Report",
            extra=ReportLogSchema().dump(
                {
                    **ReportSchema().dump(report),
                    **dict(
                        action=ReportAction.Delete.value,
                        report_kind=ReportKind.Report.value,
                    ),
                }
            ),
        )


@blp.route("/<int:report_id>/clone")
class ReportByIdClone(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(
        ReportWithChartsCloneSchema,
        location="json",
    )
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ReportSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathReportIdSchema,
        clone_report: ReportWithChartsCloneSchema,
        report_id: int,
    ):
        """Clone report by id

        Create a report cloning another report.

        *Note: The API may throw a HTTP 404 if there are no reports found with a given id.*

        The API returns a 201 CREATED if the cloning was successful.
        """
        # Validate new report property has report_builder enabled
        new_property_id = clone_report.get("property_id")
        if new_property_id and not PropertyFeatureService.has_feature_flag(
            BillingPortalFeatures.ReportBuilder.value, new_property_id
        ):
            raise InvalidUsage.bad_request(
                f"The property ID {new_property_id} does not have the Report Builder feature enabled"
            )

        report = ReportService.get_by_id(report_id, g.user)

        custom_cdfs = report.custom_cdfs
        report = ReportSchema(
            exclude=(
                "id",
                "custom_cdfs",
                "type",
                "created_by",
                "updated_at",
                "folder_id",
                "tags",
                "schedules",
            )
        ).dump(report)

        new_charts = clone_report.pop("charts") if "charts" in clone_report else None
        charts = (
            new_charts
            if new_charts
            else ChartService.get_all_by_datasource_kind_and_id(
                ReportKind.Report.value, report_id
            )
        )

        if charts:
            test_report = Report(
                user_id=None,
                **dict(
                    report,
                    custom_cdfs=[
                        ReportCustomCdf(
                            column=custom_cdf.column,
                            kind=custom_cdf.kind,
                        )
                        for custom_cdf in custom_cdfs
                    ],
                    **clone_report,
                ),
            )
            for chart in charts:
                validate_chart(ChartSchema().dump(chart), test_report)

        report.update(**clone_report)

        custom_field_cdfs = report.pop("custom_field_cdfs", [])
        ReportSchema().load(report)

        if custom_field_cdfs:
            if new_property_id and not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.CustomFieldsCDFsFF, new_property_id
            ):
                raise InvalidUsage.bad_request(
                    "New Property Does not have access to custom fields"
                )
            report["custom_field_cdfs"] = [
                ReportCustomFieldCdf(
                    **dict(
                        column=custom_field_cdf["column"],
                        name=custom_field_cdf["name"],
                        properties=[
                            ReportCustomFieldCdfProperty(**property)
                            for property in custom_field_cdf["properties"]
                        ],
                    )
                )
                for custom_field_cdf in custom_field_cdfs
            ]
        report = ReportService.create_report(
            report, user=g.user, property_id=header_property_id
        )

        for chart in charts:
            ChartService.create(
                ChartSchema(exclude=["id", "user_id", "created_at", "updated_at"]).dump(
                    chart
                ),
                ReportKind.Report.value,
                report.id,
            )

        try:
            CustomCdfService.create(
                ReportKind.Report,
                ReportCustomCdfSchema,
                custom_cdfs,
                report.id,
                g.user.id,
            )
        except Exception:
            report.delete()
            raise InvalidUsage.server_error()

        logger.info(
            "Save As Report",
            extra=ReportLogSchema().dump(
                {
                    **ReportSchema().dump(report),
                    **dict(
                        action=ReportAction.SaveAs.value,
                        report_kind=ReportKind.Report.value,
                        user_id=g.user.id,
                    ),
                }
            ),
        )
        return report


@blp.route("/<int:report_id>/data")
class ReportByIdData(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryFormatParamsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportDataSchema)
    def get(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryFormatParamsSchema,
        report_id: int,
    ):
        """Get report data by id

        Based on the report `type` generated while creating a report the data structure returned may have multiple formats.

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """

        return ReportService.get_report_data_by_id(
            report_id,
            query["format"],
            header_property_id.get("property_id"),
            organization_id=g.organization_id,
            user=g.user,
        )


@blp.route("/<int:report_id>/summary")
class ReportByIdSummary(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryFormatParamsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportSummarySchema)
    def get(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryFormatParamsSchema,
        report_id: int,
    ):
        """Get report summary by id"""
        return ReportService.get_report_summary_by_id(
            report_id,
            query["format"],
            header_property_id.get("property_id"),
            organization_id=g.organization_id,
            user=g.user,
        )


@blp.route("/<int:report_id>/export")
class ReportByIdExport(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExportSchema, location="query")
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportExportSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportExportSchema,
        path: PathReportIdSchema,
        report_id: int,
    ):
        """Get report export by id"""
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return ReportService.get_report_export_by_id(
            report_id,
            query.get("view"),
            query.get("format"),
            query.get("include_charts"),
            header_property_id.get("property_id"),
            g.organization_id,
            g.user,
        )


@blp.route("/<int:report_id>/properties")
class ReportByIdProperties(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportPropertySchema(many=True))
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathReportIdSchema,
        report_id: int,
    ):
        """Get report properties by id"""
        report = ReportService.get_by_id(report_id, user=g.user)
        properties = PropertyService.get_all(property_ids=report.property_ids)
        return properties


@blp.route("/query/data")
class ReportQueryData(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportQueryParamsSchema, location="query")
    @blp.arguments(ReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportDataSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportQueryParamsSchema,
        report_query: ReportQuerySchema,
    ):
        """Query report data

        Query report data from a report that is not created

        Mode:
        - Preview: 100 Records
        - Run: 12000 Records

        Based on the report `type` sent in the request, the data structure returned may have multiple formats.

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """

        return ReportService.query_report_data(
            query,
            report_query,
            header_property_id.get("property_id"),
            organization_id=g.organization_id,
            user=g.user,
        )


@blp.route("/query/summary")
class ReportQuerySummary(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportQueryParamsSchema, location="query")
    @blp.arguments(ReportQuerySchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportSummarySchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportQueryParamsSchema,
        report_query: ReportQuerySchema,
    ):
        """Query report summary

        Query report summary from a report that is not created

        Mode:
        - Preview: 100 Records
        - Run: 12000 Records

        Based on the report `type` sent in the request, the data structure returned may have multiple formats.

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """

        return ReportService.query_report_summary(
            query,
            report_query,
            header_property_id.get("property_id"),
            organization_id=g.organization_id,
            user=g.user,
        )


@blp.route("/query/export")
class ReportQueryExport(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExportSchema, location="query")
    @blp.arguments(ReportQueryExportSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportExportSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportExportSchema,
        report_query: ReportQueryExportSchema,
    ):
        """Query report data and export to file

        Request a file export of data from any report json that is not created

        Query report data from a report that is not created

        Based on the report `type` sent in the request, the data structure returned may have multiple formats.

        Mode:
        - Export: 100000 Records

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return ReportService.get_report_export(
            report_query,
            query,
            header_property_id.get("property_id"),
            g.organization_id,
            g.user,
        )


@blp.route("/<int:report_id>/custom_cdfs")
class ReportsByIdCustomCdfs(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomCdfSchema(many=True))
    def get(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
    ):
        """Get custom cdfs on a report

        Get a list of all the custom cdfs created on a report.
        """
        ReportService.get_by_id(report_id, g.user)
        return CustomCdfService.get_by_report_id(ReportKind.Report, report_id)

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportCustomCdfSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ReportCustomCdfSchema)
    def post(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        new_report_custom_cdf: ReportCustomCdfSchema,
        report_id: int,
    ):
        """Create a custom cdf on a report

        - A custom cdf gives the user the ability to create a new cdf based on existing cdf.
        - Column is generated over the name, only with alphanumerical values and lower case. Special characters are replaced with underscore(_)

        **Note**:
        - A custom cdf should have a validate formula.
        """
        report_custom_cdf = CustomCdfService.get_by_report_id_and_column(
            ReportKind.Report, report_id, new_report_custom_cdf["column"]
        )

        if report_custom_cdf is not None:
            return InvalidUsage.conflict(
                "A report custom cdf with this name already exists in this report"
            )

        report_custom_field_cdf = CustomFieldService.get_by_report_id_and_column(
            report_id, new_report_custom_cdf["column"]
        )

        if report_custom_field_cdf is not None:
            return InvalidUsage.conflict(
                "A report custom field cdf with this name already exists in this report"
            )

        report_custom_cdf = ReportCustomCdf.create(
            **new_report_custom_cdf, report_id=report_id, user_id=g.user.id
        )
        return report_custom_cdf


@blp.route("/<int:report_id>/custom_cdfs/<int:custom_cdf_id>")
class ReportsByIdCustomCdfsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdCustomCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomCdfSchema)
    def get(
        self,
        path: PathReportIdCustomCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
        custom_cdf_id: int,
    ):
        """Get custom cdf on a report

        Get a created custom cdf on a report.
        """
        ReportService.get_by_id(report_id, g.user)
        report_custom_cdf = CustomCdfService.get_by_id_and_report_id(
            ReportKind.Report, custom_cdf_id, report_id
        )

        if report_custom_cdf is None:
            return InvalidUsage.not_found(
                "A custom cdf with this id doesn't exist or doesn't belong to the report"
            )

        return report_custom_cdf

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdCustomCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathReportIdCustomCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
        custom_cdf_id: int,
    ):
        """Delete a custom cdf on a report

        Delete a custom cdf on a report.
        """
        ReportService.get_by_id(report_id, g.user)
        report_custom_cdf = CustomCdfService.get_by_id_and_report_id(
            ReportKind.Report, custom_cdf_id, report_id
        )

        if report_custom_cdf is None:
            return InvalidUsage.not_found(
                "A report custom with this id doesn't exist or doesn't belong to the report"
            )

        report_custom_cdf.delete()

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdCustomCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportCustomCdfSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomCdfSchema)
    def put(
        self,
        path: PathReportIdCustomCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        new_report_custom_cdf: ReportCustomCdfSchema,
        report_id: int,
        custom_cdf_id: int,
    ):
        """Update a custom cdf on a report"""
        report_custom_cdf = CustomCdfService.get_by_id_and_report_id(
            ReportKind.Report, custom_cdf_id, report_id
        )

        if report_custom_cdf is None:
            return InvalidUsage.not_found(
                "A custom cdf with this id doesn't exist or doesn't belong to the report"
            )

        report_custom_cdf_duplicate = CustomCdfService.get_by_report_id_and_column(
            ReportKind.Report, report_id, new_report_custom_cdf["column"]
        )

        if (
            report_custom_cdf_duplicate is not None
            and hasattr(report_custom_cdf_duplicate, "id")
            and report_custom_cdf_duplicate.id != custom_cdf_id
        ):
            return InvalidUsage.conflict(
                "A report custom cdf with this name already exists in this report"
            )

        report_custom_field_cdf_duplicate = (
            CustomFieldService.get_by_report_id_and_column(
                report_id, new_report_custom_cdf["column"]
            )
        )

        if report_custom_field_cdf_duplicate is not None:
            return InvalidUsage.conflict(
                "A report custom field cdf with this name already exists in this report"
            )

        new_report_custom_cdf = ReportCustomCdfSchema().dump(new_report_custom_cdf)

        # Update the Custom CDFs in the report if the column name or kind is changed
        if (report_custom_cdf.column != new_report_custom_cdf["column"]) or (
            report_custom_cdf.kind != new_report_custom_cdf["kind"]
        ):
            # Validate custom cdf on charts
            charts = ChartService.get_all_by_datasource_kind_and_id(
                ReportKind.Report.value, report_id
            )

            if charts:
                for chart in charts:
                    validate_chart_custom_cdf(
                        chart, report_custom_cdf, new_report_custom_cdf
                    )

            report = ReportService.get_by_id(report_id, g.user)
            new_report = dict()
            cdf = lambda custom_cdfs: CDF(
                dataset=Dataset(report.dataset_id),
                name=report_custom_cdf.column,
                is_custom_cdf=True,
            ).get_custom_cdf_in_custom_cdfs(custom_cdfs)

            if (
                report.columns is not None
                and bool(CDFs.get_custom_cdfs(report.columns))
                and cdf(report.columns)
            ):
                new_report["columns"] = [
                    (
                        {
                            **column,
                            "cdf": {
                                **column["cdf"],
                                "column": new_report_custom_cdf["column"],
                            },
                        }
                        if column["cdf"]["column"] == report_custom_cdf.column
                        else column
                    )
                    for column in report.columns
                ]

            if (
                report.group_rows is not None
                and bool(CDFs.get_custom_cdfs(report.group_rows))
                and cdf(report.group_rows)
            ):
                new_report["group_rows"] = [
                    (
                        {
                            **group_row,
                            "cdf": {
                                **group_row["cdf"],
                                "column": new_report_custom_cdf["column"],
                            },
                        }
                        if group_row["cdf"]["column"] == report_custom_cdf.column
                        else group_row
                    )
                    for group_row in report.group_rows
                ]

            if (
                report.group_columns is not None
                and bool(CDFs.get_custom_cdfs(report.group_columns))
                and cdf(report.group_columns)
            ):
                new_report["group_columns"] = [
                    (
                        {
                            **group_column,
                            "cdf": {
                                **group_column["cdf"],
                                "column": new_report_custom_cdf["column"],
                            },
                        }
                        if group_column["cdf"]["column"] == report_custom_cdf.column
                        else group_column
                    )
                    for group_column in report.group_columns
                ]

            if (
                report.sort is not None
                and bool(CDFs.get_custom_cdfs(report.sort))
                and cdf(report.sort)
            ):
                new_report["sort"] = [
                    (
                        {
                            **sort,
                            "cdf": {
                                **sort["cdf"],
                                "column": new_report_custom_cdf["column"],
                            },
                        }
                        if sort["cdf"]["column"] == report_custom_cdf.column
                        else sort
                    )
                    for sort in report.sort
                ]

            report.update(**new_report)

            # Update the custom cdfs in the charts
            ChartService.update_custom_cdfs(
                report_id,
                ReportKind.Report.value,
                report_custom_cdf.column,
                new_report_custom_cdf,
            )

        report_custom_cdf.update(**new_report_custom_cdf)
        return report_custom_cdf


@blp.route("/<int:report_id>/custom_cdfs/validate")
class ReportsByIdCustomCdfsValidate(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportCustomCdfValidateSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomCdfValidateSchema)
    def put(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        custom_cdf: ReportCustomCdfValidateSchema,
        report_id: int,
    ):
        """Validate a custom cdf for a report

        In order to create a custom cdf the formula needs to be validated.

        **Notes**:
        - Column is generated over the name, only with alphanumerical values and lower case. Special characters are replaced with underscore(_)
        - A valid formula could be a string concatenation.
        - A valid formula could be a math operation.
        - A valid formula is a list of objects that contains kind and value.
        - `kind`: cdf | separator | operator | operand | parenthesis.
        - `value`: It's the value of the cdf, separator, operator, operand or parenthesis.
        - Separator: Any value.
        - Operator: Valid operators.
        - Operand: Numeric values.
        - Parenthesis: ( or ).
        """
        return custom_cdf


@blp.route("/<int:report_id>/tags")
class ReportsByIdTags(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportTagSchema(many=True))
    def get(
        self,
        path: PathReportIdSchema,
        header_propert_id: HeaderPropertyIdSchema,
        report_id: int,
    ):
        """Get tags on report

        Get a list of tags that a report is associated with.
        """
        report = ReportService.get_by_id(report_id, g.user)
        return report.tags

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportTagIdSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, TagSchema)
    def post(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        tag: ReportTagIdSchema,
        report_id: int,
    ):
        """Create an association between a Report and a tag

        It associates a created tag with a created report.

        """
        report = ReportService.get_by_id(report_id, g.user)
        tag_to_link = Tag.get_by_id(**tag)
        if tag_to_link is None:
            return InvalidUsage.not_found(f'A tag with id:{tag["id"]} does not exist')

        if Report.is_report_tagged(report_id, tag["id"]):
            return InvalidUsage.conflict(
                f'This tag id: {tag["id"]} is already associated with this report id: {report_id}'
            )

        report.tags.append(tag_to_link)
        report.save()

        return tag_to_link


@blp.route("/<int:report_id>/tags/<int:tag_id>")
class ReportsByIdTagsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdTagIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathReportIdTagIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
        tag_id: int,
    ):
        """Delete an association between a Report and a tag

        It unassigns a created tag from a created report.

        """
        report = ReportService.get_by_id(report_id, g.user)
        tag_to_unlink = Tag.get_by_id(tag_id)

        if tag_to_unlink is None:
            return InvalidUsage.not_found(f"A tag with id:{tag_id} does not exist")

        if Report.is_report_tagged(report_id, tag_id) is False:
            return InvalidUsage.not_found(
                f"This tag id: {tag_id} is not associated with this report id: {report_id}"
            )

        report.tags.remove(tag_to_unlink)
        report.save()


@blp.route("filters/relative_dates")
class ReportRelativeDates(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportRelativeDateSchema(many=True))
    def get(self, _header_property_id: HeaderPropertyIdSchema):
        """Get list of possible relative date entries

        Relative dates can either be a standalone keyword, or they can be a keyword
        with a duration separated by a semicolon `;`.

        **Examples without duration:**
        - `"value": "yesterday"`

        - `"value": "years_prior"` (In this case duration is 0 by default)

        **Examples with duration:**
        - `"value": "days_later;42"`

        - `"value": "years_prior;1"`
        """

        return [dict(value=relative_date.value) for relative_date in RelativeDateEnum]


@blp.route("limits")
class ReportLimits(MethodView):
    @authorization
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportLimitSchema)
    def get(self):
        return ReportService.get_limits()


@blp.route("formats")
class ReportFormats(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FormatOptionsSchema(many=True))
    def get(self, _header_property_id: HeaderPropertyIdSchema):
        """Get list of available report format types"""
        return [dict(value=format.value) for format in FormatsKind]


@blp.route("formats/<string:format_type_id>")
class ReportFormatsDate(MethodView):
    @authorization
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathFormatTypeSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, FormatOptionsSchema(many=True))
    def get(
        self,
        _header_property_id: HeaderPropertyIdSchema,
        _: PathFormatTypeSchema,
        format_type_id: str,
    ):
        """Get list of options for a given format type

        **Date Format String Definitions:**
        The defined date format must contain the day, month, and year format strings,
        and a separator for the different units
        - `DD`: 0 padded, 2 digit day number
        - `MM`: 0 padded, 2 digit month number
        - `YYYY`: 4 digit year format
        *Date Separators:*
        - `/`
        - `-`
        - `.`
        """
        return FormatsService.get_formats_by_id(FormatsKind(format_type_id))


@blp.route("search")
class Search(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(SearchReportsQueryParamsSchema, location="query")
    @blp.arguments(QuerySortReportSchema, location="query")
    @blp.arguments(QueryPaginationSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportsSchema)
    @pagination("reports")
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        params: SearchReportsQueryParamsSchema,
        sort: QuerySortReportSchema,
        _: QueryPaginationSchema,
    ):
        """Get report based on search results

        Get reports filtered by different search criteria

        **Notes**:
        - Rows are returned if title contains values (case-insensitive comparison)
        - The API returns 200 even if no results are found.
        """
        return ReportService.get_all_partial_search(
            header_property_id.get("property_id"), params, sort, g.user
        )


@blp.route("/<int:report_id>/charts")
class ReportCharts(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartSchema(many=True))
    def get(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
    ):
        report = ReportService.get_by_id(report_id, g.user)
        charts = ChartService.get_all_by_datasource_kind_and_id(
            ReportKind.Report.value, report.id
        )
        return charts

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ChartSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ChartSchema)
    def post(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        chart_schema: ChartSchema,
        report_id: int,
    ):
        return ChartService.create(chart_schema, ReportKind.Report.value, report_id)


@blp.route("/<int:report_id>/charts/<int:chart_id>")
class ReportChartsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathReportIdChartIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartSchema)
    def get(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathReportIdChartIdSchema,
        report_id: int,
        chart_id: int,
    ):
        report = ReportService.get_by_id(report_id, g.user)
        chart = ChartService.get_by_id_datasource_kind_and_id(
            chart_id, ReportKind.Report.value, report.id
        )
        return chart

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathReportIdChartIdSchema, location="path")
    @blp.arguments(ChartSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ChartSchema)
    def put(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathReportIdChartIdSchema,
        chart_schema: ChartSchema,
        report_id: int,
        chart_id: int,
    ):
        return ChartService.update(
            chart_id, ReportKind.Report.value, report_id, chart_schema
        )

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(PathReportIdChartIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        header_property_id: HeaderPropertyIdSchema,
        path: PathReportIdChartIdSchema,
        report_id: int,
        chart_id: int,
    ):
        ChartService.delete(chart_id, ReportKind.Report.value, report_id)


@blp.route("/<int:report_id>/export/async")
class ReportByIdExportAsync(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExportSchema, location="query")
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportExportSchema,
        path: PathReportIdSchema,
        report_id: int,
    ):
        """Queue task to export report by id"""
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return TaskService.queue_report_export_by_id(
            report_id,
            query.get("view"),
            query.get("format"),
            query.get("include_charts"),
            header_property_id.get("property_id"),
        )


@blp.route("/query/export/async")
class ReportQueryExportAsync(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExportSchema, location="query")
    @blp.arguments(ReportQueryExportSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportExportSchema,
        report_query: ReportQueryExportSchema,
    ):
        """Query report data and export to file

        Request a file export of data from any report json that is not created

        Query report data from a report that is not created

        Mode:
        - Export: 100000 Records

        Based on the report `type` sent in the request, the data structure returned may have multiple formats.

        ## Types
        #### List
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": false,
            }
        }
        ```

        #### List (Totals)
        ```
        {
            ...
            "columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": false,

            }
        }
        ```
        #### PeriodList
        ```
        {
            ...
            "columns": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Summary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,

            }
        }
        ```

        #### Summary (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "settings": {
                ...
                "totals": true
                "transpose: true|false,
            }
        }
        ```
        #### PeriodSummary
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "periods": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false,
            }
        }
        ```


        #### Pivot
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": false,
                "transpose": true|false
            }
        }
        ```

        #### Pivot (Totals)
        ```
        {
            ...
            "columns": ...
            "group_rows": ...
            "group_columns": ...
            "settings": {
                ...
                "totals": true,
                "transpose": true|false,
            }
        }
        ```

        ## Data

        | Type                  | Headers | Index  | Records | Group Rows | Group Columns | Totals | Periods | Transpose |
        | --------------------  | ------- | ------ | ------- | ---------- | ------------- | ------ | ------- | --------- |
        | **List**              | Yes     | No     | Yes     | No         | No            | No     | No      | No        |
        | **List (Totals)**     | Yes     | No     | Yes     | No         | No            | Yes    | No      | No        |
        | **PeriodList**        | Yes     | Yes    | Yes     | No         | No            | No     | Yes     | Yes       |
        | **Summary**           | Yes     | Yes    | Yes     | Yes        | No            | No     | No      | Yes       |
        | **Summary (Details)** | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | No        |
        | **Summary (Totals)**  | Yes     | Yes    | Yes     | Yes        | No            | Yes    | No      | Yes       |
        | **PeriodSummary**     | Yes     | Yes    | Yes     | Yes        | No            | No     | Yes     | Yes       |
        | **Pivot**             | Yes     | Yes    | Yes     | Yes        | Yes           | No     | No      | Yes       |
        | **Pivot (Totals)**    | Yes     | Yes    | Yes     | Yes        | Yes           | Yes    | No      | Yes       |
        """
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return TaskService.queue_report_export_by_query(
            report_query, query, header_property_id.get("property_id")
        )


@blp.route("/export/async")
class ReportsByIdsExportsAsync(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(QueryReportExportByIdsSchema, location="query")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, QueueResponseSchema)
    def post(
        self,
        header_property_id: HeaderPropertyIdSchema,
        query: QueryReportExportByIdsSchema,
    ):
        """Queue task to export multiple reports by ids as single Excel workbook"""
        if (
            not LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExportPdf, header_property_id.get("property_id")
            )
            and query.get("format") == Format.PDF.value
        ):
            raise InvalidUsage.bad_request("Export PDF is not available at the moment")

        return TaskService.queue_export_multiple_reports_by_ids(
            query.get("report_ids"),
            query.get("name"),
            query.get("include_charts"),
        )


@blp.route("/<int:report_id>/custom_field_cdfs")
class ReportsByIdCustomFieldCdfs(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomFieldCdfSchema(many=True))
    def get(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
    ):
        """Get custom field cdfs on a report

        Get a list of all the custom field cdfs created on a report.
        """
        ReportService.get_by_id(report_id, g.user)
        return CustomFieldService.get_by_report_id(report_id)

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Report, PermissionRole.AUTHOR
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportCustomFieldCdfSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, ReportCustomFieldCdfSchema)
    def post(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        new_report_custom_field_cdf: ReportCustomFieldCdfSchema,
        report_id: int,
    ):
        """Create a custom field cdf on a report

        - A custom field cdf gives the user the ability to create a new cdf based on custom fields defined in MFD.
        - Column is generated over the name, only with alphanumerical values and lower case. Special characters are replaced with underscore(_)

        **Note**:
        - A custom cdf field should have a valid internal name to map to for each property id.
        """
        ReportService.get_by_id(report_id, g.user)
        report_custom_field_cdf = CustomFieldService.get_by_report_id_and_column(
            report_id, new_report_custom_field_cdf["column"]
        )

        if report_custom_field_cdf is not None:
            return InvalidUsage.conflict(
                "A report custom field cdf with this name already exists in this report"
            )

        report_custom_cdf = CustomCdfService.get_by_report_id_and_column(
            ReportKind.Report, report_id, new_report_custom_field_cdf["column"]
        )

        if report_custom_cdf is not None:
            return InvalidUsage.conflict(
                "A custom cdf with this name already exists in this report"
            )
        properties = [
            ReportCustomFieldCdfProperty(
                property_id=prop["property_id"], internal_name=prop["internal_name"]
            )
            for prop in new_report_custom_field_cdf["properties"]
        ]

        new_report_custom_field_cdf["properties"] = properties
        report_custom_field_cdf = ReportCustomFieldCdf.create(
            **new_report_custom_field_cdf,
            report_id=report_id,
        )
        return report_custom_field_cdf


@blp.route("/<int:report_id>/custom_field_cdfs/validate")
class ReportsByIdCustomFieldCdfsValidate(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportCustomFieldCdfSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomFieldCdfSchema)
    def put(
        self,
        path: PathReportIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        custom_field_cdf: ReportCustomFieldCdfSchema,
        report_id: int,
    ):
        """Validate custom field cdf"""
        report = ReportService.get_by_id(report_id, g.user)

        validate_report_custom_field_cdf(
            custom_field_cdf, report.dataset_id, report.property_ids
        )

        return custom_field_cdf


@blp.route("/<int:report_id>/custom_field_cdfs/<int:custom_field_cdf_id>")
class ReportsByIdCustomFieldCdfsById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdCustomFieldCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomFieldCdfSchema)
    def get(
        self,
        path: PathReportIdCustomFieldCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
        custom_field_cdf_id: int,
    ):
        """Get custom field cdf on a report

        Get a created custom field cdf on a report.
        """
        ReportService.get_by_id(report_id, g.user)
        report_custom_field_cdf = CustomFieldService.get_by_id_and_report_id(
            custom_field_cdf_id, report_id
        )

        if report_custom_field_cdf is None:
            return InvalidUsage.not_found(
                "A custom field cdf with this id doesn't exist or doesn't belong to the report"
            )

        return report_custom_field_cdf

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdCustomFieldCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(ReportCustomFieldCdfSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, ReportCustomFieldCdfSchema)
    def put(
        self,
        path: PathReportIdCustomFieldCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        new_report_custom_field_cdf: ReportCustomFieldCdfSchema,
        report_id: int,
        custom_field_cdf_id: int,
    ):

        report = ReportService.get_by_id(report_id, g.user)
        """Update a custom field cdf on a report"""
        report_custom_field_cdf = CustomFieldService.get_by_id_and_report_id(
            custom_field_cdf_id, report_id
        )

        if report_custom_field_cdf is None:
            return InvalidUsage.not_found()

        report_custom_field_cdf_duplicate = (
            CustomFieldService.get_by_report_id_and_column(
                report_id, new_report_custom_field_cdf["column"]
            )
        )

        if (
            report_custom_field_cdf_duplicate is not None
            and hasattr(report_custom_field_cdf_duplicate, "id")
            and report_custom_field_cdf_duplicate.id != custom_field_cdf_id
        ):
            return InvalidUsage.conflict(
                "A report custom cdf with this name already exists in this report"
            )

        report_custom_cdf_duplicate = CustomCdfService.get_by_report_id_and_column(
            ReportKind.Report, report_id, new_report_custom_field_cdf["column"]
        )

        if report_custom_cdf_duplicate is not None:
            return InvalidUsage.conflict(
                "A report custom field cdf with this name already exists in this report"
            )

        new_report_custom_field_cdf = ReportCustomFieldCdfSchema().dump(
            new_report_custom_field_cdf
        )

        # Update the Custom CDFs in the report if the column name or kind is changed
        if report_custom_field_cdf.column != new_report_custom_field_cdf["column"]:
            CustomFieldService.update_custom_field_in_report(
                report, report_custom_field_cdf, new_report_custom_field_cdf
            )

        # define model for any new properties added to the custom field cdf
        new_properties = [
            ReportCustomFieldCdfProperty(
                property_id=property["property_id"],
                internal_name=property["internal_name"],
            )
            for property in new_report_custom_field_cdf["properties"]
            if property["property_id"]
            not in [
                str(property.property_id)
                for property in report_custom_field_cdf.properties
            ]
        ]

        # update any existing ReportCustomFieldCdfProperty relationships to have updated internal name
        new_report_custom_field_cdf["properties"] = [
            (
                setattr(
                    property,
                    "internal_name",
                    next(
                        prop["internal_name"]
                        for prop in new_report_custom_field_cdf["properties"]
                        if str(prop["property_id"]) == str(property.property_id)
                    ),
                ),
                property,
            )[1]
            for property in report_custom_field_cdf.properties
            if str(property.property_id)
            in [
                str(prop["property_id"])
                for prop in new_report_custom_field_cdf["properties"]
            ]
        ]

        # merge the new list
        new_report_custom_field_cdf["properties"] += new_properties

        report_custom_field_cdf.update(**new_report_custom_field_cdf)
        return report_custom_field_cdf

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Report, PermissionRole.VIEWER
    )
    @blp.arguments(PathReportIdCustomFieldCdfIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self,
        path: PathReportIdCustomFieldCdfIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        report_id: int,
        custom_field_cdf_id: int,
    ):
        """Delete a custom field cdf on a report

        Delete a custom field cdf on a report.
        """
        report = ReportService.get_by_id(report_id, g.user)

        report_custom_field_cdf = CustomFieldService.get_by_id_and_report_id(
            custom_field_cdf_id, report_id
        )

        if report_custom_field_cdf is None:
            return InvalidUsage.not_found(
                "A report custom with this id doesn't exist or doesn't belong to the report"
            )

        custom_field_cdf_columns = list(
            set(
                [cdf["cdf"]["column"] for cdf in get_custom_field_cdfs(report.__dict__)]
            )
        )

        if report_custom_field_cdf.column in list(
            set(
                [
                    formula["value"]
                    for custom_cdf in report.custom_cdfs
                    for formula in custom_cdf.formula
                ]
            )
        ):
            raise InvalidUsage.bad_request(
                "Cannot delete a custom field cdf that is being used in a formulated cdf"
            )

        if report_custom_field_cdf.column in custom_field_cdf_columns:
            raise InvalidUsage.bad_request(
                "Cannot delete a custom field cdf that is being used in the report"
            )

        report_custom_field_cdf.delete()
        return
