from http import HTTPStatus

from flask import g, request
from flask.views import MethodView

from app.api.v1_1.schemas.tag import PathTagIdSchema, TagSchema
from app.common.constants.api_version import API_VERSION
from app.common.enums.permissions import PermissionAction, PermissionRole, Resources
from app.common.exceptions import InvalidUsage
from app.common.smorest import Blueprint
from app.decorators.authorization import (
    check_user_has_permission,
    has_property_permission,
)
from app.models.tag import Tag
from app.schemas.authorization import HeaderPropertyIdSchema
from app.schemas.error import ErrorSchema


blp = Blueprint(
    "Tags", "tags", url_prefix=f"{API_VERSION}/tags", description="Operations on Tags"
)


@blp.route("")
class TagsByPropertyId(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.VIEW, Resources.Tag, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, TagSchema(many=True))
    def get(self, header_property_id: HeaderPropertyIdSchema):
        """List of Tags per property Id

        Obtain a list of all the created tags under a property.
        """
        tags = Tag.get_all_by_property_id(header_property_id.get("property_id"))
        return tags

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.CREATE, Resources.Tag, PermissionRole.VIEWER
    )
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(TagSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.CONFLICT, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.CREATED, TagSchema)
    def post(self, header_property_id: HeaderPropertyIdSchema, tag: TagSchema):
        """Create a tag for a property

        A tag gives the ability to the user to organize, and label their reports to a better seach and easy indentification.

        **Notes**
        - A Tag could not be duplicated.
        - There is distincion with capital letters. So Financial and financial are 2 different tags.

        """
        existing_tag = Tag.get_by_name_and_property_id(property_id=g.property_id, **tag)

        if existing_tag is not None:
            InvalidUsage.conflict(message="This tag already exist")

        tag = Tag.create(**tag, user_id=g.user.id, property_id=g.property_id)
        return tag


@blp.route("/<int:id>")
class TagById(MethodView):
    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.DELETE, Resources.Tag, PermissionRole.VIEWER
    )
    @blp.arguments(PathTagIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.NO_CONTENT)
    def delete(
        self, path: PathTagIdSchema, header_property_id: HeaderPropertyIdSchema, id: int
    ):
        """Delete tag

        Delete a tag that is not used anymore.
        """
        tag = Tag.get_by_id_with_permissions(
            id, access_token=request.headers.get("Authorization")
        )
        tag.delete()

    @has_property_permission()
    @check_user_has_permission(
        PermissionAction.UPDATE, Resources.Tag, PermissionRole.VIEWER
    )
    @blp.arguments(PathTagIdSchema, location="path")
    @blp.arguments(HeaderPropertyIdSchema, location="headers")
    @blp.arguments(TagSchema, location="json")
    @blp.alt_response(HTTPStatus.INTERNAL_SERVER_ERROR, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.BAD_REQUEST, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.UNAUTHORIZED, schema=ErrorSchema)
    @blp.alt_response(HTTPStatus.NOT_FOUND, schema=ErrorSchema)
    @blp.response(HTTPStatus.OK, TagSchema)
    def put(
        self,
        path: PathTagIdSchema,
        header_property_id: HeaderPropertyIdSchema,
        updated_tag: TagSchema,
        id: int,
    ):
        """Update a tag

        Update a tag name. All the reports using it will be updated with the new name.

        **Note**
        - A Tag could not be duplicated.
        - There is distincion with capital letters. So Financial and financial are 2 different tags.
        """
        tag = Tag.get_by_id_with_permissions(
            id, access_token=request.headers.get("Authorization")
        )

        if tag.name != updated_tag["name"]:
            existing_tag = Tag.get_by_name_and_property_id(
                property_id=header_property_id.get("property_id"), **updated_tag
            )

            if existing_tag is not None:
                InvalidUsage.conflict(message="A tag with this name already exist")

        tag.update(**updated_tag)
        return tag
