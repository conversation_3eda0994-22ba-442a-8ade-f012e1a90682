from sqlalchemy import (
    BIGINT,
    Column,
    DATE,
    INTEGER,
    TIMESTAMP,
    VARCHAR,
)

from app.common.constants.reservation_status import RESERVATION_STATUSES
from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class OccupancyReservationsViewMixin(object):
    # FIXED CDFS
    organization_id = Column(BIGINT, primary_key=True)
    property_id = Column(BIGINT, primary_key=True)
    booking_id = Column(BIGINT, primary_key=True)
    booking_room_id = Column(INTEGER, primary_key=True)
    room_type_id = Column(BIGINT, primary_key=True)
    stay_date = Column(DATE, primary_key=True)

    # CDFS
    booking_date = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Reservation,
            name="Booking Date",
            description="The date when the reservation was originally made.",
            kind=CdfKind.Timestamp,
        ),
    )
    public_rate_plan_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Rate Plan - Public Name",
            description="""The rate plan name that is visible to the customer.
            If the reservation contains multiple rooms the values are separated by commas.""",
            kind=CdfKind.String,
        ),
    )
    private_rate_plan_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Rate Plan - Private Name",
            description="""The internal rate plan name that is unique to the rate plan.
            If the reservation contains multiple rooms the values are separated by commas.""",
            kind=CdfKind.String,
        ),
    )
    reservation_source = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Source",
            description="The method used to make the reservation, such as phone, booking engine, or an online travel agency (OTA).",
            kind=CdfKind.String,
            translate=False,
        ),
    )
    reservation_source_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Source Category",
            description="The type of reservation source used, such as direct, group, wholesaler, or travel agent.",
            kind=CdfKind.String,
            translate=False,
        ),
    )
    reservation_origin = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Origin",
            description="""The specific third party that the guest chose to use to make their
            reservation, which may have been made available through agreements between the
            hotel and a different third party. This means that if the guest made the reservation
            through Hotels.com, even if the hotel had an agreement with Expedia, the origin of
            the booking would still be Hotels.com.""",
            kind=CdfKind.String,
        ),
    )
    reservation_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Status",
            description="""Status of the reservation: Status of the Reservation: Confirmed,
            Confirmed Pending, Cancelled, In-House, Checked Out, No Show, or Inquiry.""",
            kind=CdfKind.PickList,
            options=RESERVATION_STATUSES,
        ),
    )


class OccupancyReservationsView(OccupancyReservationsViewMixin, db.Model):
    __tablename__ = "occupancy_v1_reservations_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class OccupancyReservationsFFView(OccupancyReservationsViewMixin, db.Model):
    __tablename__ = "occupancy_v1_reservations_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"

    group_allotment_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Allotment Block Name",
            description="Name associated with the reservation's group block.",
            kind=CdfKind.String,
        ),
    )

    group_profile_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Name",
            description="""Group Profile is the master profile under which a certain
            group of reservations is created or added. This could be a company,
            wedding party, travel agent, etc. It may or may not be associated
            with a group block allotment.
            """,
            kind=CdfKind.String,
        ),
    )
