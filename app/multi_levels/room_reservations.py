from sqlalchemy import (
    BIGINT,
    BOOLEAN,
    Case,
    Column,
    DATE,
    FLOAT,
    INTEGER,
    VARCHAR,
    and_,
    case,
    func,
)
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.orm import column_property, declared_attr


from app.common.constants.room_type_category import ROOM_TYPE_CATEGORIES
from app.common.database import db
from app.common.enums.features import LaunchDarklyFeature
from app.enums import CdfKind


class RoomReservationsViewMixin(object):
    organization_id = Column(BIGINT)
    property_id = Column(BIGINT)
    reservation_number = Column(VARCHAR)
    booking_id = Column(BIGINT)
    room_identifier = Column(
        VARCHAR,
        primary_key=True,
        info=dict(
            name="Room Identifier",
            description="Room number(s) assigned to the reservation, with each room listed separately.",
            kind=CdfKind.String,
        ),
    )
    room_type = Column(
        VARCHAR,
        info=dict(
            name="Room Type",
            description="The room type(s) associated with the reservation, with each listed separately.",
            kind=CdfKind.String,
        ),
    )
    room_number = Column(
        VARCHAR,
        info=dict(
            name="Room Number",
            description="The room number associated with the reservation, with each room listed separately.",
            kind=CdfKind.String,
        ),
    )
    room_primary_guest_first_name = Column(
        VARCHAR,
        info=dict(
            name="Room Primary Guest First Name",
            description="The first name of the primary guest for this room.",
            kind=CdfKind.String,
        ),
    )
    room_primary_guest_last_name = Column(
        VARCHAR,
        info=dict(
            name="Room Primary Guest Surname",
            description="The surname of the primary guest for this room.",
            kind=CdfKind.String,
        ),
    )
    room_primary_guest_full_name = Column(
        VARCHAR,
        info=dict(
            name="Room Primary Guest Full Name",
            description="The full name of the primary guest for this room.",
            kind=CdfKind.String,
        ),
    )
    room_reservation_price = Column(
        FLOAT,
        info=dict(
            name="Room Reservation Revenue",
            description="The room revenue for a particular room.",
            kind=CdfKind.Currency,
        ),
    )
    room_total_price = Column(
        FLOAT,
        info=dict(
            name="Room Total Price",
            description="The total price for the room reservation including taxes and fees.",
            kind=CdfKind.Currency,
        ),
    )
    room_checkin_date = Column(
        DATE,
        info=dict(
            name="Room Check-in Date",
            description="The day that the reservation for the room begins.",
            kind=CdfKind.Date,
        ),
    )
    room_checkout_date = Column(
        DATE,
        info=dict(
            name="Room Check-out Date",
            description="The day that the reservation for the room ends.",
            kind=CdfKind.Date,
        ),
    )
    room_reservation_status = Column(
        VARCHAR,
        info=dict(
            name="Room Reservation Status",
            description="Status of the room reservation: Confirmed, Confirmed Pending, Cancelled, In-House, Checked Out, or No Show. Could be another custom, hardcoded status.",
            kind=CdfKind.String,
        ),
    )
    room_length_of_stay_count = Column(
        INTEGER,
        info=dict(
            name="Room Length of Stay",
            description="The number of room nights associated with the reservation. For example, 3 rooms for 2 nights = 6 room nights.",
            kind=CdfKind.Number,
        ),
    )
    adults_per_room_count = Column(
        INTEGER,
        info=dict(
            name="Adults per Room Reservation",
            description="The number of adults staying in the room.",
            kind=CdfKind.Number,
        ),
    )
    kids_per_room_count = Column(
        INTEGER,
        info=dict(
            name="Children per Room Reservation",
            description="Number of children staying in the room.",
            kind=CdfKind.Number,
        ),
    )
    room_guest_count = Column(
        INTEGER,
        info=dict(
            name="Room Guest Count",
            description="Number of guests in the room. Equals Room Adult Count + Room Children Count.",
            kind=CdfKind.Number,
        ),
    )
    breakfast_flag = Column(
        BOOLEAN,
        info=dict(
            name="Breakfast Flag",
            description="It indicates whether a reservation booked through Booking.com or Agoda includes breakfast or not.",
            kind=CdfKind.Boolean,
            feature_flag=LaunchDarklyFeature.ReservationDatasetBreakfast,
        ),
    )
    room_private_rate_plan = Column(
        VARCHAR,
        info=dict(
            name="Room Rate Plans - Private Names",
            description="The rate plan names related to the reservation rooms that are visible to the customer.",
            kind=CdfKind.String,
        ),
    )
    room_public_rate_plan = Column(
        VARCHAR,
        info=dict(
            name="Room Rate Plans - Public Names",
            description="The internal rate plan name that is unique to the rate plan. OTA internal rate plan names are not supported and will be empty.",
            kind=CdfKind.String,
        ),
    )
    room_channel_rate_plan = Column(
        VARCHAR,
        info=dict(
            name="Room Rate Plans - Channel Name",
            description="The rate plan name received from the channel.",
            kind=CdfKind.String,
        ),
    )
    room_meal_plans = Column(
        VARCHAR,
        info=dict(
            name="Meal Plans",
            description="A list of meal plans included in this room reservation. If it contains multiple meal plans, the values are separated by commas.",
            kind=CdfKind.String,
        ),
    )
    room_type_category = Column(
        VARCHAR,
        info=dict(
            name="Room Type Category",
            description="Shows if the room booked was either a private room or a shared room.",
            kind=CdfKind.PickList,
            options=ROOM_TYPE_CATEGORIES,
        ),
    )

    @declared_attr
    def unassigned_accommodation(cls):
        return column_property(
            case(
                (cls.room_number.is_(None), 1),
                else_=0,
            ),
            info=dict(
                name="Unassigned Accommodation",
                description=(
                    "The count of unassigned accommodations for the reservation. "
                    "The difference between how many room_types were reserved and how many room_numbers were assigned."
                ),
                kind=CdfKind.Dynamic,
            ),
        )

    @declared_attr
    def unassigned_accommodation_summary(cls):
        return column_property(
            (
                func.count(cls.room_type)
                - func.sum(case((cls.room_number.is_(None), 0), else_=1))
            ),
            info=dict(
                name="Unassigned Accommodation",
                description=(
                    "The count of unassigned accommodations for the reservation. "
                    "The difference between how many room_types were reserved and how many room_numbers were assigned."
                ),
                hide=True,
                kind=CdfKind.Dynamic,
            ),
        )

    @hybrid_method
    def unassigned_accommodation_cases(cls, cases: list[Case], **kwargs):
        unassigned_room_number = case(
            (
                and_(*cases),
                case(
                    (cls.room_number.is_(None), 1),
                    else_=0,
                ),
            ),
            else_=0,
        )
        return column_property(
            unassigned_room_number,
            info=dict(
                name="Unassigned Accommodation",
                description=(
                    "The count of unassigned accommodations for the reservation. "
                    "The difference between how many room_types were reserved and how many room_numbers were assigned."
                ),
                kind=CdfKind.Dynamic,
            ),
        )

    @hybrid_method
    def unassigned_accommodation_summary_cases(cls, cases: list[Case], **kwargs):
        reservations_with_room_types = case(
            (and_(*cases), cls.room_type),
            else_=None,
        )
        unassigned_room_number = case(
            (
                and_(*cases),
                case(
                    (cls.room_number.is_(None), 0),
                    else_=1,
                ),
            ),
            else_=None,
        )

        return column_property(
            (
                func.count(reservations_with_room_types)
                - func.sum(unassigned_room_number)
            ),
            info=dict(
                name="Unassigned Accommodation",
                description=(
                    "The count of unassigned accommodations for the reservation. "
                    "The difference between how many room_types were reserved and how many room_numbers were assigned."
                ),
                hide=True,
                kind=CdfKind.Dynamic,
            ),
        )


class RoomReservationsView(RoomReservationsViewMixin, db.Model):
    __tablename__ = "room_reservations_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class RoomReservationsFFView(RoomReservationsViewMixin, db.Model):
    __tablename__ = "room_reservations_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
