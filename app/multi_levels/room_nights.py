from sqlalchemy import BIGINT, Column, DATE, FLOAT, INTEGER, VARCHAR


from app.common.database import db
from app.enums import CdfKind


class RoomNightsViewMixin(object):
    property_id = Column(BIGINT)
    reservation_number = Column(VARCHAR)
    booking_id = Column(BIGINT)
    room_identifier = Column(VARCHAR, primary_key=True)
    stay_date = Column(
        DATE,
        primary_key=True,
        info=dict(
            name="Stay Date",
            description="The dates that the customer stays in the hotel. For example, if a guest checks in on July 1st and checks out on July 3rd, "
            "their stay dates are July 1st and July 2nd.",
            kind=CdfKind.Date,
        ),
    )
    room_rate = Column(
        FLOAT,
        info=dict(
            name="Room Rate",
            description="The rate of the room for the particular stay date.",
            kind=CdfKind.Currency,
        ),
    )
    additional_charges_extra_adults = Column(
        INTEGER,
        info=dict(
            name="Room Night Extra Adult Charges",
            description="The charges per night for an extra adult in the room.",
            kind=CdfKind.Currency,
        ),
    )
    additional_charges_extra_kids = Column(
        INTEGER,
        info=dict(
            name="Room Night Extra Kids Charges",
            description="The charges per night for an extra child in the room.",
            kind=CdfKind.Currency,
        ),
    )

    organization_id = Column(BIGINT)


class RoomNightsView(RoomNightsViewMixin, db.Model):
    __tablename__ = "room_night_rates_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class RoomNightsFFView(RoomNightsViewMixin, db.Model):
    __tablename__ = "room_night_rates_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
