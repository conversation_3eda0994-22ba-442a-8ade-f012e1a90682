from sqlalchemy import BIGINT, Column, DATE, FLOAT, INTEGER, VARCHAR


from app.common.database import db
from app.enums import CdfKind


class InvoiceItemsViewMixin(object):
    property_id = Column(BIGINT)
    invoice_item_id = Column(
        VARCHAR,
        primary_key=True,
        info=dict(
            name="Invoice Item ID",
            description="The ID associated with the invoice item.",
            kind=CdfKind.Identifier,
        ),
    )
    invoice_id = Column(INTEGER)
    date_posted = Column(
        DATE,
        info=dict(
            name="Date Posted", description="Transaction date", kind=CdfKind.Timestamp
        ),
    )
    description = Column(
        VARCHAR,
        info=dict(
            name="Description",
            description="Description of the transaction",
            kind=CdfKind.String,
        ),
    )
    quantity = Column(
        INTEGER,
        info=dict(
            name="Invoice Item Quantity",
            description="""This field represents the numerical quantity or count associated with each item or product within an
            invoice. It indicates the number of units or items being invoiced for a particular product or service.""",
            kind=CdfKind.Number,
        ),
    )
    amount = Column(
        FLOAT,
        info=dict(
            name="Invoice Item Amount",
            description="This field represents the transaction amount of each item within an invoice.",
            kind=CdfKind.Currency,
        ),
    )
    parent_id_link = Column(
        VARCHAR,
        info=dict(
            name="Parent ID Link",
            description="ID linking Transaction with associated fees or taxes",
            kind=CdfKind.String,
        ),
    )
    tax_type = Column(
        VARCHAR,
        info=dict(
            name="Invoice Item Tax Type",
            description="This field describes the type or category of tax applied to each individual item within an invoice.",
            kind=CdfKind.String,
        ),
    )
    tax_total = Column(
        FLOAT,
        info=dict(
            name="Invoice Item Tax Amount",
            description="""This field indicates the tax amount linked to each individual transaction or item within the invoice,
            providing a detailed breakdown of the tax component for better analysis and tracking.""",
            kind=CdfKind.Currency,
        ),
    )
    tax_total_percentage = Column(
        FLOAT,
        info=dict(
            name="Invoice Item Tax Percentage",
            description="This field represents the percentage tax rate applied to each individual item within an invoice.",
            kind=CdfKind.Number,
        ),
    )

    organization_id = Column(BIGINT)


class InvoiceItemsView(InvoiceItemsViewMixin, db.Model):
    __tablename__ = "invoice_items_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class InvoiceItemsFFView(InvoiceItemsViewMixin, db.Model):
    __tablename__ = "invoice_items_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
