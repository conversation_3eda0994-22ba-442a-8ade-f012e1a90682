import logging
import os

from app.common.constants.languages import DEFAULT_LANGUAGE, LANGUAGES


class Config(object):
    # Application Configuration
    FLASK_APP = "run.py"
    FLASK_DEBUG = True
    APP_DIR = os.path.abspath(os.path.dirname(__file__))
    PROJECT_ROOT = os.path.abspath(os.path.join(APP_DIR, os.pardir))
    TESTING = False
    DEBUG = False
    ASSETS_DEBUG = False
    JSON_SORT_KEYS = False
    SQLALCHEMY_ECHO = False
    SERVICE_KEY = os.getenv("SERVICE_KEY", None)
    BABEL_SUPPORTED_LOCALES = LANGUAGES
    BABEL_DEFAULT_LOCALE = DEFAULT_LANGUAGE
    BABEL_TRANSLATION_DIRECTORIES = "translations"
    SERVICE_ROUTE = "/datainsights"
    LOG_LEVEL = int(os.getenv("LOG_LEVEL", logging.INFO))
    # Aurora Database Configuration
    AURORA_HOST = os.getenv("AURORA_HOST", None)
    AURORA_PORT = int(os.getenv("AURORA_PORT", 0))
    AURORA_DATABASE = os.getenv("AURORA_DATABASE", None)
    # Aurora Dataset User Configuration
    DATASET_USER = os.getenv("DATASET_USER", None)
    DATASET_PASSWORD = os.getenv("DATASET_PASSWORD", None)
    DATASET_POOL_SIZE = int(os.getenv("DATASET_POOL_SIZE", 1))
    DATASET_MAX_OVERFLOW = int(os.getenv("DATASET_MAX_OVERFLOW", 1))
    DATASET_POOL_RECYCLE = int(os.getenv("DATASET_POOL_RECYCLE", 3600))
    # Aurora Default User Configuration
    AURORA_USER = os.getenv("AURORA_USER", None)
    AURORA_PASSWORD = os.getenv("AURORA_PASSWORD", None)
    AURORA_POOL_SIZE = int(os.getenv("AURORA_POOL_SIZE", 1))
    AURORA_MAX_OVERFLOW = int(os.getenv("AURORA_MAX_OVERFLOW", 1))
    AURORA_POOL_RECYCLE = int(os.getenv("AURORA_POOL_RECYCLE", 3600))
    AURORA_UPDATED_AT_QUERY_TIMEOUT_MILISECONDS = int(
        os.getenv("AURORA_UPDATED_AT_QUERY_TIMEOUT_MILISECONDS", 5000)
    )
    # Reporting Database Configuration
    DATABASE_HOST = os.getenv("DATABASE_HOST", None)
    DATABASE_PORT = int(os.getenv("DATABASE_PORT", 0))
    DATABASE_USER = os.getenv("DATABASE_USER", None)
    DATABASE_PASSWORD = os.getenv("DATABASE_PASSWORD", None)
    DATABASE_NAME = os.getenv("DATABASE_NAME", None)
    DATABASE_POOL_SIZE = int(os.getenv("DATABASE_POOL_SIZE", 1))
    DATABASE_MAX_OVERFLOW = int(os.getenv("DATABASE_MAX_OVERFLOW", 1))
    DATABASE_POOL_RECYCLE = int(os.getenv("DATABASE_POOL_RECYCLE", 3600))
    # Redis Configuration
    CACHE_TYPE = "flask_caching.backends.RedisCache"
    CACHE_REDIS_HOST = os.getenv("CACHE_REDIS_HOST", None)
    CACHE_REDIS_PORT = os.getenv("CACHE_REDIS_PORT", 0)
    # AWS/S3/SES Configuration
    AWS_S3_ACCESS_KEY_ID = os.getenv("AWS_S3_ACCESS_KEY_ID", None)
    AWS_S3_SECRET_ACCESS_KEY = os.getenv("AWS_S3_SECRET_ACCESS_KEY", None)
    AWS_REGION = os.getenv("AWS_REGION", None)
    S3_BUCKET = os.getenv("S3_BUCKET", None)
    S3_ENDPOINT_URL = os.getenv("S3_ENDPOINT_URL", None)
    BOTO_CLIENT_READ_TIMEOUT_SECONDS = int(
        os.getenv("BOTO_CLIENT_READ_TIMEOUT_SECONDS", 600)
    )
    BOTO_CLIENT_CONNECT_TIMEOUT_SECONDS = int(
        os.getenv("BOTO_CLIENT_CONNECT_TIMEOUT_SECONDS", 600)
    )
    BOTO_CLIENT_MAX_RETRIES = int(os.getenv("BOTO_CLIENT_MAX_RETRIES", 1))
    BOTO_CLIENT_MAX_POOL_CONNECTIONS = int(
        os.getenv("BOTO_CLIENT_MAX_POOL_CONNECTIONS", 10)
    )
    EMAIL_SENDER = os.getenv("EMAIL_SENDER", "<EMAIL>")
    # Microservices Configuration
    ORGANIZATION_SERVICE_URL = os.getenv("ORGANIZATION_SERVICE_URL", None)
    ORGANIZATION_SERVICE_SSL = bool(int(os.getenv("ORGANIZATION_SERVICE_SSL", 0)))
    DISTRIBUTED_ID_SERVICE_URL = os.getenv("DISTRIBUTED_ID_SERVICE_URL", None)
    DISTRIBUTED_ID_SERVICE_SSL = bool(int(os.getenv("DISTRIBUTED_ID_SERVICE_SSL", 1)))
    USER_SERVICE_URL = os.getenv("USER_SERVICE_URL", None)
    USER_SERVICE_SSL = bool(int(os.getenv("USER_SERVICE_SSL", 1)))
    GRPC_TIMEOUT = int(os.getenv("GRPC_TIMEOUT", 10))
    # Kong Configuration
    KONG_OIDC_INSTROSPECTION_ENDPOINT = os.getenv(
        "KONG_OIDC_INSTROSPECTION_ENDPOINT", None
    )
    KONG_OIDC_CLIENT_ID = os.getenv("KONG_OIDC_CLIENT_ID", None)
    KONG_OIDC_CLIENT_SECRET = os.getenv("KONG_OIDC_CLIENT_SECRET", None)
    # Other Dependencies
    LAUNCH_DARKLY_SDK_KEY = os.getenv("LAUNCH_DARKLY_SDK_KEY", None)
    REPORTING_CHARTS_LAMBDA = os.getenv("REPORTING_CHARTS_LAMBDA", None)
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", None)
    GX_LINK_SHORTENER_URL = os.getenv("GX_LINK_SHORTENER_URL", None)
    GX_API_KEY = os.getenv("GX_API_KEY", None)
    INSIGHTS_STREAMING_API_URL = os.getenv("INSIGHTS_STREAMING_API_URL", None)
    # Worker Configuration
    CELERY_LOG_LEVEL = int(
        os.getenv("CELERY_LOG_LEVEL", LOG_LEVEL)
    )  # Default to service log level
    CELERY = dict(
        broker_url=f"redis://{CACHE_REDIS_HOST}:{CACHE_REDIS_PORT}/0",
        broker_transport_options=dict(
            visibility_timeout=int(os.getenv("BROKER_VISIBILITY_TIMEOUT_SECONDS", 3600))
        ),
        result_backend=f"redis://{CACHE_REDIS_HOST}:{CACHE_REDIS_PORT}/0",
        worker_max_tasks_per_child=int(os.getenv("WORKER_MAX_TASKS_PER_CHILD", 100)),
        worker_max_memory_per_child=int(
            os.getenv("WORKER_MAX_MEMORY_PER_CHILD_KILOBYTES", 3900000)
        ),  # Kilobytes
        result_expires=int(
            os.getenv("RESULT_EXPIRES_SECONDS", 604800)
        ),  # seconds (1 week = 604800 seconds)
        result_extended=bool(int(os.getenv("RESULT_EXTENDED", 1))),
        task_track_started=bool(int(os.getenv("TASK_TRACK_STARTED", 1))),
        task_time_limit=int(os.getenv("TASK_TIME_LIMIT_SECONDS", 900)),  # seconds
        task_acks_late=bool(os.getenv("TASK_ACKS_LATE", 1)),
        task_reject_on_worker_lost=bool(os.getenv("TASK_REJECT_ON_WORKER_LOST", 1)),
        broker_connection_retry_on_startup=True,
        broker_connection_retry=True,
        broker_channel_error_retry=True,
        worker_cancel_long_running_tasks_on_connection_loss=True,  # Will be default in 6.0
        worker_log_format=(
            "{'time': %(asctime)s," "'level': %(levelname)s," "'message': %(message)s}"
        ),
        worker_task_log_format=(
            "{'time': %(asctime)s,"
            "'level': %(levelname)s,"
            "'message': %(message)s,"
            "'task_name': %(task_name)s,"
            "'task_id': %(task_id)s}"
        ),
    )
    GX_LINK_SHORTENER_URL = os.getenv("GX_LINK_SHORTENER_URL", None)
    GX_API_KEY = os.getenv("GX_API_KEY", None)
    PROTECTED_TASK_PAGE = os.getenv("PROTECTED_TASK_PAGE", None)
    DI_TOKEN_SECRET = os.getenv("DI_TOKEN_SECRET", "")
    DI_TOKEN_EXPIRATION_DAYS = os.getenv("DI_TOKEN_EXPIRATION_DAYS", 7)
    DI_SECURE_DOWNLOAD_PAGE = os.getenv("DI_SECURE_DOWNLOAD_PAGE", None)
    DI_ANON_DOWNLOAD_PAGE = os.getenv("DI_ANON_DOWNLOAD_PAGE", None)
    MFD_DOMAIN = os.getenv("MFD_DOMAIN", None)
    PERMISSION_SERVICE_URL = os.getenv("PERMISSION_SERVICE_URL", None)
    PERMISSION_SERVICE_SSL = bool(int(os.getenv("PERMISSION_SERVICE_SSL", 0)))
    PERMISSION_SERVICE_THREADS = os.getenv("PERMISSION_SERVICE_THREADS", 2)
    PERMISSION_SERVICE_CHUNKS = os.getenv("PERMISSION_SERVICE_CHUNKS", 2)


class LOCAL(Config):
    """Local configuration"""

    ENV = "LOCAL"
    DEBUG = True
    TESTING = True
    FLASK_DEBUG = True


class DEVELOPMENT(Config):
    """Development configuration"""

    ENV = "DEVELOPMENT"
    DEBUG = True
    TESTING = True
    SQLALCHEMY_ECHO = True
    FLASK_DEBUG = True


class STAGING(Config):
    """Staging configuration"""

    ENV = "STAGING"
    DEBUG = False
    TESTING = False


class PRODUCTION(Config):
    """Production configuration"""

    ENV = "PRODUCTION"
    DEBUG = False
    TESTING = False


class TEST(Config):
    """Test configuration"""

    ENV = "TEST"
    TESTING = True
    DEBUG = True
    CACHE_TYPE = "flask_caching.backends.SimpleCache"
    CACHE_REDIS_HOST = None
    CACHE_REDIS_PORT = 0
    DATABASE_HOST = None
    DATABASE_PORT = 0
    DATABASE_USER = None
    DATABASE_PASSWORD = None
    DATABASE_NAME = None
    AURORA_HOST = None
    AURORA_PORT = 0
    AURORA_USER = None
    AURORA_PASSWORD = None
    AURORA_DATABASE = None
    SERVICE_KEY = "key"


config = {
    "LOCAL": LOCAL,
    "DEVELOPMENT": DEVELOPMENT,
    "STAGING": STAGING,
    "PRODUCTION": PRODUCTION,
    "TEST": TEST,
    "DEFAULT": LOCAL,
}
