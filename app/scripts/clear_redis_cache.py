import os

import redis

from app.common.cache import CACHE_PREFIX
from app.common.keys import (
    DATASETS_CDFS,
    DATASET_CDF_OPTIONS,
    DATASET_MULTI_LEVELS,
    MULTI_LEVEL,
)


def clear_redis_cache():
    # Connect to Redis using environment variables for configuration
    # To run this locally do
    # CACHE_REDIS_HOST=localhost CACHE_REDIS_PORT=6379 python -m app.scripts.clear_redis_cache
    redis_host = os.getenv("CACHE_REDIS_HOST", "localhost")
    redis_port = int(os.getenv("CACHE_REDIS_PORT", 6379))

    client = redis.Redis(host=redis_host, port=redis_port)

    try:
        # Check connection
        client.ping()
        print("Successfully connected to Redis.")

        # Specify the glob pattern
        keys_to_delete_patterns = [
            f"{CACHE_PREFIX}{DATASETS_CDFS}*",
            f"{CACHE_PREFIX}{MULTI_LEVEL}*",
            f"{CACHE_PREFIX}{DATASET_MULTI_LEVELS}*",
            f"{CACHE_PREFIX}{DATASET_CDF_OPTIONS}*",
        ]

        print(f"Keys to delete: {','.join(keys_to_delete_patterns)}")

        # Fetch all keys that match the patterns
        keys_to_delete = []
        for pattern in keys_to_delete_patterns:
            cursor = "0"
            while cursor != 0:
                cursor, keys = client.scan(cursor=cursor, match=pattern)
                keys_to_delete.extend(keys)

        if not keys_to_delete:
            print("No keys matching the pattern were found.")
            return

        # Delete the keys
        deleted = client.delete(*keys_to_delete)
        print(
            f"Successfully deleted {deleted} keys matching the patterns '{keys_to_delete_patterns}'."
        )
    except Exception as e:
        print(f"Error clearing Redis cache: {e}")


if __name__ == "__main__":
    clear_redis_cache()
