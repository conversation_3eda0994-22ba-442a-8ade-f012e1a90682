from typing import Optional

import jwt

from app.enums import UserScopes


class TokenService:
    def __init__(self, token: str):
        if token.startswith("Bearer "):
            token = token[7:]

        self.access_token = token
        self.token = token

    @property
    def token(self):
        return self.__token

    @token.setter
    def token(self, token):
        self.__token = jwt.decode(
            token,
            verify=False,
            algorithms=["RS256"],
            options={"verify_signature": False},
        )

    def get_user_id(self) -> Optional[int]:
        return self.token.get("mfdUserId")

    def get_property_ids(self) -> list[int]:
        property_ids = self.token.get("propertyIds", [])
        return [int(property) for property in property_ids]

    def get_email(self) -> str:
        return self.token.get("sub")

    def get_expired_at(self) -> int:
        return int(self.token.get("exp"))

    def get_issued_at(self) -> int:
        return int(self.token.get("iat"))

    def is_super_admin(self) -> bool:
        return bool(self.token.get("isSuperAdmin", False))

    def get_di_scopes(self) -> list[str]:
        return [
            scope
            for scope in self.token.get("scp", [])
            if scope in [user_scope.value for user_scope in UserScopes]
        ]

    def get_token_type(self) -> Optional[str]:
        return self.token.get("type")
