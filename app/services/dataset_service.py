from datetime import datetime

from flask_babel import lazy_gettext as _

from sqlalchemy.exc import OperationalError

from app.cdfs.cdf import CDF
from app.common.cache import TIMEOUT_5_MINUTES, TIMEOUT_DAY, TIMEOUT_MINUTE, cache
from app.common.constants.dataset import DAT<PERSON>ET_LABELS
from app.common.constants.languages import DEFAULT_LANGUAGE
from app.common.enums.features import LaunchDarklyFeature
from app.common.exceptions import InvalidUsage
from app.common.keys import dataset_updated_at_by_property_id_organization_id_key
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum
from app.models.dataset_monitoring import DatasetMonitoring
from app.services.custom_field_service import CustomFieldService
from app.services.launch_darkly_service import LaunchDarklyService
from app.services.property_feature_service import PropertyFeatureService
from app.services.property_service import PropertyService


class DatasetService:
    def get_read_only_dataset_ids(property_id: int, user_email: str):
        """Get read only dataset ids. If the property has the occupancy
        v1 feature flag, then the occupancy v1 dataset is read only.
        """
        return []

    @cache.memoize(TIMEOUT_5_MINUTES)
    def get_datasets_by_property_id(
        property_id: int, user_email: str, _accept_language: str = DEFAULT_LANGUAGE
    ):
        """Get datasets by property_id"""
        read_only_dataset_ids = DatasetService.get_read_only_dataset_ids(
            property_id, user_email
        )

        occupancy_v1_flag = LaunchDarklyService.has_feature_flag(
            LaunchDarklyFeature.OccupancyV1, property_id
        )

        accounting_flag = LaunchDarklyService.has_feature_flag(
            LaunchDarklyFeature.AccountingDataset, property_id
        )

        bed_occupancy_flag = LaunchDarklyService.has_feature_flag(
            LaunchDarklyFeature.BedOccupancyDataset, property_id
        )

        datasets = [
            {
                "id": dataset.value,
                "name": (f"{_(DATASET_LABELS[dataset])}"),
                "read_only": (
                    True if dataset.value in read_only_dataset_ids else False
                ),
            }
            for dataset in DatasetEnum
            if (
                dataset
                not in (
                    DatasetEnum.OccupancyV1,
                    DatasetEnum.Accounting,
                    DatasetEnum.BedOccupancy,
                )
            )
            or (dataset == DatasetEnum.OccupancyV1 and occupancy_v1_flag)
            or (dataset == DatasetEnum.Accounting and accounting_flag)
            or (dataset == DatasetEnum.BedOccupancy and bed_occupancy_flag)
        ]

        return datasets

    @staticmethod
    @cache.memoize(TIMEOUT_MINUTE)
    def get_dataset_updated_at_by_property_id(
        dataset: DatasetEnum, property_id: int, organization_id: int
    ) -> datetime | None:

        # TODO: Update Freshness for House Keeping and Accounting
        if dataset in [
            DatasetEnum.Housekeeping,
            DatasetEnum.Accounting,
            DatasetEnum.BedOccupancy,
        ]:
            return None

        cache_key = dataset_updated_at_by_property_id_organization_id_key(
            dataset.value, property_id, organization_id
        )
        default_value = cache.get(cache_key)

        try:
            result = (
                DatasetMonitoring.query.with_entities(DatasetMonitoring.freshness_utc)
                .filter(
                    DatasetMonitoring.reporting_table == dataset.name,
                    DatasetMonitoring.property_id == property_id,
                    DatasetMonitoring.organization_id == organization_id,
                )
                .first()
            )
            if result:
                cache.set(cache_key, result.freshness_utc)
                return result.freshness_utc

        except OperationalError as exception:
            logger.warning(
                f"Get Updated at query has been canceled because of query timeout {dataset.name}",
                extra={"error": str(exception)},
            )

        except Exception as exception:
            logger.error(
                f"There was an error getting updated_at from Dataset {dataset.name}",
                extra={"error": str(exception)},
            )

        return default_value

    @staticmethod
    def get_property_custom_fields_by_dataset_id(
        dataset_id: int,
        organization_id: int,
        property_id: int,
        property_ids: list[int] = [],
    ):

        if LaunchDarklyService.has_feature_flag(
            LaunchDarklyFeature.CustomFieldsCDFsFF, property_id
        ):
            custom_fields = CustomFieldService.get_custom_fields_by_dataset_id(
                dataset_id,
                property_id,
                organization_id,
                property_ids,
            )

            return {"property_custom_fields": custom_fields}

        else:
            return {"property_custom_fields": []}

    @staticmethod
    @cache.memoize(TIMEOUT_DAY)
    def get_cdf_options(property_id, dataset_id, cdf):
        category_feature = None

        cdf = CDF(DatasetEnum(dataset_id), cdf)
        dataset = Dataset(DatasetEnum(dataset_id))

        if bool(dataset.feature_categories):
            property_features = PropertyFeatureService.get_all_by_property_id(
                property_id
            )

            category_feature = next(
                (
                    category["feature"]
                    for category in dataset.feature_categories
                    if cdf.category == category["category"]
                ),
                None,
            )

        if bool(dataset.country_categories):
            category_country = next(
                (
                    category["country_code"]
                    for category in dataset.country_categories
                    if cdf.category == category["category"]
                ),
                None,
            )
            if category_feature and category_feature not in property_features:
                return InvalidUsage.not_authorized(
                    "Property does not have access to this cdf"
                )

            country_code = PropertyService.get_property_country_code(property_id)
            if category_country and category_country != country_code:
                return InvalidUsage.not_authorized(
                    "CDF is not applicable to property country"
                )

        dataset_cdf = dict(**cdf.cdf, options=cdf.options)
        return dataset_cdf
