from flask import g, request

from app.common.exceptions import InvalidUsage
from app.common.openapi import X_API_KEY
from app.enums import Format, View
from app.models.task import Task
from app.services.report_service import ReportService
from app.services.stock_report_service import StockReportService

from celery_app.tasks import (
    email_export_reports_by_ids,
    export_report_by_id,
    export_report_by_query,
    export_reports_by_ids,
    export_stock_report_by_id,
    export_stock_report_by_query,
)


class TaskService:
    @staticmethod
    def configure_user_g():
        return dict(
            access_token=g.access_token,
            api_key=request.headers.get(X_API_KEY),
            property_id=g.property_id,
            organization_id=g.organization_id,
            property_ids=g.property_ids,
            island=g.island,
            user=dict(
                id=g.user.id,
                email=g.user.email,
                admin=g.user.admin,
                enabled_datasets=g.user.enabled_datasets,
            ),
            request_id=g.request_id,
            x_amzn_trace_id=request.headers.get("x-amzn-trace-id"),
        )

    @staticmethod
    def queue_report_export_by_id(report_id, view, format, include_charts, property_id):
        """Get report export by id"""
        report = ReportService.get_by_id(report_id, g.user)

        property_ids = report.property_ids
        user_g = TaskService.configure_user_g()
        result = export_report_by_id.delay(
            report_id,
            view,
            format,
            include_charts,
            property_id,
            property_ids,
            user_g,
        )
        results = dict(id=result.id, status=result.status, result=result.result)
        TaskService.insert_task(result.id, "export_report_by_id", report.property_ids)
        return results

    @staticmethod
    def queue_email_report_export_by_id(
        report_ids: list[int],
        subject: str,
        recipients: list[str],
        view: str = View.Formatted.value,
        format: str = Format.XLSX.value,
        schedule_id: int = None,
    ):
        user_g = TaskService.configure_user_g()
        report_property_ids = {
            report_id: ReportService.get_by_id(report_id, g.user).property_ids
            for report_id in report_ids
        }
        """Get report export by ids"""
        result = email_export_reports_by_ids.delay(
            report_ids,
            subject,
            recipients,
            user_g,
            report_property_ids,
            view,
            format,
            schedule_id,
        )
        results = dict(id=result.id, status=result.status, result=result.result)
        TaskService.insert_task(
            result.id, "email_export_reports_by_ids", report_property_ids
        )
        return results

    @staticmethod
    def queue_report_export_by_query(report_query, query, property_id):
        user_g = TaskService.configure_user_g()
        result = export_report_by_query.delay(report_query, query, property_id, user_g)
        results = dict(id=result.id, status=result.status, result=result.result)
        TaskService.insert_task(
            result.id, "export_report_by_query", report_query.get("property_ids")
        )

        return results

    @staticmethod
    def queue_stock_report_export_by_id(stock_report_id, property_id, query):
        StockReportService.get_by_id(stock_report_id, g.user, property_id, g.user.admin)
        user_g = TaskService.configure_user_g()
        result = export_stock_report_by_id.delay(
            stock_report_id, property_id, query, user_g
        )
        results = dict(id=result.id, status=result.status, result=result.result)
        TaskService.insert_task(
            result.id, "export_stock_report_by_id", query.get("property_ids")
        )

        return results

    @staticmethod
    def queue_stock_report_export_by_query(
        stock_report_id, header_property_id, query, report_query
    ):
        user_g = TaskService.configure_user_g()
        result = export_stock_report_by_query.delay(
            stock_report_id,
            header_property_id,
            query,
            report_query,
            user_g,
        )
        results = dict(id=result.id, status=result.status, result=result.result)
        TaskService.insert_task(
            result.id, "export_stock_report_by_query", query.get("property_ids")
        )
        return results

    @staticmethod
    def insert_task(id, name, property_ids):
        task = dict(
            id=id,
            name=name,
            property_id=g.property_id,
            property_ids=property_ids,
            user_id=g.user.id,
        )
        Task.create(**task)

    @staticmethod
    def get_all(user_id, property_id) -> list[Task]:
        return Task.get_all(user_id, property_id)

    @staticmethod
    def get_task_by_id_and_g(id: str):
        task = Task.get_task_by_id(id)

        if not task:
            raise InvalidUsage.not_found()
        else:
            return task

    @staticmethod
    def queue_export_multiple_reports_by_ids(report_ids, name, include_charts):
        """Get report export by id"""
        user_g = TaskService.configure_user_g()
        report_property_ids = {
            report_id: ReportService.get_by_id(report_id, g.user).property_ids
            for report_id in report_ids
        }

        result = export_reports_by_ids.delay(
            report_ids,
            report_property_ids,
            name,
            include_charts,
            user_g,
        )
        results = dict(id=result.id, status=result.status, result=result.result)
        TaskService.insert_task(result.id, "export_reports_by_ids", report_property_ids)
        return results

    @staticmethod
    def get_task_by_id_or_404(id: str):
        return Task.get_task_by_id_or_404(id)
