from typing import List

from flask import g, has_request_context, request

from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.openapi import AUTHORIZATION, X_API_KEY
from app.common.user import User
from app.services.api_key_service import APIKeyService
from app.services.dataset_service import DatasetService
from app.services.property_service import PropertyService


class AccessControlService:
    @staticmethod
    def is_allowed_to_access_properties(property_ids: List[int]) -> bool | Exception:
        """
        Returns true if the property_ids that are given, match with the property_ids that belongs to the access token
        or returns true if the API Key is valid. Otherwhise returns 401 Not Authorized
        """
        if has_request_context():
            api_key = request.headers.get(X_API_KEY)
            authorization = request.headers.get(AUTHORIZATION)
        else:
            api_key = g.get("api_key")
            authorization = g.get("access_token")

        if api_key:
            logger.debug("User is logging with API Key")
            if not APIKeyService.is_api_key_valid(api_key):
                raise InvalidUsage.not_authorized("API Key is invalid")
        elif authorization and bool(property_ids):
            PropertyService.are_valid_property_ids(property_ids)
        else:
            raise InvalidUsage.bad_request(
                "API Key or Authorization header are missing"
            )

        return True

    @staticmethod
    def is_allowed_to_access_dataset(dataset: int, user: User) -> bool | Exception:
        """
        Returns true if the property_ids that are given, match with the property_ids that belongs to the access token
        or returns true if the API Key is valid. Otherwise returns 401 Not Authorized
        """
        if not user.admin and dataset not in user.enabled_datasets:
            raise InvalidUsage.forbidden(
                f"The user is not allowed to access dataset ID {dataset}"
            )

        return True

    @staticmethod
    def is_allowed_to_create_dataset(
        dataset: int, user: User, property_id: int
    ) -> bool | Exception:
        """
        Returns true if the property_ids that are given, match with the property_ids that belongs to the access token
        or returns true if the API Key is valid. Otherwise returns 401 Not Authorized
        """
        if (
            not user.admin
            and dataset not in user.enabled_datasets
            and dataset
            not in DatasetService.get_read_only_dataset_ids(property_id, user.email)
        ):
            raise InvalidUsage.forbidden(
                f"The user is not allowed to access dataset ID {dataset}"
            )

        return True
