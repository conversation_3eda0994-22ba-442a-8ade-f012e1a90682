import os
import re
import unicodedata
from io import BytesIO
from typing import Callable

from boto3.session import Session

from botocore.config import Config

from flask import current_app as app

from openpyxl.utils import get_column_letter

import pandas as pd
from pandas.core.api import DataFrame

from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.enums.export import Format
from app.reports.report_export import ReportExport


def safe_unicode_coerce(val):
    """Coerce non-numeric value to a unicode string, replacing invalid characters.

    :param val: The value to coerce.
    :return: The coerced value.
    """
    if isinstance(val, (int, float, bool)) or val is None:
        return val  # preserve native type
    # Remove invalid characters from other data
    try:
        if isinstance(val, str):
            # Clean bad surrogates
            val = re.sub(r"[\ud800-\udfff]", "", val)
            return (
                unicodedata.normalize("NFKD", val)
                .encode("utf-8", "replace")
                .decode("utf-8", "replace")
            )
        elif isinstance(val, bytes):
            return val.decode("utf-8", "replace")
        else:
            return str(val)
    except Exception:
        # Ultimate fallback
        return f"<unserializable: {type(val).__name__}>"


class S3Service:
    def __init__(
        self,
        path: str,
        filename: str,
        format: Format,
        index: bool,
        customization: Callable,
        charts: list = [],
    ) -> None:
        self.path = path
        self.filename = filename
        self.format = format
        self.index = index
        self.customization = customization
        self.charts = charts
        self.config = Config(
            read_timeout=app.config["BOTO_CLIENT_READ_TIMEOUT_SECONDS"],
            connect_timeout=app.config["BOTO_CLIENT_CONNECT_TIMEOUT_SECONDS"],
            retries={"max_attempts": app.config["BOTO_CLIENT_MAX_RETRIES"]},
            max_pool_connections=app.config["BOTO_CLIENT_MAX_POOL_CONNECTIONS"],
        )
        self.CREDENTIALS = dict(
            aws_access_key_id=app.config["AWS_S3_ACCESS_KEY_ID"],
            aws_secret_access_key=app.config["AWS_S3_SECRET_ACCESS_KEY"],
        )
        self.storage_options = dict(
            client_kwargs=dict(
                endpoint_url=app.config["S3_ENDPOINT_URL"],
                region_name=app.config["AWS_REGION"],
                **self.CREDENTIALS,
            )
        )
        self.s3 = Session().client(
            "s3",
            endpoint_url=app.config["S3_ENDPOINT_URL"],
            config=self.config,
            **self.CREDENTIALS,
        )
        self.bucket = app.config["S3_BUCKET"]

    def __repr__(self):
        """Representation of S3Service
        :return: string
        """
        return f"<S3Service, path={self.path}, filename={self.filename}, format={self.format}, index={self.index}>"

    @property
    def filename(self):
        return self.__filename

    @filename.setter
    def filename(self, filename):
        self.__filename = re.sub(r"[^\w\s\-]|\s{2,}", "", filename)

    @property
    def key(self):
        base = "service"
        return f"{os.path.join(base, self.path, self.filename)}.{self.format}"

    @property
    def uri(self):
        return f"s3://{os.path.join(self.bucket, self.key)}"

    def upload(
        self,
        total_record_count: int,
        df: DataFrame,
        formatted: bool = False,
        index: bool = False,
    ):
        if self.format == Format.CSV.value:
            self.__to_csv(df)
        elif self.format == Format.XLSX.value and not formatted:
            self.__to_xlsx(df)
        elif self.format == Format.XLSX.value and formatted:
            # rename blank indexes to avoid issues with openpyxl
            if df.index.nlevels > 1:
                df.index = pd.MultiIndex.from_tuples(
                    [
                        tuple("~RESERVED_BLANK_STRING" if x == "" else x for x in idx)
                        for idx in df.index
                    ],
                    names=df.index.names,
                )
            else:
                df.index = df.index.map(
                    lambda x: "~RESERVED_BLANK_STRING" if x == "" else x
                )

            self.__to_xlsx_formatted(df, index, total_record_count)
        elif self.format == Format.JSON.value:
            self.__to_json(df)
        elif self.format == Format.PDF.value and formatted:
            self.__to_pdf(df, total_record_count)

    def get_presigned_url(self) -> str:
        return self.s3.generate_presigned_url(
            "get_object",
            Params=dict(Bucket=self.bucket, Key=self.key),
            ExpiresIn=604800,
        )

    def __to_csv(self, df: DataFrame):
        df = df.map(safe_unicode_coerce)
        df.to_csv(self.uri, index=False, storage_options=self.storage_options)

    def __to_xlsx(self, df: DataFrame):
        df.to_excel(self.uri, index=self.index, storage_options=self.storage_options)

    def __to_xlsx_formatted(self, df: DataFrame, index: bool, total_record_count: int):
        writer = pd.ExcelWriter(
            self.uri, engine="openpyxl", storage_options=self.storage_options
        )

        START_ROW = (
            ReportExport.START_ROW_WITH_CHARTS
            if len(self.charts)
            else ReportExport.DEFAULT_START_ROW
        )

        df.to_excel(writer, index=index, startrow=START_ROW)
        worksheet = writer.book.active
        worksheet = self.customization(worksheet, df, total_record_count)

        for idx, _ in enumerate(worksheet.columns, 1):
            worksheet.column_dimensions[get_column_letter(idx)].auto_size = True

        writer.close()

        # Upload images to S3
        base_key = re.sub(f"{self.filename}|.xlsx", "", self.key)
        for index, chart in enumerate(self.charts):
            chart_key = f"{base_key}chart-{index}.png"
            self.s3.put_object(Body=chart, Bucket=self.bucket, Key=chart_key)

    def __to_json(self, df: DataFrame):
        df = df.map(safe_unicode_coerce)
        df.to_json(self.uri, storage_options=self.storage_options)

    def __to_pdf(self, df: DataFrame, total_record_count: int):
        pdf_buffer = BytesIO()

        pdf_buffer = self.customization(
            df=df, pdf_buffer=pdf_buffer, total_record_count=total_record_count
        )

        try:
            self.s3.put_object(Body=pdf_buffer, Bucket=self.bucket, Key=self.key)
        except Exception as exception:
            logger.error(f"Failed to upload PDF to S3: {exception}")
            raise InvalidUsage.server_error("There was a problem generating the export")
        finally:
            pdf_buffer.close()

        # Upload images to S3
        base_key = re.sub(f"{self.filename}|.xlsx", "", self.key)
        for index, chart in enumerate(self.charts):
            chart_key = f"{base_key}chart-{index}.png"
            self.s3.put_object(Body=chart, Bucket=self.bucket, Key=chart_key)
