from http import HTT<PERSON><PERSON><PERSON>

from flask import current_app as app

import requests
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from app.common.cache import TIMEOUT_5_MINUTES, cache
from app.common.constants.limits import REQUESTS_TIMEOUT_SECONDS
from app.common.keys import (
    valid_access_token_key,
)
from app.common.logger import logger
from app.services.token_service import TokenService


class OktaService:
    @staticmethod
    def get_expires_in(issued_at: int, expired_at: int) -> int:
        try:
            return int((expired_at - issued_at))
        except:
            logger.warning(
                "There was a problem calculating the expires in",
                extra={"issued_at": issued_at, "expired_at": expired_at},
            )
            return TIMEOUT_5_MINUTES

    @staticmethod
    def is_valid_access_token(bearer_token: str) -> bool:
        token_service = TokenService(bearer_token)
        access_token = token_service.access_token

        valid_access_token = cache.get(valid_access_token_key(access_token))

        if valid_access_token:
            return True

        response = requests.post(
            url=app.config["KONG_OIDC_INSTROSPECTION_ENDPOINT"],
            auth=HTTPBasicAuth(
                username=app.config["KONG_OIDC_CLIENT_ID"],
                password=app.config["KONG_OIDC_CLIENT_SECRET"],
            ),
            data=dict(token=access_token),
            timeout=REQUESTS_TIMEOUT_SECONDS,
        )

        if response.status_code == HTTPStatus.OK:
            cache.set(
                valid_access_token_key(access_token),
                access_token,
                timeout=OktaService.get_expires_in(
                    token_service.get_issued_at(), token_service.get_expired_at()
                ),
            )
            return True

        logger.warning(
            "OKTA: There was a problem checking the access token",
            extra={"status_code": str(response.status_code)},
        )
        return False
