import datetime
from typing import Optional

from flask import current_app as app

import jwt


class DITokenService:
    """Service for handling minting and validating DI internal tokens"""

    def __init__(self, token: str):
        self.token = token

    @property
    def token(self):
        return self.__token

    @token.setter
    def token(self, token):
        self.__token = jwt.decode(
            token,
            app.config["DI_TOKEN_SECRET"],
            algorithms=["HS256"],
        )

    def get(self, key: str) -> Optional[str]:
        return self.token.get(key)

    @staticmethod
    def mint_token(sub: str, extra: Optional[dict] = None) -> str:
        message = dict(
            sub=sub,
            exp=datetime.datetime.now(datetime.timezone.utc)
            + datetime.timedelta(days=int(app.config["DI_TOKEN_EXPIRATION_DAYS"])),
        )
        if extra:
            message.update(extra)
        return jwt.encode(
            message,
            app.config["DI_TOKEN_SECRET"],
            algorithm="HS256",
        )
