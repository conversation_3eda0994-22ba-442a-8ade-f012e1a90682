from datetime import datetime
from email.mime.image import MIMEImage
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from http import HTTPStatus
from typing import Optional

from boto3.session import Session

from flask import current_app as app

from app.api.v1_1.schemas.log import EmailLogSchema
from app.common.constants.blacklisted_emails import BLACKLISTED_EMAILS
from app.common.logger import logger
from app.enums.log_actions import EmailAction
from app.schemas.scheduled_email import (
    EmailChartSchema,
    ReportLinksSchema,
    ScheduledEmailSchema,
)
from app.services.link_shortener_service import LinkShortenerService
from app.services.organization_service import PropertyService


class EmailService:
    DATETIME_FORMAT = "%Y-%m-%d %H:%M %Z"

    def __init__(self) -> None:
        self.client = Session().client("ses", region_name=app.config["AWS_REGION"])

    def format_datetime(self, date: str) -> str:
        try:
            return datetime.fromisoformat(str(date)).strftime(self.DATETIME_FORMAT)
        except ValueError:
            return str(date)

    def send_export_link(
        self,
        subject: str,
        title: str,
        description: str,
        recipients: list[str],
        created_at: str,
        download_link: str,
        charts: list[EmailChartSchema] = [],
        report_links: list[ReportLinksSchema] = [],
        schedule_id: Optional[int] = None,
        schedule_user_email: Optional[str] = None,
        property_name: Optional[str] = None,
        schedule_url: Optional[str] = None,
        report_kind: Optional[str] = "",
        subscription_recipients: Optional[list] = [],
    ):
        recipients = [email for email in recipients if email not in BLACKLISTED_EMAILS]
        if recipients:
            try:
                # Create the email message
                msg = MIMEMultipart("related")
                msg["Subject"] = subject
                msg["From"] = f"Cloudbeds Insights <{app.config['EMAIL_SENDER']}>"
                msg["To"] = ",".join(recipients)

                download_link = LinkShortenerService.shorten_link(download_link)

                scheduled_email = ScheduledEmailSchema().load(
                    dict(
                        lang="en",
                        title=title,
                        description=description,
                        download_link=download_link,
                        recipients=(
                            subscription_recipients
                            if subscription_recipients
                            else recipients
                        ),
                        created_at=self.format_datetime(created_at),
                        charts=charts,
                        report_links=report_links,
                        schedule_id=schedule_id,
                        schedule_url=schedule_url,
                        schedule_user_email=schedule_user_email,
                        property_name=property_name,
                        expiration_days=app.config["DI_TOKEN_EXPIRATION_DAYS"],
                    )
                )
                # Create the email body HTML part
                html_part = MIMEText(
                    scheduled_email["html"],
                    "html",
                )

                # Attach the HTML part to the email message
                msg.attach(html_part)

                # Create the attachment part for the PNG image
                if charts and len(charts) == 1 and len(report_links) == 1:
                    for index, chart in enumerate(charts, 1):
                        attachment = MIMEImage(chart["chart"])
                        attachment.add_header("Content-ID", f"<cbchart{index}>")
                        attachment.add_header(
                            "Content-Disposition",
                            "inline",
                            filename=f"cbchart{index}.png",
                        )
                        attachment.add_header("Content-Type", "image/png")

                        # Attach the PNG image to the email message
                        msg.attach(attachment)

                # Send the raw email
                response = self.client.send_raw_email(
                    Source=msg["From"],
                    Destinations=recipients,
                    RawMessage={"Data": msg.as_string()},
                )
                if response["ResponseMetadata"]["HTTPStatusCode"] == HTTPStatus.OK:
                    logger.info(
                        "Email Sent",
                        extra=EmailLogSchema().dump(
                            dict(
                                recipients=len(recipients),
                                subject=subject,
                                action=EmailAction.EmailSent.value,
                                report_kind=report_kind,
                            )
                        ),
                    )
                else:
                    logger.info(
                        f"Email Invalid Status Code {response['ResponseMetadata']['HTTPStatusCode']}",
                        extra=EmailLogSchema().dump(
                            dict(
                                recipients=len(recipients),
                                subject=subject,
                                action=EmailAction.EmailFailed.value,
                                report_kind=report_kind,
                            )
                        ),
                    )
                return response
            except Exception as email_error:
                logger.error(
                    f"Error sending email: {email_error}",
                    extra=EmailLogSchema().dump(
                        dict(
                            recipients=len(recipients),
                            subject=subject,
                            action=EmailAction.EmailFailed.value,
                            report_kind=report_kind,
                        )
                    ),
                )
                return None
        else:
            logger.info(
                "Email Not Sent",
                extra=EmailLogSchema().dump(
                    dict(
                        recipients=0,
                        subject=subject,
                        action=EmailAction.EmailNotSent.value,
                        report_kind=report_kind,
                    )
                ),
            )
            return None

    def build_download_link(
        self,
        schedule_property_id: int,
        user_property_ids: list[int],
        download_token: str,
        require_login: bool,
        lang: str,
    ) -> str:
        """Function that will build the subscription download link for the email,
            or an empty string if the user does not have MFD access to a property

        Args:
            schedule_property_id (int): The property id of the schedule
            user_property_ids (list[int]): The active property ids the user has access to
            download_token (str): The download token
            require_login (bool): If the download link requires login"""
        download_link = ""
        if len(user_property_ids) < 1:
            return download_link

        # Use the schedule property ID URL if the user has access to the property
        url_property_id = (
            schedule_property_id
            if schedule_property_id in user_property_ids
            else user_property_ids[0]
        )
        url_sub_domain = PropertyService.get_property_sub_domain(url_property_id)

        download_link = (
            f"https://{url_sub_domain}.{app.config['MFD_DOMAIN']}/connect/{url_property_id}#/insights/downloads"
            if require_login
            else app.config["DI_ANON_DOWNLOAD_PAGE"]
        ) + (
            f"?token={download_token}"
            if require_login
            else f"?token={download_token}&lang={lang}"
        )

        return download_link
