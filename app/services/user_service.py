from typing import List

from app.common.cache import TIMEOUT_HOUR, TIMEOUT_MINUTE, cache
from app.common.constants.permission_service import MFD_ACCESS_REPORT_BUILDER_ACL
from app.common.enums import Resources, TokenTypes
from app.common.enums.features import BillingPortalFeatures, LaunchDarklyFeature
from app.common.enums.permissions import (
    CLASSIC_REPORTS_TO_DATASET_MAPPING,
    PermissionRole,
    Resource,
)
from app.common.keys import classic_reports_datasets_key
from app.common.logger import logger
from app.common.user import User
from app.enums import Dataset, UserScopes, UserTypes
from app.services.acl_service import ACLService
from app.services.grpc import UserServiceClient
from app.services.launch_darkly_service import LaunchDarklyService
from app.services.permission_service import PermissionService
from app.services.property_feature_service import PropertyFeatureService


class UserService:
    @staticmethod
    def has_acl(user: User, property_id: int, acl: str) -> bool:
        if user.admin:
            return True

        # TODO: Remove this once totally migrated to permission service
        if LaunchDarklyService.has_feature_flag(
            LaunchDarklyFeature.PermissionService, property_id
        ):
            permissions = PermissionService.get_permissions(property_id, user)[
                "permissions"
            ]

            if acl == MFD_ACCESS_REPORT_BUILDER_ACL:
                required_datasets = {
                    str(dataset)
                    for dataset in Dataset.values()
                    if dataset
                    not in (
                        [Dataset.Accounting.value]
                        if PropertyFeatureService.has_feature_flag(
                            BillingPortalFeatures.Housekeeping.value, property_id
                        )
                        else [Dataset.Accounting.value, Dataset.Housekeeping.value]
                    )
                }

                user_datasets = {
                    permission["resource"]["id"]
                    for permission in permissions.get(Resource.REPORTS.value, [])
                    if permission["role"] != PermissionRole.VIEWER.value
                }

                return user_datasets == required_datasets

            return all(
                any(
                    permission["resource"]["id"] == dataset
                    for permission in permissions.get(Resource.STOCK_REPORTS.value, [])
                )
                for dataset in CLASSIC_REPORTS_TO_DATASET_MAPPING.get(acl, [])
            )

        return bool(ACLService.get_by_user_id(user.id, property_id, acl))

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_created_by(user_id: int):
        with UserServiceClient() as client:
            user = client.get_user_by_id(user_id)

            if not user:
                return dict(id=0, name="", email="")

            user = user.get("user")
            name = f"{user.get('first_name', '')} {user.get('last_name', '')}"
            return dict(id=user_id, name=name, email=user.get("email", ""))

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_user_active(user_id: int) -> bool:
        """Method that will return if the user is active or not

        Args:
            user_id (int): User id to get the active status from
        """
        with UserServiceClient() as client:
            user = client.get_user_by_id(user_id)

            if not user:
                return False

            user = user.get("user", dict())
            # allow employee email addresses to be used
            active = (
                user.get("is_active", False)
                or user.get("type") == UserTypes.USER_TYPE_EMPLOYEE.name
            )
            return active

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def is_employee(user_id: int) -> bool:
        """Method that will return if the user is active or not

        Args:
            user_id (int): User id to get the active status from
        """
        with UserServiceClient() as client:
            user = client.get_user_by_id(user_id)

            if not user:
                False

            user = user.get("user", dict())
            # allow employee email addresses to be used
            is_employee = user.get("type") == UserTypes.USER_TYPE_EMPLOYEE.name
            return is_employee

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_id_by_email(user_email: str) -> int | None:
        """Method that will return the user id by email address

        Args:
            user_email (int): Email address to get the user id from
        """
        with UserServiceClient() as client:
            users = client.get_users_by_email(user_email)

            if not users:
                logger.debug(f"User not found by email: {user_email}")
                return None

            users = users.get("users", [])
            first_user_id = int(users[0].get("id")) if len(users) else None
            if first_user_id is None:
                logger.debug(f"User not found by email: {user_email}")
            return first_user_id

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_user_properties(user_id: int) -> list[int]:
        """Method that will return a list of property IDs assigned to the user

        Args:
            user_id (int): User id to get the property id from
        """
        with UserServiceClient() as client:
            assignment = client.get_assignment_by_user_id(user_id=user_id)

            if not assignment:
                logger.debug(f"No properties found for user: {user_id}")
                return []

            properties = assignment.get("assignment", dict()).get("properties", [])
            property_ids = [property.get("id") for property in properties]

            if len(property_ids) == 0:
                logger.debug(f"No properties found for user: {user_id}")

            return property_ids

    @staticmethod
    def get_insights_acls(user: User, property_id: int) -> List[str]:
        """Method that will return a list of the insights acls the user has access to it from MFD Table

        Args:
            user (User): User object
            property_id (int): Property id
        """
        enabled_datasets_cache_key = classic_reports_datasets_key(
            user.id, property_id, len(user.scopes)
        )
        enabled_datasets = cache.get(enabled_datasets_cache_key)

        if enabled_datasets is not None:
            return enabled_datasets

        enabled_datasets = []

        if user.token_type in (TokenTypes.PARTNER.value, TokenTypes.INTERNAL.value):
            if UserScopes.ReadPaymentsData.value in user.scopes:
                enabled_datasets.append(Dataset.Payment.value)
                enabled_datasets.append(Dataset.Payout.value)
            if UserScopes.ReadFinancialData.value in user.scopes:
                enabled_datasets.append(Dataset.Financial.value)
            if UserScopes.ReadGuestsData.value in user.scopes:
                enabled_datasets.append(Dataset.Guests.value)
            if UserScopes.ReadReservationsData.value in user.scopes:
                enabled_datasets.append(Dataset.Reservations.value)
            if UserScopes.ReadOccupancyData.value in user.scopes:
                enabled_datasets.append(Dataset.Occupancy.value)
                enabled_datasets.append(Dataset.OccupancyV1.value)
            if UserScopes.ReadInvoicesData.value in user.scopes:
                enabled_datasets.append(Dataset.Invoices.value)

        elif user.admin:
            enabled_datasets.append(Dataset.Financial.value)
            enabled_datasets.append(Dataset.Invoices.value)
            enabled_datasets.append(Dataset.Payment.value)
            enabled_datasets.append(Dataset.Payout.value)
            enabled_datasets.append(Dataset.Reservations.value)
            enabled_datasets.append(Dataset.Occupancy.value)
            enabled_datasets.append(Dataset.OccupancyV1.value)
            enabled_datasets.append(Dataset.Guests.value)
            enabled_datasets.append(Dataset.Housekeeping.value)
            enabled_datasets.append(Dataset.Accounting.value)

        else:
            if LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.PermissionService, property_id
            ):
                permissions = PermissionService.get_permissions(property_id, user)
                enabled_datasets = [
                    int(permission["resource"]["id"])
                    for permission in permissions["permissions"].get(
                        Resource.STOCK_REPORTS.value, []
                    )
                ]
                logger.info(
                    "Pulling data from permission service",
                    extra={"enabled_datasets": enabled_datasets},
                )
            else:
                acls = ACLService.get_all_by_user_id(user.id, property_id)
                if Resources.PaymentReports.value in acls:
                    enabled_datasets.append(Dataset.Payment.value)
                    enabled_datasets.append(Dataset.Payout.value)
                if Resources.FinancialReports.value in acls:
                    enabled_datasets.append(Dataset.Financial.value)
                    enabled_datasets.append(Dataset.Invoices.value)
                    enabled_datasets.append(Dataset.Accounting.value)
                if Resources.ProductionReports.value in acls:
                    enabled_datasets.append(Dataset.Reservations.value)
                    enabled_datasets.append(Dataset.Occupancy.value)
                    enabled_datasets.append(Dataset.OccupancyV1.value)
                if Resources.DailyActivityReports.value in acls:
                    enabled_datasets.append(Dataset.Reservations.value)
                    enabled_datasets.append(Dataset.Occupancy.value)
                    enabled_datasets.append(Dataset.OccupancyV1.value)
                    enabled_datasets.append(Dataset.Guests.value)
                if Resources.HouseKeeping.value in acls:
                    enabled_datasets.append(Dataset.Housekeeping.value)
                if Resources.PoliceReport.value in acls:
                    enabled_datasets.append(Dataset.Guests.value)

        # remove duplicates
        enabled_datasets = [*set(enabled_datasets)]

        cache.set(enabled_datasets_cache_key, enabled_datasets, timeout=TIMEOUT_MINUTE)

        return enabled_datasets
