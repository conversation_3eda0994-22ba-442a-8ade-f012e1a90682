import json
import re
import uuid
from datetime import datetime

from flask import current_app as app

from openai import OpenAI

from rapidfuzz import fuzz

from sqlalchemy import text

from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.cache import TIMEOUT_DAY, cache
from app.common.enums.features import LaunchDarklyFeature
from app.common.exceptions import InvalidUsage
from app.common.keys import explore_chat_key, explore_context_key
from app.common.logger import logger
from app.common.user import User
from app.datasets.dataset import Dataset
from app.enums import Dataset as DatasetEnum
from app.enums.cdf import Cdf
from app.enums.filter_operator import FilterOperator as Operator
from app.enums.sort import Sort
from app.models.stock_report import StockReport
from app.services.dataset_service import DatasetService
from app.services.launch_darkly_service import LaunchDarklyService


class ExploreService:
    def __init__(self, question: str, property_id: str, user: User) -> None:
        if not LaunchDarklyService.has_feature_flag(
            LaunchDarklyFeature.Explore, property_id
        ):
            raise InvalidUsage.not_found("Feature is not enabled")

        self.question = question
        self.property_id = property_id
        self.user = user

    @staticmethod
    def explore_report(
        question: str, context_id: str, property_id: int, user: User
    ) -> dict:
        if not ExploreService.__is_question_valid(question):
            raise InvalidUsage.unprocessable_entity(
                "The question appears to be invalid or nonsensical."
            )

        if not context_id:
            context_id, context, chat = str(uuid.uuid4()), [], []
        else:
            context = cache.get(explore_context_key(context_id)) or []
            chat = cache.get(explore_chat_key(context_id)) or []

        datasets = ExploreService.__get_datasets(property_id, user)
        schemas, table_to_dataset = [], {}

        for dataset in datasets:
            try:
                dataset = Dataset(DatasetEnum(dataset["id"]), True)
                schema = ExploreService.__generate_database_schema(dataset)
                schemas.append(schema)
                table_to_dataset[dataset.model.__table__.name] = dataset
            except Exception as e:
                logger.error(
                    f"Failed to generate schema for dataset {dataset['id']}: {str(e)}"
                )

        if not schemas:
            raise InvalidUsage.not_found("No schemas could be generated")

        schemas, messages = "\n\n".join(schemas), []

        if not context:
            system_prompt = f"""
            You are a SQL expert. Analyze the provided schemas and generate a SQL query based on user requirements.
            Schemas include tables with columns, types, contextual names, and descriptions.
            SCHEMAS:
            {schemas}
            """
            messages.append({"role": "system", "content": system_prompt})

        if not context:
            user_prompt = f"""
            Write a SQL query, along with a report title and description, to fulfill the request: "{question}".

            ### Rules:
            1. Use **only** the schema provided below. Do not assume or create additional tables or columns.
            2. Select columns explicitly from the schema. Do not make assumptions about any column names.
            3. Use a **single table** for the query. Do not include joins or combine columns across tables.
            4. Never use "SELECT *", "COUNT(*)", or any aggregate functions that include all rows or columns.
            5. Specify columns explicitly in SELECT statements. Do not use inferred or unrelated column names.
            6. To "GROUP BY" columns, ensure:
                - At least one metric (e.g., SUM, AVG, COUNT, MAX, or MIN) is included in the SELECT clause.
                - Metrics must aggregate columns that are not part of the GROUP BY clause.
                - Metrics need to be applied to integer or numeric columns. Can't be applied to text or date columns.
            7. Avoid PostgreSQL-specific functions like INTERVAL, EXTRACT, YEAR, or DATEDIFF.
            8. If no valid columns or filters exist in the schema for the request, return an error indicating that the query cannot be fulfilled.
            9. Use filters (WHERE clauses) only with valid column names and available data from the schema. Do not include assumptions or invalid comparisons.
            10. Do not use "HAVING", "LIMIT", or rename columns with "AS".
            11. If the column includes predefined options, use only those options for filtering:
                - Use "IN" or "NOT IN" operators to filter by these options.
                - Do not use any other operators (e.g., =, !=, LIKE) for such columns.
            12. If grouping or filtering is not possible based on the schema, omit those clauses.

            ### Schema:
            The schema defines valid tables and columns. Use **only** the tables and columns listed below:
            {schemas}

            ### Additional Notes:
            - Always try to provide an answer, even if it is not perfect and a simple SELECT statement.
            - Last and avoidable scenario: If you cannot generate a valid query, return an error message indicating the issue.
            {{
                "error": "The requested query cannot be generated due to undersanding the user's request."
            }}
            - Do not attempt to infer or create columns or tables not listed in the schema.
            - If a column has options, strictly adhere to them for any WHERE clause filtering.

            The current datetime is: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}.

            ### Output:
            Provide the result in JSON format with the following fields:
            - sql: the generated SQL SELECT statement.
            - title: a short title for the report.
            - description: a brief explanation of the report.
            - message: a concise summary of the query's purpose, excluding anything related to the SQL query itself.
            """
            messages.append({"role": "user", "content": user_prompt})
        else:
            user_prompt = f'Refine the previous query to also include: "{question}".'
            messages.extend(context)
            messages.append({"role": "user", "content": user_prompt})

        client = OpenAI(api_key=app.config["OPENAI_API_KEY"])
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                response_format={"type": "json_object"},
            )
            response = json.loads(response.choices[0].message.content)

            if "error" in response:
                logger.error(f"OpenAI response error: {response.get('error')}")
                raise InvalidUsage.unprocessable_entity(response.get("error"))

            cleaned_sql = ExploreService.__get_cleaned_sql(response.get("sql"))
            if not cleaned_sql:
                logger.error("No SQL query generated in the OpenAI response.")
                raise InvalidUsage.unprocessable_entity("No SQL query generated.")

            cleaned_sql = ExploreService.__get_cleaned_sql(response.get("sql"))
            sql_query = text(cleaned_sql)
            table, columns, filters, groups, sort = ExploreService.__get_parsed_sql(
                sql_query.text
            )
            dataset = table_to_dataset.get(table)

            if not dataset:
                raise InvalidUsage.not_found(
                    f"Table '{table}' not found in available datasets"
                )

            group_rows = ExploreService.__get_group_rows(groups, dataset.model)
            columns = ExploreService.__get_columns(dataset.model, columns, groups)
            filters = ExploreService.__get_filters(filters)
            sort = ExploreService.__get_sort(dataset.model, sort)
            settings = ExploreService.__get_details(question, group_rows)

            context.append({"role": "user", "content": user_prompt})
            context.append({"role": "assistant", "content": json.dumps(response)})
            cache.set(explore_context_key(context_id), context, TIMEOUT_DAY)

            chat.append({"actor": "user", "message": question})
            chat.append({"actor": "explore", "message": response.get("message")})
            cache.set(explore_chat_key(context_id), chat, TIMEOUT_DAY)

        except Exception as e:
            logger.error(f"Failed to process OpenAI response: {str(e)}")
            raise InvalidUsage.server_error("Failed to process the response")

        return dict(
            context_id=context_id,
            title=response.get("title"),
            description=response.get("description"),
            dataset_id=dataset.kind.value,
            columns=columns,
            group_rows=group_rows,
            filters=filters,
            sort=sort,
            settings=settings,
            chat=chat,
        )

    @staticmethod
    def explore_stock_reports(question: str) -> None:
        client = OpenAI(api_key=app.config["OPENAI_API_KEY"])
        stock_reports = StockReport.query.all()

        def get_dataset(id: int) -> str:
            dataset = Dataset(DatasetEnum(id), False)
            return dataset.kind.name

        def get_filters(filters: dict) -> str:
            if not filters:
                return ""
            filters = CDFs.get_cdfs_from_filters(filters, [])
            filters = ", ".join(
                [
                    f"<column: {filter['cdf']['column']} operator: {filter['operator']} value: {filter['value'] if filter['value'] else 'no value'}>"
                    for filter in filters
                ]
            )
            return filters

        list_reports = "\n".join(
            [
                "\n".join(
                    filter(
                        None,
                        [
                            f"ID: {report.id}",
                            f"Dataset: {get_dataset(report.dataset_id)}",
                            f"Title: {report.title}",
                            f"Description: {report.description}",
                            f"Columns: {', '.join([column['cdf']['column'] for column in report.columns])}",
                            f"Filters: {get_filters(report.filters)}"
                            if report.filters
                            else None,
                            f"Group Rows: {', '.join([group_row['cdf']['column'] for group_row in report.group_rows])}"
                            if report.group_rows
                            else None,
                            f"Group Columns: {', '.join([group_column['cdf']['column'] for group_column in report.group_columns])}"
                            if report.group_columns
                            else None,
                            f"Sort: {', '.join([sort['cdf']['column'] + ' - ' + sort['direction'] for sort in report.sort])}"
                            if report.sort
                            else None,
                            "---",
                        ],
                    )
                )
                for report in stock_reports
            ]
        )

        system_prompt = f"""
        You are an AI model analyzing stock reports to recommend the most relevant ones based on a user query. The report details include:
        - ID
        - Dataset
        - Title
        - Description
        - Columns
        - Filters
        - Group Rows
        - Group Columns
        - Sort

        ### Scoring Criteria:
        1. **High Priority:** Match relevance on Title and Description.
        2. **Medium Priority:** Match relevance on Columns, Filters, and Group Rows.
        3. **Low Priority:**  Match relevance on Group Columns and Sort.
        4. If no clear query is provided, prioritize the most detailed or widely applicable reports.

        ### Output Requirements:
        - Return a JSON array containing up to 5 reports, ordered by descending relevance.
        - Include:
        - `id`: The report ID.
        - `score`: Relevance score (0–100%).
        - `argument`: A brief explanation of why this report is relevant.

        ### Reports for Analysis:
        {list_reports}
        """
        user_prompt = f"""
        The user needs: {question}.

        Based on the reports provided, determine the relevance of each report using the criteria in the system prompt. Your output must:
        1. Include up to 5 report IDs ordered by relevance.
        2. Always return at least 1 report with the highest relevance.
        3. Provide a relevance score (as an integer percentage) for each report.
        4. Explain briefly why each report is relevant in the `argument` field.

        Return the result in this JSON format:
        [
            "reports": {{
                "id": <report ID>,
                "score": <relevance score (0-100)>,
                "argument": "<brief explanation>"
            }}
        ]
        """

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        response = client.chat.completions.create(
            model="gpt-4o", messages=messages, response_format={"type": "json_object"}
        )
        response_content = response.choices[0].message.content

        try:
            parsed_response = json.loads(response_content)
            suggested_reports = parsed_response.get("reports", [])
        except json.JSONDecodeError as error:
            logger.error("Failed to parse JSON response", extra={"error": str(error)})
            raise InvalidUsage.server_error("Failed to process the response")

        enriched_suggested_reports = []
        for suggestion in suggested_reports:
            found_stock_report = next(
                (
                    stock_report
                    for stock_report in stock_reports
                    if stock_report.id == suggestion.get("id")
                ),
                None,
            )
            if found_stock_report:
                enriched_suggested_reports.append(
                    {
                        "id": suggestion.get("id"),
                        "score": suggestion.get("score"),
                        "argument": suggestion.get("argument"),
                        "title": found_stock_report.title,
                        "description": found_stock_report.description,
                        "dataset_id": found_stock_report.dataset_id,
                    }
                )

        return dict(stock_reports=enriched_suggested_reports)

    @staticmethod
    def explore_dataset(question: str, property_id: int, user: User) -> dict:
        datasets = ExploreService.__get_datasets(property_id, user)
        schemas = []

        for dataset in datasets:
            try:
                dataset_enum = DatasetEnum(dataset["id"])
                dataset = Dataset(dataset_enum, True)
                schema = ExploreService.__generate_dataset_schema(dataset)
                schemas.append(schema)
            except Exception as e:
                logger.error(
                    f"Failed to generate schema for dataset {dataset['id']}: {str(e)}"
                )

        if not schemas:
            raise InvalidUsage.not_found("No valid schemas could be generated")

        schemas = "\n".join(schemas)

        system_prompt = f"""
        You are an expert in datasets. Below are several datasets with its schemas.
        Based on the user's request, determine which dataset is the most relevant. Prioritize relevance based on dataset name, cdfs (columns), and dataset descriptions provided.
        SCHEMAS:
        {schemas}
        """
        user_prompt = f"""
        The user has asked: "{question}". Suggest the single most relevant dataset for this request. Format the output as JSON with fields:
        - dataset: the dataset name
        """
        client = OpenAI(api_key=app.config["OPENAI_API_KEY"])
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]

        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                response_format={"type": "json_object"},
            )
            response_content = response.choices[0].message.content
            parsed_response = json.loads(response_content)
            dataset = parsed_response.get("dataset", None)

            if not dataset:
                raise InvalidUsage.not_found("No relevant dataset found")

            return dict(id=DatasetEnum[dataset].value, name=DatasetEnum[dataset].name)

        except json.JSONDecodeError as e:
            logger.error("Failed to parse OpenAI response.", extra={"error": str(e)})
            raise InvalidUsage.server_error("Failed to process OpenAI response")
        except Exception as e:
            logger.error("OpenAI API call failed.", extra={"error": str(e)})
            raise InvalidUsage.server_error("OpenAI API error")

    @staticmethod
    def __is_question_valid(question: str) -> bool:
        MIN_VALIDATION_THRESHOLD = 50
        SHORT_NONSENSICAL_PENALTY = 20
        LENGTH_DIFFERENCE_THRESHOLD = 5
        NEAR_PERFECT_MATCH_THRESHOLD = 95

        if not question or len(question.strip()) < 3:
            return False

        if re.fullmatch(r"(.)\1{2,}", question.strip()):
            return False

        if not re.search(r"[a-zA-Z0-9]", question.strip()):
            return False

        nonsensical_inputs = ["test", "bla", "aaa", "abc", "hello", "xyz"]
        for nonsensical in nonsensical_inputs:
            score = fuzz.partial_ratio(question.lower(), nonsensical)

            if score >= NEAR_PERFECT_MATCH_THRESHOLD:
                return False

            if abs(len(question) - len(nonsensical)) > LENGTH_DIFFERENCE_THRESHOLD:
                continue

            normalized_score = score - (
                SHORT_NONSENSICAL_PENALTY * len(nonsensical) / len(question)
            )

            if normalized_score > MIN_VALIDATION_THRESHOLD:
                return False

        return True

    @staticmethod
    def __get_datasets(property_id: int, user: User) -> list:
        property_datasets = [
            dataset
            for dataset in DatasetService.get_datasets_by_property_id(
                property_id, user.email
            )
            if dataset["read_only"] is False
        ]

        datasets = [
            dataset
            for dataset in property_datasets
            if not dataset["name"].lower().startswith("occupancy")
        ]

        if not datasets:
            raise InvalidUsage.not_found("No datasets available for the property")

        return datasets

    @staticmethod
    def __generate_database_schema(dataset: Dataset) -> str:
        table = dataset.model.__table__
        flatten_cdfs = dataset.flatten_cdfs
        columns = []

        for cdf in flatten_cdfs:
            column_name = cdf["column"]
            if column_name in table.columns:
                column = table.columns[column_name]
                column_info = column.info
                column_type = str(column.type)
                column_display_name = column_info.get("name", "N/A")
                column_description = column_info.get(
                    "description", "No description available"
                )
                options = CDF(dataset.kind, cdf["column"]).options
                column_entry = (
                    f"{column_name}: {column_type} ({column_display_name}: {column_description})"
                    + (f" [options: {', '.join(options)}]" if options else "")
                )
                columns.append(column_entry)

        database_schema = f"{table.name}:\n  - " + "\n  - ".join(columns)
        return database_schema

    @staticmethod
    def __generate_dataset_schema(dataset: Dataset) -> str:
        id = dataset.kind.value
        name = dataset.kind.name
        flatten_cdfs = dataset.flatten_cdfs
        cdfs = " / ".join(
            [
                f"{cdf['name']} ({cdf['column']}): {cdf['description']}"
                for cdf in flatten_cdfs
            ]
        )
        return f"<ID: {id}, Dataset: {name}, CDFs: {cdfs}>"

    @staticmethod
    def __get_cleaned_sql(response_content: str) -> str:
        sql_match = re.search(r"```sql(.*?)```", response_content, re.DOTALL)
        return (
            sql_match.group(1).strip().rstrip(";")
            if sql_match
            else response_content.strip().rstrip(";")
        )

    @staticmethod
    def __get_columns(model, columns: list, groups: list) -> list:
        if groups is None:
            groups = []

        cast_regex = re.compile(r"(\w+)::(\w+)")

        def parse_column(column):
            if any(
                column.startswith(prefix)
                for prefix in (
                    "AVG(",
                    "SUM(",
                    "MAX(",
                    "MIN(",
                    "COUNT(",
                    "STDDEV(",
                    "VARIANCE(",
                )
            ):
                column_name = column[column.index("(") + 1 : column.index(")")]
                metric = column[: column.index("(")].lower()
                return dict(
                    cdf=dict(column=column_name, type=Cdf.Default.value),
                    metrics=[metric],
                )

            cast_match = cast_regex.match(column)
            if cast_match:
                column_name, _ = cast_match.groups()
                if hasattr(model, column_name):
                    return dict(cdf=dict(column=column_name, type=Cdf.Default.value))

            if hasattr(model, column):
                return dict(cdf=dict(column=column, type=Cdf.Default.value))

        parsed_columns = [
            parse_column(column)
            for column in columns
            if any(
                column.startswith(prefix)
                for prefix in (
                    "AVG(",
                    "SUM(",
                    "MAX(",
                    "MIN(",
                    "COUNT(",
                    "STDDEV(",
                    "VARIANCE(",
                )
            )
            or (hasattr(model, column.split("::")[0]) and column not in groups)
        ]

        if groups:
            grouped_data = {}
            for item in parsed_columns:
                column = item["cdf"]["column"]
                if column not in grouped_data:
                    grouped_data[column] = {"cdf": item["cdf"], "metrics": []}
                grouped_data[column]["metrics"].extend(item["metrics"])

            return [grouped_data[column] for column in grouped_data]

        return parsed_columns

    @staticmethod
    def __get_group_rows(group_rows: list, model) -> list:
        if group_rows is None:
            return []

        valid_group_rows = []
        for group_row in group_rows:
            if hasattr(model, group_row):
                valid_group_rows.append(
                    dict(cdf=dict(column=group_row, type=Cdf.Default.value))
                )

        return valid_group_rows

    @staticmethod
    def __get_filters(filters: str | None) -> dict:
        if filters is None:
            return {}

        tokens = re.findall(r"\(|\)|AND|OR|[^\s()]+", filters)
        stack = []
        current = []
        buffer = []
        last_operator = None

        def add_condition(condition):
            if isinstance(condition, list):
                if last_operator == "or":
                    current.append({"or": condition})
                elif last_operator == "and" or last_operator is None:
                    current.append(
                        {"and": condition} if len(condition) > 1 else condition[0]
                    )
            else:
                current.append(condition)

        skip_next = False
        for i, token in enumerate(tokens):
            if skip_next:
                skip_next = False
                continue

            if token in ("AND", "OR"):
                if buffer:
                    if "BETWEEN" in buffer:
                        column = buffer[0]
                        lower_bound = buffer[2].strip("'")
                        upper_bound = (
                            tokens[i + 1].strip("'") if i + 1 < len(tokens) else None
                        )
                        if not upper_bound:
                            raise InvalidUsage.bad_request(
                                f"Invalid BETWEEN format: {' '.join(buffer)}"
                            )

                        current.extend(
                            [
                                {
                                    "cdf": {"type": "default", "column": column},
                                    "operator": Operator.GreaterThan.value,
                                    "value": lower_bound,
                                },
                                {
                                    "cdf": {"type": "default", "column": column},
                                    "operator": Operator.LessThan.value,
                                    "value": upper_bound,
                                },
                            ]
                        )
                        buffer = []
                        skip_next = True
                        continue
                    else:
                        condition = " ".join(buffer)
                        parsed_condition = ExploreService.__parse_condition(condition)
                        add_condition(parsed_condition)
                        buffer = []
                last_operator = token.lower()
            elif token == "(":
                stack.append((current, last_operator))
                current = []
                last_operator = None
            elif token == ")":
                if buffer:
                    condition = " ".join(buffer)
                    parsed_condition = ExploreService.__parse_condition(condition)
                    add_condition(parsed_condition)
                    buffer = []
                if stack:
                    group = current
                    current, last_operator = stack.pop()
                    if last_operator == "or":
                        current.append({"or": group})
                    else:
                        current.append({"and": group} if len(group) > 1 else group[0])
                else:
                    raise ValueError("Mismatched parentheses in filter string.")
            else:
                buffer.append(token)

        if buffer:
            condition = " ".join(buffer)
            parsed_condition = ExploreService.__parse_condition(condition)
            add_condition(parsed_condition)

        if stack:
            raise ValueError("Mismatched parentheses in filter string.")

        if len(current) == 2 and last_operator == "or":
            return {last_operator: current}

        return {"and": current}

    @staticmethod
    def __parse_condition(condition: str) -> dict:
        tokens = condition.split()

        if len(tokens) < 3:
            raise InvalidUsage.bad_request(f"Invalid condition format: {condition}")

        column, operator, value = (
            tokens[0],
            tokens[1],
            " ".join(tokens[2:]).strip("'"),
        )

        if operator == "NOT" and len(tokens) > 2 and tokens[2] == "IN":
            operator = "NOT IN"
            value = " ".join(tokens[3:]).strip("'")

        cast_regex = re.compile(r"(\w+)::(\w+)")
        cast_match = cast_regex.match(column)
        if cast_match:
            column = cast_match.groups()[0]

        if operator == "IN":
            operator = Operator.ListContains.value
            value = [v.strip().strip("'") for v in value.strip("()").split(",")]
        elif operator == "NOT IN":
            operator = Operator.NotListContains.value
            value = [v.strip().strip("'") for v in value.strip("()").split(",")]
        elif operator == "LIKE":
            operator = Operator.Contains.value
            value = value.strip("%")
        elif operator == "NOT LIKE":
            operator = Operator.NotContains.value
            value = value.strip("%")

        operator, value = ExploreService.__adjust_operator_and_value(
            operator, tokens, value
        )

        return {
            "cdf": {"column": column, "type": "default"},
            "operator": operator,
            "value": value,
        }

    @staticmethod
    def __adjust_operator_and_value(operator, tokens, value):
        operator_map = {
            "=": Operator.Equals.value,
            ">": Operator.GreaterThan.value,
            ">=": Operator.GreaterThanOrEqual.value,
            "<": Operator.LessThan.value,
            "<=": Operator.LessThanOrEqual.value,
            "!=": Operator.NotEquals.value,
        }

        if operator == "IS":
            if len(tokens) > 2 and tokens[2].upper() == "NULL":
                return Operator.IsNull.value, None
            elif (
                len(tokens) > 3
                and tokens[2].upper() == "NOT"
                and tokens[3].upper() == "NULL"
            ):
                return Operator.IsNotNull.value, None

        if operator == "LIKE":
            if value.startswith("%") and value.endswith("%"):
                return Operator.Contains.value, value.strip("%")
            if value.startswith("%"):
                return Operator.Ends.value, value.strip("%")
            if value.endswith("%"):
                return Operator.Begins.value, value.strip("%")

        if operator == "NOT" and tokens[2] == "LIKE":
            value = " ".join(tokens[3:]).strip("'")
            if value.startswith("%") and value.endswith("%"):
                return Operator.NotContains.value, value.strip("%")
            if value.startswith("%"):
                return Operator.NotEnds.value, value.strip("%")
            if value.endswith("%"):
                return Operator.NotBegins.value, value.strip("%")

        if operator == "IS":
            if tokens[2].upper() == "EMPTY":
                return Operator.IsEmpty.value, ""
            if tokens[2].upper() == "NOT" and tokens[3].upper() == "EMPTY":
                return Operator.IsNotEmpty.value, ""

        return operator_map.get(operator, operator), value

    @staticmethod
    def __get_sort(model, sort: str | None) -> list:
        if sort is None:
            return []

        sort_sql = {"ASC": Sort.asc.value, "DESC": Sort.desc.value}
        columns = sort.split(" ")
        parsed_sort = []

        def parse_column(column, direction):
            if any(
                column.startswith(prefix)
                for prefix in (
                    "AVG(",
                    "SUM(",
                    "MAX(",
                    "MIN(",
                    "COUNT(",
                    "STDDEV(",
                    "VARIANCE(",
                )
            ):
                column_name = column[column.index("(") + 1 : column.index(")")]
                return dict(
                    cdf=dict(column=column_name, type=Cdf.Default.value),
                    direction=direction,
                )
            return dict(
                cdf=dict(column=column, type=Cdf.Default.value), direction=direction
            )

        for i in range(0, len(columns), 2):
            column = columns[i]
            if hasattr(model, column):
                direction = columns[i + 1] if i + 1 < len(columns) else "ASC"
                parsed_sort.append(parse_column(column, sort_sql.get(direction, "ASC")))

        return parsed_sort

    @staticmethod
    def __get_details(question: str, group_rows: list) -> dict:
        target_keywords = ["totals", "metrics", "dimensions", "summary", "summarize"]
        threshold = 85
        details = len(group_rows) < 0
        totals = any(
            fuzz.ratio(word, keyword) > threshold and len(word) >= 0.75 * len(keyword)
            for word in re.findall(r"\b\w+\b", question.lower())
            for keyword in target_keywords
        )
        return dict(details=details, totals=totals)

    @staticmethod
    def __get_parsed_sql(sql: str):
        sql = sql.replace(";", "")
        tokens = sql.split()
        columns = [
            column.replace(",", "")
            for column in tokens[tokens.index("SELECT") + 1 : tokens.index("FROM")]
        ]
        table = tokens[tokens.index("FROM") + 1]

        where = None
        if "WHERE" in tokens:
            where_start = tokens.index("WHERE") + 1
            where_end = (
                tokens.index("GROUP")
                if "GROUP" in tokens
                else tokens.index("ORDER")
                if "ORDER" in tokens
                else len(tokens)
            )
            where = " ".join(tokens[where_start:where_end])

        group = None
        if "GROUP BY" in sql:
            group_start = sql.index("GROUP BY") + len("GROUP BY")
            group_end = sql.index("ORDER BY") if "ORDER BY" in sql else len(sql)
            group = [column.strip() for column in sql[group_start:group_end].split(",")]

        sort = None
        if "ORDER BY" in sql:
            order_start = sql.index("ORDER BY") + len("ORDER BY")
            sort = sql[order_start:].strip()

        return table, columns, where, group, sort
