from cloudbeds.organization.v1 import (
    propertyprofile_pb2,
    propertyprofile_pb2_grpc,
)


from flask import current_app as app

from google.protobuf.json_format import MessageToDict

import grpc

from app.common.exceptions import InvalidUsage
from app.common.logger import logger


class PropertyProfileServiceClient:
    def __init__(self):
        self._grpc_url = app.config["ORGANIZATION_SERVICE_URL"]
        self._ssl = app.config["ORGANIZATION_SERVICE_SSL"]
        self._channel = (
            grpc.secure_channel(self._grpc_url, grpc.ssl_channel_credentials())
            if self._ssl
            else grpc.insecure_channel(self._grpc_url)
        )
        self._stub = propertyprofile_pb2_grpc.PropertyProfileServiceStub(self._channel)

    def close(self):
        self._channel.close()

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    def get_property_profile(self, property_id: str | int):
        try:
            response = MessageToDict(
                self._stub.GetPropertyProfile(
                    propertyprofile_pb2.PropertyProfileServiceGetPropertyProfileRequest(
                        property_id=int(property_id),
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
        except Exception as exception:
            logger.error(
                "Organization Service had error",
                extra={
                    "error": str(exception),
                    "arguments": {"property_id": property_id},
                },
            )
            raise InvalidUsage.server_error()

        return response
