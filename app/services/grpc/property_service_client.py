from cloudbeds.organization.v1 import (
    property_pb2,
    property_pb2_grpc,
)


from flask import current_app as app

from google.protobuf.json_format import MessageToDict

import grpc

from app.common.exceptions import InvalidUsage
from app.common.logger import logger


class PropertyServiceClient:
    def __init__(self):
        self._grpc_url = app.config["ORGANIZATION_SERVICE_URL"]
        self._ssl = app.config["ORGANIZATION_SERVICE_SSL"]
        self._channel = (
            grpc.secure_channel(self._grpc_url, grpc.ssl_channel_credentials())
            if self._ssl
            else grpc.insecure_channel(self._grpc_url)
        )
        self._stub = property_pb2_grpc.PropertyServiceStub(self._channel)

    def close(self):
        self._channel.close()

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    def get_property(self, property_id: str | int):
        try:
            return MessageToDict(
                self._stub.GetProperty(
                    property_pb2.GetPropertyRequest(
                        id=int(property_id),
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
        except grpc.RpcError as rpc_error:
            match rpc_error.code():
                case grpc.StatusCode.NOT_FOUND:
                    logger.warning(
                        "Organization Service could not find property",
                        extra={
                            "error": str(rpc_error.exception()),
                            "arguments": {"property_id": property_id},
                        },
                    )
                    return InvalidUsage.not_found(
                        "Property not found on the Organization"
                    )
                case _:
                    logger.error(
                        "Organization Service had error",
                        extra={
                            "error": str(rpc_error.exception()),
                            "arguments": {"property_id": property_id},
                        },
                    )
                    raise InvalidUsage.server_error()
        except Exception as exception:
            logger.error(
                "Organization Service had error",
                extra={
                    "error": str(exception),
                    "arguments": {"property_id": property_id},
                },
            )
            raise InvalidUsage.server_error()

    def get_properties_by_ids(self, property_ids: list[str | int]):
        try:
            response = MessageToDict(
                self._stub.ListProperties(
                    property_pb2.ListPropertiesRequest(
                        ids=[int(property) for property in property_ids],
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
        except Exception as exception:
            logger.error(
                "Organization Service had error",
                extra={
                    "error": str(exception),
                    "arguments": {"property_ids": property_ids},
                },
            )
            raise InvalidUsage.server_error()

        return response

    def get_property_features_by_id(self, property_id: str | int, enabled: bool = True):
        try:
            response = MessageToDict(
                self._stub.ListPropertyFeatures(
                    property_pb2.ListPropertyFeaturesRequest(
                        property_id=int(property_id), enabled=enabled
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
            return response
        except Exception as exception:
            logger.error(
                "Organization Service had error",
                extra={
                    "error": str(exception),
                    "arguments": {"property_id": property_id, "enabled": enabled},
                },
            )
            raise InvalidUsage.server_error()

    def get_property_feature_by_name(self, name: str, property_id: str | int):
        try:
            response = MessageToDict(
                self._stub.GetPropertyFeature(
                    property_pb2.GetPropertyFeatureRequest(
                        property_id=int(property_id), name=name
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
            return response
        except grpc.RpcError as rpc_error:
            match rpc_error.code():
                case grpc.StatusCode.NOT_FOUND:
                    logger.debug(
                        "Organization Service could not find resource",
                        extra={
                            "error": str(rpc_error.exception()),
                            "arguments": {"property_id": property_id, "name": name},
                        },
                    )
                    return None
                case _:
                    logger.error(
                        "Organization Service had error",
                        extra={
                            "error": str(rpc_error.exception()),
                            "arguments": {"property_id": property_id, "name": name},
                        },
                    )
                    raise InvalidUsage.server_error()

        except Exception as exception:
            logger.error(
                "Organization Service had error",
                extra={
                    "error": str(exception),
                    "arguments": {"property_id": property_id, "name": name},
                },
            )
            raise InvalidUsage.server_error()

    def list_properties(
        self,
        property_ids: list[str | int],
        is_active: bool = True,
        is_deleted: bool = False,
    ) -> dict:
        try:
            # If there are no property_ids, return an empty list
            if len(property_ids) == 0:
                return dict(properties=[])
            response = MessageToDict(
                self._stub.ListProperties(
                    property_pb2.ListPropertiesRequest(
                        ids=[int(property_id) for property_id in property_ids],
                        is_active=is_active,
                        is_deleted=is_deleted,
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
            return response
        except grpc.RpcError as rpc_error:
            logger.error(
                "Organization Service error",
                extra={
                    "error": str(rpc_error.exception()),
                    "arguments": {
                        "property_ids": property_ids,
                        "is_active": is_active,
                        "is_deleted": is_deleted,
                    },
                },
            )
            raise InvalidUsage.server_error()

        except Exception as exception:
            logger.error(
                "Organization Service error",
                extra={
                    "error": str(exception),
                    "arguments": {
                        "property_ids": property_ids,
                        "is_active": is_active,
                        "is_deleted": is_deleted,
                    },
                },
            )
            raise InvalidUsage.server_error()
