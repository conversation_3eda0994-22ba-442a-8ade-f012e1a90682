import functools

from grpc import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StatusCode

from app.common.exceptions import InvalidUsage
from app.common.logger import logger


def grpc_exception_handler(service_name: str = "(Missing Service Name)"):
    """
    A decorator to wrap a grpc service function in a try-except block.
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except RpcError as rpc_error:
                match rpc_error.code():
                    case StatusCode.NOT_FOUND:
                        logger.debug(
                            f"{service_name} could not find resource",
                            extra=dict(
                                error=rpc_error.debug_error_string(),
                                rpc_error_code=rpc_error.code().name,
                                grpc_args=args[1:],
                                kwargs=kwargs,
                            ),
                        )
                        return None
                    case _:
                        logger.error(
                            f"{service_name} had an error",
                            extra=dict(
                                error=rpc_error.debug_error_string(),
                                rpc_error_code=rpc_error.code().name,
                                grpc_args=args[1:],
                                kwargs=kwargs,
                            ),
                        )
                        raise InvalidUsage.server_error()
            except Exception as exception:
                logger.error(
                    f"{service_name} had an error",
                    extra=dict(error=str(exception), grpc_args=args, kwargs=kwargs),
                )
                raise InvalidUsage.server_error()

        return wrapper

    return decorator
