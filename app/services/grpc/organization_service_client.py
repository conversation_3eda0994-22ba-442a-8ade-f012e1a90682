from cloudbeds.organization.v1 import (
    organization_pb2,
    organization_pb2_grpc,
)


from flask import current_app as app

from google.protobuf.json_format import MessageToDict

import grpc

from app.common.exceptions import InvalidUsage
from app.common.logger import logger


class OrganizationServiceClient:
    def __init__(self):
        self._grpc_url = app.config["ORGANIZATION_SERVICE_URL"]
        self._ssl = app.config["ORGANIZATION_SERVICE_SSL"]
        self._channel = (
            grpc.secure_channel(self._grpc_url, grpc.ssl_channel_credentials())
            if self._ssl
            else grpc.insecure_channel(self._grpc_url)
        )
        self._stub = organization_pb2_grpc.OrganizationServiceStub(self._channel)

    def close(self):
        self._channel.close()

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    def get_organization_by_id(self, organization_id: int | str):
        try:
            response = MessageToDict(
                self._stub.GetOrganization(
                    organization_pb2.GetOrganizationRequest(
                        id=int(organization_id),
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
        except Exception as exception:
            logger.error(
                "Organization Service had error", extra={"error": str(exception)}
            )
            raise InvalidUsage.server_error()

        return response
