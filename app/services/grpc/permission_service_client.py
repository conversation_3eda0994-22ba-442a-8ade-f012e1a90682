from cloudbeds.permission.di.v1 import permission_api_pb2, permission_api_pb2_grpc
from cloudbeds.permission.v1.types_pb2 import ScopeType, SubjectType

from flask import current_app as app

from google.protobuf.json_format import MessageToDict

import grpc

from app.common.exceptions import InvalidUsage
from app.common.logger import logger


class PermissionServiceClient:
    SERVICE_NAME = "data_insights"

    def __init__(self):
        self._grpc_url = app.config["PERMISSION_SERVICE_URL"]
        self._ssl = app.config["PERMISSION_SERVICE_SSL"]
        self._channel = (
            grpc.secure_channel(self._grpc_url, grpc.ssl_channel_credentials())
            if self._ssl
            else grpc.insecure_channel(self._grpc_url)
        )
        self._stub = permission_api_pb2_grpc.PermissionServiceStub(self._channel)

    def close(self):
        self._channel.close()

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    def list_permissions(
        self,
        property_id: int | str,
        user_id: int | str,
        permissions: list,
        log_context: dict = {},
    ):
        """
        Calls ListPermissions gRPC method to fetch all permissions assigned to a user for a given property.
        """
        try:
            request = permission_api_pb2.ListPermissionsRequest(
                permissions=permissions,
                scope_id=int(property_id),
                scope_type=ScopeType.SCOPE_PROPERTY,
                subject_id=int(user_id),
                subject_type=SubjectType.SUBJECT_USER,
            )

            logger.info(
                "Requesting data from permission service",
                extra={**log_context, "permissions": permissions},
            )

            response = MessageToDict(
                self._stub.ListPermissions(request, timeout=app.config["GRPC_TIMEOUT"]),
                preserving_proto_field_name=True,
            )

            logger.info(
                "Response data from permission service",
                extra={**log_context, "permissions": response["permissions"]},
            )

            return response["permissions"]

        except Exception as exception:
            logger.error(
                f"{self.SERVICE_NAME} encountered an error in list_permissions_per_user",
                extra={**log_context, "error": str(exception)},
            )
            raise InvalidUsage.server_error()
