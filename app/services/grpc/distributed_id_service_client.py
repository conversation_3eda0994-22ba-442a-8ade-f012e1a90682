from cloudbeds.distributedid.server.v1 import server_pb2, server_pb2_grpc

from flask import current_app as app

from google.protobuf.json_format import MessageToDict

import grpc

from app.common.constants.permission_service import SERVICE_ID
from app.common.exceptions import InvalidUsage
from app.common.logger import logger


class DistributedIdServiceClient:
    def __init__(self):
        self._grpc_url = app.config["DISTRIBUTED_ID_SERVICE_URL"]
        self._ssl = app.config["DISTRIBUTED_ID_SERVICE_SSL"]
        self._channel = (
            grpc.secure_channel(self._grpc_url, grpc.ssl_channel_credentials())
            if self._ssl
            else grpc.insecure_channel(self._grpc_url)
        )
        self._stub = server_pb2_grpc.DistributedIdServiceStub(self._channel)

    def close(self):
        self._channel.close()

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    def get_distributed_ids(self, batch_size: int = 1):
        try:
            response = MessageToDict(
                self._stub.BatchCreateIds(
                    server_pb2.BatchCreateIdsRequest(
                        service_id=SERVICE_ID, batch_size=batch_size
                    ),
                    timeout=app.config["GRPC_TIMEOUT"],
                ),
                preserving_proto_field_name=True,
            )
            return response

        except grpc.RpcError as rpc_error:
            match rpc_error.code():
                case grpc.StatusCode.NOT_FOUND:
                    logger.debug(
                        "Distributed Id Service could not find resource",
                        extra={"error": {str(rpc_error.exception())}},
                    )
                case _:
                    logger.error(
                        "Distributed Id Service had error",
                        extra={"error": str(rpc_error.exception())},
                    )
            raise InvalidUsage.server_error()

        except Exception as exception:
            logger.error(
                "Distributed Id Service had error", extra={"error": str(exception)}
            )
            raise InvalidUsage.server_error()
