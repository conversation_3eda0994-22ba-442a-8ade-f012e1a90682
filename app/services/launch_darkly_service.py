import re
from typing import List

import ldclient
from ldclient import Context

from app.common.cache import TIMEOUT_MINUTE, cache
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import PermissionAction, PermissionActions
from app.common.logger import logger


class LaunchDarklyService:
    @staticmethod
    @cache.memoize(TIMEOUT_MINUTE)
    def has_feature_flag(key: LaunchDarklyFeature, property_id: int) -> bool:
        return ldclient.get().variation(
            key.value, Context.builder(f"property:{property_id}").build(), False
        )

    @staticmethod
    @cache.memoize(TIMEOUT_MINUTE)
    def get_policy_by_key(key: LaunchDarklyFeature, property_id: int) -> List[dict]:
        try:
            if LaunchDarklyService.has_feature_flag(key, property_id):
                actions = (
                    [
                        dict(name=PermissionAction.CREATE.value),
                        dict(name=PermissionAction.VIEW.value),
                        dict(name=PermissionAction.UPDATE.value),
                        dict(name=PermissionAction.DELETE.value),
                    ]
                    if LaunchDarklyService.has_feature_flag(
                        LaunchDarklyFeature.PermissionService, property_id
                    )
                    else [
                        dict(name=permission.value) for permission in PermissionActions
                    ]
                )
                return [
                    dict(
                        resource=re.sub(r"(?<!^)(?=[A-Z])", "_", key.name).lower(),
                        actions=actions,
                    )
                ]
        except Exception as error:
            logger.error(
                "There was a problem with Launch Darkly", extra={"error": str(error)}
            )

        return []
