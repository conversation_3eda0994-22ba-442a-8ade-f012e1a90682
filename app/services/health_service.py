import traceback

from sqlalchemy import text

from app.common.cache import cache
from app.common.database import db
from app.common.logger import logger
from app.enums.export import Format
from app.models.health import Health
from app.services.database import db_connection
from app.services.s3_service import S3Service


class HealthService:
    @staticmethod
    def check_maintenance_mode() -> bool:
        try:
            return bool(Health.get_by_name("maintenance").status)
        except Exception as maintenance_exception:
            logger.error(
                f"Maintenance Failed: {maintenance_exception}",
                extra={"error": traceback.format_exc()},
            )
            return False

    @staticmethod
    def database_available() -> bool:
        try:
            db.session.execute(text("SELECT 1"))
            return True
        except Exception as rds_exception:
            logger.error(
                f"Database Failed: {rds_exception}",
                extra={"error": traceback.format_exc()},
            )
            return False

    @staticmethod
    def aurora_available() -> bool:
        try:
            db_connection("aurora").execute(text("SELECT 1"))
            return True
        except Exception as aurora_exception:
            logger.error(
                f"Aurora Failed: {aurora_exception}",
                extra={"error": traceback.format_exc()},
            )
            return False

    @staticmethod
    def redis_available() -> bool:
        try:
            cache.cache._write_client.ping()
            return True
        except Exception as redis_exception:
            logger.error(
                f"Redis Failed: {redis_exception}",
                extra={"error": traceback.format_exc()},
            )
            return False

    @staticmethod
    def s3_available() -> bool:
        try:
            s3_service = S3Service(
                path="",
                filename="",
                format=Format.JSON,
                index=False,
                customization=None,
            )
            response = s3_service.s3.get_bucket_location(Bucket=s3_service.bucket)
            status_code = response["ResponseMetadata"]["HTTPStatusCode"]
            bucket_location = response["LocationConstraint"]
            if status_code == 200 and bucket_location is not None:
                return True
            return False
        except Exception as s3_exception:
            logger.error(
                f"S3 Failed: {s3_exception}", extra={"error": traceback.format_exc()}
            )
            return False
