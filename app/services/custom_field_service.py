import copy

from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.enums.features import LaunchDarklyFeature
from app.common.logger import log_query
from app.enums.dataset import Dataset
from app.models.guest_custom_fields_metadata_vue import (
    GuestCustomFieldsMetadataVue,
    GuestCustomFieldsMetadataVueFF,
)
from app.models.report import ReportCustomFieldCdf
from app.models.reservation_custom_fields_metadata_vue import (
    ReservationCustomFieldsMetadataVue,
    ReservationCustomFieldsMetadataVueFF,
)
from app.services.launch_darkly_service import LaunchDarklyService


class CustomFieldService:
    @staticmethod
    def get_custom_fields_by_dataset_id(
        dataset_id: int, property_id: int, organization_id: int, property_ids: list[int]
    ):
        match dataset_id:
            case Dataset.Guests.value:
                if LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.CustomFieldsCDFsMetadataFFView, property_id
                ):
                    model = GuestCustomFieldsMetadataVueFF
                else:
                    model = GuestCustomFieldsMetadataVue
                custom_fields = model.query.filter(
                    model.organization_id == organization_id,
                )
                if property_ids:
                    custom_fields = custom_fields.filter(
                        model.property_id.in_(property_ids)
                    )
                log_query(custom_fields.statement)
                custom_fields = custom_fields.all()
            case Dataset.Reservations.value:
                if LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.CustomFieldsCDFsMetadataFFView, property_id
                ):
                    model = ReservationCustomFieldsMetadataVueFF
                else:
                    model = ReservationCustomFieldsMetadataVue
                custom_fields = model.query.filter(
                    model.organization_id == organization_id,
                )
                if property_ids:
                    custom_fields = custom_fields.filter(
                        model.property_id.in_(property_ids)
                    )
                log_query(custom_fields.statement)
                custom_fields = custom_fields.all()
            case _:
                return []

        return [
            dict(
                property_id=property_id,
                custom_fields=[
                    dict(
                        internal_name=cf.internal_name,
                        field_name=cf.field_name,
                        creation_order=cf.creation_order,
                    )
                    for cf in custom_fields
                    if cf.property_id == property_id
                ],
            )
            for property_id in property_ids
            if property_id
            in [custom_field.property_id for custom_field in custom_fields]
        ]

    @staticmethod
    def get_custom_field_by_dataset_id_and_internal_name(
        dataset_id: int, internal_name: str, property_id: int
    ):
        match dataset_id:
            case Dataset.Guests.value:
                if LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.CustomFieldsCDFsMetadataFFView, property_id
                ):
                    model = GuestCustomFieldsMetadataVueFF
                else:
                    model = GuestCustomFieldsMetadataVue
                return model.query.filter(
                    model.property_id == property_id,
                    model.internal_name == internal_name,
                ).first()
            case Dataset.Reservations.value:
                if LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.CustomFieldsCDFsMetadataFFView, property_id
                ):
                    model = ReservationCustomFieldsMetadataVueFF
                else:
                    model = ReservationCustomFieldsMetadataVue
                return model.query.filter(
                    model.property_id == property_id,
                    model.internal_name == internal_name,
                ).first()

    @staticmethod
    def get_by_report_id(report_id: int):
        return ReportCustomFieldCdf.query.filter(
            ReportCustomFieldCdf.report_id == report_id
        ).all()

    @staticmethod
    def get_by_id_and_report_id(custom_field_id: int, report_id: int):
        return ReportCustomFieldCdf.query.filter(
            ReportCustomFieldCdf.id == custom_field_id,
            ReportCustomFieldCdf.report_id == report_id,
        ).first()

    @staticmethod
    def get_by_report_id_and_column(report_id, column):
        return ReportCustomFieldCdf.query.filter(
            ReportCustomFieldCdf.report_id == report_id,
            ReportCustomFieldCdf.column == column,
        ).first()

    @staticmethod
    def update_custom_field_in_report(
        report, report_custom_field_cdf, new_report_custom_field_cdf
    ):
        new_report = dict()
        cdf = lambda custom_field_cdfs: CDF(
            dataset=Dataset(report.dataset_id),
            name=report_custom_field_cdf.column,
            is_custom_field_cdf=True,
        ).get_custom_field_cdf_in_custom_field_cdfs(custom_field_cdfs)

        if (
            report.columns is not None
            and bool(CDFs.get_custom_field_cdfs(report.columns))
            and cdf(report.columns)
        ):
            new_report["columns"] = [
                (
                    {
                        **column,
                        "cdf": {
                            **column["cdf"],
                            "column": new_report_custom_field_cdf["column"],
                        },
                    }
                    if column["cdf"]["column"] == report_custom_field_cdf.column
                    else column
                )
                for column in report.columns
            ]

        if (
            report.group_rows is not None
            and bool(CDFs.get_custom_field_cdfs(report.group_rows))
            and cdf(report.group_rows)
        ):
            new_report["group_rows"] = [
                (
                    {
                        **group_row,
                        "cdf": {
                            **group_row["cdf"],
                            "column": new_report_custom_field_cdf["column"],
                        },
                    }
                    if group_row["cdf"]["column"] == report_custom_field_cdf.column
                    else group_row
                )
                for group_row in report.group_rows
            ]

        if (
            report.group_columns is not None
            and bool(CDFs.get_custom_field_cdfs(report.group_columns))
            and cdf(report.group_columns)
        ):
            new_report["group_columns"] = [
                (
                    {
                        **group_column,
                        "cdf": {
                            **group_column["cdf"],
                            "column": new_report_custom_field_cdf["column"],
                        },
                    }
                    if group_column["cdf"]["column"] == report_custom_field_cdf.column
                    else group_column
                )
                for group_column in report.group_columns
            ]

        if (
            report.sort is not None
            and bool(CDFs.get_custom_field_cdfs(report.sort))
            and cdf(report.sort)
        ):
            new_report["sort"] = [
                (
                    {
                        **sort,
                        "cdf": {
                            **sort["cdf"],
                            "column": new_report_custom_field_cdf["column"],
                        },
                    }
                    if sort["cdf"]["column"] == report_custom_field_cdf.column
                    else sort
                )
                for sort in report.sort
            ]

        if report.filters:
            new_filters = copy.deepcopy(report.filters)
            CDFs.update_filter_columns(
                new_filters,
                report_custom_field_cdf.column,
                new_report_custom_field_cdf["column"],
            )
            new_report["filters"] = new_filters

        if report.custom_cdfs:
            # check each custom cdf to see if it uses the column being updated
            for custom_cdf in report.custom_cdfs:
                if any(
                    True
                    for formula in custom_cdf.formula
                    if formula["kind"] == "custom_field_cdf"
                    and formula["value"] == report_custom_field_cdf.column
                ):
                    custom_cdf.formula = [
                        (
                            {
                                **formula,
                                "value": new_report_custom_field_cdf["column"],
                            }
                            if formula["kind"] == "custom_field_cdf"
                            and formula["value"] == report_custom_field_cdf.column
                            else formula
                        )
                        for formula in custom_cdf.formula
                    ]
                    custom_cdf.save()
        if report.charts:
            # custom field cdfs can be used as categories in charts, rename in the chart if necessary
            for chart in report.charts:
                if any(
                    True
                    for category in chart.categories
                    if category["cdf"]["type"] == "custom_field"
                    and category["cdf"]["column"] == report_custom_field_cdf.column
                ):
                    chart.categories = [
                        (
                            {
                                **category,
                                "cdf": {
                                    "column": new_report_custom_field_cdf["column"],
                                    "type": "custom_field",
                                },
                            }
                            if category["cdf"]["column"]
                            == report_custom_field_cdf.column
                            and category["cdf"]["type"] == "custom_field"
                            else category
                        )
                        for category in chart.categories
                    ]

                    chart.save()

        report.update(**new_report)
