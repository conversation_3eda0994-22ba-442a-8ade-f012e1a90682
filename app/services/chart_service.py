from http import HTTPStatus

from cairosvg import svg2png

from flask import current_app as app, g

from app.api.v1_1.schemas.chart import ChartSchema
from app.cdfs.cdfs import CDFs
from app.common.constants.chart import SCALE_PNG
from app.common.constants.limits import MAX_CHARTS_PER_REPORT
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.enums.report import ReportKind
from app.models.chart import Chart
from app.models.report import ReportCustomCdf
from app.services.hub_service import HubService
from app.services.lambda_service import LambdaService


class ChartService:
    @staticmethod
    def map_schema_to_model(chart: ChartSchema) -> dict:
        return dict(
            title=chart["title"],
            kind=chart["kind"],
            categories=chart["categories"],
            metrics=chart["metrics"],
            settings=chart["settings"],
            report_id=chart["datasource_id"]
            if chart["datasource_kind"] == ReportKind.Report.value
            else None,
            stock_report_id=chart["datasource_id"]
            if chart["datasource_kind"] == ReportKind.StockReport.value
            else None,
        )

    @staticmethod
    def get_all_by_datasource_kind_and_id(
        datasource_kind: str, datasource_id: int
    ) -> list[Chart]:
        charts = Chart.get_all_by_datasource_kind_and_datasource_id(
            datasource_kind, datasource_id
        )
        return charts

    @staticmethod
    def create(
        chart: ChartSchema, datasource_kind: str, datasource_id: int
    ) -> Chart | InvalidUsage:
        chart["datasource_kind"] = datasource_kind
        chart["datasource_id"] = datasource_id
        if (
            len(
                ChartService.get_all_by_datasource_kind_and_id(
                    datasource_kind, datasource_id
                )
            )
            >= MAX_CHARTS_PER_REPORT
        ):
            raise InvalidUsage.bad_request(
                f"{datasource_kind} {datasource_id} already has maximum of {MAX_CHARTS_PER_REPORT} chart(s)"
            )
        return Chart.create(
            user_id=g.user.id,
            **ChartService.map_schema_to_model(chart),
        )

    @staticmethod
    def update(
        chart_id: int,
        datasource_kind: str,
        datasource_id: int,
        chart_schema: ChartSchema,
    ) -> Chart:
        chart = ChartService.get_by_id_datasource_kind_and_id(
            chart_id, datasource_kind, datasource_id
        )

        chart.user_id = g.user.id
        return chart.update(**chart_schema)

    @staticmethod
    def delete(chart_id: int, datasource_kind: str, datasource_id: int) -> None:
        cards = HubService.get_cards_by_chart_id(chart_id)
        if cards:
            raise InvalidUsage.bad_request("Chart is in use by a card")
        chart = ChartService.get_by_id_datasource_kind_and_id(
            chart_id, datasource_kind, datasource_id
        )
        return chart.delete()

    @staticmethod
    def get_by_id_datasource_kind_and_id(
        chart_id: int, datasource_kind: str, datasource_id: int
    ) -> Chart | InvalidUsage:
        chart = Chart.get_by_id_datasource_kind_and_id(
            chart_id, datasource_kind, datasource_id
        )
        if not chart:
            raise InvalidUsage.not_found()
        return chart

    @staticmethod
    def update_custom_cdfs(
        datasource_id: int,
        datasource_kind: str,
        old_column: str,
        new_custom_cdf: ReportCustomCdf,
    ) -> None:
        """Update the charts in the report when the column name is updated"""
        charts = ChartService.get_all_by_datasource_kind_and_id(
            datasource_kind, datasource_id
        )

        for chart in charts:
            # Update the categories and metrics
            new_chart = ChartSchema(
                exclude=(
                    "id",
                    "datasource_kind",
                    "datasource_id",
                    "updated_at",
                    "created_at",
                    "user_id",
                    "datasource_title",
                    "datasource_description",
                )
            ).dump(chart)

            # Update custom cdf in chart metrics if exist
            if chart.metrics is not None and bool(CDFs.get_custom_cdfs(chart.metrics)):
                new_chart["metrics"] = [
                    {
                        **column,
                        "cdf": {
                            **column["cdf"],
                            "column": new_custom_cdf["column"],
                        },
                    }
                    if column["cdf"]["column"] == old_column
                    else column
                    for column in chart.metrics
                ]

            if (
                chart.settings
                and chart.settings.get("metrics") is not None
                and bool(CDFs.get_custom_cdfs(chart.settings["metrics"]))
            ):
                new_chart["settings"]["metrics"] = [
                    {
                        **column,
                        "cdf": {
                            **column["cdf"],
                            "column": new_custom_cdf["column"],
                        },
                    }
                    if column["cdf"]["column"] == old_column
                    else column
                    for column in chart.settings["metrics"]
                ]

            # Update custom cdf in chart categories if exist
            if chart.categories is not None and bool(
                CDFs.get_custom_cdfs(chart.categories)
            ):
                new_chart["categories"] = [
                    {
                        **column,
                        "cdf": {
                            **column["cdf"],
                            "column": new_custom_cdf["column"],
                        },
                    }
                    if column["cdf"]["column"] == old_column
                    else column
                    for column in chart.categories
                ]

            ChartService.update(
                chart.id,
                datasource_kind,
                datasource_id,
                new_chart,
            )
        return

    @staticmethod
    def get_png(
        chart: Chart,
        chart_data: dict,
        flatten_cdfs: list[dict],
        custom_cdfs: list[dict] = [],
    ) -> bytes | None:
        """Generate a PNG for a given chart"""
        cdfs = [{**cdf, "kind": cdf["kind"]} for cdf in flatten_cdfs] + custom_cdfs
        try:
            lambda_service = LambdaService(app.config["REPORTING_CHARTS_LAMBDA"])
            payload = {
                "chart": chart.__dict__,
                "cdfs": cdfs,
                "reportData": chart_data,
                "chartOptions": {
                    "height": 600,
                    "width": 600,
                },
            }

            response = lambda_service.invoke(payload)

            if response["statusCode"] != HTTPStatus.OK:
                logger.error("Lambda response is 500", extra={"error": response})
                return None
            return svg2png(bytestring=response["body"]["svg"], scale=SCALE_PNG)

        except Exception as exception:
            logger.error(
                "There was a problem generating the PNG",
                extra={"error": str(exception)},
            )
            return None

    @staticmethod
    def get_search_results(query, sort_query, property_id):
        charts = Chart.get_search_results(query, sort_query, property_id)
        return charts

    @staticmethod
    def get_all(filter, sort, property_id):
        charts = Chart.get_all(filter, sort, property_id)
        return charts
