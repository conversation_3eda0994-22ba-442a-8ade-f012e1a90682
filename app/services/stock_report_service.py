import copy
from datetime import datetime

from marshmallow import <PERSON>XCLUDE

from sqlalchemy import text
from sqlalchemy.orm.collections import InstrumentedList


from app.api.v1_1.schemas.chart import ChartSchema
from app.api.v1_1.schemas.log import ReportLogSchema
from app.api.v1_1.schemas.report import ReportCustomCdfSchema
from app.api.v1_1.schemas.report.report import ReportQuerySchema, ReportSchema
from app.api.v1_1.schemas.stock_report import (
    StockReportCountrySchema,
    StockReportFeatureSchema,
    StockReportImportSchema,
    StockReportInsertSchema,
    StockReportPropertySchema,
    StockReportPropertyTypeSchema,
    StockReportRevisionSchema,
    StockReportSchema,
)
from app.api.v1_1.schemas.stock_report.stock_report_custom_cdf import (
    StockReportCustomCdfSchema,
)
from app.api.v1_1.validations.chart import validate_chart
from app.api.v1_1.validations.reports import (
    validate_filters,
    validate_no_custom_field_cdfs,
)
from app.cdfs.cdfs import CDFs
from app.common.cache import cache
from app.common.constants.country_codes import COUNTRY_CODES
from app.common.constants.table_limits import (
    EXPORT_LIMIT,
    PIVOT_PREVIEW_LIMIT,
    PIVOT_RUN_LIMIT,
    SUMMARY_PREVIEW_LIMIT,
    SUMMARY_RUN_LIMIT,
    TABULAR_PREVIEW_LIMIT,
    TABULAR_RUN_LIMIT,
)
from app.common.database import db
from app.common.enums.features import BillingPortalFeatures
from app.common.enums.property_types import PropertyTypes
from app.common.enums.rules import (
    RulesKind,
    SupportedRules,
    SupportedRulesKind,
    SupportedRulesNames,
)
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.user import User
from app.datasets.dataset import Dataset
from app.decorators.pagination import pagination
from app.enums.dataset import Dataset as DatasetEnum
from app.enums.export import Format, View
from app.enums.format import InternalNumericFormat
from app.enums.log_actions import ReportAction
from app.enums.mode import Mode
from app.enums.report import ReportKind
from app.models.report import Report
from app.models.stock_report import (
    StockReport,
    StockReportAdmin,
    StockReportCountry,
    StockReportCustomCdf,
    StockReportFeature,
    StockReportProperty,
    StockReportPropertyType,
)
from app.models.stock_report_revision import StockReportRevision
from app.reports.report import Report as ReportData
from app.services.chart_service import ChartService
from app.services.custom_cdf_service import CustomCdfService
from app.services.permission_service import PermissionService
from app.services.property_feature_service import PropertyFeatureService
from app.services.property_service import PropertyService
from app.services.property_setting_service import PropertySettingService
from app.services.stock_report_folder_service import StockReportFolderService
from app.services.stock_tag_service import StockTagService


class StockReportService:
    @staticmethod
    def get_by_id(
        report_id: int,
        user: User,
        property_id: int = None,
        include_cloudbeds: bool = False,
    ):
        if include_cloudbeds:
            stock_report = StockReport.get_by_id(report_id)

        else:
            stock_report = StockReport.get_by_id_with_rules(
                report_id, user, property_id
            )
        if not stock_report:
            return InvalidUsage.not_found(
                message=f"A Stock report with id: {report_id} does not exist"
            )

        stock_report.property_ids = [property_id]
        return stock_report

    @staticmethod
    def get_all(
        user: User,
        property_id: int,
        filters: dict,
        sort: str,
        include_cloudbeds: bool = False,
        property_country_only: bool = False,
    ):
        if include_cloudbeds:
            stock_reports = StockReport.get_all_sorted(
                filters,
                sort,
            )

        else:
            stock_reports = StockReport.get_all_sorted_with_rules(
                user, property_id, filters, sort, False, property_country_only
            )

        return stock_reports

    @staticmethod
    def get_all_by_partial_search(
        user: User,
        property_id: int,
        filters: dict,
        sort: str,
        include_cloudbeds: bool = False,
        property_country_only: bool = False,
    ):
        if filters.get("search_columns") and filters.get("search_term"):
            if include_cloudbeds:
                return StockReportAdmin.get_all_search_results(
                    property_id, filters, sort, user.enabled_datasets
                )
            else:
                return StockReport.get_all_search_results_with_rules(
                    user,
                    property_id,
                    filters,
                    sort,
                    True,
                    property_country_only,
                )

        else:
            if include_cloudbeds:
                return StockReportAdmin.get_all_sorted(
                    filters,
                    sort,
                    True,
                    user.enabled_datasets,
                )
            else:
                return StockReport.get_all_sorted_with_rules(
                    user,
                    property_id,
                    filters,
                    sort,
                    True,
                    property_country_only,
                )

    @staticmethod
    def get_stock_report_cdfs(property_id: int, dataset: Dataset):
        features = PropertyFeatureService.get_all_by_property_id(property_id)

        if bool(features):
            categories_to_exclude = [
                category["category"]
                for category in dataset.feature_categories
                if category["feature"] not in features
            ]
            return CDFs.filter_cdfs_by_category(dataset.cdfs, categories_to_exclude)

        return dataset.non_feature_cdfs

    @staticmethod
    def get_query_report_by_id(
        query_stock_report: dict,
        report_id: int,
        user: User,
        property_id: int,
        validate_stock_report: bool = True,
    ) -> dict:
        """Method that will get existing stock report by id, and will return it with the updated fields after validation"""
        stock_report = StockReportSchema(partial=True).dump(
            StockReportService.get_by_id(
                report_id, user, property_id, not validate_stock_report
            )
        )

        if validate_stock_report:
            # Validate that columns are the same in original stock report, matching cdf and metrics
            if query_stock_report.get("columns"):
                column_names = [
                    column["cdf"]["column"] for column in stock_report["columns"]
                ]
                if not stock_report["columns"] or not all(
                    column["cdf"]["column"] in column_names
                    for column in query_stock_report["columns"]
                ):
                    raise InvalidUsage.bad_request(
                        f"Columns are not the same on stock report id {report_id}"
                    )

            # Validate that groups cdfs are the same in original stock report, modifiers can be different
            for key in ["group_rows", "group_columns"]:
                if query_stock_report.get(key):
                    if not stock_report[key] or not all(
                        cdf in [cdf["cdf"]["column"] for cdf in stock_report[key]]
                        for cdf in [
                            cdf["cdf"]["column"] for cdf in query_stock_report[key]
                        ]
                    ):
                        raise InvalidUsage.bad_request(
                            f"Groups are not the same on stock report id {report_id}"
                        )

            # Validate that sorts only are applied to existing stock report columns or group rows
            if query_stock_report.get("sort"):
                sort_columns = [
                    sort["cdf"]["column"] for sort in query_stock_report["sort"]
                ]

                if len(sort_columns) != len(list(set(sort_columns))):
                    raise InvalidUsage.bad_request(
                        "Sort columns must not contain duplicates"
                    )

                if not all(
                    cdf
                    in [
                        cdf["cdf"]["column"]
                        for cdf in stock_report["columns"]
                        + (
                            stock_report["group_rows"]
                            if stock_report["group_rows"] is not None
                            else []
                        )
                    ]
                    for cdf in sort_columns
                ):
                    raise InvalidUsage.bad_request(
                        f"Sort columns must be selected columns or group_rows on stock report id {report_id}"
                    )

            # Validate that filter cdfs are the same in original stock report, operators and value can be different
            if query_stock_report.get("filters"):
                if not stock_report["filters"] or not all(
                    cdf
                    in [
                        cdf["cdf"]["column"]
                        for cdf in CDFs.get_cdfs_from_filters(
                            stock_report["filters"], []
                        )
                    ]
                    for cdf in [
                        cdf["cdf"]["column"]
                        for cdf in CDFs.get_cdfs_from_filters(
                            query_stock_report["filters"], []
                        )
                    ]
                ):
                    raise InvalidUsage.bad_request(
                        f"Filters are not the same on stock report id {report_id}"
                    )

                validate_filters({**stock_report, **query_stock_report})

        stock_report_query = {**stock_report, **query_stock_report}

        StockReportService.validate_report_query(stock_report_query)
        return stock_report_query

    @staticmethod
    @pagination("items")
    def get_search_results(user: User, property_id, params, _):
        return StockReport.get_search_results(
            user,
            property_id,
            params,
        )

    @staticmethod
    def insert_rules(stock_report_id, rules):
        feature_ids = rules.get("feature_ids")
        property_ids = rules.get("property_ids")
        country_codes = rules.get("country_codes")
        property_types = rules.get("property_types")

        if feature_ids:
            features = [
                StockReportFeature(
                    **StockReportFeatureSchema().dump(
                        dict(stock_report_id=stock_report_id, feature_id=feature_id)
                    )
                )
                for feature_id in feature_ids
            ]

            db.session.bulk_save_objects(features)
            db.session.commit()

        if property_ids:
            properties = [
                StockReportProperty(
                    **StockReportPropertySchema().dump(
                        dict(stock_report_id=stock_report_id, property_id=property_id)
                    )
                )
                for property_id in property_ids
            ]

            db.session.bulk_save_objects(properties)
            db.session.commit()

        if country_codes:
            countries = [
                StockReportCountry(
                    **StockReportCountrySchema().dump(
                        dict(stock_report_id=stock_report_id, country_code=country_code)
                    )
                )
                for country_code in country_codes
            ]

            db.session.bulk_save_objects(countries)
            db.session.commit()

        if property_types:
            property_types_list = [
                StockReportPropertyType(
                    **StockReportPropertyTypeSchema().dump(
                        dict(
                            stock_report_id=stock_report_id, property_type=property_type
                        )
                    )
                )
                for property_type in property_types
            ]
            db.session.bulk_save_objects(property_types_list)
            db.session.commit()

    @staticmethod
    def delete_rules(stock_report_id):
        StockReportFeature.query.filter(
            StockReportFeature.stock_report_id == stock_report_id
        ).delete()
        StockReportProperty.query.filter(
            StockReportProperty.stock_report_id == stock_report_id
        ).delete()
        StockReportCountry.query.filter(
            StockReportCountry.stock_report_id == stock_report_id
        ).delete()
        StockReportPropertyType.query.filter(
            StockReportPropertyType.stock_report_id == stock_report_id
        ).delete()

        db.session.commit()

    @staticmethod
    def update_rules(stock_report, rules, user: User):
        # validate stock report exists, and update its updated at time
        update_stock_report = StockReportSchema(exclude=["type", "custom_cdfs"]).dump(
            stock_report
        )
        update_stock_report["updated_at"] = datetime.now()
        update_stock_report["user_id"] = user.id
        stock_report.update(**update_stock_report)

        # remove any existing, and replace with updated rules
        StockReportService.delete_rules(stock_report.id)
        StockReportService.insert_rules(stock_report.id, rules)

    @staticmethod
    def publish(publish_stock_report, user: User, property_id: int):
        rules = publish_stock_report.pop("rules", dict())

        report = Report.get_by_id(publish_stock_report.get("report_id"))
        report_schema = ReportSchema().dump(report)
        validate_no_custom_field_cdfs(report_schema)

        report.published = True
        if report.periods:
            report.periods = [
                {key: period[key] for key in period if key != "id"}
                for period in report.periods
            ]
        report.user_id = user.id
        stock_report_schema = StockReportImportSchema().load(
            StockReportImportSchema(
                only=list(StockReportImportSchema().load_fields.keys())
            ).dump(report)
        )
        stock_report_schema.pop("custom_cdfs")
        stock_report = StockReport.create(**stock_report_schema)
        stock_report_id = stock_report.id

        try:
            if report.custom_cdfs:
                CustomCdfService.create(
                    ReportKind.StockReport,
                    StockReportCustomCdfSchema,
                    report.custom_cdfs,
                    stock_report_id,
                    user.id,
                )

            charts = report.charts
            if charts:
                for chart in charts:
                    validate_chart(ChartSchema().dump(chart), report)

                for chart in charts:
                    ChartService.create(
                        ChartSchema(
                            exclude=[
                                "id",
                                "user_id",
                                "created_at",
                                "updated_at",
                                "datasource_title",
                                "datasource_description",
                            ]
                        ).dump(chart),
                        ReportKind.StockReport.value,
                        stock_report_id,
                    )

            StockReportService.insert_rules(stock_report_id, rules)

        except Exception as exception:
            stock_report.delete()
            logger.error(
                "There was a problem publishing a stock report",
                extra={"error": str(exception)},
            )
            raise InvalidUsage.server_error(
                "There was a problem publishing the stock report"
            )

        logger.info(
            "Stock Report Publish",
            extra=ReportLogSchema().dump(
                {
                    **StockReportSchema().dump(stock_report),
                    **dict(
                        action=ReportAction.Create.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=property_id,
                        user_id=user.id,
                    ),
                }
            ),
        )
        return stock_report

    @staticmethod
    def get_supported_rules():
        return {
            "rules": [
                {
                    "id": rule.value,
                    "name": SupportedRulesNames[rule.name].value,
                    "kind": RulesKind(SupportedRulesKind[rule.name].value).name,
                }
                for rule in SupportedRules
            ]
        }

    @staticmethod
    def get_limits():
        return {
            "limits": {
                "tabular_preview": TABULAR_PREVIEW_LIMIT,
                "tabular_run": TABULAR_RUN_LIMIT,
                "summary_preview": SUMMARY_PREVIEW_LIMIT,
                "summary_run": SUMMARY_RUN_LIMIT,
                "pivot_preview": PIVOT_PREVIEW_LIMIT,
                "pivot_run": PIVOT_RUN_LIMIT,
                "export": EXPORT_LIMIT,
            }
        }

    @staticmethod
    def get_rule_by_key(rules_key):
        match rules_key:
            case SupportedRules.FEATURE_IDS.value:
                return [
                    {"id": feature.value, "name": (feature.name)}
                    for feature in BillingPortalFeatures
                ]
            case SupportedRules.PROPERTY_IDS.value:
                return []
            case SupportedRules.COUNTRY_CODES.value:
                return [
                    {"id": COUNTRY_CODES[country], "name": country.value}
                    for country in COUNTRY_CODES
                ]
            case SupportedRules.PROPERTY_TYPES.value:
                return [
                    {"id": property_type.name, "name": property_type.value}
                    for property_type in PropertyTypes
                ]

    @staticmethod
    def query_summary(
        stock_report_id, property_id, organization_id, query, report, user: User
    ):
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        stock_report = StockReportService.get_query_report_by_id(
            report,
            stock_report_id,
            user,
            property_id,
            not PermissionService.is_whitelisted(
                user.email,
            ),
        )
        dataset = Dataset(DatasetEnum(stock_report["dataset_id"]))

        stock_report["custom_cdfs"] = InstrumentedList(
            [
                StockReportCustomCdf(**custom_cdf)
                for custom_cdf in stock_report.get("custom_cdfs") or []
            ]
        )

        data = ReportData(
            mode=query["mode"],
            dataset=dataset,
            custom_cdfs=stock_report["custom_cdfs"],
            columns=stock_report["columns"],
            group_rows=stock_report["group_rows"],
            group_columns=stock_report["group_columns"],
            filters=stock_report["filters"],
            property_ids=report.get("property_ids"),
            organization_id=organization_id,
            sort=stock_report["sort"],
            settings=stock_report["settings"],
            format=query["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            formats=stock_report.get("formats"),
            periods=stock_report.get("periods"),
            comparisons=stock_report.get("comparisons"),
        ).get_summary()

        logger.info(
            "Stock Report query summary data",
            extra=ReportLogSchema().dump(
                {
                    **stock_report,
                    **dict(
                        action=ReportAction.SummaryView.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return data

    @staticmethod
    def query_data(
        stock_report_id, property_id, organization_id, query, report, user: User
    ):
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        stock_report = StockReportService.get_query_report_by_id(
            report,
            stock_report_id,
            user,
            property_id,
            not PermissionService.is_whitelisted(
                user.email,
            ),
        )

        dataset = Dataset(DatasetEnum(stock_report["dataset_id"]))

        stock_report["custom_cdfs"] = InstrumentedList(
            [
                StockReportCustomCdf(**custom_cdf)
                for custom_cdf in stock_report.get("custom_cdfs") or []
            ]
        )

        data = ReportData(
            mode=Mode.Run,
            dataset=dataset,
            custom_cdfs=stock_report["custom_cdfs"],
            columns=stock_report["columns"],
            group_rows=stock_report["group_rows"],
            group_columns=stock_report["group_columns"],
            filters=stock_report["filters"],
            property_ids=report.get("property_ids"),
            organization_id=organization_id,
            sort=stock_report["sort"],
            settings=stock_report["settings"],
            format=query["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=stock_report["periods"],
            formats=stock_report.get("formats"),
            comparisons=stock_report.get("comparisons"),
        ).get_data()

        logger.info(
            "Stock Report query data",
            extra=ReportLogSchema().dump(
                {
                    **stock_report,
                    **dict(
                        action=ReportAction.DataView.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return data

    @staticmethod
    def get_data_by_id(
        stock_report_id: int,
        property_id: int,
        organization_id: int,
        query: dict,
        user: User,
    ):
        """Receive a stock report id and return the data for the report"""
        include_cloudbeds = PermissionService.is_whitelisted(
            user.email,
        )
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        stock_report = StockReportSchema(partial=True).dump(
            StockReportService.get_by_id(
                stock_report_id, user, property_id, include_cloudbeds
            )
        )

        if "custom_cdfs" not in stock_report:
            stock_report["custom_cdfs"] = []

        stock_report["custom_cdfs"] = InstrumentedList(
            [
                StockReportCustomCdf(**custom_cdf)
                for custom_cdf in stock_report["custom_cdfs"]
            ]
        )

        dataset = Dataset(DatasetEnum(stock_report["dataset_id"]))

        report_data = ReportData(
            mode=Mode.Run,
            dataset=dataset,
            custom_cdfs=stock_report["custom_cdfs"],
            columns=stock_report["columns"],
            group_rows=stock_report["group_rows"],
            group_columns=stock_report["group_columns"],
            filters=stock_report["filters"],
            property_ids=query.get("property_ids"),
            organization_id=organization_id,
            sort=stock_report["sort"],
            settings=stock_report["settings"],
            format=query["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=stock_report["periods"],
            formats=stock_report.get("formats"),
            comparisons=stock_report.get("comparisons"),
        ).get_data()

        logger.info(
            "Stock Report view data",
            extra=ReportLogSchema().dump(
                {
                    **stock_report,
                    **dict(
                        action=ReportAction.DataView.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return report_data

    @staticmethod
    def get_summary_by_id(
        stock_report_id, property_id, organization_id, query, user: User
    ):
        include_cloudbeds = PermissionService.is_whitelisted(
            user.email,
        )

        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        stock_report = StockReportSchema(partial=True).dump(
            StockReportService.get_by_id(
                stock_report_id, user, property_id, include_cloudbeds
            )
        )

        stock_report["custom_cdfs"] = InstrumentedList(
            [
                StockReportCustomCdf(**custom_cdf)
                for custom_cdf in stock_report.get("custom_cdfs") or []
            ]
        )

        dataset = Dataset(DatasetEnum(stock_report["dataset_id"]))

        report_data = ReportData(
            mode=Mode.Run,
            dataset=dataset,
            custom_cdfs=stock_report["custom_cdfs"],
            columns=stock_report["columns"],
            group_rows=stock_report["group_rows"],
            group_columns=stock_report["group_columns"],
            filters=stock_report["filters"],
            property_ids=query.get("property_ids"),
            organization_id=organization_id,
            sort=stock_report["sort"],
            settings=stock_report["settings"],
            format=query["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            formats=stock_report.get("formats"),
            periods=stock_report.get("periods"),
            comparisons=stock_report.get("comparisons"),
        ).get_summary()

        logger.info(
            "Stock Report view summary data",
            extra=ReportLogSchema().dump(
                {
                    **stock_report,
                    **dict(
                        action=ReportAction.SummaryView.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return report_data

    @staticmethod
    def get_export_by_id(
        stock_report_id, property_id, organization_id, query, user: User
    ):
        include_cloudbeds = PermissionService.is_whitelisted(
            user.email,
        )

        stock_report_model = StockReportService.get_by_id(
            stock_report_id, user, property_id, include_cloudbeds
        )
        stock_report = StockReportSchema(partial=True).dump(stock_report_model)

        stock_report["custom_cdfs"] = InstrumentedList(
            [
                StockReportCustomCdf(**custom_cdf)
                for custom_cdf in stock_report.get("custom_cdfs") or []
            ]
        )

        dataset = Dataset(DatasetEnum(stock_report["dataset_id"]))
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        custom_cdfs_list = ReportCustomCdfSchema(
            only=("name", "column", "kind", "description")
        ).dump(stock_report["custom_cdfs"], many=True)
        chart_pngs = []
        if query.get("include_charts"):
            charts = stock_report_model.charts
            if (
                charts
                and query["view"] == View.Formatted.value
                and query["format"] == Format.XLSX.value
            ):
                for chart in charts:
                    chart_png = ChartService.get_png(
                        chart,
                        ReportData(
                            mode=Mode.Export,
                            dataset=dataset,
                            custom_cdfs=stock_report["custom_cdfs"],
                            columns=chart.metrics,
                            group_rows=chart.categories,
                            group_columns=stock_report["group_columns"],
                            filters=stock_report["filters"],
                            property_ids=stock_report["property_ids"],
                            organization_id=organization_id,
                            sort=stock_report["sort"],
                            settings={
                                "transpose": False,
                                "totals": True,
                                "details": False,
                            },
                            format=InternalNumericFormat.RoundedFloat.value,
                            property_timezone=property_timezone,
                            property_settings=property_settings,
                            periods=stock_report["periods"],
                            formats=stock_report.get("formats"),
                            comparisons=stock_report.get("comparisons"),
                        ).get_data(),
                        dataset.flatten_all_cdfs,
                        custom_cdfs_list,
                    )
                    if chart_png:
                        chart_pngs.append(chart_png)

        report_data = ReportData(
            mode=Mode.Export,
            dataset=dataset,
            custom_cdfs=stock_report["custom_cdfs"],
            columns=stock_report["columns"],
            group_rows=stock_report["group_rows"],
            group_columns=stock_report["group_columns"],
            filters=stock_report["filters"],
            property_ids=query.get("property_ids"),
            organization_id=organization_id,
            sort=stock_report["sort"],
            settings=stock_report["settings"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=stock_report["periods"],
            formats=stock_report.get("formats"),
            format=InternalNumericFormat.RoundedFloat.value,
            comparisons=stock_report.get("comparisons"),
        ).get_export(
            stock_report["title"],
            query["view"],
            query["format"],
            chart_pngs,
            ["stock_reports", "exports"],
        )

        logger.info(
            "Stock Report export by id",
            extra=ReportLogSchema().dump(
                {
                    **stock_report,
                    **dict(
                        action=ReportAction.Export.value,
                        report_kind=ReportKind.StockReport.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return report_data

    @staticmethod
    def get_max_revision(stock_report_id):
        version = (
            StockReportRevision.query.with_entities(StockReportRevision.revision_id)
            .filter(StockReportRevision.stock_report_id == stock_report_id)
            .order_by(StockReportRevision.revision_id.desc())
            .first()
        )
        if not version:
            return 0
        return version.revision_id

    @staticmethod
    def save_revision(stock_report):
        stock_report_revision = StockReportRevision(
            **StockReportRevisionSchema(
                exclude=("type", "property_ids", "folder_id", "created_by", "tags")
            ).dump(stock_report)
        )
        stock_report_revision.stock_report_id = stock_report.id
        stock_report_revision.revision_id = (
            StockReportService.get_max_revision(stock_report.id) + 1
        )
        stock_report_revision.save()

        StockReportRevision.prune(stock_report.id)

    @staticmethod
    def get_revisions_by_stock_report_id(stock_report_id):
        stock_report = StockReport.get_by_id(stock_report_id)
        if not stock_report:
            raise InvalidUsage.not_found(
                message=f"A Stock report with id: {stock_report_id} does not exist"
            )
        return StockReportRevision.get_all_by_id(stock_report.id)

    @staticmethod
    def get_revision(stock_report_id, revision_id):
        stock_report = StockReport.get_by_id(stock_report_id)
        if not stock_report:
            raise InvalidUsage.not_found(
                message=f"A Stock report with id: {stock_report_id} does not exist"
            )
        return StockReportRevision.get_revision(stock_report.id, revision_id)

    @staticmethod
    def upsert_stock_report(stock_report_json: dict) -> None:
        """Insert a stock report at an id or update a stock report from json

        Args:
            stock_report_json (dict): a stock report json
        """
        rules = stock_report_json.pop("rules", {})
        stock_report_id = stock_report_json.get("id")
        existing_stock_report = StockReport.get_by_id(stock_report_id)

        if existing_stock_report:
            existing_stock_report.custom_cdfs = (
                existing_stock_report.custom_cdfs
                if existing_stock_report.custom_cdfs is not None
                else []
            )
            stock_report = StockReportImportSchema().load(
                StockReportImportSchema(
                    only=list(StockReportImportSchema().load_fields.keys())
                ).dump(stock_report_json)
            )
            StockReportService.save_revision(existing_stock_report)
            stock_report.pop("custom_cdfs")

            if stock_report_json.get("tags"):
                tags = []
                for tag in stock_report_json.get("tags"):
                    tags.append(StockTagService.get_by_id_or_404(tag["id"]))
                stock_report["tags"] = tags
            existing_stock_report.update(**stock_report)

            StockReportService.delete_rules(stock_report_id)
            StockReportService.insert_rules(stock_report_id, rules)

            # Upsert Custom CDF's
            if stock_report_json.get("custom_cdfs"):
                if existing_stock_report.custom_cdfs:
                    CustomCdfService.delete_by_report_kind_and_id(
                        ReportKind.StockReport, stock_report_id
                    )
                CustomCdfService.create(
                    ReportKind.StockReport,
                    StockReportCustomCdfSchema,
                    stock_report_json["custom_cdfs"],
                    existing_stock_report.id,
                    existing_stock_report.user_id,
                )
        else:
            stock_report_schema = StockReportInsertSchema().load(
                StockReportImportSchema(
                    only=list(StockReportInsertSchema().load_fields.keys())
                ).dump(stock_report_json)
            )
            stock_report_schema.pop("custom_cdfs")

            if stock_report_json.get("tags"):
                tags = []
                for tag in stock_report_json.get("tags"):
                    tags.append(StockTagService.get_by_id_or_404(tag["id"]))
                stock_report_schema["tags"] = tags

            stock_report = StockReport.create(**stock_report_schema)
            # always reset id sequence after inserting with id
            db.session.execute(
                text(
                    "SELECT SETVAL('stock_report_id_seq', (SELECT MAX(id) FROM stock_report))"
                )
            )

            StockReportService.insert_rules(stock_report_id, rules)

            # Create Custom Cdfs
            if stock_report_json.get("custom_cdfs"):
                CustomCdfService.create(
                    ReportKind.StockReport,
                    StockReportCustomCdfSchema,
                    stock_report_json["custom_cdfs"],
                    stock_report.id,
                    stock_report.user_id,
                )
                # always reset id sequence after inserting with id
                db.session.execute(
                    text(
                        "SELECT SETVAL('stock_report_custom_cdf_id_seq', (SELECT MAX(id) FROM stock_report_custom_cdf))"
                    )
                )

    @staticmethod
    def assign_folder(stock_report_id: int, folder_id: int) -> None:
        # Check folder exist
        StockReportFolderService.get_by_id(folder_id)

        stock_report = StockReportService.get_by_id(stock_report_id, None, None, True)

        stock_report.folder_id = folder_id
        stock_report.update()

    @staticmethod
    def remove_stock_report_from_folder(stock_report_id: int, folder_id: int) -> None:
        stock_report = StockReport.query.filter(
            StockReport.id == stock_report_id, StockReport.folder_id == folder_id
        ).first_or_404()
        stock_report.folder_id = None
        stock_report.update()
        cache.delete_memoized(StockReportFolderService.get_all)

    @staticmethod
    def is_stock_report_tagged(stock_report_id: int, stock_tag_id):
        return StockReport.is_stock_report_tagged(stock_report_id, stock_tag_id)

    @staticmethod
    def get_by_stock_tag_id(stock_tag_id: int):
        return StockReport.get_by_stock_tag_id(stock_tag_id)

    @staticmethod
    def validate_report_query(stock_report_query: dict):
        stock_report_query = copy.deepcopy(stock_report_query)
        for cdf in stock_report_query.get("custom_cdfs", []):
            cdf.pop("column", None)
            cdf.pop("id", None)
            cdf.pop("created_at", None)
            cdf.pop("updated_at", None)

        schema = ReportQuerySchema(unknown=EXCLUDE)
        schema.load(stock_report_query)
