from operator import and_

from sqlalchemy import literal_column, select, union
from sqlalchemy.orm.query import Query

from app.common.cache import TIMEOUT_5_MINUTES, cache
from app.common.database import db
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.models.acl import (
    AHTLAssociationProperty,
    RoleAssociation,
    RoleAssociationProperty,
    TblAcl,
    TblAclResources,
    TblAclRoles,
    UserRoleAssociation,
    UserRoleAssociationProperty,
)


class ACLService:
    def __get_query(user_id: int, property_id: int, acl: str = None) -> Query:
        user_role_association = (
            UserRoleAssociation.query.with_entities(
                UserRoleAssociation.user_id.label("user_id"),
                RoleAssociation.role_id.label("role_id"),
                AHTLAssociationProperty.property_id.label("property_id"),
            )
            .join(
                RoleAssociation,
                UserRoleAssociation.role_association_id == RoleAssociation.id,
            )
            .join(
                AHTLAssociationProperty,
                and_(
                    AHTLAssociationProperty.association_id
                    == RoleAssociation.association_id,
                    AHTLAssociationProperty.deleted == 0,
                ),
            )
            .filter(
                UserRoleAssociation.active == 1,
                UserRoleAssociation.user_id == user_id,
                AHTLAssociationProperty.property_id == property_id,
            )
        )

        user_role_association_property = (
            UserRoleAssociationProperty.query.with_entities(
                UserRoleAssociationProperty.user_id.label("user_id"),
                RoleAssociationProperty.role_id.label("role_id"),
                AHTLAssociationProperty.property_id.label("property_id"),
            )
            .join(
                RoleAssociationProperty,
                UserRoleAssociationProperty.role_association_property_id
                == RoleAssociationProperty.id,
            )
            .join(
                AHTLAssociationProperty,
                and_(
                    AHTLAssociationProperty.id
                    == RoleAssociationProperty.association_property_id,
                    AHTLAssociationProperty.deleted == 0,
                ),
            )
            .filter(
                UserRoleAssociationProperty.active == 1,
                UserRoleAssociationProperty.user_id == user_id,
                AHTLAssociationProperty.property_id == property_id,
            )
        )

        user_roles = union(user_role_association, user_role_association_property).alias(
            "user_roles"
        )

        filter = [
            literal_column("action") == "allow",
            user_roles.c.user_id == user_id,
            user_roles.c.property_id.in_([property_id]),
        ]

        if acl:
            filter.append(TblAclResources.resource == acl)

        query = (
            select(TblAclResources.resource)
            .select_from(user_roles)
            .join(
                TblAclRoles,
                and_(
                    TblAclRoles.id == user_roles.c.role_id, TblAclRoles.is_active == 1
                ),
            )
            .join(
                TblAcl,
                and_(TblAcl.type == "role", TblAcl.type_id == user_roles.c.role_id),
            )
            .join(TblAclResources, TblAcl.resource_id == TblAclResources.id)
            .filter(*filter)
        )

        return query

    @staticmethod
    @cache.memoize(TIMEOUT_5_MINUTES)
    def get_all_by_user_id(user_id: int, property_id: int) -> list[str]:
        try:
            query = ACLService.__get_query(user_id, property_id)
            acls = [row for row, in db.session.execute(query).fetchall()]
            return acls
        except Exception as exception:
            logger.error(
                "There was an error getting all ACL's for user",
                extra={"error": str(exception)},
            )
            raise InvalidUsage.server_error()

    @staticmethod
    @cache.memoize(TIMEOUT_5_MINUTES)
    def get_by_user_id(user_id: int, property_id: int, acl: str) -> str | None:
        try:
            query = ACLService.__get_query(user_id, property_id, acl)
            acl = db.session.execute(query).fetchone()
            return acl.resource if acl else None
        except Exception as exception:
            logger.error(
                "There was an error getting one ACL", extra={"error": str(exception)}
            )
            raise InvalidUsage.server_error()
