from app.common.cache import TIMEOUT_HOUR, cache
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.services.grpc import OrganizationServiceClient
from app.services.property_service import PropertyService


class OrganizationService:
    @staticmethod
    def get_organization_id_by_property_id(property_id) -> int:
        try:
            property = PropertyService.get_property(property_id)
            organization = property.get("organization")
            return organization.get("id")

        except AttributeError as exception:
            logger.error(
                f"Organization ID not found for the property {property_id}",
                extra={"error": str(exception), "service_response": property},
            )
            raise InvalidUsage.server_error()

        except Exception as exception:
            logger.error(
                "There was a problem getting the properties from the organization",
                extra={"error": str(exception)},
            )
            raise InvalidUsage.server_error()

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_organization_property_ids_by_organization_id(
        organization_id: int,
    ) -> list[int]:
        with OrganizationServiceClient() as client:
            try:
                organization = client.get_organization_by_id(organization_id)
                organization = organization.get("organization")

                property_ids = (
                    [
                        int(property.get("id"))
                        for property in organization.get("properties")
                    ]
                    if organization
                    else []
                )

                return property_ids

            except AttributeError as exception:
                logger.error(
                    f"Property IDs not found for the organization {organization_id}",
                    extra={"error": str(exception), "service_response": organization},
                )
                raise InvalidUsage.server_error()

            except Exception as exception:
                logger.error(
                    "There was a problem getting the properties from the organization",
                    extra={"error": str(exception), "organization_id": organization_id},
                )
                raise InvalidUsage.server_error()

    def get_organization_property_ids(organization_id, property_id):
        property_ids = (
            OrganizationService.get_organization_property_ids_by_organization_id(
                organization_id
            )
        )
        if not property_ids:
            logger.warning(
                f"Organization with id {organization_id} not found for "
                f"property {property_id}, refreshing cache and retrying",
                extra={
                    "organization_id": organization_id,
                    "property_id": property_id,
                },
            )
            cache.delete_memoized(
                PropertyService.get_property,
                property_id,
            )
            cache.delete_memoized(
                OrganizationService.get_organization_property_ids_by_organization_id,
                organization_id,
            )
            organization_id = OrganizationService.get_organization_id_by_property_id(
                property_id
            )
            property_ids = (
                OrganizationService.get_organization_property_ids_by_organization_id(
                    organization_id
                )
            )
        return property_ids
