from sqlalchemy import select
from sqlalchemy.orm import Session

from app.common.cache import TIMEOUT_HOUR, cache
from app.common.database import db
from app.common.logger import log_query


class MappingService:
    @staticmethod
    @cache.memoize(timeout=TIMEOUT_HOUR)
    def get_mappings(model, mapping_name: str, property_id: int, organization_id: int):

        match (mapping_name):
            case "reservation_source":
                mappings_statement = (
                    select(
                        model.reservation_source_category,
                        model.reservation_source,
                    )
                    .where(
                        model.organization_id == organization_id,
                        model.property_id == property_id,
                        model.reservation_source.isnot(None),
                    )
                    .group_by(
                        model.reservation_source_category,
                        model.reservation_source,
                    )
                )
        log_query(mappings_statement)
        with Session(db.get_engine(bind="dataset_views")) as session:
            mappings_results = session.execute(mappings_statement).fetchall()

        map = {}
        for mapping in mappings_results:
            if mapping.reservation_source_category not in map:
                map[mapping.reservation_source_category] = [mapping.reservation_source]
            else:
                map[mapping.reservation_source_category].append(
                    mapping.reservation_source
                )
        result = {
            "mappings": [
                {
                    "key": key,
                    "values": values,
                }
                for key, values in map.items()
            ],
        }

        return result
