from datetime import date

from sqlalchemy.orm import Session

from app.api.v1_1.schemas.log import ClassicReportLogSchema
from app.classic_reports.production_reports.filter_values import FilterValues
from app.classic_reports.production_reports.revpar.revpar import RevPar
from app.common.cache import TIMEOUT_HOUR, cache
from app.common.database import db
from app.common.logger import log_query, logger


class RevParService:
    @staticmethod
    def get_revpar_data(
        organization_id: int,
        property_id: int,
        report_year: int,
        period_start: date,
        period_end: date,
        grouping: str,
        compare_years: list[int] = None,
        room_types: list[str] = None,
        reservation_sources: list[str] = None,
        reservation_source_categories: list[str] = None,
    ) -> list[dict]:
        """
        Get RevPar data for the given property and period.

        :param property_id: Property ID
        :param year: Year of the report
        :param period_start: Start of the period
        :param period_end: End of the period
        :param compare_years: Comparison years
        :param room_types: Room types to include in the report
        :param reservation_sources: Reservation sources to include in the report
        :return: List of RevPar data
        """

        revpar_statement = RevPar(
            organization_id=organization_id,
            property_id=property_id,
            report_year=report_year,
            period_start=period_start,
            period_end=period_end,
            grouping=grouping,
            compare_years=compare_years,
            room_types=room_types,
            reservation_sources=reservation_sources,
            reservation_source_categories=reservation_source_categories,
        ).get_revpar_report()

        # Compile and log SQL
        log_query(revpar_statement)

        # Get the data
        with Session(db.get_engine(bind="dataset_views")) as session:
            revpar_result = session.execute(revpar_statement).fetchall()
            response = {
                "year": report_year,
                "results": revpar_result or [],
            }

        if compare_years is not None:
            response["compare_years"] = compare_years

        logger.info(
            "Get Classic Report Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="room_revenue",
                    title="Daily Financial Report | Room Revenue",
                    property_id=property_id,
                    organization_id=organization_id,
                ),
            ),
        )

        return response

    @staticmethod
    @cache.memoize(timeout=TIMEOUT_HOUR)
    def get_revpar_filter_values(
        property_id: int, organization_id: int, filter_name: str
    ):

        filter_values = FilterValues(organization_id, property_id)
        match filter_name:
            case "room_type":
                filter_values_statement = filter_values.get_room_type_filter_values()
            case "reservation_source":
                filter_values_statement = (
                    filter_values.get_reservation_source_filter_values()
                )
            case "reservation_source_category" | _:
                filter_values_statement = (
                    filter_values.get_reservation_source_category_filter_values()
                )
        log_query(filter_values_statement)

        with Session(db.get_engine(bind="dataset_views")) as session:
            results = session.execute(filter_values_statement).fetchall()

        logger.info(
            "Get Filter Values Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="filter_values",
                    title="Payment Ledger Filter Values",
                    property_id=property_id,
                    organization_id=organization_id,
                ),
            ),
        )

        return {"filter_values": [result[0] for result in results]}
