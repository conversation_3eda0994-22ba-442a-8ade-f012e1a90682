from datetime import date

from sqlalchemy.orm import Session

from app.api.v1_1.schemas.log import ClassicReportLogSchema
from app.classic_reports.financial_reports.daily_financial_report.daily_statistics import (
    DailyFinancialStatistics,
)
from app.classic_reports.financial_reports.daily_financial_report.occupancy import (
    DailyFinancialOccupancy,
)
from app.common.database import db
from app.common.logger import log_query, logger


class DailyFinancialReportService:
    @staticmethod
    def get_statistics_report(
        property_id: int, organization_id: int, report_date: date
    ) -> dict:
        daily_financial_report = DailyFinancialStatistics(
            organization_id, property_id, report_date
        )
        today_query = daily_financial_report.get_today_query()
        month_to_date_query = daily_financial_report.get_month_to_date_query()

        # Compile and log SQL
        log_query(today_query)
        log_query(month_to_date_query)

        # Execute the query using the 'aurora' bind engine from the pool
        with db.get_engine(bind="dataset_views").connect() as connection:
            today_result = connection.execute(today_query).fetchone()
            month_to_date_result = connection.execute(month_to_date_query).fetchone()

        response = {
            "date": report_date,
            "today": today_result or {},
            "month_to_date": month_to_date_result or {},
        }

        logger.info(
            "Get Classic Report Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="daily_statistics",
                    title="Daily Financial Report | Daily Statistics",
                    property_id=property_id,
                    organization_id=organization_id,
                ),
            ),
        )
        return response

    @staticmethod
    def get_occupancy_rate(
        property_id: int, organization_id: int, report_date: date
    ) -> dict:

        daily_financial_occupancy_rate = DailyFinancialOccupancy(
            organization_id, property_id, report_date
        )
        occupancy_rate_today_statement = (
            daily_financial_occupancy_rate.get_occupancy_rate_today()
        )
        occupancy_rate_month_to_date_statement = (
            daily_financial_occupancy_rate.get_occupancy_rate_month_to_date()
        )

        log_query(occupancy_rate_today_statement)
        log_query(occupancy_rate_month_to_date_statement)

        # Get the data
        with Session(db.get_engine(bind="dataset_views")) as session:
            occupancy_rate_today_result = session.execute(
                occupancy_rate_today_statement
            ).fetchone()
            occupancy_rate_month_to_date_result = session.execute(
                occupancy_rate_month_to_date_statement
            ).fetchone()
        response = {
            "date": report_date,
            "today": occupancy_rate_today_result or {},
            "month_to_date": occupancy_rate_month_to_date_result or {},
        }

        logger.info(
            "Get Classic Report Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="occupancy_rate",
                    title="Daily Financial Report | Occupancy Rate",
                    property_id=property_id,
                    organization_id=organization_id,
                ),
            ),
        )

        return response

    @staticmethod
    def get_room_revenue(
        property_id: int, organization_id: int, report_date: date
    ) -> dict:

        daily_financial_occupancy = DailyFinancialOccupancy(
            organization_id, property_id, report_date
        )
        room_revenue_today_statement = (
            daily_financial_occupancy.get_room_revenue_today()
        )
        room_revenue_month_to_date_statement = (
            daily_financial_occupancy.get_room_revenue_month_to_date()
        )

        # Compile and log SQL
        log_query(room_revenue_today_statement)
        log_query(room_revenue_month_to_date_statement)

        # Get the data
        with Session(db.get_engine(bind="dataset_views")) as session:
            room_revenue_today_result = session.execute(
                room_revenue_today_statement
            ).fetchone()
            room_revenue_month_to_date_result = session.execute(
                room_revenue_month_to_date_statement
            ).fetchone()
        response = {
            "date": report_date,
            "today": room_revenue_today_result or {},
            "month_to_date": room_revenue_month_to_date_result or {},
        }

        logger.info(
            "Get Classic Report Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="room_revenue",
                    title="Daily Financial Report | Room Revenue",
                    property_id=property_id,
                    organization_id=organization_id,
                ),
            ),
        )

        return response

    @staticmethod
    def get_room_revenue_14_days_forecast(
        property_id: int, organization_id: int, report_date: date
    ) -> dict:

        daily_financial_forecast = DailyFinancialOccupancy(
            organization_id, property_id, report_date
        )
        forecast_statement = (
            daily_financial_forecast.get_room_revenue_14_days_forecast()
        )

        log_query(forecast_statement)

        # Get the data
        with Session(db.get_engine(bind="dataset_views")) as session:
            forecast_result = session.execute(forecast_statement).fetchall()

        response = {
            "date": report_date,
            "results": forecast_result,
        }

        logger.info(
            "Get Classic Report Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="room_revenue_14_days_forecast",
                    title="Daily Financial Report | Room Revenue 14 days Forecast",
                    property_id=property_id,
                    organization_id=organization_id,
                ),
            ),
        )
        return response

    @staticmethod
    def get_on_the_books_forecast(
        property_id: int, organization_id: int, report_date: date
    ) -> dict:

        daily_financial_occupancy = DailyFinancialOccupancy(
            organization_id, property_id, report_date
        )

        room_revenue_month_to_date_statement = (
            daily_financial_occupancy.get_on_the_books_forecast()
        )

        # Compile and log SQL
        log_query(room_revenue_month_to_date_statement)

        # Get the data
        with Session(db.get_engine(bind="dataset_views")) as session:
            room_revenue_month_to_date_result = session.execute(
                room_revenue_month_to_date_statement
            ).fetchone()

        response = {
            "date": report_date,
            "result": room_revenue_month_to_date_result or {},
        }

        logger.info(
            "Get Classic Report Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="room_revenue",
                    title="Daily Financial Report | Room Revenue",
                    property_id=property_id,
                    organization_id=organization_id,
                ),
            ),
        )

        return response
