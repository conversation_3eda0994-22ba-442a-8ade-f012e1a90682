from sqlalchemy.orm import Session

from app.api.v1_1.schemas.log import ClassicReportLogSchema
from app.common.cache import TIMEOUT_HOUR, cache
from app.common.database import db
from app.common.logger import log_query, logger
from app.reports.picklist_values import PicklistValues


class PicklistValueService:
    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_picklist_values_by_dataset_id_and_cdfs(
        dataset_id: int,
        cdfs: list[dict],
        organization_id: int,
        property_ids: list[int],
        filters: dict = None,
        custom_cdfs: list = None,
    ):
        multi_levels = []
        [multi_levels.append(cdf) for cdf in cdfs if cdf["cdf"].get("multi_level_id")]
        picklist_values = PicklistValues(
            organization_id, property_ids, filters, custom_cdfs, multi_levels
        )
        stmt = picklist_values.get_picklist_values_by_dataset_id_and_cdfs(
            dataset_id,
            cdfs,
        )
        log_query(stmt)

        with Session(db.get_engine(bind="dataset_views")) as session:
            results = session.execute(stmt).fetchall()

        logger.info(
            "Get Picklist Values Data",
            extra=ClassicReportLogSchema().dump(
                dict(
                    id="picklist_values",
                    title="Picklist Values",
                    property_ids=property_ids,
                    organization_id=organization_id,
                ),
            ),
        )
        return dict(index=results, headers=[cdf["cdf"]["column"] for cdf in cdfs])
