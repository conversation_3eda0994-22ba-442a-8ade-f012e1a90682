from flask import current_app as app

import requests

from app.common.constants.limits import REQUESTS_TIMEOUT_SECONDS
from app.common.logger import logger


class LinkShortenerService:
    @staticmethod
    def shorten_link(url):
        headers = {"Authorization": f"Basic {app.config['GX_API_KEY']}"}

        try:
            response = requests.post(
                url=app.config["GX_LINK_SHORTENER_URL"],
                headers=headers,
                json=dict(url=url),
                timeout=REQUESTS_TIMEOUT_SECONDS,
            )

            match response.status_code:
                case 200:
                    return response.json()["results"]["url"]
                case _:
                    logger.error(
                        "Link shortener returned invalid status code",
                        extra={"error": str(response)},
                    )
                    raise Exception("Link shortener returned invalid status code")

        except Exception as exception:
            logger.error(
                "There was a problem shortening the link",
                extra={"error": str(exception)},
            )
            return url
