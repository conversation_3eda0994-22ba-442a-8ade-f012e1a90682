from flask import g

from app.api.v1_1.schemas.property_setting import (
    PropertySettingOptionsSchema,
    PropertySettingsSchema,
)
from app.common.cache import TIMEOUT_DAY, cache
from app.common.constants.property_setting import (
    DEFAULT_ROUNDING_PRECISION,
    property_setting_options,
    property_setting_options_type,
    property_setting_title,
)
from app.enums.property_setting import (
    PropertySetting as PropertySettingEnum,
    WeekDays,
)
from app.models.property_setting import PropertySetting


class PropertySettingService:
    @property
    def default_settings(self):
        # this determines the order of the property's settings
        return {
            PropertySettingEnum.START_OF_WEEK.value: PropertySettingsSchema().dump(
                {
                    "name": PropertySettingEnum.START_OF_WEEK.value,
                    "value": WeekDays.Monday.name,
                }
            ),
            PropertySettingEnum.CURRENCY_CDF_ROUNDING_PRECISION.value: PropertySettingsSchema().dump(
                {
                    "name": PropertySettingEnum.CURRENCY_CDF_ROUNDING_PRECISION.value,
                    "value": DEFAULT_ROUNDING_PRECISION,
                }
            ),
        }

    @staticmethod
    @cache.memoize(timeout=TIMEOUT_DAY)
    def get_setting_by_name(property_id: int, name: str) -> PropertySetting:
        return PropertySetting.get_by_name_and_property_id(property_id, name)

    @staticmethod
    @cache.memoize(timeout=TIMEOUT_DAY)
    def get_all_by_property_id(property_id: int) -> list[PropertySetting]:
        """
        Get all the settings by property id. If setting is not set will return default setting value for that setting
        """
        property_settings = PropertySetting.get_all_by_property_id(property_id)
        default_settings_values = PropertySettingService().default_settings.values()

        combined_settings = [
            dict(
                name=default_setting["name"],
                value=(
                    default_setting["value"]
                    if default_setting["name"]
                    not in [
                        property_setting.name for property_setting in property_settings
                    ]
                    else [
                        property_setting.value
                        for property_setting in property_settings
                        if property_setting.name == default_setting["name"]
                    ][0]
                ),
            )
            for default_setting in default_settings_values
        ]
        return combined_settings

    @staticmethod
    def update_by_name_and_property_id(
        property_id: int, value: str, name: str
    ) -> PropertySetting:
        """
        Update Property setting by name and property id. In case the property is not created on the DB, this method will create it
        because there are always settings by default
        """
        setting = PropertySetting.get_by_name_and_property_id(property_id, name)

        if not setting:
            return PropertySetting.create(
                name=name, value=value, property_id=property_id, user_id=g.user.id
            )

        setting = setting.update(value=value)
        PropertySettingService.clear_cache(property_id)

        return setting

    @staticmethod
    def get_setting_options_by_name(name: str) -> dict:
        return PropertySettingOptionsSchema().dump(
            {
                "title": property_setting_title[name],
                "type": property_setting_options_type[name],
                "options": property_setting_options[name],
            }
        )

    @staticmethod
    def clear_cache(property_id: int | None):
        """Method that will clear the cache by property_id and user_id if pass"""
        cache.delete_memoized(
            PropertySettingService.get_all_by_property_id, property_id
        )

    @staticmethod
    def get_currency_format(property_id: int) -> str:
        property_setting = PropertySettingService.get_setting_by_name(
            property_id, PropertySettingEnum.CURRENCY_CDF_ROUNDING_PRECISION.value
        )

        currency_rounding_precision = (
            int(property_setting.value)
            if property_setting
            else DEFAULT_ROUNDING_PRECISION
        )

        return f"FM{'999,'*13}990{'.' + '0' * currency_rounding_precision}"
