from typing import List

from app.api.v1_1.schemas.favorite import FavoriteReportTitleSchema, FavoriteSchema
from app.api.v1_1.schemas.log import FavoriteLogSchema
from app.common.cache import TIMEOUT_DAY, cache
from app.common.constants.favorite_limits import MAX_FAVORITES
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.user import User
from app.enums.favorite import FavoriteKind
from app.enums.log_actions import FavoriteAction
from app.enums.report import ReportKind
from app.models.favorite import Favorite
from app.services.hub_service import HubService
from app.services.report_service import ReportService
from app.services.stock_report_service import StockReportService


class FavoriteService:
    @staticmethod
    def map_schema_to_model(favorite: FavoriteSchema) -> dict:
        return dict(
            rank=favorite["rank"],
            kind=favorite["kind"],
            report_id=favorite["entity_id"]
            if favorite["kind"] == FavoriteKind.Report.value
            else None,
            stock_report_id=favorite["entity_id"]
            if favorite["kind"] == FavoriteKind.StockReport.value
            else None,
            hub_id=favorite["entity_id"]
            if favorite["kind"] == FavoriteKind.Hub.value
            else None,
        )

    @staticmethod
    def order_rank_by_property_id_and_user_id(property_id: int, user_id: int) -> None:
        """
        This function will receive the property id and the user id where the favorites needs to be ordered.
        It will get all the favorites stored at this moment order by rank and updated at. It will order
        based on rank performing a hash map from the first element to the last element. If founds
        that there is one rank with the MAX_FAVORITES value already in the hash map, it means that the max
        possible rank was already achieved, so it will look the position that is empty to set the last element.
        This will allow that the element that was created remains on the position
        """
        favorites = Favorite.get_all_by_property_id_and_user_id(property_id, user_id)

        favorites_order_by_rank_map = dict()

        for favorite in favorites:
            if favorite.rank in favorites_order_by_rank_map:
                # Check if the rank is the MAX_FAVORITES to accomodate it in the missing rank of the hash map
                if favorite.rank == MAX_FAVORITES:
                    favorites_with_ranks = list(favorites_order_by_rank_map.keys())
                    empty_position = next(
                        (
                            empty_rank
                            for empty_rank in list(range(1, MAX_FAVORITES + 1))
                            if empty_rank not in favorites_with_ranks
                        ),
                        None,
                    )
                    favorites_order_by_rank_map[empty_position] = dict(
                        id=favorite.id, rank=empty_position
                    )
                else:
                    favorites_order_by_rank_map[favorite.rank + 1] = dict(
                        id=favorite.id, rank=favorite.rank + 1
                    )
            else:
                favorites_order_by_rank_map[favorite.rank] = dict(
                    id=favorite.id, rank=favorite.rank
                )

        Favorite.bulk_update_mappings(
            Favorite,
            favorites_order_by_rank_map.values()
            if favorites_order_by_rank_map
            else favorites_order_by_rank_map.values(),
        )
        FavoriteService.clear_cache(property_id, user_id)

    @staticmethod
    @cache.memoize(timeout=TIMEOUT_DAY)
    def get_all_with_report_title_by_property_id_and_user_id(
        property_id: int, user_id: int
    ) -> List[FavoriteReportTitleSchema]:
        favorites = Favorite.get_all_with_report_title_by_property_id_and_user_id(
            property_id, user_id
        )

        favorites = [
            dict(
                id=favorite.id,
                rank=favorite.rank,
                kind=favorite.kind,
                report_kind=favorite.kind,
                entity_id=favorite.report_id
                if favorite.kind == ReportKind.Report.value
                else favorite.stock_report_id
                if favorite.kind == ReportKind.StockReport.value
                else favorite.hub_id,
                report_id=favorite.report_id
                if favorite.kind == ReportKind.Report.value
                else favorite.stock_report_id
                if favorite.kind == ReportKind.StockReport.value
                else favorite.hub_id,
                title=favorite.report_title
                if favorite.kind == ReportKind.Report.value
                else favorite.stock_report_title
                if favorite.kind == ReportKind.StockReport.value
                else favorite.hub_title,
                report_title=favorite.report_title
                if favorite.kind == ReportKind.Report.value
                else favorite.stock_report_title
                if favorite.kind == ReportKind.StockReport.value
                else favorite.hub_title,
            )
            for favorite in favorites
        ]

        return dict(favorites=favorites, total=len(favorites))

    @staticmethod
    @cache.memoize(timeout=TIMEOUT_DAY)
    def get_by_id_property_id_and_user_id(
        favorite_id: int, property_id: int, user_id: int
    ) -> Favorite:
        favorite = Favorite.get_by_id_property_id_and_user_id(
            favorite_id, property_id, user_id
        )
        return favorite

    @staticmethod
    @cache.memoize(timeout=TIMEOUT_DAY)
    def get_all_by_property_id_and_user_id(
        property_id: int, user_id: int
    ) -> List[Favorite]:
        favorite_data = Favorite.get_all_by_property_id_and_user_id(
            property_id, user_id
        )
        favorites = dict(favorites=favorite_data, total=len(favorite_data))
        return favorites

    @staticmethod
    def create(favorite: FavoriteSchema, property_id: int, user: User) -> Favorite:
        if Favorite.is_max_favorited(property_id, user.id):
            InvalidUsage.conflict(
                message=f"There are already {MAX_FAVORITES} favorited reports"
            )

        # Check if Entity exists based on Kind
        match (favorite["kind"]):
            case FavoriteKind.Report.value:
                ReportService.get_by_id_and_property_id(
                    favorite["entity_id"], property_id
                )
            case FavoriteKind.StockReport.value:
                StockReportService.get_by_id(favorite["entity_id"], user, property_id)
            case FavoriteKind.Hub.value:
                HubService.get_hub_by_id(favorite["entity_id"], property_id)

        if Favorite.is_entity_favorited(
            favorite["entity_id"], favorite["kind"], property_id, user.id
        ):
            InvalidUsage.conflict(
                message=f"This Entity with id: {favorite['entity_id']} and kind: {favorite['kind']} is already favorited"
            )

        favorite = Favorite.create(
            **FavoriteService.map_schema_to_model(favorite),
            user_id=user.id,
            property_id=property_id,
        )

        # Order the rank of the favorites for the property id
        FavoriteService.order_rank_by_property_id_and_user_id(property_id, user.id)

        # Log for Service Insights
        favorite_log_schema = FavoriteLogSchema().dump(
            {
                **FavoriteSchema(exclude=("id", "rank")).dump(favorite),
                **dict(
                    action=FavoriteAction.Favorite.value,
                    property_id=property_id,
                    user_id=user.id,
                ),
            }
        )

        logger.info("New Favorite", extra=favorite_log_schema)
        FavoriteService.clear_cache(property_id, user.id)
        return favorite

    @staticmethod
    def delete_by_id_property_id_and_user_id(
        favorite_id: int, property_id: int, user_id: int
    ) -> None:
        favorite = Favorite.get_by_id_property_id_and_user_id(
            favorite_id, property_id, user_id
        )

        # Log for Service Insights
        favorite_log_schema = FavoriteLogSchema().dump(
            {
                **FavoriteSchema(exclude=("id", "rank")).dump(favorite),
                **dict(
                    action=FavoriteAction.Unfavorite.value,
                    property_id=property_id,
                    user_id=user_id,
                ),
            }
        )
        favorite.delete()
        logger.info(f"Favorite {favorite_id} deleted", extra=favorite_log_schema)
        FavoriteService.clear_cache(property_id, user_id)

    @staticmethod
    def update_rank_by_id_property_id_and_user_id(
        favorite_id: int, new_rank: int, property_id: int, user_id: int
    ) -> Favorite:
        updated_favorite = Favorite.get_by_id_property_id_and_user_id(
            favorite_id, property_id, user_id
        )

        # Return the Favorites that wants to be updated if the new rank is the same to the one that already has
        if updated_favorite.rank == new_rank:
            FavoriteService.clear_cache(property_id, user_id)
            return updated_favorite

        current_favorite = Favorite.get_by_rank_property_id_and_user_id(
            new_rank, property_id, user_id
        )

        # Check if there is a favorite in the desired rank and swap them
        if current_favorite:
            Favorite.bulk_update_mappings(
                Favorite,
                [
                    dict(id=updated_favorite.id, rank=new_rank),
                    dict(id=current_favorite.id, rank=updated_favorite.rank),
                ],
            )
            updated_favorite.rank = new_rank
            FavoriteService.clear_cache(property_id, user_id)
            return updated_favorite

        updated_favorite.rank = new_rank
        updated_favorite.update()
        FavoriteService.clear_cache(property_id, user_id)
        return updated_favorite

    @staticmethod
    def clear_cache(property_id: int | None, user_id: int | None):
        """Method that will clear the cache by property_id and user_id if pass"""
        cache.delete_memoized(
            FavoriteService.get_all_by_property_id_and_user_id, property_id, user_id
        )
        cache.delete_memoized(
            FavoriteService.get_all_with_report_title_by_property_id_and_user_id,
            property_id,
            user_id,
        )
        # Will clear everything that includes property_id and user_id, not neccesary to include favorite_id
        cache.delete_memoized(
            FavoriteService.get_by_id_property_id_and_user_id,
            property_id=property_id,
            user_id=user_id,
        )

    @staticmethod
    def get_all_by_kind_and_entity_id(kind: str, entity_id: int) -> list[Favorite]:
        match (kind):
            case FavoriteKind.Report.value:
                filter = Favorite.report_id == entity_id
            case FavoriteKind.Hub.value:
                filter = Favorite.hub_id == entity_id
            case FavoriteKind.StockReport.value:
                filter = Favorite.stock_report_id == entity_id
            case _:
                logger.error(f"Invalid Favorite Kind {kind}")
                raise ValueError(f"Invalid kind {kind}")
        favorites = Favorite.query.filter(filter).all()
        return favorites

    @staticmethod
    def clear_user_caches_for_favorites(favorites: list[Favorite]):
        for favorite in favorites:
            FavoriteService.clear_cache(favorite.property_id, favorite.user_id)
