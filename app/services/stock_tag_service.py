from flask import g

from sqlalchemy import text

from app.common.database import db
from app.models.stock_tag import StockTag


class StockTagService:
    @staticmethod
    def get_all():
        return StockTag.get_all()

    @staticmethod
    def create(stock_tag):
        new_stock_tag = StockTag(name=stock_tag["name"], user_id=g.user.id)

        new_stock_tag.save()
        return new_stock_tag

    @staticmethod
    def get_by_id_or_404(id):
        return StockTag.get_by_id_or_404(id)

    @staticmethod
    def update(id, update_stock_tag):
        stock_tag = StockTag.get_by_id_or_404(id)
        stock_tag.update(**update_stock_tag)
        return stock_tag

    @staticmethod
    def delete(id):
        stock_tag = StockTag.get_by_id_or_404(id)
        stock_tag.delete()
        return stock_tag

    @staticmethod
    def upsert_stock_tag(stock_tag_json):
        existing_stock_tag = StockTag.query.filter(
            StockTag.id == stock_tag_json["id"]
        ).first()
        if existing_stock_tag:
            stock_tag = StockTagService.update(stock_tag_json["id"], stock_tag_json)
        else:
            stock_tag = StockTag.create(**stock_tag_json)
            # always reset id sequence after inserting with id
            db.session.execute(
                text(
                    "SELECT SETVAL('stock_tag_id_seq', (SELECT MAX(id) FROM stock_tag))"
                )
            )
        return stock_tag
