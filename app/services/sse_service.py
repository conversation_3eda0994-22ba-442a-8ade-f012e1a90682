import json
from typing import Optional

from flask import current_app as app, g

import requests

from app.common.constants.limits import REQUESTS_TIMEOUT_SECONDS
from app.common.enums.features import LaunchDarklyFeature
from app.common.logger import logger
from app.schemas.sse import SSEPublishSchema
from app.services.launch_darkly_service import LaunchDarklyService


class SSEService:
    @staticmethod
    def publish(
        kind: str,
        name: str,
        task_id: str,
        status: str,
        result: Optional[dict] = None,
    ):
        if (
            g.api_key is None
            and task_id
            and LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.PublishSSE, g.property_id
            )
        ):
            message = SSEPublishSchema().load(
                dict(
                    kind=kind,
                    name=name,
                    task_id=task_id,
                    status=status,
                    result=result,
                )
            )
            # not publishing if the user is api key user / scheduler
            channel = f"di-stream-user-{g.user.id}"
            # check a feature flag for property id
            api_url = app.config["INSIGHTS_STREAMING_API_URL"]

            message_payload = {
                "query": """
                    mutation Publish($data: AWSJSON!, $name: String!) {
                        publish(data: $data, name: $name) {
                            data
                            name
                        }
                    },
                """,
                "variables": {"name": channel, "data": json.dumps(message)},
            }

            headers = {
                "Authorization": g.access_token,
                "Content-Type": "application/json",
            }

            # Make the HTTP POST request to publish the message
            try:
                response = requests.post(
                    api_url,
                    headers=headers,
                    json=message_payload,
                    timeout=REQUESTS_TIMEOUT_SECONDS,
                )
                match (response.status_code):
                    case 200:
                        logger.debug("Message published successfully")
                    case _:
                        logger.error(
                            "Failed to publish message",
                            extra={
                                "response": response.json(),
                                "status_code": response.status_code,
                            },
                        )
            except Exception as exception:
                logger.error("SSE Service had Error:", extra={"error": str(exception)})
