from app.common.cache import TIMEOUT_MINUTE, cache
from app.common.enums.features import BillingPortalFeatures
from app.common.logger import logger
from app.services.grpc import PropertyServiceClient


class PropertyFeatureService:
    @staticmethod
    @cache.memoize(TIMEOUT_MINUTE)
    def get_property_features(
        property_id: int,
        enabled: bool = True,
        select_features: list[str] = [
            feature.value for feature in BillingPortalFeatures
        ],
    ) -> list[dict]:
        """Get property features by property id and enabled status and return the
        selected features.

        Args:
            property_id (int): Property ID
            enabled (bool, optional): Enabled status. Defaults to True.
            select_features (list[str], optional): Selected features, otherwise
                return all of the features. Defaults to [].
        """
        with PropertyServiceClient() as client:
            response = client.get_property_features_by_id(property_id, enabled)
            features = [
                feature
                for feature in response.get("property_features", [])
                if not select_features or feature["name"] in select_features
            ]

            logger.debug(
                f"Property Features for property {property_id} with enabled status {enabled}",
                extra={
                    "features": features,
                },
            )

            return features

    @staticmethod
    def get_all_by_property_id(property_id: int) -> list[str]:
        """Get a list of enabled property features' names by property id.

        Args:
            property_id (int): Property ID
        """
        features = [
            feature["name"]
            for feature in PropertyFeatureService.get_property_features(
                property_id,
                enabled=True,
            )
        ]

        logger.debug(
            f"Property Features for property {property_id}",
            extra={
                "features": features,
            },
        )

        return features

    @staticmethod
    @cache.memoize(TIMEOUT_MINUTE)
    def has_feature_flag(feature_flag: str, property_id: int) -> bool:
        """Check if a property has a feature flag enabled.

        Args:
            feature_flag (str): Feature flag
            property_id (int): Property ID
        """
        with PropertyServiceClient() as client:
            feature = client.get_property_feature_by_name(feature_flag, property_id)

            logger.debug(
                f"Checking {feature_flag} feature flag for property: {property_id}",
                extra={"feature": feature},
            )

            feature = (
                feature.get("property_feature", dict()).get("enabled", False)
                if feature
                else False
            )
            return feature
