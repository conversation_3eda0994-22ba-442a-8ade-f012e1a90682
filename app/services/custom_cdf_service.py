from typing import List

from marshmallow import Schema

from app.common.database import db
from app.common.exceptions import InvalidUsage
from app.enums.report import ReportKind
from app.models.report import ReportCustomCdf
from app.models.stock_report import StockReportCustomCdf

MODEL = {
    ReportKind.Report.value: ReportCustomCdf,
    ReportKind.StockReport.value: StockReportCustomCdf,
}


class CustomCdfService:
    @staticmethod
    def get_by_id(report_kind: ReportKind, id: int) -> dict:
        return MODEL[report_kind.value].query.get_or_404(id)

    @staticmethod
    def get_by_report_id(report_kind: ReportKind, report_id):
        return (
            MODEL[report_kind.value]
            .query.filter(MODEL[report_kind.value].report_id == report_id)
            .all()
        )

    @staticmethod
    def get_by_id_and_report_id(report_kind: ReportKind, id, report_id):
        return (
            MODEL[report_kind.value]
            .query.filter(
                MODEL[report_kind.value].id == id,
                MODEL[report_kind.value].report_id == report_id,
            )
            .first()
        )

    @staticmethod
    def get_by_report_id_and_column(
        report_kind: ReportKind, report_id: int, column: str
    ):
        return (
            MODEL[report_kind.value]
            .query.filter(
                MODEL[report_kind.value].report_id == report_id,
                MODEL[report_kind.value].column == column,
            )
            .first()
        )

    @staticmethod
    def is_custom_cdf_in_report(
        report_kind: ReportKind, report_id: int, column: str
    ) -> bool:
        return bool(
            CustomCdfService.get_by_report_id_and_column(report_kind, report_id, column)
        )

    @staticmethod
    def create(
        report_kind: ReportKind,
        report_schema: Schema,
        custom_cdfs: List[dict],
        report_id: int,
        user_id: int,
    ) -> None:
        """Method that based on the Kind will create all the custom cdfs in the corresponding model"""
        create_custom_cdfs = [
            MODEL[report_kind.value](
                **report_schema(exclude=("id", "created_at", "updated_at")).dump(
                    custom_cdf
                ),
                report_id=report_id,
                user_id=user_id
            )
            for custom_cdf in custom_cdfs
        ]

        try:
            db.session.bulk_save_objects(create_custom_cdfs)
            db.session.commit()
        except:
            raise InvalidUsage.server_error()

    @staticmethod
    def update(
        report_kind: ReportKind,
        report_schema: Schema,
        custom_cdfs: List[dict],
        report_id: int,
        user_id: int,
    ) -> None:
        """Method that will update all the custom cdfs by id"""
        update_custom_cdfs = [
            MODEL[report_kind.value](
                **report_schema(exclude=("created_at", "updated_at")).dump(custom_cdf),
                report_id=report_id,
                user_id=user_id
            )
            for custom_cdf in custom_cdfs
        ]
        for update_custom_cdf in update_custom_cdfs:
            custom_cdf = (
                MODEL[report_kind.value]
                .query.filter(MODEL[report_kind.value].id == update_custom_cdf.id)
                .first()
            )
            custom_cdf.update(
                dict(
                    column=custom_cdf.column,
                    name=custom_cdf.name,
                    description=custom_cdf.description,
                    formula=custom_cdf.formula,
                    kind=custom_cdf.kind,
                )
            )

    @staticmethod
    def delete_by_report_kind_and_id(report_kind: ReportKind, report_id: int):
        MODEL[report_kind.value].query.filter(
            MODEL[report_kind.value].report_id == report_id
        ).delete()
        db.session.commit()
