from datetime import timezone
from typing import List
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

from flask import g

from app.common.cache import TIMEOUT_DAY, TIMEOUT_HOUR, TIMEOUT_WEEK, cache
from app.common.enums.property_islands import PropertyIslands
from app.common.enums.property_types import PropertyTypes
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.enums import PropertyStatus
from app.services.grpc import PropertyProfileServiceClient, PropertyServiceClient


class PropertyService:
    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_property(id: str | int) -> dict:
        with PropertyServiceClient() as client:
            property = client.get_property(id).get("property")
            return property

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_property_profile(id: str | int) -> dict:
        with PropertyProfileServiceClient() as client:
            property_profile = client.get_property_profile(id).get("property_profile")
            return property_profile

    @staticmethod
    def get_property_country_code(id: str | int) -> List[dict]:
        try:
            country_code = (
                PropertyService.get_property_profile(id)
                .get("hotel_address")
                .get("country_code")
            )
        except Exception as exception:
            logger.warning(
                "Could not get country code from organization service",
                extra=dict(error=exception),
            )
            country_code = None

        return country_code

    @staticmethod
    def get_property_sub_domain(id: str | int) -> str:
        """
        Get the property subdomain from the organization service. Return 'hotels' as default,
        meaning that the property does not belong to an organization.

        Parameters:
        @id: str | int: The property id

        Returns:
        str: The property subdomain, default 'hotels'
        """
        DEFAULT_SUBDOMAIN = "hotels"
        try:
            sub_domain = (
                PropertyService.get_property(id)
                .get("organization")
                .get("sub_domain", DEFAULT_SUBDOMAIN)
            )
        except Exception as exception:
            logger.warning(
                f"Could not get subdomain from organization service, "
                f"using default {DEFAULT_SUBDOMAIN}",
                extra=dict(error=exception),
            )
            sub_domain = DEFAULT_SUBDOMAIN

        return sub_domain

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_properties_by_ids(property_ids: List[int]) -> List[dict]:
        with PropertyServiceClient() as client:
            properties = client.get_properties_by_ids(property_ids).get("properties")
            return [
                dict(
                    id=property.get("id"),
                    name=PropertyService.get_property_profile(property.get("id")).get(
                        "hotel_name"
                    ),
                    timezone=PropertyService.get_timezone(property.get("timezone")),
                    country_code=PropertyService.get_property_country_code(
                        property.get("id")
                    ),
                    currency=property.get("currency"),
                )
                for property in properties
            ]

    @staticmethod
    def property_deactivated(property_id: int) -> bool:
        """
        Check if the property has a 'deactivated' status
        """
        try:
            property_status = PropertyService.get_property_profile(property_id).get(
                "status"
            )
        except Exception as exception:
            logger.warning(
                "Could not get property status from organization service",
                extra=dict(error=exception),
            )
            property_status = PropertyStatus.STATUS_UNSPECIFIED

        return property_status in (
            PropertyStatus.STATUS_CANCELED.name,
            PropertyStatus.STATUS_SUSPENDED.name,
            PropertyStatus.STATUS_EXPIRED.name,
        )

    @staticmethod
    def get_timezone_by_id(property_id: int | None) -> timezone:
        if not property_id:
            return ZoneInfo("UTC")

        property = PropertyService.get_property(property_id)
        return PropertyService.get_timezone(property.get("timezone"))

    @staticmethod
    @cache.memoize(TIMEOUT_DAY)
    def get_timezone(timezone_key: str) -> timezone:
        try:
            return ZoneInfo(timezone_key)
        except (ZoneInfoNotFoundError, Exception) as timezone_error:
            logger.warning(
                f"ZoneInfo could not convert {timezone_key} to IANA timezone, check MFD settings",
                extra={"error": timezone_error},
            )
            return ZoneInfo("UTC")

    @staticmethod
    def get_all(
        property_ids: List[int] = None, ids: bool = False
    ) -> List[dict] | List[int]:
        """
        Method that based on the list of property ids given will return for each property the property schema if ids is False
        By default property_ids will be equal to the property_ids that are received in the access token

        If ids is True it will return just the list of property ids

        If the User is Super Admin it will use the property given in the X-PROPERTY-ID as the list of property_ids

        Parameters:
        @property_ids: List[int] | None: A list of property ids to get the name and timezone. If None the property_ids of the token will be used
        @ids: bool: By default is None returning for each property id the property schema. If true it will return the list of ids


        Returns:
        List[dict] | List[int]: A list of property dictionary including {id, name, description, timezone} or a list of property ids if ids is True
        """
        if not property_ids:
            property_ids = g.property_ids
            logger.info("Get Properties from access token")

        if ids:
            return property_ids

        return PropertyService.get_properties_by_ids(property_ids)

    @staticmethod
    def are_valid_property_ids(property_ids: List[int]) -> bool | InvalidUsage:
        """
        Method that will validate if the given property_ids correspond to the one the user has access

        Parameters:
        property_ids: List[int]: A list of property_ids to validate

        Returns:
        bool | InvalidUsage: Returns true if the property ids given are the one the user has access or throws an exception
        """
        if (
            not g.api_key
            and not g.user.admin
            and not all(property in g.property_ids for property in property_ids)
        ):
            logger.warning(
                "User is not allowed to access the property ids",
                extra={
                    "admin": g.user.admin,
                    "property_ids": property_ids,
                    "access_token_property_ids": g.property_ids,
                },
            )
            raise InvalidUsage.forbidden(
                f"User is not allowed to access the property ids {property_ids}"
            )

        return True

    @staticmethod
    @cache.memoize(TIMEOUT_WEEK)
    def get_property_type(property_id: int) -> str:
        with PropertyServiceClient() as client:
            property = client.get_property(property_id).get("property")
            return PropertyTypes.get_by_key(int(property.get("type", 1))).name

    @staticmethod
    @cache.memoize(TIMEOUT_WEEK)
    def get_property_island(property_id: int) -> str | InvalidUsage:
        with PropertyServiceClient() as client:
            property = client.get_property(property_id).get("property", dict())
            island = property.get("island", dict())
            island_id = island.get("id")
            island = (
                island_id
                if island_id in [island.value for island in PropertyIslands]
                else None
            )

            if island is None:
                logger.warning(
                    "Island was not found", extra={"property_id": property_id}
                )

            return island

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_active_property_ids(property_ids: list[int]) -> list[int]:
        with PropertyServiceClient() as client:
            properties = client.list_properties(property_ids).get("properties", [])
            return [properties.get("id") for properties in properties]

    @staticmethod
    def get_property_date_format(property_id: int) -> str:
        property_info = PropertyService.get_property(property_id)
        return property_info.get("date_format", "Y/m/d")
