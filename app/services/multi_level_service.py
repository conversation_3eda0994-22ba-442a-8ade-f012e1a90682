from app.common.cache import TIMEOUT_HOUR, cache
from app.common.keys import multi_level_by_id, multi_levels_by_dataset_id
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums.dataset import Dataset as DatasetEnum
from app.enums.multi_level import MultiLevel as MultiLevelEnum


class MultiLevelService:
    """Handles business logic for multi-levels"""

    def get_multi_level_by_id(multi_level_id: int):
        multi_level_cdfs_key = multi_level_by_id(multi_level_id)
        multi_level = cache.get(multi_level_cdfs_key)

        if multi_level is not None:
            return multi_level

        multi_level = MultiLevel(MultiLevelEnum(multi_level_id))

        cache.set(multi_level_cdfs_key, multi_level, timeout=TIMEOUT_HOUR)
        return multi_level

    def get_multi_levels_by_dataset_id(dataset_id: int):
        multi_levels_key = multi_levels_by_dataset_id(dataset_id)
        multi_levels = cache.get(multi_levels_key)

        if multi_levels is not None:
            return multi_levels

        multi_levels = [
            MultiLevel(MultiLevelEnum(multi_level))
            for multi_level in Dataset(DatasetEnum(dataset_id)).multi_levels
        ]

        cache.set(multi_levels_key, multi_levels, timeout=TIMEOUT_HOUR)
        return multi_levels
