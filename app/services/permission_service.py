import asyncio
import concurrent
import concurrent.futures
from itertools import islice

from flask import current_app
from flask.ctx import AppContext

from app.common.cache import TIMEOUT_HOUR, TIMEOUT_MINUTE, cache
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.permissions import (
    PERMISSION_RESOURCE_TO_DATASET_MAPPING,
    PermissionAction,
    PermissionResource,
    PermissionResourcePrefix,
    PermissionRole,
    ROLES_HIERARCHY,
    Resource,
)
from app.common.exceptions import InvalidUsage
from app.common.keys import list_permissions_key, whitelisted_emails_key
from app.common.logger import get_log_metadata, logger
from app.common.user import User
from app.services.grpc.permission_service_client import PermissionServiceClient
from app.services.launch_darkly_service import LaunchDarklyService
from app.services.s3_service import S3Service


class PermissionService:
    @staticmethod
    def get_whitelisted_emails() -> list:
        cache_key = whitelisted_emails_key()
        emails = cache.get(cache_key)

        if emails is None:
            emails = []
            s3_service = S3Service("", "whitelisted-emails", "csv", False, None)
            s3_object = s3_service.s3.get_object(
                Bucket=s3_service.bucket,
                Key=f"{s3_service.filename}.{s3_service.format}",
            )
            data = s3_object["Body"].read()

            for row in data.decode("utf-8").splitlines():
                items = row.split(",")
                emails.append(items[0])
            if emails:
                cache.set(cache_key, emails, TIMEOUT_HOUR)

        return emails

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def is_whitelisted(subject: str) -> bool:
        """Method that will return true if the email is on the DI Whitelisted list"""
        return subject in PermissionService.get_whitelisted_emails()

    @staticmethod
    @cache.memoize(TIMEOUT_HOUR)
    def get_policies(subject: str) -> list:
        admin_policies = [
            dict(resource="stock_report_revision", actions=[dict(name="read")]),
            dict(
                resource="stock_report",
                actions=[
                    dict(name="create"),
                    dict(name="delete"),
                    dict(name="read"),
                    dict(name="update"),
                ],
            ),
        ]
        return admin_policies if PermissionService.is_whitelisted(subject) else []

    @staticmethod
    def is_author(property_id: int, user: User, resource: Resource):
        """Method that will return True if User has Author on at least 1 permission on the given resource"""
        if LaunchDarklyService.has_feature_flag(
            LaunchDarklyFeature.PermissionService, property_id
        ):
            return any(
                permission["role"] != PermissionRole.VIEWER.value
                for permission in PermissionService.get_permissions(property_id, user)[
                    "permissions"
                ].get(resource.value, [])
            )

        return False

    @staticmethod
    def get_feature_flag_permissions(property_id: int, permissions: list) -> dict:
        """Return a new permissions list with feature flag-based permissions."""
        explore_permissions = {Resource.EXPLORE.value: []}

        permission_map = {}

        try:
            if LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.Explore, property_id
            ):
                for permission in permissions.get(Resource.STOCK_REPORTS.value, []):
                    key = (PermissionRole.VIEWER.value, permission["resource"]["id"])
                    if key not in permission_map:
                        permission_map[key] = {
                            "role": PermissionRole.VIEWER.value,
                            "resource": permission["resource"],
                            "actions": [],
                        }
                    if (
                        PermissionAction.EXPLORE.value
                        not in permission_map[key]["actions"]
                    ):
                        permission_map[key]["actions"].append(
                            PermissionAction.EXPLORE.value
                        )

            # EXPLORE_REPORT from reports (non-viewer only)
            if LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.ExploreReports, property_id
            ):
                for permission in permissions.get(Resource.REPORTS.value, []):
                    if permission["role"] == PermissionRole.VIEWER.value:
                        continue
                    key = (PermissionRole.VIEWER.value, permission["resource"]["id"])
                    if key not in permission_map:
                        permission_map[key] = {
                            "role": PermissionRole.VIEWER.value,
                            "resource": permission["resource"],
                            "actions": [],
                        }
                    if (
                        PermissionAction.EXPLORE_REPORT.value
                        not in permission_map[key]["actions"]
                    ):
                        permission_map[key]["actions"].append(
                            PermissionAction.EXPLORE_REPORT.value
                        )

        except StopIteration:
            logger.error(
                "Unexpected StopIteration from LaunchDarkly, returning no feature flag permissions"
            )

        explore_permissions[Resource.EXPLORE.value] = list(permission_map.values())
        return explore_permissions

    @staticmethod
    def get_permissions(property_id: int, user: User) -> list:
        log_metadata = get_log_metadata()
        app = current_app._get_current_object()

        cache_key = list_permissions_key(property_id, user.id)
        permissions = cache.get(cache_key)

        if not permissions:
            with app.app_context():
                permissions = asyncio.run(
                    PermissionService.get_permissions_async(
                        app, property_id, user, log_metadata
                    )
                )
                cache.set(cache_key, permissions, TIMEOUT_MINUTE)

        return permissions

    @staticmethod
    async def get_permissions_async(
        app, property_id: int, user: User, log_context: dict
    ) -> list:
        """Fetch permissions using Flask’s persistent async pool."""
        if PermissionService.is_whitelisted(user.email):
            permissions = PermissionService.get_role_hierarchy(
                [
                    dict(resource=permission.value, actions=PermissionAction.list())
                    for permission in PermissionResource
                ]
            )
        else:
            permissions = await PermissionService.get_all_permissions_async(
                app, property_id, user.id, log_context
            )

        feature_flag_permissions = await asyncio.to_thread(
            PermissionService.get_feature_flag_permissions, property_id, permissions
        )

        merged_permissions = {
            key: permissions.get(key, []) + feature_flag_permissions.get(key, [])
            for key in permissions.keys() | feature_flag_permissions.keys()
        }

        return {"permissions": merged_permissions}

    @staticmethod
    def fetch_permissions_with_context(
        app: AppContext,
        property_id: int,
        user_id: int,
        permissions: list,
        log_context: dict,
    ):
        """Ensures Flask app context is available for gRPC call inside the executor."""
        with app.app_context():
            with PermissionServiceClient() as client:
                return client.list_permissions(
                    property_id, user_id, permissions, log_context
                )

    @staticmethod
    def get_role_hierarchy(permissions: list) -> dict:
        role_hierarchy = {
            Resource.REPORTS.value: [],
            Resource.STOCK_REPORTS.value: [],
            Resource.SETTINGS.value: [],
        }

        for permission in permissions:
            resource_key = permission.get("resource")

            actions = set(permission.get("actions", []))

            if not actions:
                continue

            role = next(
                (
                    role
                    for role in reversed(ROLES_HIERARCHY)
                    if ROLES_HIERARCHY[role].issubset(actions)
                ),
                PermissionRole.VIEWER.value,
            )

            if (
                not role_hierarchy[Resource.SETTINGS.value]
                and role is not PermissionRole.VIEWER.value
                and PermissionAction.MANAGE.value in actions
            ):
                role_hierarchy[Resource.SETTINGS.value].append(
                    {
                        "role": role,
                        "resource": {
                            "id": "manage",
                            "name": "Manage",
                        },
                        "actions": [PermissionAction.MANAGE.value],
                    }
                )

            resources = PERMISSION_RESOURCE_TO_DATASET_MAPPING[resource_key]

            [
                role_hierarchy[
                    Resource.STOCK_REPORTS.value
                    if PermissionResourcePrefix.STOCK_REPORT.value in resource_key
                    else Resource.REPORTS.value
                ].append(
                    {
                        "role": role,
                        "resource": {
                            "id": str(resource.value),
                            "name": resource.name,
                        },
                        "actions": actions,
                    }
                )
                for resource in resources
            ]

        return role_hierarchy

    @staticmethod
    async def get_all_permissions_async(
        app: AppContext, property_id: int, user_id: int, log_context: dict
    ):
        """Fetch permissions asynchronously in chunks of 2 using Flask’s async resources."""

        loop = asyncio.get_running_loop()
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=app.config["PERMISSION_SERVICE_THREADS"]
        ) as executor:

            if not executor:
                raise RuntimeError("Executor is missing or Flask is shutting down.")

            permissions = [
                {
                    "actions": PermissionAction.get_permission_actions(),
                    "resource": resource,
                }
                for resource in PermissionResource.list()
            ]

            def chunked(iterable, size):
                """Splits an iterable into chunks of a given size"""
                it = iter(iterable)
                return iter(lambda: list(islice(it, size)), [])

            async def process_chunk(chunk):
                """Processes a chunk of permissions in a separate thread using the persistent executor."""
                return await loop.run_in_executor(
                    executor,
                    PermissionService.fetch_permissions_with_context,
                    app,
                    property_id,
                    user_id,
                    chunk,
                    log_context,
                )

            all_permissions = []

            for chunk in chunked(permissions, app.config["PERMISSION_SERVICE_CHUNKS"]):
                results = await process_chunk(chunk)
                if isinstance(results, Exception):
                    logger.error(
                        "There is a problem getting the permissions",
                        extra={**log_context, "exception": str(results)},
                    )
                    raise InvalidUsage.server_error()
                else:
                    all_permissions.extend(results)

            return PermissionService.get_role_hierarchy(all_permissions)
