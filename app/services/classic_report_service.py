import copy
from enum import Enum


from marshmallow import validate

from app.api.v1_1.schemas.classic_report.classic_report import (
    ClassicReportDataSchema,
    ClassicReportQueryParamsSchema,
    ClassicReportSchema,
)
from app.api.v1_1.schemas.classic_report.classic_report_query import (
    ClassicReportQuerySchema,
)
from app.api.v1_1.schemas.log import ReportLogSchema
from app.api.v1_1.schemas.report.report import ReportQuerySchema
from app.classic_reports.common.report_titles import ClassicReportTitles
from app.classic_reports.static_reports import (
    ADJUSTMENTS_REPORT,
    ARRIVALS_REPORT,
    CHANNEL_PRODUCTION_REPORT,
    DAILY_REVENUE_REPORT,
    DEPARTURES_REPORT,
    NO_SHOW_REPORT,
    PAYMENT_RECONCILIATION_REPORT,
    RESERVATIONS_BY_COUNTRY_REPORT,
    TAX_REPORT,
    USER_RECONCILIATION_REPORT,
)
from app.classic_reports.static_reports.daily_activity.account_balances_report import (
    ACCOUNT_BALANCES,
)
from app.classic_reports.static_reports.daily_activity.cancellation_report import (
    CANCELLATION_REPORT,
)
from app.classic_reports.static_reports.daily_activity.in_house_report import (
    IN_HOUSE_REPORT,
)
from app.classic_reports.static_reports.daily_activity.notes_report import NOTES_REPORT
from app.classic_reports.static_reports.daily_activity.reservations_by_rate_plan_report import (
    RESERVATIONS_BY_RATE_PLAN_REPORT,
)
from app.classic_reports.static_reports.financial.commission_report import (
    COMMISSION_REPORT,
)
from app.classic_reports.static_reports.financial.payment_ledger import PAYMENT_LEDGER
from app.classic_reports.static_reports.financial.transactions_report import (
    TRANSACTIONS_REPORT,
)
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum
from app.enums.log_actions import ReportAction
from app.enums.mode import Mode
from app.enums.report import ReportKind
from app.reports.report import Report as ReportData
from app.services.property_service import PropertyService
from app.services.property_setting_service import PropertySettingService


class ClassicReportService:
    @staticmethod
    def get_report(report_name: Enum) -> dict:
        """Get Classic Report

        Args:
            report_name (Enum): the name of the classic report to load

        Returns:
            dict: the classic report
        """
        match (report_name):
            case ClassicReportTitles.ArrivalsReport:
                report = copy.deepcopy(ARRIVALS_REPORT)
            case ClassicReportTitles.ChannelProductionReport:
                report = copy.deepcopy(CHANNEL_PRODUCTION_REPORT)
            case ClassicReportTitles.DeparturesReport:
                report = copy.deepcopy(DEPARTURES_REPORT)
            case ClassicReportTitles.UserReconciliationReport:
                report = copy.deepcopy(USER_RECONCILIATION_REPORT)
            case ClassicReportTitles.DailyRevenueReport:
                report = copy.deepcopy(DAILY_REVENUE_REPORT)
            case ClassicReportTitles.PaymentReconciliationReport:
                report = copy.deepcopy(PAYMENT_RECONCILIATION_REPORT)
            case ClassicReportTitles.NoShowReport:
                report = copy.deepcopy(NO_SHOW_REPORT)
            case ClassicReportTitles.InHouseReport:
                report = copy.deepcopy(IN_HOUSE_REPORT)
            case ClassicReportTitles.CancellationReport:
                report = copy.deepcopy(CANCELLATION_REPORT)
            case ClassicReportTitles.ReservationsByRatePlanReport:
                report = copy.deepcopy(RESERVATIONS_BY_RATE_PLAN_REPORT)
            case ClassicReportTitles.ReservationsByCountryReport:
                report = copy.deepcopy(RESERVATIONS_BY_COUNTRY_REPORT)
            case ClassicReportTitles.TaxReport:
                report = copy.deepcopy(TAX_REPORT)
            case ClassicReportTitles.CommissionReport:
                report = copy.deepcopy(COMMISSION_REPORT)
            case ClassicReportTitles.AdjustmentsReport:
                report = copy.deepcopy(ADJUSTMENTS_REPORT)
            case ClassicReportTitles.PaymentLedger:
                report = copy.deepcopy(PAYMENT_LEDGER)
            case ClassicReportTitles.TransactionsReport:
                report = copy.deepcopy(TRANSACTIONS_REPORT)
            case ClassicReportTitles.NotesReport:
                report = copy.deepcopy(NOTES_REPORT)
            case ClassicReportTitles.AccountBalances:
                report = copy.deepcopy(ACCOUNT_BALANCES)
            case _:
                raise InvalidUsage.not_found(f"Report {report_name} not found")

        return ClassicReportSchema().load(report)

    @staticmethod
    def query_data(
        report_name: Enum,
        property_id: str,
        organization_id: str,
        query: ClassicReportQueryParamsSchema,
        report: ClassicReportQuerySchema,
    ) -> ClassicReportDataSchema:
        """Query Classic Report Data

        Args:
            report_name (str): the name of the classic report to load
            property_id (str): the property running the report
            organization_id (str): the organization running the report
            query (ClassicReportQueryParamsSchema): the page info of the report
            report (ClassicReportQuerySchema): any modifications to the report
            user (User): _description_

        Returns:
            ClassicReportDataSchema
        """
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        classic_report = ClassicReportService.get_report(report_name)

        classic_report.update(report)

        ClassicReportService.validate_report_query(classic_report)

        dataset = Dataset(DatasetEnum(classic_report["dataset_id"]))

        data = ReportData(
            mode=query["mode"],
            dataset=dataset,
            custom_cdfs=classic_report["custom_cdfs"],
            columns=classic_report["columns"],
            group_rows=classic_report["group_rows"],
            group_columns=classic_report["group_columns"],
            filters=classic_report["filters"],
            property_ids=report.get("property_ids"),
            organization_id=organization_id,
            sort=classic_report["sort"],
            settings=classic_report["settings"],
            format=query["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=classic_report["periods"],
            formats=classic_report.get("formats"),
            comparisons=classic_report.get("comparisons"),
            offset=query.get("offset"),
            limit=query.get("limit"),
        ).get_data()

        logger.info(
            "Classic Report query data",
            extra=ReportLogSchema().dump(
                {
                    **classic_report,
                    **dict(
                        action=ReportAction.DataView.value,
                        report_kind=ReportKind.ClassicReport.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return data

    @staticmethod
    def query_summary(
        report_name: Enum,
        property_id: str,
        organization_id: str,
        query: ClassicReportQueryParamsSchema,
        report: ClassicReportQuerySchema,
    ) -> ClassicReportDataSchema:
        """Query Classic Report Summary

        Args:
            report_name (str): the name of the classic report to load
            property_id (str): the property running the report
            organization_id (str): the organization running the report
            query (ClassicReportQueryParamsSchema): the page info of the report
            report (ClassicReportQuerySchema): any modifications to the report
            user (User): _description_

        Returns:
            StockReportDataSchema: _description_
        """
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        classic_report = ClassicReportService.get_report(report_name)

        classic_report.update(report)

        ClassicReportService.validate_report_query(classic_report)

        dataset = Dataset(DatasetEnum(classic_report["dataset_id"]))

        summary = ReportData(
            mode=Mode.Run,
            dataset=dataset,
            custom_cdfs=classic_report["custom_cdfs"],
            columns=classic_report["columns"],
            group_rows=classic_report["group_rows"],
            group_columns=classic_report["group_columns"],
            filters=classic_report["filters"],
            property_ids=report.get("property_ids"),
            organization_id=organization_id,
            sort=classic_report["sort"],
            settings=classic_report["settings"],
            format=query["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=classic_report["periods"],
            formats=classic_report.get("formats"),
            comparisons=classic_report.get("comparisons"),
        ).get_summary()

        logger.info(
            "Stock Report query data",
            extra=ReportLogSchema().dump(
                {
                    **classic_report,
                    **dict(
                        action=ReportAction.DataView.value,
                        report_kind=ReportKind.ClassicReport.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return summary

    @staticmethod
    def validate_report_query(classic_report):
        dumped_data = ReportQuerySchema().dump(classic_report)
        dumped_data_copy = copy.deepcopy(dumped_data)
        for cdf in dumped_data_copy.get("custom_cdfs", []):
            cdf.pop("column", None)
        # have to override group rows max length validator, making a copy of everything to avoid leaks
        schema = ReportQuerySchema()
        schema.fields["group_rows"].validators = [
            copy.deepcopy(validator)
            for validator in schema.fields["group_rows"].validators
        ]
        for validator in schema.fields["group_rows"].validators:
            if isinstance(validator, validate.Length):
                validator.max = 5
        schema.load(dumped_data_copy)
