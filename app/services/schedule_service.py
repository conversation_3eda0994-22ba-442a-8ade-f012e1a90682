from typing import NoReturn

from sqlalchemy.sql.expression import Delete

from app.common.cache import TIMEOUT_DAY, cache
from app.common.enums.features import BillingPortalFeatures
from app.common.exceptions import InvalidUsage
from app.common.keys import PROPERTIES_DI_DISABLED_KEY
from app.models.schedule import Schedule
from app.services.property_feature_service import PropertyFeatureService
from app.services.property_service import PropertyService
from app.services.report_service import ReportService
from app.services.user_service import UserService


class ScheduleService:
    @staticmethod
    def get_by_id_and_property_id(
        property_id: int, schedule_id: int
    ) -> Schedule | NoReturn:
        return Schedule.get_by_id_and_property_id(property_id, schedule_id)

    @staticmethod
    def get_by_id(schedule_id: int) -> Schedule | NoReturn:
        return Schedule.get_by_id(schedule_id)

    @staticmethod
    def get_all(property_id: dict, query: dict, sort: dict) -> list[dict]:
        """Get all schedules for a property_id, or all enabled properties"""
        ignore_properties = (
            cache.get(PROPERTIES_DI_DISABLED_KEY)
            if not property_id.get("property_id")
            else []
        )
        if ignore_properties is None:
            ignore_properties = ScheduleService.get_di_disabled_properties()
            cache.set(PROPERTIES_DI_DISABLED_KEY, ignore_properties, TIMEOUT_DAY)
        return Schedule.get_all(property_id, query, sort, ignore_properties)

    @staticmethod
    def get_di_disabled_properties() -> list:
        return [
            schedule.property_id
            for schedule in Schedule.get_all_property_ids()
            if not PropertyFeatureService.has_feature_flag(
                BillingPortalFeatures.ReportBuilder.value, schedule.property_id
            )
            or PropertyService.property_deactivated(schedule.property_id)
        ]

    @staticmethod
    def get_schedule_reports(schedule: dict, property_id: int) -> list[dict]:
        return [
            ReportService.get_by_id_and_property_id(
                id=report_id, property_id=property_id
            )
            for report_id in schedule["report_ids"]
            if schedule["report_ids"]
        ]

    @staticmethod
    def create_schedule(
        schedule: dict, property_id: int, user_id: int
    ) -> Schedule | NoReturn:
        reports = ScheduleService.get_schedule_reports(schedule, property_id)

        return Schedule.create(
            subject=schedule["subject"],
            frequency=schedule["frequency"],
            property_id=property_id,
            view=schedule["view"],
            format=schedule["format"],
            recipients=schedule["recipients"],
            user_id=user_id,
            reports=reports,
            settings=schedule.get("settings", {}),
        )

    @staticmethod
    def update_schedule(
        schedule_id: int,
        update_schedule: dict,
        property_id: int,
        user_id: int,
    ) -> Schedule | NoReturn:
        reports = ScheduleService.get_schedule_reports(update_schedule, property_id)

        return Schedule.get_by_id(schedule_id).update(
            subject=update_schedule["subject"],
            frequency=update_schedule["frequency"],
            property_id=property_id,
            view=update_schedule["view"],
            format=update_schedule["format"],
            recipients=update_schedule["recipients"],
            user_id=user_id,
            reports=reports,
            settings=update_schedule.get("settings"),
        )

    @staticmethod
    def delete_schedule(
        schedule_id: int,
        property_id: int,
    ) -> Delete:
        return Schedule.get_by_id_and_property_id(property_id, schedule_id).delete()

    @staticmethod
    def validate_active_user_with_property_access(user_email: str) -> bool:
        """Method that will return True if the user is active and has at least one property assigned

        Args:
            user_email (str): User email to validate
        """
        user_id = UserService.get_id_by_email(user_email)
        if (
            # User exists
            user_id
            # User is active
            and UserService.get_user_active(user_id)
            # User has at least one property assigned or is an employee
            and (
                PropertyService.get_active_property_ids(
                    UserService.get_user_properties(user_id)
                )
                or UserService.is_employee(user_id)
            )
        ):
            return True
        return False

    @staticmethod
    def validate_active_users_with_property_access(user_emails: list[str]) -> None:
        """Method that will validate if the users are active and have at least one property assigned

        Args:
            user_emails (list[str]): List of user emails to validate
        """
        invalid_users = [
            user_email
            for user_email in user_emails
            if not ScheduleService.validate_active_user_with_property_access(user_email)
        ]

        if invalid_users:
            readable_list = ", ".join(invalid_users)
            raise InvalidUsage.bad_request(
                f"The following email addresses are not active Cloudbeds users "
                f"or have no properties assigned: {readable_list}"
            )

        return
