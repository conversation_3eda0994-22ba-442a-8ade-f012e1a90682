from collections import OrderedDict

from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.api.v1_1.schemas.search import SearchSchema
from app.common.enums.search import SearchResources
from app.common.user import User
from app.services.report_service import ReportService
from app.services.stock_report_service import StockReportService


class SearchService:
    @staticmethod
    def get_search_results(
        user: User,
        header_property_id: dict,
        params: OrderedDict,
        page: QueryPaginationSchema,
    ):
        search_results_schema = SearchSchema()

        resources = params.get("resources")

        for resource in resources:
            if resource == SearchResources.Report.value:
                search_results_schema.reports = ReportService.get_search_results(
                    property_id=header_property_id.get("property_id"),
                    params=params,
                    user=user,
                    _=page,
                )
            if resource == SearchResources.StockReport.value:
                search_results_schema.stock_reports = (
                    StockReportService.get_search_results(
                        user,
                        header_property_id.get("property_id"),
                        params=params,
                        _=page,
                    )
                )

        return search_results_schema
