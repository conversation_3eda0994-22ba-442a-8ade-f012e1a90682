from flask import g

from sqlalchemy.orm import aliased

from app.common.exceptions import InvalidUsage
from app.models.card import Card
from app.models.hub import Hub


class HubService:
    @staticmethod
    def create_hub(hub, property_id) -> Hub:
        hub = Hub(
            title=hub.get("title"),
            description=hub.get("description"),
            settings=hub.get("settings"),
            property_id=property_id,
            user_id=g.user.id,
        )
        hub.save()

        return hub

    @staticmethod
    def create_card(card, hub_id, property_id) -> Card:
        card = Card(
            hub_id=hub_id,
            chart_id=card.get("chart_id"),
        )

        try:
            card.save()
        except Exception as exception:
            if "Maximum rows per hub_id exceeded" in str(exception):
                raise InvalidUsage.bad_request(
                    message="Maximum number of cards reached."
                )
            raise exception

        return card

    @staticmethod
    def get_all(
        property_id: int, filters: dict, sort: dict, ids_only: bool = False
    ) -> list[Hub]:
        if ids_only:
            hubs = Hub.query.with_entities(Hub.id.label("id"))
        else:
            hubs = Hub.query

        hubs = hubs.filter_by(property_id=property_id)
        if filters.get("ids"):
            hubs = hubs.filter(Hub.id.in_(filters.get("ids")))
        if filters.get("title"):
            hubs = hubs.filter(Hub.title == filters.get("title"))
        if filters.get("description"):
            hubs = hubs.filter(Hub.description == filters.get("description"))
        if sort:
            hubs = hubs.order_by(Hub.get_order_by(sort))
        return hubs

    @staticmethod
    def get_hub_by_id(hub_id, property_id) -> Hub:
        hub = Hub.query.filter_by(id=hub_id, property_id=property_id).first_or_404()

        return hub

    @staticmethod
    def update_hub_by_id(hub_id, update_hub, property_id) -> Hub:
        hub = Hub.query.filter_by(id=hub_id, property_id=property_id).first_or_404()
        hub.title = update_hub.get("title")
        hub.description = update_hub.get("description")
        hub.settings = update_hub.get("settings")
        hub.save()

        return hub

    @staticmethod
    def delete_hub_by_id(hub_id, property_id) -> Hub:
        cards = Card.query.filter_by(hub_id=hub_id).all()
        if cards:
            raise InvalidUsage.bad_request(message="Cannot delete hub with cards.")
        hub = Hub.query.filter_by(id=hub_id, property_id=property_id).first_or_404()
        hub.delete()

    @staticmethod
    def get_card_by_id(hub_id, card_id, property_id) -> Card:
        Hub.query.filter_by(id=hub_id, property_id=property_id).first_or_404()
        card = Card.query.filter_by(id=card_id, hub_id=hub_id).first_or_404()

        return card

    @staticmethod
    def update_card_by_id(hub_id, card_id, update_card, property_id) -> Card:
        Hub.query.filter_by(id=hub_id, property_id=property_id).first_or_404()
        card = Card.query.filter_by(id=card_id, hub_id=hub_id).first_or_404()
        card.chart_id = update_card.get("chart_id")
        card.save()

        return card

    @staticmethod
    def delete_card_by_id(hub_id, card_id, property_id) -> None:
        Hub.query.filter_by(id=hub_id, property_id=property_id).first_or_404()
        card = Card.query.filter_by(id=card_id, hub_id=hub_id).first_or_404()
        card.delete()

    @staticmethod
    def get_cards_by_chart_id(chart_id) -> list[Card]:
        cards = Card.query.filter_by(chart_id=chart_id).all()

        return cards

    @staticmethod
    def search_hubs(property_id: int, filters: dict, sort: dict) -> list[Hub]:
        search_columns = filters.get("search_columns")
        search_term = filters.get("search_term")

        if not search_columns:
            return HubService.get_all(property_id, filters, sort)

        hub_ids = None
        for search_column in search_columns:
            search_column_hubs = HubService.get_all(
                # just get a list of ids, unsorted
                property_id,
                filters,
                sort=None,
                ids_only=True,
            )
            search_column_hubs = search_column_hubs.filter(
                getattr(Hub, search_column).ilike(f"%{search_term}%")
            )
            if not hub_ids:
                hub_ids = search_column_hubs
            else:
                hub_ids = hub_ids.union(search_column_hubs)

        hub_ids_aliased = aliased(hub_ids.subquery())
        hubs = Hub.query.join(hub_ids_aliased, Hub.id == hub_ids_aliased.c.id)

        return hubs.order_by(Hub.get_order_by(sort))
