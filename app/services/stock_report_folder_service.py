from typing import Annotated, <PERSON><PERSON>

from flask_sqlalchemy.query import Query as BaseQuery

from app.api.v1_1.schemas.stock_report.stock_report_folder import (
    StockReportFolderSchema,
)
from app.common.cache import TIMEOUT_DAY, cache
from app.common.exceptions import InvalidUsage
from app.decorators.pagination import pagination
from app.models.stock_report import StockReport
from app.models.stock_report_folder import StockReportFolder
from app.services.distributed_id_service import DistributedIdService

StockReportFolder = Annotated[StockReportFolder, Tuple[str, int]]


class StockReportFolderService:
    @cache.memoize(TIMEOUT_DAY)
    @staticmethod
    def get_by_id(folder_id: int) -> StockReportFolder:
        folder = StockReportFolder.get_by_id(folder_id)

        if not folder:
            raise InvalidUsage.not_found(f"A folder with id: {folder_id} was not found")

        return folder

    @staticmethod
    @pagination("folders")
    def __get_all_paginated(**kwargs):
        return StockReportFolder.get_all()

    @cache.memoize(TIMEOUT_DAY)
    @staticmethod
    def get_all(**kwargs) -> BaseQuery:
        return StockReportFolderService.__get_all_paginated(**kwargs)

    @staticmethod
    def create(folder: StockReportFolderSchema, user_id: int) -> StockReportFolder:
        stock_report_folder = StockReportFolder.create(
            id=DistributedIdService.get_distributed_id(),
            user_id=user_id,
            **folder,
        )
        cache.delete_memoized(StockReportFolderService.get_all)
        return stock_report_folder

    @staticmethod
    def delete(folder_id: int, limit: int = 500) -> None:
        """Method that will delete the folder"""
        folder = StockReportFolder.query.filter(
            StockReportFolder.id == folder_id
        ).first()

        if not folder:
            raise InvalidUsage.not_found(f"A folder with id: {folder_id} was not found")

        if StockReport.are_reports_assign_to_folder(folder_id):
            return InvalidUsage.bad_request(
                message="This folder cannot be deleted. Please contact your administrator to proceed."
            )

        folder.delete()

        cache.delete_memoized(StockReportFolderService.get_all)
