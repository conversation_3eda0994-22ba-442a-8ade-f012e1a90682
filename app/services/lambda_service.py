import json

from boto3.session import Session

from flask import current_app as app

from app.common.logger import logger


class LambdaService:
    def __init__(self, function_name: str) -> "LambdaService":
        self.function_name = function_name
        self.CREDENTIALS = dict(
            aws_access_key_id=app.config["AWS_S3_ACCESS_KEY_ID"],
            aws_secret_access_key=app.config["AWS_S3_SECRET_ACCESS_KEY"],
            region_name=app.config["AWS_REGION"],
        )
        self.client = Session().client("lambda", **self.CREDENTIALS)

    def __repr__(self):
        """Representation of LambdaService
        :return: string
        """
        return f"<LambdaService, function_name={self.function_name}>"

    def invoke(self, payload: dict):
        try:
            response = self.client.invoke(
                FunctionName=self.function_name,
                InvocationType="RequestResponse",
                LogType="None",
                Payload=json.dumps(payload, default=str),
            )

            return json.loads(response["Payload"].read())
        except Exception as error:
            logger.error(
                "Error invoking lambda",
                extra={"function_name": self.function_name, "error": str(error)},
            )
