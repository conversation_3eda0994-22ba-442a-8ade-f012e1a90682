from io import BytesIO

import openpyxl

from pandas import ExcelWriter

from sqlalchemy.orm.collections import InstrumentedList

from app.api.v1_1.schemas.log import ReportLogSchema
from app.api.v1_1.schemas.pagination import QueryPaginationSchema
from app.api.v1_1.schemas.report import (
    QueryReportExportSchema,
    ReportCustomCdfSchema,
    ReportQueryExportSchema,
    ReportQuerySchema,
    ReportSchema,
)
from app.common.constants.table_limits import (
    EXPORT_LIMIT,
    PIVOT_PREVIEW_LIMIT,
    PIVOT_RUN_LIMIT,
    SUMMARY_PREVIEW_LIMIT,
    SUMMARY_RUN_LIMIT,
    TABULAR_PREVIEW_LIMIT,
    TABULAR_RUN_LIMIT,
)
from app.common.logger import logger
from app.common.user import User
from app.datasets.dataset import Dataset
from app.decorators.pagination import pagination
from app.enums import (
    Dataset as DatasetEnum,
    Format,
    Mode,
    ReportAction,
    ReportKind,
    View,
)
from app.enums.format import InternalNumericFormat
from app.models.chart import Chart
from app.models.report import Report, ReportCustomCdf
from app.reports.report import Report as ReportClass
from app.services.access_control_service import AccessControlService
from app.services.chart_service import ChartService
from app.services.property_service import PropertyService
from app.services.property_setting_service import PropertySettingService


class ReportService:
    @staticmethod
    def get_all(property_id: int, filters: dict, sort: dict, user: User) -> list:
        return Report.get_all_sorted(property_id, filters, sort, user)

    @staticmethod
    @pagination("items")
    def get_search_results(
        property_id: int, params: dict, user: User, _: QueryPaginationSchema
    ):
        return Report.get_search_results(property_id, params, user)

    @staticmethod
    def get_all_partial_search(property_id: int, params: dict, sort: dict, user: User):
        if params.get("search_columns"):
            reports = Report.get_all_search_results(
                property_id,
                params,
                user,
                sort,
            )

        else:
            reports = Report.get_all_sorted(property_id, params, sort, user, True)

        return reports

    @staticmethod
    def get_by_id(id: int, user: User):
        report = Report.get_by_id(id)
        if AccessControlService.is_allowed_to_access_properties(
            report.property_ids
        ) and AccessControlService.is_allowed_to_access_dataset(
            report.dataset_id, user
        ):
            return report

    @staticmethod
    def get_by_id_and_property_id(id: int, property_id: int):
        return Report.get_by_id_and_property_id(id, property_id)

    @staticmethod
    def query_report_data(
        query_params: dict,
        report_query: dict,
        property_id: int,
        organization_id: int,
        user: User,
    ):
        dataset = Dataset(DatasetEnum(report_query["dataset_id"]))
        AccessControlService.is_allowed_to_access_dataset(dataset.kind.value, user)

        report_query = ReportQuerySchema(exclude=["dataset_id"]).dump(report_query)

        property_timezone = PropertyService.get_timezone_by_id(property_id)

        report_query["custom_cdfs"] = InstrumentedList(
            [
                ReportCustomCdf(**custom_cdf)
                for custom_cdf in report_query["custom_cdfs"] or []
            ]
        )

        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        report_data = ReportClass(
            dataset=dataset,
            mode=query_params["mode"],
            format=query_params["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            organization_id=organization_id,
            offset=query_params.get("offset", 0),
            limit=query_params.get("limit", None),
            **report_query,
        ).get_data()

        logger.info(
            "Get Report Data",
            extra=ReportLogSchema().dump(
                {
                    **report_query,
                    **dict(
                        action=ReportAction.DataView.value,
                        report_kind=ReportKind.Report.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return report_data

    @staticmethod
    def query_report_summary(
        query_params: dict,
        report_query: dict,
        property_id: int,
        organization_id: int,
        user: User,
    ):
        dataset = Dataset(DatasetEnum(report_query["dataset_id"]))
        AccessControlService.is_allowed_to_access_dataset(dataset.kind.value, user)

        report_query = ReportQuerySchema(exclude=["dataset_id"]).dump(report_query)

        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        report_query["custom_cdfs"] = InstrumentedList(
            [
                ReportCustomCdf(**custom_cdf)
                for custom_cdf in report_query["custom_cdfs"] or []
            ]
        )

        report_data = ReportClass(
            dataset=dataset,
            mode=query_params["mode"],
            format=query_params["format"],
            property_timezone=property_timezone,
            property_settings=property_settings,
            organization_id=organization_id,
            **report_query,
        ).get_summary()

        logger.info(
            "Get Report Summary Data",
            extra=ReportLogSchema().dump(
                {
                    **report_query,
                    **dict(
                        action=ReportAction.SummaryView.value,
                        report_kind=ReportKind.Report.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return report_data

    @staticmethod
    def get_report_export(
        report_query: ReportQueryExportSchema,
        query_params: QueryReportExportSchema,
        property_id: int,
        organization_id: int,
        user: User,
        charts: list[Chart] = [],
        report_kind: str = ReportKind.Report.value,
    ):
        """Receive a report query json and return a link to download an exported file"""
        dataset = Dataset(DatasetEnum(report_query["dataset_id"]))
        AccessControlService.is_allowed_to_access_dataset(dataset.kind.value, user)

        report_title = report_query["title"]
        custom_cdfs = report_query.get("custom_cdfs") or []
        for custom_cdf in custom_cdfs:
            custom_cdf.pop("created_at", None)
            custom_cdf.pop("updated_at", None)

        report_kwargs = ReportQueryExportSchema(exclude=["dataset_id", "title"]).dump(
            report_query
        )
        custom_cdfs_kwargs = report_kwargs.get("custom_cdfs", [])
        custom_cdfs = (
            [
                ReportCustomCdfSchema(
                    only=("name", "column", "kind", "description")
                ).dump(custom_cdf_kwargs)
                for custom_cdf_kwargs in custom_cdfs_kwargs
            ]
            if custom_cdfs_kwargs
            else []
        )

        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        report_kwargs["custom_cdfs"] = InstrumentedList(
            [
                ReportCustomCdf(**custom_cdf)
                for custom_cdf in report_kwargs["custom_cdfs"] or []
            ]
        )

        chart_pngs = []
        if (
            charts
            and query_params.get("view") == View.Formatted.value
            and query_params.get("format") == Format.XLSX.value
        ):
            for chart in charts:
                chart_png = ChartService.get_png(
                    chart,
                    ReportClass(
                        mode=Mode.Export,
                        dataset=dataset,
                        format=InternalNumericFormat.RoundedFloat.value,
                        columns=chart.metrics,
                        custom_cdfs=report_kwargs.get("custom_cdfs", []),
                        custom_field_cdfs=report_kwargs.get("custom_field_cdfs", []),
                        group_rows=chart.categories,
                        group_columns=report_kwargs.get("group_columns", []),
                        filters=report_kwargs.get("filters", []),
                        property_ids=report_kwargs.get("property_ids", []),
                        organization_id=organization_id,
                        sort=report_kwargs.get("sort", []),
                        settings=dict(
                            transpose=False,
                            totals=True,
                            details=False,
                        ),
                        property_timezone=property_timezone,
                        property_settings=property_settings,
                        periods=report_kwargs.get("periods", []),
                        formats=report_kwargs.get("formats", []),
                        comparisons=report_kwargs.get("comparisons", []),
                    ).get_data(),
                    dataset.flatten_all_cdfs,
                    custom_cdfs,
                )
                if chart_png:
                    chart_pngs.append(chart_png)

        report_data = ReportClass(
            mode=Mode.Export,
            dataset=dataset,
            format=InternalNumericFormat.RoundedFloat.value,
            property_timezone=property_timezone,
            property_settings=property_settings,
            organization_id=organization_id,
            **report_kwargs,
        ).get_export(
            report_title,
            query_params["view"],
            query_params["format"],
            chart_pngs,
        )
        report_data["charts"] = chart_pngs

        logger.info(
            "Export Report",
            extra=ReportLogSchema().dump(
                {
                    **report_query,
                    **dict(
                        action=ReportAction.Export.value,
                        report_kind=report_kind,
                        property_id=property_id,
                    ),
                    **query_params,
                }
            ),
        )

        return report_data

    @staticmethod
    def get_report_data_by_id(
        report_id: int,
        numeric_format: str,
        property_id: int,
        organization_id: int,
        user: User,
    ):
        report = ReportService.get_by_id(report_id, user)
        dataset = Dataset(DatasetEnum(report.dataset_id))
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        report_data = ReportClass(
            mode=Mode.Run,
            dataset=dataset,
            custom_cdfs=report.custom_cdfs,
            custom_field_cdfs=report.custom_field_cdfs,
            columns=report.columns,
            group_rows=report.group_rows,
            group_columns=report.group_columns,
            filters=report.filters,
            property_ids=report.property_ids,
            organization_id=organization_id,
            sort=report.sort,
            settings=report.settings or dict(),
            format=numeric_format,
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=report.periods,
            formats=report.formats,
            comparisons=report.comparisons,
        ).get_data()

        logger.info(
            "Get Report Data",
            extra=ReportLogSchema().dump(
                {
                    **ReportSchema().dump(report),
                    **dict(
                        action=ReportAction.DataView.value,
                        report_kind=ReportKind.Report.value,
                        property_id=property_id,
                    ),
                }
            ),
        )
        return report_data

    @staticmethod
    def get_report_summary_by_id(
        report_id: int,
        numeric_format: str,
        property_id: int,
        organization_id: int,
        user: User,
    ):
        report = ReportService.get_by_id(report_id, user)
        dataset = Dataset(DatasetEnum(report.dataset_id))
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        report_data = ReportClass(
            mode=Mode.Run,
            dataset=dataset,
            custom_cdfs=report.custom_cdfs,
            custom_field_cdfs=report.custom_field_cdfs,
            columns=report.columns,
            group_rows=report.group_rows,
            group_columns=report.group_columns,
            filters=report.filters,
            property_ids=report.property_ids,
            organization_id=organization_id,
            sort=report.sort,
            settings=report.settings or dict(),
            format=numeric_format,
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=report.periods,
            formats=report.formats,
            comparisons=report.comparisons,
        ).get_summary()

        logger.info(
            "Get Report Summary Data",
            extra=ReportLogSchema().dump(
                {
                    **ReportSchema().dump(report),
                    **dict(
                        action=ReportAction.SummaryView.value,
                        report_kind=ReportKind.Report.value,
                        property_id=property_id,
                    ),
                }
            ),
        )

        return report_data

    @staticmethod
    def get_report_export_by_id(
        report_id: int,
        view: str,
        export_format: str,
        include_charts: bool,
        property_id: int,
        organization_id: int,
        user: User,
        property_ids: list[int] = None,
    ):
        logger.debug("Exporting report: get report by ID")
        report_model = ReportService.get_by_id(report_id, user)
        logger.debug("Exporting report: is allowed to access dataset")
        AccessControlService.is_allowed_to_access_dataset(report_model.dataset_id, user)
        logger.debug("Exporting report: dump report model")
        report = ReportSchema(partial=True).dump(report_model)
        report["custom_cdfs"] = report.get("custom_cdfs") or []

        dataset = Dataset(DatasetEnum(report["dataset_id"]))
        logger.debug("Exporting report: get timezone by ID")
        property_timezone = PropertyService.get_timezone_by_id(property_id)
        logger.debug("Exporting report: get all by property ID")
        property_settings = PropertySettingService.get_all_by_property_id(property_id)

        custom_cdfs = ReportCustomCdfSchema(
            only=("name", "column", "kind", "description")
        ).dump(report["custom_cdfs"], many=True)

        if include_charts:
            charts = report_model.charts
            logger.debug("Exporting report: get chart PNGs")
            chart_pngs = ReportService.get_chart_pngs(
                charts,
                report,
                view,
                export_format,
                dataset,
                property_ids,
                property_timezone,
                property_settings,
                custom_cdfs,
                organization_id,
            )
        else:
            chart_pngs = []

        logger.debug("Exporting report: get report export data")
        report_data = ReportService.get_report_export_data(
            report,
            dataset,
            property_timezone,
            property_settings,
            property_ids,
            organization_id,
        )

        report_data.custom_cdfs = InstrumentedList(
            [
                ReportCustomCdf(**custom_cdf)
                for custom_cdf in report_data.custom_cdfs or []
            ]
        )

        logger.debug("Exporting report: get export")

        report_data = report_data.get_export(
            report["title"], view, export_format, chart_pngs
        )

        logger.info(
            "Export Report",
            extra=ReportLogSchema().dump(
                {
                    **report,
                    **dict(
                        action=ReportAction.Export.value,
                        report_kind=ReportKind.Report.value,
                        view=view,
                        format=export_format,
                        property_id=property_id,
                    ),
                }
            ),
        )
        return report_data

    @staticmethod
    def get_reports_to_export(
        report_ids: list[int],
        property_ids: dict,
        include_charts: bool,
        organization_id: int,
        user: User,
    ):
        view = View.Formatted.value
        export_format = Format.XLSX.value
        reports_to_export = []
        for report_id in report_ids:
            report_property_ids = property_ids[str(report_id)]
            report_model = Report.get_by_id(report_id)
            report = ReportSchema(partial=True).dump(report_model)
            report["custom_cdfs"] = report.get("custom_cdfs") or []

            dataset = Dataset(DatasetEnum(report["dataset_id"]))
            property_timezone = PropertyService.get_timezone_by_id(
                report["property_id"]
            )
            property_settings = PropertySettingService.get_all_by_property_id(
                report["property_id"]
            )

            report_object = ReportClass(
                mode=Mode.Export,
                dataset=report_model.dataset,
                format=InternalNumericFormat.RoundedFloat.value,
                columns=report_model.columns,
                custom_cdfs=report_model.custom_cdfs,
                custom_field_cdfs=report_model.custom_field_cdfs,
                group_rows=report_model.group_rows,
                group_columns=report_model.group_columns,
                filters=report_model.filters,
                property_ids=report_model.property_ids,
                organization_id=organization_id,
                sort=report_model.sort,
                settings=report_model.settings,
                property_timezone=property_timezone,
                property_settings=property_settings,
                periods=report_model.periods,
                formats=report_model.formats,
                comparisons=report_model.comparisons,
            )

            custom_cdfs = ReportCustomCdfSchema(
                only=("name", "column", "kind", "description")
            ).dump(report["custom_cdfs"], many=True)

            if include_charts:
                charts = report_model.charts

                chart_pngs = ReportService.get_chart_pngs(
                    charts,
                    report,
                    view,
                    export_format,
                    dataset,
                    report_property_ids,
                    property_timezone,
                    property_settings,
                    custom_cdfs,
                    organization_id,
                )
            else:
                chart_pngs = []

            logger.info(
                "Export Report",
                extra=ReportLogSchema().dump(
                    {
                        **report,
                        **dict(
                            action=ReportAction.Export.value,
                            report_kind=ReportKind.Report.value,
                            user_id=user.id,
                        ),
                    }
                ),
            )

            report_data = ReportService.get_report_export_data(
                report,
                dataset,
                property_timezone,
                property_settings,
                report_property_ids,
                organization_id,
            )
            reports_to_export.append(
                dict(
                    report_id=report_id,
                    report_title=report["title"],
                    report_data=report_data,
                    chart_pngs=chart_pngs,
                    report=report_object,
                )
            )
        return reports_to_export

    @staticmethod
    def combine_exports(reports_to_export, s3_service) -> openpyxl.Workbook:
        workbook_data = BytesIO()
        writer = ExcelWriter(workbook_data, engine="openpyxl")
        for report_to_export in reports_to_export:
            report_title = report_to_export.get("report_title")
            report_data = report_to_export.get("report_data")
            chart_pngs = report_to_export.get("chart_pngs")
            report = report_to_export.get("report_data")
            view = "details"
            report_data.add_export_sheet(
                report_title, view, writer, s3_service, chart_pngs, report
            )

        writer.close()
        workbook_data = workbook_data.getvalue()

        return workbook_data

    @staticmethod
    def get_chart_pngs(
        charts: list,
        report: dict,
        view,
        export_format,
        dataset,
        property_ids,
        property_timezone,
        property_settings,
        custom_cdfs,
        organization_id: int,
    ):
        chart_pngs = []
        if (
            charts
            and view == View.Formatted.value
            and export_format == Format.XLSX.value
        ):
            for chart in charts:
                chart_png = ChartService.get_png(
                    chart,
                    ReportClass(
                        mode=Mode.Export,
                        dataset=dataset,
                        custom_cdfs=report["custom_cdfs"],
                        custom_field_cdfs=report["custom_field_cdfs"],
                        columns=chart.metrics,
                        group_rows=chart.categories,
                        group_columns=report["group_columns"],
                        filters=report["filters"],
                        property_ids=(
                            report["property_ids"]
                            if property_ids is None
                            else property_ids
                        ),
                        organization_id=organization_id,
                        sort=report["sort"],
                        settings=dict(transpose=False, totals=True, details=False),
                        format=InternalNumericFormat.RoundedFloat.value,
                        property_timezone=property_timezone,
                        property_settings=property_settings,
                        periods=report["periods"],
                        formats=report["formats"],
                        comparisons=report["comparisons"],
                    ).get_data(),
                    dataset.flatten_all_cdfs,
                    custom_cdfs,
                )
                if chart_png:
                    chart_pngs.append(chart_png)
        return chart_pngs

    @staticmethod
    def get_report_export_data(
        report,
        dataset,
        property_timezone,
        property_settings,
        property_ids,
        organization_id: int,
    ):
        report_data = ReportClass(
            mode=Mode.Export,
            dataset=dataset,
            custom_cdfs=report["custom_cdfs"],
            custom_field_cdfs=report["custom_field_cdfs"],
            columns=report["columns"],
            group_rows=report["group_rows"],
            group_columns=report["group_columns"],
            filters=report["filters"],
            property_ids=(
                report["property_ids"] if property_ids is None else property_ids
            ),
            organization_id=organization_id,
            sort=report["sort"],
            settings=report["settings"],
            format=InternalNumericFormat.RoundedFloat.value,
            property_timezone=property_timezone,
            property_settings=property_settings,
            periods=report["periods"],
            formats=report["formats"],
            comparisons=report["comparisons"],
        )
        return report_data

    @staticmethod
    def get_limits():
        return {
            "tabular_preview": TABULAR_PREVIEW_LIMIT,
            "tabular_run": TABULAR_RUN_LIMIT,
            "summary_preview": SUMMARY_PREVIEW_LIMIT,
            "summary_run": SUMMARY_RUN_LIMIT,
            "pivot_preview": PIVOT_PREVIEW_LIMIT,
            "pivot_run": PIVOT_RUN_LIMIT,
            "export": EXPORT_LIMIT,
        }

    @staticmethod
    def create_report(report: dict, user: User, property_id: int):
        if AccessControlService.is_allowed_to_create_dataset(
            report["dataset_id"], user, property_id
        ):

            return Report.create(**report, user_id=user.id)
