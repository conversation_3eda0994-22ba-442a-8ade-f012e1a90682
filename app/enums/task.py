from app.enums.values import ValuesEnum


class TaskNames(ValuesEnum):
    """Task names enum."""

    # Task types
    EXPORT_REPORT_BY_ID = "export_report_by_id"
    EXPORT_REPORT_BY_QUERY = "export_report_by_query"
    EXPORT_STOCK_REPORT_BY_ID = "export_stock_report_by_id"
    EXPORT_STOCK_REPORT_BY_QUERY = "export_stock_report_by_query"
    EMAIL_EXPORT_REPORT_BY_IDS = "email_export_report_by_ids"
    EXPORT_REPORTS_BY_IDS = "export_reports_by_ids"


class TaskStatus(ValuesEnum):
    STARTED = "STARTED"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
