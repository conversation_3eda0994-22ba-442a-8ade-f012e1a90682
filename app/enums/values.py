from enum import Enum


class ValuesEnum(Enum):
    """
    An Enum class to provide easy access to its values.

    This class extends Enum and adds a convenient method to retrieve all the values
    defined within the Enum.

    Usage:
        values_list = ValuesEnum.values()

    Example:
        from enum import Enum

        class MyEnum(ValuesEnum):
            VALUE_ONE = 'one'
            VALUE_TWO = 'two'
            VALUE_THREE = 'three'

        print(MyEnum.values())  # Output: ['one', 'two', 'three']
    """

    @classmethod
    def values(cls):
        """Returns a list of all the values defined within the Enum."""
        return [name.value for name in cls]
