from app.enums.values import ValuesEnum


class RelativeDate(ValuesEnum):
    """Static relative dates with implied deltas and relative dates
    with customizable durations
    """

    # Static Relative Dates
    Today = "today"
    Tomorrow = "tomorrow"
    Yesterday = "yesterday"
    StartCurrentWeek = "start_current_week"
    StartLastWeek = "start_last_week"
    StartNextWeek = "start_next_week"
    StartCurrentMonth = "start_current_month"
    StartLastMonth = "start_last_month"
    StartNextMonth = "start_next_month"
    StartCurrentQuarter = "start_current_quarter"
    StartLastQuarter = "start_last_quarter"
    StartNextQuarter = "start_next_quarter"
    StartCurrentYear = "start_current_year"
    StartLastYear = "start_last_year"
    StartNextYear = "start_next_year"

    # Relative dates with customizable durations
    DaysPrior = "days_prior"
    DaysLater = "days_later"
    WeeksPrior = "weeks_prior"
    WeeksLater = "weeks_later"
    MonthsPrior = "months_prior"
    MonthsLater = "months_later"
    QuartersPrior = "months_prior"
    QuartersLater = "months_later"
    YearsPrior = "years_prior"
    YearsLater = "years_later"
