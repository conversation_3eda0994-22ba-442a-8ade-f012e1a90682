from app.enums.values import ValuesEnum


class Dataset(ValuesEnum):
    Financial = 1
    Guests = 2
    Reservations = 3
    Occupancy = 4
    Payment = 5
    Invoices = 6
    OccupancyV1 = 7
    Housekeeping = 8
    Accounting = 9
    Payout = 10
    BedOccupancy = 11


class ValidPicklistCdfs(ValuesEnum):
    ReservationSourceCategory = "reservation_source_category"
    ReservationSource = "reservation_source"
    RoomTypes = "room_types"
    ReservationStatus = "reservation_status"
    User = "user"
