import enum


class FilterOperator(enum.Enum):
    Begins = "begins"
    NotBegins = "not_begins"
    Ends = "ends"
    NotEnds = "not_ends"
    Contains = "contains"
    NotContains = "not_contains"
    ListContains = "list_contains"
    NotListContains = "not_list_contains"
    IsNull = "is_null"
    IsNotNull = "is_not_null"
    IsEmpty = "is_empty"
    IsNotEmpty = "is_not_empty"
    Equals = "equals"
    NotEquals = "not_equals"
    GreaterThan = "greater_than"
    LessThan = "less_than"
    LessThanOrEqual = "less_than_or_equal"
    GreaterThanOrEqual = "greater_than_or_equal"
