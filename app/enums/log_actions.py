from enum import Enum


class FavoriteAction(Enum):
    Favorite = "favorite"
    Unfavorite = "unfavorite"


class ReportAction(Enum):
    DataView = "data_view"
    SummaryView = "summary_view"
    Export = "export"
    Create = "create"
    Update = "update"
    Delete = "delete"
    SaveAs = "save_as"


class EmailAction(Enum):
    EmailSent = "email_sent"
    EmailFailed = "email_failed"
    EmailNotSent = "email_not_sent"


class HubAction(Enum):
    Create = "create_hub"
    Read = "read_hub"
    Update = "update_hub"
    Delete = "delete_hub"
    AddCard = "add_card_hub"
    RemoveCard = "remove_card_hub"
