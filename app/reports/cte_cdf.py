from typing import Optional

from sqlalchemy import (
    DATE,
    DECIMAL,
    FLOAT,
    INTEGER,
    NUMERIC,
    TIME,
    TIMESTAMP,
    asc,
    case,
    cast,
    desc,
    func,
)
from sqlalchemy.orm.decl_api import DeclarativeMeta
from sqlalchemy.sql.elements import Label

from app.datasets.dataset import Dataset
from app.enums import (
    CdfKind,
    Dataset as DatasetEnum,
    Metric,
    NumericFormat,
    Sort,
)
from app.reports.base_cdf import BaseCDF
from app.reports.report_metadata import ReportMetadata


class CTECDF(BaseCDF):
    def __init__(
        self,
        model: DeclarativeMeta,
        cdf: dict,
        dataset_id: int,
        custom_cdf: Optional[dict] = None,
        custom_field_cdf: Optional[dict] = None,
        metric: Optional[str] = None,
        modifier: Optional[str] = None,
        dynamic: bool = False,
        to_char: bool = True,
        format: str = NumericFormat.Raw.value,
        property_settings: list[dict] = [],
        formats: Optional[dict] = None,
        custom_field_cdfs: Optional[list[dict]] = [],
        metadata: ReportMetadata = None,
        cte_meta: Optional[dict] = None,
        raw_group_for_sort: bool = False,
    ):
        super().__init__(
            model=model,
            cdf=cdf,
            dataset_id=dataset_id,
            custom_cdf=custom_cdf,
            custom_field_cdf=custom_field_cdf,
            metric=metric,
            modifier=modifier,
            dynamic=dynamic,
            to_char=to_char,
            format=format,
            property_settings=property_settings,
            formats=formats,
            custom_field_cdfs=custom_field_cdfs,
            metadata=metadata,
            raw_group_for_sort=raw_group_for_sort,
        )
        self.cte_meta = cte_meta

    def get_cdf(self, skip_aggregation: bool = False) -> Label:
        dataset = Dataset(DatasetEnum(self.dataset_id))
        cdf_kind = (
            dataset.model.__dict__[self.column].info["kind"].name
            if self.column in dataset.model.__dict__
            else None
        )
        if self.column == self.STAR_CDF:
            return func.count(self.STAR_CDF).label(self.label)
        cdf = getattr(self.model.c, self.column, None)
        # special handling for dynamic custom cdfs
        if self.custom_cdf and self.custom_cdf.kind == CdfKind.Dynamic.name:
            cdf = self._get_custom(skip_aggregation, column_collection=self.model.c)
        match self.column:
            case "adr_summary":
                cdf = cast(
                    func.sum(cast(self.model.c.room_revenue, NUMERIC))
                    / (
                        func.nullif(
                            (func.sum(cast(self.model.c.rooms_sold, NUMERIC))),
                            0,
                        )
                    ),
                    FLOAT,
                )
            case "revpar_summary":
                cdf = cast(
                    func.sum(cast(self.model.c.room_revenue, NUMERIC))
                    / func.nullif(
                        func.sum(cast(self.model.c.capacity_count, NUMERIC)), 0
                    ),
                    FLOAT,
                )
            case "occupancy_summary":
                cdf = cast(
                    func.sum(cast(self.model.c.rooms_sold, NUMERIC))
                    / (
                        (
                            func.nullif(
                                func.sum(cast(self.model.c.capacity_count, NUMERIC)),
                                0,
                            )
                        )
                        * 100
                    ),
                    FLOAT,
                )
            case "mfd_occupancy_summary":
                cdf = cast(
                    cast(
                        func.sum(cast(self.model.c.rooms_sold, NUMERIC))
                        / (
                            func.nullif(
                                func.sum(cast(self.model.c.capacity_count, NUMERIC))
                                - func.sum(
                                    cast(self.model.c.blocked_room_count, NUMERIC)
                                )
                                - func.sum(
                                    cast(self.model.c.out_of_service_count, NUMERIC)
                                ),
                                0,
                            )
                        ),
                        NUMERIC,
                    )
                    * cast(100, NUMERIC),
                    FLOAT,
                )
            case "unassigned_accommodations_summary":
                room_types_count = case(
                    (self.model.c.room_types.is_(None), 0),
                    (func.trim(self.model.c.room_types) == "", 0),
                    else_=func.array_length(
                        func.string_to_array(self.model.c.room_types, ","), 1
                    ),
                )
                room_numbers_count = case(
                    (self.model.c.room_numbers.is_(None), 0),
                    (func.trim(self.model.c.room_numbers) == "", 0),
                    else_=func.array_length(
                        func.string_to_array(self.model.c.room_numbers, ","), 1
                    ),
                )
                unassigned_accomodation_expr = cast(
                    room_types_count - room_numbers_count, INTEGER
                )

                cdf = func.sum(unassigned_accomodation_expr)
            case "unassigned_accommodation_summary":
                cdf = func.count(self.model.c.room_type) - func.sum(
                    case((self.model.c.room_number.is_(None), 0), else_=1)
                )
            case _:
                pass

        if cdf_kind == "Date":
            cdf = cast(cdf, DATE)
        if self.metric:
            function = getattr(func, self._get_db_metric_function(self.metric))

            if isinstance(cdf.type, INTEGER) and self.metric in [
                Metric.sum.name,
                Metric.max.name,
                Metric.min.name,
            ]:
                return func.cast(function(cdf), INTEGER).label(self.label)

            if self.numeric_format:
                return func.to_char(
                    func.cast(function(cdf), DECIMAL(38, 2)), self.numeric_format
                ).label(self.label)

            return function(cdf).label(self.label)

        if isinstance(cdf.type, (DATE, TIME, TIMESTAMP)):
            cdf = self._cast_dates_cdf(cdf)

        if "direction" in self.cdf:
            cdf = desc(cdf) if self.cdf["direction"] == Sort.desc.value else asc(cdf)

        return cdf.label(self.label)
