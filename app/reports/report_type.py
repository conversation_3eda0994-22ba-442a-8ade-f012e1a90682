from typing import Optional


class ReportType:
    List = "List"
    Summary = "Summary"
    Pivot = "Pivot"
    PeriodList = "PeriodList"
    PeriodSummary = "PeriodSummary"
    ListComparison = "ListComparison"
    SummaryComparison = "SummaryComparison"

    def __init__(
        self,
        group_rows: list = None,
        group_columns: list = None,
        periods: Optional[list] = None,
        comparisons: Optional[list] = None,
    ) -> None:
        self.type = self.__get_type(group_rows, group_columns, periods, comparisons)

    def __get_type(
        self, group_rows: list, group_columns: list, periods: list, comparisons: list
    ) -> str:
        if group_rows and not group_columns and not periods and not comparisons:
            return self.Summary

        if group_rows and not group_columns and periods:
            return self.PeriodSummary

        if group_rows and not group_columns and comparisons:
            return self.SummaryComparison

        if group_rows and group_columns:
            return self.Pivot

        if not group_rows and not group_columns and periods:
            return self.PeriodList

        if not group_rows and not group_columns and comparisons:
            return self.ListComparison

        return self.List
