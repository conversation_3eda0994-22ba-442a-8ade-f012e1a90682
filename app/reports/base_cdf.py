import re
from collections import OrderedDict
from datetime import timed<PERSON>ta
from types import SimpleNamespace
from typing import Optional

from flask import current_app as app, g


from sqlalchemy import (
    BIGINT,
    DATE,
    DECIMAL,
    FLOAT,
    INTEGER,
    NUMERIC,
    TIME,
    TIMESTAMP,
    VARCHAR,
    and_,
    asc,
    case,
    desc,
    func,
    literal,
    literal_column,
    or_,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm.decl_api import DeclarativeMeta
from sqlalchemy.sql.elements import Label
from sqlalchemy.sql.functions import concat

from app.common.constants.dynamic_cdf_label import DYNAMIC_CDF_LABEL
from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.constants.property_setting import DEFAULT_ROUNDING_PRECISION
from app.common.constants.raw_group_for_sort import RAW_GROUP_FOR_SORT_SUFFIX
from app.common.constants.report_formats import (
    DEFAULT_DATE_FORMAT,
    MONTH_NUMERIC_FORMAT,
)
from app.common.logger import logger
from app.datasets.custom_field import CustomField
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums import (
    Cdf,
    CdfKind,
    CustomCdfFormulaKind,
    Dataset as DatasetEnum,
    GroupModifier,
    Metric,
    MultiLevel as MultiLevelEnum,
    NumericFormat,
    Sort,
)
from app.enums.custom_cdf_numeric_formats import CustomCdfNumericFormats
from app.enums.custom_fields import CustomFields as CustomFieldsEnum
from app.enums.export import Format
from app.enums.filter_operator import FilterOperator
from app.enums.format import InternalNumericFormat
from app.enums.mode import Mode
from app.enums.operator import Operator
from app.enums.property_setting import WeekDays
from app.models.report import ReportCustomFieldCdf, ReportCustomFieldCdfProperty
from app.reports.report_metadata import ReportMetadata
from app.services.custom_field_service import CustomFieldService
from app.services.property_service import PropertyService
from app.subscriptable_simple_namespace import (
    SubscriptableSimpleNamespace,
)


class BaseCDF:
    STAR_CDF = "*"

    COALESCE = ""  # Replace Nulls with empty char, making possible the concatenation

    CASTS = {
        DATE.__visit_name__: "YYYY-MM-DD",
        TIMESTAMP.__visit_name__: "YYYY-MM-DD HH24:MI:SS",
        TIME.__visit_name__: "HH24:MI:SS",
        GroupModifier.Second.value: "YYYY-MM-DD HH24:MI:SS",
        GroupModifier.Minute.value: "YYYY-MM-DD HH24:MI",
        GroupModifier.Hour.value: "YYYY-MM-DD HH24",
        GroupModifier.Day.value: "YYYY-MM-DD",
        GroupModifier.Month.value: "YYYY-MM",
        GroupModifier.Year.value: "YYYY",
        GroupModifier.Week.value: "Mon DD, YYYY",
        GroupModifier.WeekDay.value: "Dy",
        GroupModifier.DayWithoutYear.value: "MM-DD",
        GroupModifier.MonthWithoutYear.value: "Mon",
        GroupModifier.TimeHour.value: "HH24",
        GroupModifier.TimeMinute.value: "HH24:MI",
        GroupModifier.TimeSecond.value: "HH24:MI:SS",
    }

    WHOLE_NUMBER_FORMAT_STRING = "FM999,999,999,999,999,999,999,999,999,999,999,999,990"

    NUMERIC_FORMAT_STRINGS = {
        NumericFormat.Raw.value: None,
        NumericFormat.Formatted.value: f"{WHOLE_NUMBER_FORMAT_STRING}.00",
    }

    def __init__(
        self,
        model: DeclarativeMeta,
        cdf: dict,
        dataset_id: int,
        custom_cdf: Optional[dict] = None,
        custom_field_cdf: Optional[dict] = None,
        metric: Optional[str] = None,
        modifier: Optional[str] = None,
        dynamic: bool = False,
        to_char: bool = True,
        format: str = NumericFormat.Raw.value,
        property_settings: list[dict] = [],
        formats: Optional[dict] = None,
        custom_field_cdfs: Optional[list[dict]] = [],
        metadata: ReportMetadata = None,
        raw_group_for_sort: bool = False,
        comparison_name: str = None,
        comparison_cases: list = None,
    ):
        self.model = model
        self.cdf = cdf
        self.dataset_id = dataset_id
        self.custom_cdf = custom_cdf
        self.custom_field_cdf = custom_field_cdf
        self.metric = metric
        self.modifier = modifier
        self.dynamic = dynamic
        self.to_char = to_char
        self.property_settings = {
            setting["name"]: setting["value"] for setting in property_settings
        }
        self.number_format = format
        self.date_format = (
            formats.get("date")
            if formats
            else (None if self.modifier not in ("week",) else DEFAULT_DATE_FORMAT)
        )
        self.link_format = formats.get("link") if formats else False
        self.function_args = dict(currency=formats.get("currency") if formats else None)
        self.custom_field_cdfs = custom_field_cdfs
        self.metadata = metadata
        self.raw_group_for_sort = raw_group_for_sort
        self.comparison_name = comparison_name if comparison_name is not None else ""
        self.comparison_cases = comparison_cases

    def __repr__(self):
        """Representation of ReportCDF
        :return: string
        """
        return f"<ReportCDF, model={self.model}, cdf={self.cdf}, metric={self.metric}, modifier={self.modifier}, dynamic={self.dynamic}, format={self.numeric_format}>"

    @property
    def column(self) -> str:
        return (
            self.cdf["cdf"]["column"]
            if self.dynamic is False
            else f"{self.cdf['cdf']['column']}_summary"
        )

    @property
    def label(self) -> str:
        if self.metric:
            label = (
                f"{self.comparison_name} - {self.column} - {self.metric}"
                if self.comparison_name
                else f"{self.column} - {self.metric}"
            )
        elif self.dynamic:
            label = (
                f"{self.comparison_name} - {self.cdf['cdf']['column']} - {DYNAMIC_CDF_LABEL}"
                if self.comparison_name
                else f"{self.cdf['cdf']['column']} - {DYNAMIC_CDF_LABEL}"
            )
        else:
            label = self.column

        if self.raw_group_for_sort:
            label = f"{label}{RAW_GROUP_FOR_SORT_SUFFIX}"

        return label

    @property
    def numeric_format(self) -> str:
        """Returns the numeric format string for the cdf"""
        if self.number_format == NumericFormat.Raw.value:
            return None
        match self.cdf["cdf"]["type"]:
            case Cdf.Default.value:
                if self.cdf["cdf"].get("multi_level_id"):
                    model = MultiLevel(
                        MultiLevelEnum(self.cdf["cdf"]["multi_level_id"])
                    ).model
                else:
                    model = Dataset(DatasetEnum(self.dataset_id)).model
                cdf_kind = getattr(model, self.column).info["kind"].name
            case Cdf.Custom.value:
                cdf_kind = self.custom_cdf.kind
                if cdf_kind in (CdfKind.Dynamic.name, CdfKind.Number.name):
                    if hasattr(self.custom_cdf, "format"):
                        cdf_kind = (
                            CdfKind[
                                CustomCdfNumericFormats(self.custom_cdf.format).name
                            ].name
                            if self.custom_cdf.format
                            else CdfKind.Number.name
                        )
                    else:
                        cdf_kind = CdfKind.Number.name
            case _:
                cdf_kind = None
        match cdf_kind:
            case CdfKind.Number.name:
                return (
                    self.NUMERIC_FORMAT_STRINGS[self.number_format]
                    if self.number_format in self.NUMERIC_FORMAT_STRINGS
                    else None
                )
            case CdfKind.Currency.name | CdfKind.DynamicCurrency.name:
                return (
                    f"{self.WHOLE_NUMBER_FORMAT_STRING}"
                    f"{'.' + '0' * self.rounding_precision if self.rounding_precision else ''}"
                )
            case CdfKind.DynamicPercentage.name | CdfKind.Percentage.name:
                return f"{self.WHOLE_NUMBER_FORMAT_STRING}.00"
            case _:
                return None

    @property
    def start_of_week(self) -> str:
        return self.property_settings.get("start_of_week", WeekDays.Monday.name)

    @property
    def rounding_precision(self) -> int:
        return int(
            self.property_settings.get(
                "currency_cdf_rounding_precision",
                DEFAULT_ROUNDING_PRECISION,
            )
        )

    def get_cdf(self, skip_aggregation: bool = False) -> Label:  # noqa: C901
        type = self.cdf["cdf"]["type"]
        if type == Cdf.CustomField.value:
            cdf = self.__get_custom_field()
        if type == Cdf.Default.value:
            cdf = self.__get_default()
            cdf_kind = (
                self.model.__dict__[self.column].info["kind"].name
                if self.column in self.model.__dict__
                else None
            )

        if type == Cdf.Custom.value:
            if not self.to_char:
                column = literal_column(self.column)

                if "direction" in self.cdf:
                    cdf = self._get_custom(skip_aggregation, sort=True)
                    column = (
                        desc(cdf)
                        if self.cdf["direction"] == Sort.desc.value
                        else asc(cdf)
                    )
                elif skip_aggregation:
                    column = self._get_custom(skip_aggregation)

                return column.label(self.label)

            cdf = self._get_custom(skip_aggregation)
            cdf_kind = (
                CustomCdfNumericFormats(self.custom_cdf.format).name
                if self.custom_cdf.format
                else None
            )

        if self.metric:
            function = getattr(func, self._get_db_metric_function(self.metric))

            if self.metric == Metric.count.name:
                return func.cast(function(cdf), INTEGER).label(self.label)

            if isinstance(cdf.type, INTEGER) and self.metric in [
                Metric.sum.name,
                Metric.max.name,
                Metric.min.name,
            ]:
                return func.cast(function(cdf), INTEGER).label(self.label)

            if (
                self.numeric_format
                and self.number_format != InternalNumericFormat.RoundedFloat.value
            ):
                return func.to_char(
                    func.cast(function(cdf), DECIMAL(38, 2)), self.numeric_format
                ).label(self.label)
            elif self.number_format == InternalNumericFormat.RoundedFloat.value:

                return func.cast(
                    func.cast(
                        function(cdf),
                        DECIMAL(
                            38,
                            (
                                self.rounding_precision
                                if cdf_kind
                                in [CdfKind.Currency.name, CdfKind.DynamicCurrency.name]
                                else 2
                            ),
                        ),
                    ),
                    FLOAT,
                ).label(self.label)
            return function(cdf).label(self.label)

        # ensure that cte queries will not cast dates so the cte cdfs can calculate dynamically
        if isinstance(cdf.type, (DATE, TIME, TIMESTAMP)) and (
            not self.metadata or not self.metadata.cte_query
        ):
            cdf = self._cast_dates_cdf(cdf)

        # CAST BIGINT to VARCHAR so javascript can handle it without losing precision
        if isinstance(cdf.type, BIGINT):
            cdf = func.cast(cdf, VARCHAR)

        if (
            isinstance(cdf.type, (FLOAT, NUMERIC))
            and self.numeric_format
            and self.to_char
            and self.number_format != InternalNumericFormat.RoundedFloat.value
        ):
            cdf = func.to_char(cdf, self.numeric_format)
        elif (
            isinstance(cdf.type, (FLOAT, NUMERIC))
            and self.numeric_format
            and self.to_char
            and self.number_format == InternalNumericFormat.RoundedFloat.value
        ):
            cdf = func.cast(
                func.cast(
                    cdf,
                    DECIMAL(
                        38,
                        (
                            self.rounding_precision
                            if cdf_kind
                            in [CdfKind.Currency.name, CdfKind.DynamicCurrency.name]
                            else 2
                        ),
                    ),
                ),
                FLOAT,
            )

        if (
            self.link_format
            and hasattr(cdf, "info")
            and cdf.info["kind"]
            in (
                CdfKind.ReservationLink,
                CdfKind.GuestLink,
            )
            and self.dataset_id
            in (DatasetEnum.Reservations.value, DatasetEnum.Financial.value)
        ):
            cdf = self.__format_link(cdf)

        if "direction" in self.cdf:
            cdf = desc(cdf) if self.cdf["direction"] == Sort.desc.value else asc(cdf)

        return cdf.label(self.label)

    def __get_default(self) -> Label:
        if self.column == self.STAR_CDF:
            column = self.STAR_CDF
        else:
            column = (
                self.model.__dict__[self.column].info["function"](
                    self.model, **self.function_args
                )
                if "function" in self.model.__dict__[self.column].info
                else getattr(self.model, self.column)
            )

        if self.comparison_cases and self.cdf.get("dynamic"):
            hybrid_method_name = f"{self.column}_cases"
            hybrid_method = getattr(self.model, hybrid_method_name, None)

            if hybrid_method is None:
                raise AttributeError(
                    f"Expected a hybrid method named '{hybrid_method_name}' on '{self.model.__name__}'"
                )
            column = hybrid_method(
                self.comparison_cases, **self.function_args
            ).expression
        else:
            column = (
                column
                if self.comparison_cases is None
                else self.__wrap_column_with_case(column)
            )

        return column

    def _get_custom(
        self, skip_aggregation: bool = False, sort: bool = False, column_collection=None
    ) -> Label:
        if column_collection is None:
            column_collection = self.model.__table__.c

        match (self.custom_cdf.kind):
            case CdfKind.String.name:
                custom_cdf = self.__get_custom_string()
            case CdfKind.Number.name:
                custom_cdf = self.__get_custom_number(sort)
            case CdfKind.Dynamic.name:
                custom_cdf = self.__get_custom_dynamic(
                    skip_aggregation, column_collection=column_collection
                )
        if (
            self.number_format == InternalNumericFormat.RoundedFloat.value
            and hasattr(custom_cdf, "type")
            and custom_cdf.type.__visit_name__ == "FLOAT"
        ):
            custom_cdf = func.cast(
                func.cast(
                    custom_cdf,
                    DECIMAL(
                        38,
                        (
                            self.rounding_precision
                            if self.custom_cdf.format
                            == CustomCdfNumericFormats.Currency.value
                            else 2
                        ),
                    ),
                ),
                FLOAT,
            )
        return custom_cdf

    def __get_custom_string(self) -> Label:
        custom = None

        for item in self.custom_cdf.formula:
            if item["kind"] == CustomCdfFormulaKind.Cdf.value:

                value = item["value"]

                # TODO: In order to be able to have Custom CDF with Dynamic CDF we would need to pull the formula
                if "function" in self.model.__dict__[value].info:
                    value = self.model.__dict__[value].info["function"](
                        self.model, **self.function_args
                    )
                else:
                    value = self.model.__table__.c[value]

                if isinstance(value.type, (DATE, TIMESTAMP)):
                    value = func.to_char(value, self.CASTS[value.type.__visit_name__])
                if (
                    isinstance(value.type, (FLOAT, DECIMAL, NUMERIC))
                    and self.numeric_format
                ):
                    value = func.to_char(value, self.numeric_format)
                value = func.cast(value, VARCHAR)
                value = func.coalesce(value, self.COALESCE)
            elif item["kind"] == CustomCdfFormulaKind.CustomFieldCdf.value:
                model = CustomField(CustomFieldsEnum(self.dataset_id)).model
                custom_field_cdf = BaseCDF(
                    model,
                    dict(cdf=dict(column=item["value"], type=Cdf.CustomField.value)),
                    self.dataset_id,
                    custom_field_cdf=next(
                        (
                            ReportCustomFieldCdf(
                                column=custom_field["column"],
                                properties=[
                                    ReportCustomFieldCdfProperty(
                                        internal_name=custom_field_property[
                                            "internal_name"
                                        ],
                                        property_id=custom_field_property[
                                            "property_id"
                                        ],
                                    )
                                    for custom_field_property in [
                                        (property)
                                        for property in custom_field["properties"]
                                    ]
                                ],
                            )
                            for custom_field in [
                                (
                                    custom_field
                                    if isinstance(custom_field, (dict, OrderedDict))
                                    else {
                                        "name": custom_field.name,
                                        "column": custom_field.column,
                                        "properties": [
                                            {
                                                "property_id": prop.property_id,
                                                "internal_name": prop.internal_name,
                                            }
                                            for prop in custom_field.properties
                                        ],
                                    }
                                )
                                for custom_field in self.custom_field_cdfs
                            ]
                            if custom_field["column"] == item["value"]
                        ),
                        None,
                    ),
                    custom_field_cdfs=self.custom_field_cdfs,
                )
                value = custom_field_cdf.get_cdf()
                value = func.coalesce(value, self.COALESCE)

            elif item["kind"] == CustomCdfFormulaKind.Separator.value:
                value = literal(item["value"])

            elif item["kind"] == CustomCdfFormulaKind.Case.value:

                default_expr = self.__parse_result(item["default_case"])
                case_items = []
                for c in item["cases"]:
                    when_expr = self.__parse_condition(c["when"])
                    then_expr = self.__parse_result(c["then"])
                    case_items.append((when_expr, then_expr))

                value = case(*case_items, else_=default_expr).label(
                    self.custom_cdf.column
                )

            custom = custom + value if custom is not None else value.cast(VARCHAR)

        return custom

    def __get_custom_number(self, sort: bool = False) -> Label:
        custom = None
        denominator_close_indexes = []
        for index, item in enumerate(self.custom_cdf.formula):
            value = item["value"]
            kind = item["kind"]
            no_cdfs = True
            if kind == CustomCdfFormulaKind.Cdf.value:
                no_cdfs = False

                if "function" in self.model.__dict__[value].info:
                    value = self.model.__dict__[value].info["function"](
                        self.model, **self.function_args
                    )

                    if self.comparison_cases is not None:
                        value = self.__wrap_column_with_case(value)

                    value = value.compile(
                        compile_kwargs={"literal_binds": True, "dialect": postgresql}
                    ).string
                elif self.comparison_cases is not None:
                    column = self.model.__table__.c[value]
                    value = self.__wrap_column_with_case(column)
                    value = value.compile(
                        compile_kwargs={"literal_binds": True, "dialect": postgresql}
                    ).string
                else:
                    value = self.model.__table__.c[value]

            if (
                kind == CustomCdfFormulaKind.Operator.value
                and value == Operator.divide.value
            ):
                value = "/ nullif("
                parentheses_open = 0
                for i in range(index + 1, len(self.custom_cdf.formula)):
                    if (
                        self.custom_cdf.formula[i]["kind"]
                        == CustomCdfFormulaKind.Parenthesis.value
                        and self.custom_cdf.formula[i]["value"] == "("
                    ):
                        parentheses_open += 1

                    if (
                        self.custom_cdf.formula[i]["kind"]
                        == CustomCdfFormulaKind.Parenthesis.value
                        and self.custom_cdf.formula[i]["value"] == ")"
                    ):
                        parentheses_open -= 1

                    if not parentheses_open:
                        denominator_close_indexes.append(i)
                        break
            if kind == CustomCdfFormulaKind.Case.value:

                default_expr = self.__parse_result(item["default_case"])
                case_items = []
                for c in item["cases"]:
                    when_expr = self.__parse_condition(c["when"])
                    then_expr = self.__parse_result(c["then"])
                    case_items.append((when_expr, then_expr))

                value = (
                    self.__wrap_column_with_case(case(*case_items, else_=default_expr))
                    if self.comparison_cases
                    else case(*case_items, else_=default_expr)
                )

                value = (
                    func.cast(value, FLOAT)
                    .compile(
                        compile_kwargs={"literal_binds": True, "dialect": postgresql}
                    )
                    .string
                )

            # ensure postgres treats numeric values as numeric
            value = (
                f"{value}::FLOAT"
                if (isinstance(value, (str,)) and bool(re.match(r"^\d*\.?\d*$", value)))
                else value
            )

            if index in denominator_close_indexes:
                value = f"{value}, 0)"

            custom = f"{custom} {value}" if index != 0 else f"{value}"

        custom = (
            literal_column(
                self.__wrap_column_with_case(literal(custom, type_=FLOAT))
                .compile(compile_kwargs={"literal_binds": True, "dialect": postgresql})
                .string
            )
            if self.comparison_cases and no_cdfs
            else literal_column(custom)
        )
        custom.type = FLOAT()
        return custom

    def __get_custom_dynamic(
        self, skip_aggregation: bool, column_collection: dict
    ) -> Label:
        custom = None
        denominator_close_indexes = []
        no_cdfs = True
        for index, item in enumerate(self.custom_cdf.formula):
            value = item["value"]
            kind = item["kind"]
            metric = item.get("metric")

            if kind == CustomCdfFormulaKind.Cdf.value:
                no_cdfs = False

                if "function" in column_collection[value].info:
                    value = column_collection[value].info["function"](
                        self.model, **self.function_args
                    )
                    if self.comparison_cases is not None:
                        value = self.__wrap_column_with_case(value)
                elif self.comparison_cases is not None:
                    column = column_collection[value]
                    value = self.__wrap_column_with_case(column)
                else:
                    value = column_collection[value]

                match (metric):
                    case Metric.count.name:
                        value = (
                            "1::NUMERIC"
                            if skip_aggregation
                            else func.cast(func.count(value), NUMERIC)
                        )
                    case Metric.sum.name:
                        value = (
                            value
                            if skip_aggregation
                            else func.cast(func.sum(value), NUMERIC)
                        )
                    case Metric.mean.name:
                        value = (
                            value
                            if skip_aggregation
                            else func.cast(func.avg(value), NUMERIC)
                        )
                    case Metric.std.name:
                        value = value = (
                            "0::NUMERIC"
                            if skip_aggregation
                            else func.cast(func.stddev(value), NUMERIC)
                        )
                    case Metric.max.name:
                        value = (
                            value
                            if skip_aggregation
                            else func.cast(func.max(value), NUMERIC)
                        )
                    case Metric.min.name:
                        value = (
                            value
                            if skip_aggregation
                            else func.cast(func.min(value), NUMERIC)
                        )
                    case Metric.var.name:
                        value = (
                            "0::NUMERIC"
                            if skip_aggregation
                            else func.cast(func.variance(value), NUMERIC)
                        )
                    case _:
                        pass

                value = (
                    value
                    if isinstance(value, str)
                    else value.compile(
                        compile_kwargs={"literal_binds": True, "dialect": postgresql}
                    ).string
                )

            if (
                kind == CustomCdfFormulaKind.Operator.value
                and value == Operator.divide.value
            ):
                value = "/ nullif("
                parentheses_open = 0
                for i in range(index + 1, len(self.custom_cdf.formula)):
                    if (
                        self.custom_cdf.formula[i]["kind"]
                        == CustomCdfFormulaKind.Parenthesis.value
                        and self.custom_cdf.formula[i]["value"] == "("
                    ):
                        parentheses_open += 1

                    if (
                        self.custom_cdf.formula[i]["kind"]
                        == CustomCdfFormulaKind.Parenthesis.value
                        and self.custom_cdf.formula[i]["value"] == ")"
                    ):
                        parentheses_open -= 1

                    if not parentheses_open:
                        denominator_close_indexes.append(i)
                        break

            # ensure postgres treats numeric values as numeric
            value = (
                f"{value}::NUMERIC"
                if (isinstance(value, (str,)) and bool(re.match(r"^\d*\.?\d*$", value)))
                else value
            )

            if index in denominator_close_indexes:
                value = f"{value}, 0)"

            custom = f"{custom} {value}" if index != 0 else f"{value}"
        custom = (
            func.max(self.__wrap_column_with_case(literal_column(custom)))
            if self.comparison_cases and no_cdfs
            else literal_column(custom)
        )
        custom.type = FLOAT()
        return custom

    def __wrap_column_with_case(self, column):
        return case(
            (and_(*self.comparison_cases), column),
            else_=None,
        )

    def _get_db_metric_function(self, metric: str) -> str:
        metrics = dict()
        metrics[Metric.mean.value] = "avg"
        metrics[Metric.std.value] = "stddev"
        metrics[Metric.var.value] = "variance"

        if metric in metrics:
            metric = metrics[metric]

        return metric

    def _cast_dates_cdf(self, cdf: dict) -> dict:
        cdf_type = cdf.type.__visit_name__

        if cdf_type == "TIME" and self.modifier:
            cast = self.CASTS[self.modifier]
        else:
            cast = self.CASTS[cdf_type]

        if self.date_format:
            cast = cast.replace(self.CASTS[DATE.__visit_name__], self.date_format)

        if self.modifier and self.modifier not in [
            GroupModifier.Week.value,
            GroupModifier.TimeHour.value,
            GroupModifier.TimeMinute.value,
            GroupModifier.TimeSecond.value,
        ]:
            cdf = func.date_trunc(self.modifier, cdf)

            if self.date_format:
                cast = self.CASTS[self.modifier].replace(
                    self.CASTS[DATE.__visit_name__], self.date_format
                )
            elif (
                self.number_format == NumericFormat.Raw.value
                and self.modifier == GroupModifier.MonthWithoutYear.value
            ):
                cast = MONTH_NUMERIC_FORMAT
            else:
                cast = self.CASTS[self.modifier]

        if self.modifier and self.modifier == GroupModifier.Week.value:
            cdf = self.__offset_cdf_to_start_of_week(cdf)

        if self.to_char and (
            not self.modifier or self.modifier != GroupModifier.Week.value
        ):
            cdf = func.to_char(cdf, cast)

        if self.to_char and self.modifier and self.modifier == GroupModifier.Week.value:
            cdf = self.__cast_group_by_week_cdf(cdf)

        if not self.to_char:
            if self.modifier == GroupModifier.MonthWithoutYear.value:
                cdf = self.__cast_group_month_without_year_cdf(cdf)
            elif self.modifier == GroupModifier.DayWithoutYear.value:
                cdf = self.__cast_group_day_without_year_cdf(cdf)
            elif (
                self.modifier is None
                and self.raw_group_for_sort
                and isinstance(cdf.type, TIMESTAMP)
            ):
                cdf = func.to_char(cdf, self.CASTS[TIMESTAMP.__visit_name__])

        return cdf

    def __offset_cdf_to_start_of_week(self, cdf: dict):
        """Gets the start of week for a cdf where the week starts on the property's start of week

        Args:
            cdf: a datetime cdf to move

        Returns:
            A cdf's new start of week day
        """
        # get number of days to offset, counting backwards from Monday as 0 days
        offset = WeekDays[self.start_of_week].value
        # move cdf up n days
        cdf = cdf + timedelta(days=offset)
        # get cdf's new start of week
        cdf = func.date_trunc(self.modifier, cdf)
        # move the cdf back n days
        cdf = cdf - timedelta(days=offset)
        return cdf

    def __cast_group_by_week_cdf(self, cdf: dict):
        """Casts a group-by-week cdf to show the range it contains

        Args:
            a cdf already updated to start on the right day
        Returns:
            A range of dates with days of week in parentheses like "Feb 07, 2019 - Feb 13, 2019 (Thu - Wed)"
        """

        cast = (
            self.CASTS[DATE.__visit_name__]
            if self.number_format == NumericFormat.Raw.value
            else self.CASTS[self.modifier]
        )  # casts a date to 3 letter month, 2 digit day a comma and 4 digit year

        weekday_cast = self.CASTS[
            GroupModifier.WeekDay.value
        ]  # casts a date to the 3 letter abbreviation for its day of the week

        # Using CASE statement to handle null dates
        cdf = case(
            (cdf.is_(None), NA_FILL_STRING),
            else_=func.concat(
                func.to_char(cdf, cast),
                " - ",
                func.to_char(cdf + timedelta(days=7) - timedelta(seconds=0.0001), cast),
                " (",
                func.to_char(cdf, weekday_cast),
                " - ",
                func.to_char(
                    cdf + timedelta(days=7) - timedelta(seconds=0.0001), weekday_cast
                ),
                ")",
            ),
        )

        return cdf

    def __cast_group_month_without_year_cdf(self, cdf: dict):
        return func.to_char(cdf, "MM")

    def __cast_group_day_without_year_cdf(self, cdf: dict):
        return func.to_char(cdf, "MM-DD")

    def __get_custom_field(self) -> Label:
        field_name = self.cdf["cdf"]["column"]
        cases = []
        for property in self.custom_field_cdf.properties:
            property = (
                property
                if isinstance(property, (dict, OrderedDict))
                else property.__dict__
            )

            custom_field = (
                CustomFieldService.get_custom_field_by_dataset_id_and_internal_name(
                    self.dataset_id,
                    property.get("internal_name"),
                    property.get("property_id"),
                )
            )
            if custom_field:
                cases.append(
                    (
                        self.model.property_id == property.get("property_id"),
                        literal_column(f"custom_string_{custom_field.creation_order}"),
                    )
                )
            else:
                logger.warning(
                    f"Custom Field {property.get('internal_name')} was not found for property id {property.get('property_id')}"
                )

        if not cases:
            logger.warning(
                f"Custom Field {field_name} does not have a valid mapping for any property in the report"
            )
            cases = [(False, None)]
        case_statement = case(*cases, else_=None).label(field_name)

        return case_statement

    def __format_link(self, cdf: dict) -> concat:
        """Function that based on the CDF will return the link of it"""
        subdomain = PropertyService.get_property_sub_domain(g.property_id)

        if self.dataset_id == DatasetEnum.Reservations.value:
            reservation_id = self.model.id
        else:
            reservation_id = self.model.booking_id

        base_url = func.concat(
            (
                f"https://{subdomain}.{g.island}."
                if g.island
                else f"https://{subdomain}."
            ),
            app.config["MFD_DOMAIN"],
            "/connect/",
            self.model.property_id,
            "#/reservations/",
            reservation_id,
            "?display=",
            func.replace(cdf, " ", "%20"),
            "&reservation_id=",
            reservation_id,
        )

        if cdf.info["kind"] == CdfKind.GuestLink:
            guest_id = (
                self.model.primary_guest_id
                if self.dataset_id == DatasetEnum.Reservations.value
                else self.model.guest_id
            )
            base_url = func.concat(base_url, "&guest_id=", guest_id)

        if (
            self.metadata
            and self.metadata.mode == Mode.Export
            and self.metadata.format == Format.XLSX
        ):
            return func.concat('=HYPERLINK("', base_url, '", "', cdf, '")')

        return base_url

    def __parse_result(self, obj):
        result_kind = obj["kind"]
        result_value = obj["value"]
        if result_kind == "cdf":
            return self.model.__table__.c[result_value]
        elif result_kind == "String":
            return literal(result_value)
        elif result_kind == "null":
            return literal(None)
        elif result_kind == "formula":
            custom_cdf = BaseCDF(
                self.model,
                dict(
                    cdf=SubscriptableSimpleNamespace(
                        **dict(
                            type="custom",
                            column=self.custom_cdf.column,
                            description=self.custom_cdf.description,
                            format=self.custom_cdf.format,
                            kind=self.custom_cdf.kind,
                            formula=result_value,
                        )
                    )
                ),
                self.dataset_id,
                SimpleNamespace(
                    **dict(
                        kind=self.custom_cdf.kind,
                        format=self.custom_cdf.format,
                        formula=result_value,
                    )
                ),
            ).get_cdf()
            return custom_cdf
        return literal(result_value)

    # Helper: parse a 'when condition' object, might be nested, might be the condition itself
    def __parse_condition(self, condition):
        if "and_" in condition:
            return and_(
                *(
                    self.__parse_condition(nested_condition)
                    for nested_condition in condition["and_"]
                )
            )
        elif "or_" in condition:
            return or_(
                *(
                    self.__parse_condition(nested_condition)
                    for nested_condition in condition["or_"]
                )
            )
        # condition node
        column = self.model.__table__.c[condition["field"]]
        operator = condition["operator"]

        case_value = condition.get("case_value")

        # If 'value' is another cdf, treat it as column; otherwise literal
        if case_value.get("kind") == "cdf":
            val = self.model.__table__.c[case_value["value"]]
        else:
            val = literal(case_value["value"])

        match operator:
            case FilterOperator.IsNull.value:
                return column.is_(None)
            case FilterOperator.IsNotNull.value:
                return column.isnot(None)
            case FilterOperator.Equals.value:
                return column == val
            case FilterOperator.NotEquals.value:
                return column != val
            case FilterOperator.ListContains.value:
                return column.in_(val)
            case FilterOperator.NotListContains.value:
                return column.notin_(val)
            case FilterOperator.IsEmpty.value:
                return column == ""
            case FilterOperator.IsNotEmpty.value:
                return column != ""
            case FilterOperator.LessThan.value:
                return column < val
            case FilterOperator.LessThanOrEqual.value:
                return column <= val
            case FilterOperator.GreaterThan.value:
                return column > val
            case FilterOperator.GreaterThanOrEqual.value:
                return column >= val
            case FilterOperator.Begins.value:
                return column.ilike(f"{val}%")
            case FilterOperator.NotBegins.value:
                return ~column.ilike(f"{val}%")
            case FilterOperator.Ends.value:
                return column.ilike(f"%{val}")
            case FilterOperator.NotEnds.value:
                return ~column.ilike(f"%{val}")
            case FilterOperator.Contains.value:
                return column.ilike(f"%{val}%")
            case FilterOperator.NotContains.value:
                return ~column.ilike(f"%{val}%")

            case _:
                raise ValueError(f"Unsupported operator {operator}")
