from datetime import datetime, timezone

from sqlalchemy import or_

from app.cdfs.cdf import CDF
from app.common.constants.datetime import DEFAULT_DATETIME_DATE_FORMAT
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.periods import get_relative_start_date_from_end_date
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums import FilterOperator as Operator
from app.enums.cdf_kind import CdfKind
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.enums.relative_date import RelativeDate as RelativeDateEnum
from app.filters.relative_date import RelativeDate
from app.multi_levels.occupancy_reservation import (
    OccupancyReservationsFFView,
    OccupancyReservationsView,
)


class ReportCases:
    OPERATORS = {
        Operator.Equals.value: "eq",
        Operator.GreaterThan.value: "gt",
        Operator.LessThan.value: "lt",
        Operator.GreaterThanOrEqual.value: "ge",
        Operator.LessThanOrEqual.value: "le",
    }

    VALUES = {
        Operator.GreaterThan.value: lambda value: value,
        Operator.LessThan.value: lambda value: value,
        Operator.GreaterThanOrEqual.value: lambda value: value,
        Operator.LessThanOrEqual.value: lambda value: value,
    }

    FILTER_OPERATORS = {
        "equals": "__eq__",
        "greater_than_or_equal": "__ge__",
        "less_than_or_equal": "__le__",
        "greater_than": "__gt__",
        "less_than": "__lt__",
    }

    def __init__(
        self,
        dataset: Dataset,
        comparisons: list,
        property_timezone: timezone = timezone.utc,
        function_args: dict = {},
    ):
        self.function_args = function_args
        self.property_timezone = property_timezone
        self.dataset = dataset
        self.comparisons = comparisons
        self.cases = comparisons
        self.report_filters = comparisons

    def __repr__(self):
        """Return a string representation of the instance."""
        class_name = self.__class__.__name__
        attrs = [f"{key}={value!r}" for key, value in self.__dict__.items()]
        return f"<{class_name}, {', '.join(attrs)}>"

    @property
    def comparisons(self):
        return self.__comparisons

    @comparisons.setter
    def comparisons(self, comparisons):
        self.__comparisons = (
            {comparison["name"]: comparison for comparison in comparisons}
            if comparisons is not None
            else comparisons
        )

    @property
    def cases(self):
        return self.__cases

    @cases.setter
    def cases(self, comparisons):

        self.__cases = (
            {
                comparison["name"]: self.__get_case_conditions(comparison)
                for comparison in comparisons
            }
            if comparisons is not None
            else comparisons
        )

    @property
    def report_filters(self):
        return self.__report_filters

    @report_filters.setter
    def report_filters(self, comparisons):
        self.__report_filters = (
            {
                "or": [
                    {"and": [self.__get_standalone_comparison_filter(comparison)]}
                    for comparison in comparisons
                ]
            }
            if comparisons is not None
            else {}
        )

    def __get_case_conditions(self, comparison):

        filters = self.get_parsed_filters(
            self.__get_standalone_comparison_filter(comparison)
        )
        conditions = []
        for filter in filters.get("and", []):
            column_attr = (
                getattr(filter["model"], filter["field"])
                if isinstance(filter["field"], str)
                else filter["field"]
            )
            if filter["model"].__name__ in (
                OccupancyReservationsFFView.__name__,
                OccupancyReservationsView.__name__,
            ):
                original_filter = self.build_condition(
                    column_attr, filter["operator"], filter["value"]
                )
                booking_id_attr = getattr(
                    self.dataset.model, self.dataset.model.booking_id.key
                )

                booking_id_null = self.build_condition(
                    booking_id_attr, Operator.IsNull.value, None
                )
                conditions.append(or_(booking_id_null, original_filter))
            else:
                conditions.append(
                    self.build_condition(
                        column_attr, filter["operator"], filter["value"]
                    )
                )

        return conditions

    def __get_standalone_comparison_filter(self, comparison):
        """this method will get the "extra" filters that define each comparison (or period) and combine them with the reports filters
        comparisons reports use the same structure as the report filters, but period reports use a different structure
        we'll need to call the get_period_filter method to get the correct structure for period reports
        this is to get period summary reports to use the same class as comparison summary
        """
        if comparison.get(
            "start"
        ):  # only period summary reports have a start and end date
            start = comparison["start"]
            end = comparison["end"]
            if comparison.get("start_relative_to_end"):
                start = get_relative_start_date_from_end_date(
                    start, end, self.property_timezone
                )
            comparison_filter = dict()
            comparison_filter["and"] = [
                dict(
                    cdf=comparison["cdf"],
                    operator=Operator.GreaterThanOrEqual.value,
                    value=start,
                ),
                dict(
                    cdf=comparison["cdf"],
                    operator=Operator.LessThan.value,
                    value=end,
                ),
            ]
            return comparison_filter
        else:
            comparison_filter = {}
            if "filters" in comparison:
                comparison_filter = {
                    key: value[:] if isinstance(value, list) else value
                    for key, value in comparison["filters"].items()
                }

        return comparison_filter

    def get_parsed_filters(self, filters=[]):
        if isinstance(filters, list):
            return [self.get_parsed_filters(filter) for filter in filters]

        else:
            if "cdf" in filters:
                is_multi_level = "multi_level_id" in filters["cdf"]
                model = (
                    self.dataset.model
                    if not is_multi_level
                    else MultiLevel(
                        MultiLevelEnum(filters["cdf"]["multi_level_id"])
                    ).model
                )

                parsed_filter = {
                    "model": model,
                    "field": (self.__get_field(filters["cdf"]["column"], model)),
                    "operator": filters["operator"],
                    "value": self.__get_value(
                        filters["value"],
                        filters["cdf"]["column"],
                        filters["operator"],
                        (
                            None
                            if not is_multi_level
                            else MultiLevelEnum(filters["cdf"]["multi_level_id"])
                        ),
                        filters["cdf"].get("type"),
                    ),
                }
                return parsed_filter
            else:
                return {
                    key: self.get_parsed_filters(filter)
                    for key, filter in filters.items()
                }

    def __get_field(self, field, model) -> str:
        if "function" in model.__dict__[field].info:
            field = model.__dict__[field].info["function"](
                self.dataset.model, **self.function_args
            )
        return field

    def build_condition(self, column, operator, value):
        match operator:
            case Operator.Equals.value:
                return column == value
            case Operator.GreaterThan.value:
                return column > value
            case Operator.LessThan.value:
                return column < value
            case Operator.GreaterThanOrEqual.value:
                return column >= value
            case Operator.LessThanOrEqual.value:
                return column <= value
            case Operator.IsNull.value:
                return column.is_(None)
            case _:
                raise ValueError(f"Unsupported operator: {operator}")

    def __get_value(
        self,
        value: str,
        field: str,
        op: str,
        multi_level: MultiLevelEnum = None,
        type: str = None,
    ) -> str:
        value_cdf = CDF(self.dataset.kind, field, False, False, multi_level, type)
        if (
            field
            not in [
                static_filter["cdf"]["column"]
                for static_filter in self.dataset.static_filters
                if "cdf" in static_filter
            ]
        ) and (value_cdf.is_kind(CdfKind.Date) or value_cdf.is_kind(CdfKind.Timestamp)):
            value, duration = RelativeDate.get_duration_from_value(value)
            if duration is not None:
                return self.__get_converted_relative_date(value, duration)
            elif value in [relative_date.value for relative_date in RelativeDateEnum]:
                return self.__get_converted_relative_date(value)
            elif value_cdf.is_kind(CdfKind.Date):
                return self.__get_formatted_date(value)

        return self.VALUES[op](value)

    def __get_converted_relative_date(
        self, relative_date_string: str, duration: int = 0
    ) -> str:
        relative_date_string = relative_date_string.lower()

        return RelativeDate(
            RelativeDateEnum(relative_date_string),
            self.property_timezone,
            duration,
        ).value.strftime(DEFAULT_DATETIME_DATE_FORMAT)

    def __get_formatted_date(self, date_value: str) -> str:
        try:
            return datetime.fromisoformat(date_value).strftime(
                DEFAULT_DATETIME_DATE_FORMAT
            )
        except ValueError:
            try:
                return datetime.fromisoformat(
                    date_value.replace("Z", "+00:00")
                ).strftime(DEFAULT_DATETIME_DATE_FORMAT)
            except ValueError as value_error:
                logger.error(
                    f"Invalid date format for value, '{date_value}', in report filters",
                    exc_info=True,
                    extra={"error": value_error},
                )
                InvalidUsage.server_error()
