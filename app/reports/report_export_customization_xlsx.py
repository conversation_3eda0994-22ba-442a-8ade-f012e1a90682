from datetime import datetime
from io import Bytes<PERSON>
from typing import Callable, Optional

from openpyxl.drawing.image import Image
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet

from pandas import DataFrame

from app.common.constants.duplicate_index_rename_string import (
    DUPLICATE_INDEX_RENAME_STRING,
)
from app.common.constants.export_messaging import (
    COMPLETE_RESULTS,
    NO_RESULTS,
    PARTIAL_RESULTS,
)
from app.common.constants.export_record_info_cell import EXPORT_RECORD_INFO_CELL
from app.common.constants.operators_map import OPERATORS_MAP
from app.common.constants.relative_dates_map import RELATIVE_DATES_MAP
from app.common.constants.table_limits import EXPORT_LIMIT
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.enums.cdf import Cdf
from app.enums.cdf_kind import CdfKind
from app.enums.export import Colors, Font as FontEnum
from app.enums.metric import Metric
from app.enums.relative_date import RelativeDate
from app.reports.report_export_customization_interface import (
    ReportExportCustomizationInterface,
)
from app.reports.report_type import ReportType


class ReportExportCustomizationXlsx(ReportExportCustomizationInterface):
    SUBTOTAL_LABELS = ("Subtotal", "Sub subtotal")
    Z_SUBTOTAL_LABELS = (f"~z{SUBTOTAL_LABELS[0]}", f"~z{SUBTOTAL_LABELS[1]}")
    DEFAULT_START_ROW = 5
    START_ROW_WITH_CHARTS = 35
    EMPTY_CELL = 1
    PLACEHOLDER_CELLS = 2
    CELL_HEADER_FONT = Font(
        name=FontEnum.Calibri.value, size=12, bold=True, color=Colors.Lightning900.value
    )
    CELL_HEADER_FILL = PatternFill(patternType="solid", fgColor=Colors.Fog100.value)
    CELL_HEADER_ALIGNMENT = Alignment(
        wrap_text=True, vertical="top", horizontal="center"
    )
    CELL_TOTAL_FILL = PatternFill(
        patternType="solid", fgColor=Colors.Lightning300.value
    )
    CELL_SUBTOTAL_FILL = PatternFill(
        patternType="solid", fgColor=Colors.Lightning200.value
    )
    CELL_SUBSUBTOTAL_FILL = PatternFill(
        patternType="solid", fgColor=Colors.Lightning100.value
    )
    CELL_BORDER = Border(
        left=Side(style="thin"),
        right=Side(style="thin"),
        top=Side(style="thin"),
        bottom=Side(style="thin"),
    )
    SCALE_PNG = 2
    SPACE_BETWEEN_CHARTS = SCALE_PNG * 4
    ZOOM_SHEET = 85

    def __init__(
        self,
        generated_at: datetime,
        worksheet: Worksheet,
        df: DataFrame,
        title: str,
        type: ReportType,
        charts: list[bytes],
        total_record_count: int,
        dataset: Dataset,
        aggregated_count: int,
        details: bool,
        custom_cdfs: list,
        custom_field_cdfs: list,
        records_df: DataFrame,
        subtotals_df: DataFrame,
        totals_df: DataFrame,
        multilevel_cdfs: list,
        transpose: bool = False,
        periods: Optional[list] = None,
        formats: Optional[dict] = None,
        comparisons: Optional[dict] = None,
        filters: Optional[dict] = None,
        original_filters: Optional[dict] = None,
    ):
        self.worksheet = worksheet
        self.df = df
        self.title = title
        self.generated_at = generated_at
        self.type = type
        self.charts = charts
        self.total_record_count = total_record_count
        self.aggregated_count = aggregated_count
        self.details = details
        self.transpose = transpose
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.records_df = records_df
        self.subtotals_df = subtotals_df
        self.totals_df = totals_df
        self.multilevel_cdfs = multilevel_cdfs
        self.transpose = transpose
        self.periods = periods
        self.formats = formats
        self.charts = charts
        self.comparisons = comparisons
        self.aggregated_count = aggregated_count
        self.filters = filters
        self.original_filters = original_filters
        self.totals_df = totals_df
        self.subtotals_df = subtotals_df
        self.dataset = dataset
        self.START_ROW = (
            self.START_ROW_WITH_CHARTS if len(self.charts) else self.DEFAULT_START_ROW
        )

    def __repr__(self):
        """Representation of ReportExportCustomizationXlsx
        :return: string
        """
        return f"<ReportExportCustomizationXlsx, worksheet={self.worksheet}, self.df={self.self.df}, type={self.type}>"

    def apply(self) -> tuple[Worksheet, list[bytes]]:
        self.worksheet["A2"] = self.title
        self.worksheet["A2"].font = Font(
            name=FontEnum.Calibri.value,
            size=20,
            bold=True,
            color=Colors.Lightning500.value,
        )
        self.worksheet[
            "A3"
        ] = f"Generated at {self.generated_at.strftime('%Y-%m-%d %H:%M:%S %Z')}"
        self.worksheet["A3"].font = Font(
            name=FontEnum.Calibri.value,
            size=14,
            bold=True,
            color=Colors.Lightning900.value,
        )

        total_count = self.total_record_count
        row_count = (
            int(self.aggregated_count) if self.aggregated_count else EXPORT_LIMIT
        )

        if total_count == 0:
            self.worksheet[EXPORT_RECORD_INFO_CELL] = NO_RESULTS
            self.worksheet[EXPORT_RECORD_INFO_CELL].font = Font(
                name=FontEnum.Calibri.value,
                size=12,
                bold=False,
                color=Colors.ExportWarning.value,
            )
        elif total_count > row_count and self.type != ReportType.ListComparison:
            self.worksheet[EXPORT_RECORD_INFO_CELL] = PARTIAL_RESULTS(
                row_count, total_count
            )
            self.worksheet[EXPORT_RECORD_INFO_CELL].font = Font(
                name=FontEnum.Calibri.value,
                size=12,
                bold=True,
                color=Colors.ExportWarning.value,
            )
        else:
            self.worksheet[EXPORT_RECORD_INFO_CELL] = COMPLETE_RESULTS

        # Add the image to the worksheet.
        for index, chart_png in enumerate(self.charts):
            try:
                image = Image(BytesIO(chart_png))
                column_letter = chr(ord("A") + index * self.SPACE_BETWEEN_CHARTS)
                self.worksheet.add_image(image, f"{column_letter}5")
            except Exception as exception:
                logger.error(
                    "There was a problem adding the chart to the export",
                    extra={"error": str(exception)},
                )
        for row in self.worksheet.iter_rows():
            for cell in row:
                if cell.value is not None:
                    cell.value = self.__reset_name_value(cell.value)
        if self.df.empty:
            self.worksheet = self.__add_filter_and_range_info()
            return self.worksheet

        match (self.type):
            case ReportType.List:
                self.worksheet = self.get_customization_list()
            case ReportType.Summary:
                if not self.details and not self.transpose:
                    self.worksheet = self.get_customization_summary()
                if not self.details and self.transpose:
                    self.worksheet = self.get_customization_summary_transposed()
                if self.details:
                    self.worksheet = self.get_customization_summary_details()
            case ReportType.Pivot:
                self.worksheet = self.get_customization_pivot()
            case ReportType.PeriodList | ReportType.ListComparison:
                self.worksheet = self.get_customization_list_comparison()
            case ReportType.PeriodSummary | ReportType.SummaryComparison:
                if self.transpose:
                    self.worksheet = (
                        self.get_customization_summary_comparison_transposed()
                    )
                else:
                    self.worksheet = self.get_customization_summary_comparison()

        self.worksheet.sheet_view.zoomScale = self.ZOOM_SHEET

        self.worksheet = self.__add_filter_and_range_info()

        return self.worksheet

    def get_customization_list(self) -> Callable:
        for row in self.worksheet[
            f"{get_column_letter(2 if not self.totals_df.empty else 1)}{self.START_ROW + self.EMPTY_CELL}:"
            f"{get_column_letter(len(self.records_df.columns))}{self.START_ROW + self.EMPTY_CELL}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        if not self.totals_df.empty:
            for row in self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}:{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}"
            ]:
                for cell in row:
                    cell.border = Border(outline=None)

            for row in self.worksheet[
                f"{get_column_letter(1)}{len(self.records_df) + self.START_ROW + self.PLACEHOLDER_CELLS}:"
                f"{get_column_letter(len(self.records_df.columns))}{len(self.df) + self.START_ROW + self.EMPTY_CELL}"
            ]:
                for cell in row:
                    cell.font = self.CELL_HEADER_FONT
                    cell.fill = self.CELL_TOTAL_FILL
                    cell.border = self.CELL_BORDER

        self.worksheet.freeze_panes = self.worksheet[
            f"A{self.START_ROW + self.PLACEHOLDER_CELLS}"
        ]

        return self.worksheet

    def get_customization_summary(self) -> Callable:
        for row in self.worksheet[
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}:{get_column_letter(len(self.df.index.names))}{self.worksheet.max_row}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        for row in self.worksheet[
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}:{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        if not self.subtotals_df.empty:
            for i, index in enumerate(self.df.index.names[1:], start=1):
                df_index = self.df.reset_index()[index]
                subtotals = [
                    subtotal + self.PLACEHOLDER_CELLS + self.START_ROW
                    for subtotal in df_index[
                        (df_index == self.SUBTOTAL_LABELS[0])
                        | (df_index == self.SUBTOTAL_LABELS[1])
                    ].index.tolist()
                ]

                for subtotal in subtotals:
                    for row in self.worksheet[
                        f"{get_column_letter(1 + i)}{subtotal}:{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{subtotal}"
                    ]:
                        for cell in row:
                            cell.fill = (
                                self.CELL_SUBTOTAL_FILL
                                if i == 1
                                else self.CELL_SUBSUBTOTAL_FILL
                            )
                            cell.font = self.CELL_HEADER_FONT
                            cell.border = self.CELL_BORDER

                    if i == 1 and len(self.df.index.names) == 3:
                        self.worksheet.merge_cells(
                            f"{get_column_letter(2)}{subtotal}:{get_column_letter(3)}{subtotal}"
                        )

        if not self.totals_df.empty:
            for row in self.worksheet[
                f"{get_column_letter(1)}{len(self.df) + self.START_ROW + self.EMPTY_CELL}:"
                f"{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{len(self.df) + self.START_ROW + self.EMPTY_CELL}"
            ]:
                for cell in row:
                    cell.fill = self.CELL_TOTAL_FILL
                    cell.font = self.CELL_HEADER_FONT
                    cell.border = self.CELL_BORDER

            self.worksheet.merge_cells(
                f"{get_column_letter(1)}{len(self.df) + self.START_ROW + self.EMPTY_CELL}:"
                f"{get_column_letter(len(self.df.index.names))}{len(self.df) + self.START_ROW + self.EMPTY_CELL}"
            )

        self.worksheet.freeze_panes = self.worksheet[
            f"{get_column_letter(1 + len(self.df.index.names))}{self.START_ROW + self.PLACEHOLDER_CELLS}"
        ]

        return self.worksheet

    def get_customization_summary_transposed(self) -> Callable:
        has_subtotal = False
        has_subsubtotal = False
        if len(self.df.columns.names) == 1:
            # work around transposed self.df.to_excel / openpyxl
            self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}"
            ] = self.df.columns.names[0]
            self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}"
            ].border = self.CELL_BORDER
        else:
            self.worksheet.delete_rows(
                len(self.df.index.names) + len(self.df.columns.names) + self.START_ROW
            )

        # index column
        for i in range(self.START_ROW + self.EMPTY_CELL, self.worksheet.max_row + 1):
            cell = self.worksheet[f"{get_column_letter(1)}{i}"]
            cell.font = self.CELL_HEADER_FONT
            cell.fill = self.CELL_HEADER_FILL
            cell.alignment = self.CELL_HEADER_ALIGNMENT

        self.worksheet = self.__format_header_rows(self.worksheet, self.df)

        if not self.subtotals_df.empty:
            has_subtotal = True
            for row in self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL + 2}:"
                f"{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL + 2}"
            ]:
                for cell in row:
                    if cell.value in self.SUBTOTAL_LABELS:
                        for i in range(self.worksheet.max_row - (cell.row - 1)):
                            subtotalcell = self.worksheet[
                                f"{get_column_letter(cell.column)}{cell.row + i}"
                            ]
                            has_subsubtotal = True
                            subtotalcell.fill = self.CELL_SUBSUBTOTAL_FILL
                            subtotalcell.font = self.CELL_HEADER_FONT
                            subtotalcell.border = self.CELL_BORDER

            for row in self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL + 1}:"
                f"{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL + 1}"
            ]:
                for cell in row:
                    if cell.value in self.SUBTOTAL_LABELS:
                        self.worksheet.merge_cells(
                            f"{get_column_letter(cell.column)}{cell.row}:{get_column_letter(cell.column)}{cell.row + (1 if has_subsubtotal else 0)}"
                        )

                        for i in range(self.worksheet.max_row - (cell.row - 1)):
                            subtotalcell = self.worksheet[
                                f"{get_column_letter(cell.column)}{cell.row + i}"
                            ]
                            subtotalcell.fill = self.CELL_SUBTOTAL_FILL
                            subtotalcell.font = self.CELL_HEADER_FONT
                            subtotalcell.border = self.CELL_BORDER

        if not self.totals_df.empty:
            for i in range(self.START_ROW, self.worksheet.max_row):
                cell = self.worksheet[i + 1][self.worksheet.max_column - 1]
                cell.fill = self.CELL_TOTAL_FILL
                cell.font = self.CELL_HEADER_FONT
                cell.border = self.CELL_BORDER

            col_letter = get_column_letter(self.worksheet.max_column)

            self.worksheet.merge_cells(
                f"{col_letter}{self.START_ROW + 1}:{col_letter}{self.START_ROW + self.EMPTY_CELL + int(has_subtotal) + int(has_subsubtotal)}"
            )

        self.worksheet.freeze_panes = self.worksheet[
            f"{get_column_letter(1 + len(self.df.index.names))}{self.START_ROW + self.PLACEHOLDER_CELLS}"
        ]

        return self.worksheet

    def get_customization_list_comparison(self) -> Callable:
        if self.transpose and len(self.df.columns.names) == 1:
            # work around transposed self.df.to_excel / openpyxl
            self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}"
            ] = self.df.columns.names[0]
            self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}"
            ].border = self.CELL_BORDER

        for row in self.worksheet[
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}:{get_column_letter(len(self.df.index.names))}{self.worksheet.max_row}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        for row in self.worksheet[
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}:{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        self.worksheet.freeze_panes = self.worksheet[
            f"{get_column_letter(1 + len(self.df.index.names))}{self.START_ROW + self.PLACEHOLDER_CELLS}"
        ]

        return self.worksheet

    def get_customization_summary_details(self) -> Callable:
        for row in self.worksheet[
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}:{get_column_letter(len(self.df.index.names))}{len(self.df) + self.START_ROW + self.EMPTY_CELL}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        for row in self.worksheet[
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}:{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        if not self.subtotals_df.empty:
            FILLS = [
                self.CELL_TOTAL_FILL,
                self.CELL_SUBTOTAL_FILL,
                self.CELL_SUBSUBTOTAL_FILL,
            ]

            for i, index in enumerate(self.df.index.names, start=1):
                df_index = self.df.reset_index()[index]
                indexes_subtotals = [
                    index
                    for index in df_index.unique()
                    for metric in Metric
                    if index is str and metric.value in index
                ]
                subtotals = [
                    subtotal + self.PLACEHOLDER_CELLS + self.START_ROW
                    for subtotal in df_index[
                        df_index.isin(indexes_subtotals)
                    ].index.tolist()
                ]

                for subtotal in subtotals:
                    for row in self.worksheet[
                        f"{get_column_letter(i)}{subtotal}:{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{subtotal}"
                    ]:
                        for cell in row:
                            cell.fill = FILLS[i - 1]
                            cell.font = self.CELL_HEADER_FONT
                            cell.border = self.CELL_BORDER

                    if i != len(self.df.index.names):
                        self.worksheet.merge_cells(
                            f"{get_column_letter(i)}{subtotal}:{get_column_letter(len(self.df.index.names))}{subtotal}"
                        )

        if not self.totals_df.empty:
            for i in range(
                len(self.totals_df.stack(1, future_stack=True).reset_index())
            ):
                self.worksheet.merge_cells(
                    f"{get_column_letter(1)}{len(self.df) + self.START_ROW + self.EMPTY_CELL - i}:"
                    f"{get_column_letter(len(self.df.index.names))}{len(self.df) + self.START_ROW + self.EMPTY_CELL - i}"
                )

        self.worksheet.freeze_panes = self.worksheet[
            f"{get_column_letter(1 + len(self.df.index.names))}{self.START_ROW + self.PLACEHOLDER_CELLS}"
        ]

        return self.worksheet

    def get_customization_pivot(self) -> Callable:
        for row in self.worksheet[
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL + len(self.df.columns.names)}:"
            f"{get_column_letter(len(self.df.index.names))}{len(self.df) + self.START_ROW + self.EMPTY_CELL + len(self.df.columns.names)}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        for row in self.worksheet[
            f"{get_column_letter(len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL}:"
            f"{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{self.START_ROW + len(self.df.columns.names)}"
        ]:
            for cell in row:
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        if not self.subtotals_df.empty:
            if len(self.df.index.names) == 2:
                df_index = self.df.reset_index()[self.df.index.names[1]]
                subtotals = [
                    subtotal
                    + self.PLACEHOLDER_CELLS
                    + self.START_ROW
                    + len(self.df.columns.names)
                    for subtotal in df_index[
                        df_index == self.SUBTOTAL_LABELS[0]
                    ].index.tolist()
                ]

                for subtotal in subtotals:
                    for row in self.worksheet[
                        f"{get_column_letter(2)}{subtotal}:{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{subtotal}"
                    ]:
                        for cell in row:
                            cell.fill = self.CELL_SUBTOTAL_FILL
                            cell.font = self.CELL_HEADER_FONT
                            cell.border = self.CELL_BORDER

        if not self.totals_df.empty:
            for row in self.worksheet[
                f"{get_column_letter(1)}{len(self.df) + len(self.df.columns.names) + self.START_ROW + self.EMPTY_CELL}:"
                f"{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{len(self.df) + len(self.df.columns.names) + self.START_ROW + self.EMPTY_CELL}"
            ]:
                for cell in row:
                    cell.fill = self.CELL_TOTAL_FILL
                    cell.font = self.CELL_HEADER_FONT
                    cell.border = self.CELL_BORDER

            self.worksheet.merge_cells(
                f"{get_column_letter(1)}{len(self.df) + len(self.df.columns.names) + self.START_ROW + self.EMPTY_CELL}:"
                f"{get_column_letter(len(self.df.index.names))}{len(self.df) + len(self.df.columns.names) + self.START_ROW + self.EMPTY_CELL}"
            )

        self.worksheet.freeze_panes = self.worksheet[
            f"{get_column_letter(1 + len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL + len(self.df.columns.names)}"
        ]

        return self.worksheet

    def get_customization_summary_comparison(self) -> Callable:
        def apply_style_to_range(worksheet, start, end):
            for row in worksheet[start:end]:
                for cell in row:
                    cell.font = self.CELL_HEADER_FONT
                    cell.fill = self.CELL_HEADER_FILL
                    cell.alignment = self.CELL_HEADER_ALIGNMENT

        apply_style_to_range(
            self.worksheet,
            f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL + len(self.df.columns.names)}",
            f"{get_column_letter(len(self.df.index.names))}{len(self.df) + self.START_ROW + self.EMPTY_CELL + len(self.df.columns.names)}",
        )

        apply_style_to_range(
            self.worksheet,
            f"{get_column_letter(len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL}",
            f"{get_column_letter(len(self.df.columns) + len(self.df.index.names))}{self.START_ROW + len(self.df.columns.names)}",
        )

        has_sub_subtotal = 0
        has_subtotal = 0

        for row in self.worksheet.iter_rows():
            for cell in row:
                cell_value = str(cell.value)
                if cell_value and self.SUBTOTAL_LABELS[1] == cell_value:
                    has_sub_subtotal = 1
                    for sub_subtotal_row in self.worksheet[
                        f"{cell.coordinate}:{get_column_letter(self.worksheet.max_column)}{cell.row}"
                    ]:
                        for sub_subtotal_row_cell in sub_subtotal_row:
                            sub_subtotal_row_cell.fill = self.CELL_SUBSUBTOTAL_FILL
                            sub_subtotal_row_cell.font = self.CELL_HEADER_FONT
                            sub_subtotal_row_cell.border = self.CELL_BORDER
                if cell_value and self.SUBTOTAL_LABELS[0] == cell_value:
                    has_subtotal = 1
                    self.worksheet.merge_cells(
                        f"{cell.coordinate}:{get_column_letter(cell.column + has_sub_subtotal)}{cell.row}"
                    )
                    for subtotal_row in self.worksheet[
                        f"{cell.coordinate}:{get_column_letter(self.worksheet.max_column)}{cell.row}"
                    ]:
                        for subtotal_row_cell in subtotal_row:
                            subtotal_row_cell.fill = self.CELL_SUBTOTAL_FILL
                            subtotal_row_cell.font = self.CELL_HEADER_FONT
                            subtotal_row_cell.border = self.CELL_BORDER
                if cell_value and "Total" == cell_value:
                    self.worksheet.merge_cells(
                        f"{cell.coordinate}:{get_column_letter(cell.column + has_sub_subtotal + has_subtotal)}{cell.row}"
                    )
                    for total_row in self.worksheet[
                        f"{cell.coordinate}:{get_column_letter(self.worksheet.max_column)}{cell.row}"
                    ]:
                        for total_row_cell in total_row:
                            total_row_cell.fill = self.CELL_TOTAL_FILL
                            total_row_cell.font = self.CELL_HEADER_FONT
                            total_row_cell.border = self.CELL_BORDER

        self.worksheet.freeze_panes = self.worksheet[
            f"{get_column_letter(1 + len(self.df.index.names))}{self.START_ROW + self.EMPTY_CELL + len(self.df.columns.names)}"
        ]

        return self.worksheet

    def get_customization_summary_comparison_transposed(self) -> Callable:
        def apply_style_to_range(worksheet, start, end, fill_color):
            for row in worksheet[start:end]:
                for cell in row:
                    cell.font = self.CELL_HEADER_FONT
                    cell.fill = fill_color
                    cell.alignment = self.CELL_HEADER_ALIGNMENT
                    cell.border = self.CELL_BORDER

        has_sub_subtotal = 0
        has_subtotal = 0

        if len(self.df.columns.names) == 1:
            periods_header = self.worksheet[
                f"{get_column_letter(1)}{self.START_ROW + self.EMPTY_CELL}"
            ]
            periods_header.font = self.CELL_HEADER_FONT
            periods_header.fill = self.CELL_HEADER_FILL
            periods_header.alignment = self.CELL_HEADER_ALIGNMENT

            self.worksheet[
                f"{get_column_letter(2)}{self.START_ROW + self.EMPTY_CELL}"
            ].fill = self.CELL_HEADER_FILL
            self.worksheet[
                f"{get_column_letter(3)}{self.START_ROW + self.EMPTY_CELL}"
            ] = self.df.columns.names[0]

        start_row = self.START_ROW + self.EMPTY_CELL
        start_col = get_column_letter(1)
        end_col = get_column_letter(len(self.df.index.names))

        apply_style_to_range(
            self.worksheet,
            f"{start_col}{start_row + len(self.df.columns.names)}",
            f"{end_col}{len(self.df) + start_row + len(self.df.columns.names)}",
            self.CELL_HEADER_FILL,
        )

        start_col = get_column_letter(len(self.df.index.names))
        end_col = get_column_letter(len(self.df.columns) + len(self.df.index.names))

        apply_style_to_range(
            self.worksheet,
            f"{start_col}{start_row}",
            f"{end_col}{start_row + len(self.df.columns.names)}",
            self.CELL_HEADER_FILL,
        )

        for row in self.worksheet.iter_rows():
            for cell in row:
                cell_value = str(cell.value)
                if self.SUBTOTAL_LABELS[1] == cell_value:
                    has_sub_subtotal = 1
                    apply_style_to_range(
                        self.worksheet,
                        f"{cell.coordinate}",
                        f"{get_column_letter(cell.column)}{self.worksheet.max_row}",
                        self.CELL_SUBSUBTOTAL_FILL,
                    )

                if self.SUBTOTAL_LABELS[0] == cell_value:
                    for subtotal_row in self.worksheet[
                        f"{cell.coordinate}:{get_column_letter(cell.column)}{self.worksheet.max_row}"
                    ]:
                        has_subtotal = 1
                        for subtotal_row_cell in subtotal_row:
                            subtotal_row_cell.fill = self.CELL_SUBTOTAL_FILL
                            subtotal_row_cell.font = self.CELL_HEADER_FONT
                            subtotal_row_cell.border = self.CELL_BORDER

                if "Total" == cell_value:
                    self.worksheet.merge_cells(
                        f"{cell.coordinate}:{get_column_letter(cell.column)}{cell.row + has_sub_subtotal + has_subtotal}"
                    )
                    for total_row in self.worksheet[
                        f"{cell.coordinate}:{get_column_letter(cell.column)}{self.worksheet.max_row}"
                    ]:
                        for total_row_cell in total_row:
                            total_row_cell.fill = self.CELL_TOTAL_FILL
                            total_row_cell.font = self.CELL_HEADER_FONT
                            total_row_cell.border = self.CELL_BORDER

        freeze_row = self.START_ROW + self.EMPTY_CELL + len(self.df.columns.names)
        freeze_col = 1 + len(self.df.index.names)

        self.worksheet.freeze_panes = self.worksheet[
            f"{get_column_letter(freeze_col)}{freeze_row}"
        ]

        return self.worksheet

    def __reset_name_value(self, cell_value):
        if cell_value == "~RESERVED_BLANK_STRING":
            cell_value = ""
        elif isinstance(cell_value, str) and cell_value.endswith(
            DUPLICATE_INDEX_RENAME_STRING
        ):
            cell_value = cell_value[: -len(DUPLICATE_INDEX_RENAME_STRING)]
        return cell_value

    def __format_header_rows(self, worksheet: Worksheet, df: DataFrame):
        # header row
        for row_number in range(
            self.START_ROW + self.EMPTY_CELL,
            self.START_ROW + self.EMPTY_CELL + len(df.columns.names),
        ):
            for i in range(1, worksheet.max_column + 1):
                cell = worksheet[f"{get_column_letter(i)}{row_number}"]
                cell.font = self.CELL_HEADER_FONT
                cell.fill = self.CELL_HEADER_FILL
                cell.alignment = self.CELL_HEADER_ALIGNMENT

        return worksheet

    def __add_filter_and_range_info(self):
        if self.comparisons or self.periods:
            self.worksheet[f"A{self.worksheet.max_row + 2}"] = "Ranges"
            self.worksheet[f"A{self.worksheet.max_row}"].font = self.CELL_HEADER_FONT

        if self.comparisons:
            for i, comparison in enumerate(self.comparisons):
                self.worksheet[
                    f"A{self.worksheet.max_row + 1}"
                ] = f"{comparison['name']}"
                self.worksheet[
                    f"B{self.worksheet.max_row}"
                ] = f"{self.__make_filters_string(comparison['filters'])}"
            if self.original_filters:
                self.worksheet[f"A{self.worksheet.max_row + 2}"] = "Filters"
                self.worksheet[
                    f"A{self.worksheet.max_row}"
                ].font = self.CELL_HEADER_FONT
                self.worksheet[
                    f"B{self.worksheet.max_row}"
                ] = self.__make_filters_string(self.original_filters)

        elif self.periods:
            for i, period in enumerate(self.periods):
                self.worksheet[f"A{self.worksheet.max_row + 1}"] = f"{period['name']}"
                self.worksheet[
                    f"B{self.worksheet.max_row}"
                ] = f"{self.__make_period_string(period)}"
            if self.original_filters:
                self.worksheet[f"A{self.worksheet.max_row + 2}"] = "Filters"
                self.worksheet[
                    f"A{self.worksheet.max_row}"
                ].font = self.CELL_HEADER_FONT
                self.worksheet[
                    f"B{self.worksheet.max_row}"
                ] = self.__make_filters_string(self.original_filters)

        elif self.filters:
            self.worksheet[f"A{self.worksheet.max_row + 2}"] = "Filters"
            self.worksheet[f"A{self.worksheet.max_row}"].font = self.CELL_HEADER_FONT
            self.worksheet[f"B{self.worksheet.max_row}"] = self.__make_filters_string(
                self.filters
            )
        return self.worksheet

    def __make_filters_string(self, filters: dict) -> str:
        cdfs = {cdf["column"]: cdf["name"] for cdf in self.dataset.flatten_all_cdfs}
        date_cdfs = [
            cdf["column"]
            for cdf in self.dataset.flatten_all_cdfs
            if cdf["kind"] in (CdfKind.Date, CdfKind.Timestamp)
        ]

        def process_filter(filter_item, is_top_level=False, custom_field_cdfs=[]):
            if "and" in filter_item or "or" in filter_item:
                logical_operator = "and" if "and" in filter_item else "or"
                sub_filters = filter_item[logical_operator]
                processed_sub_filters = [
                    process_filter(sub_filter, False, custom_field_cdfs)
                    for sub_filter in sub_filters
                ]
                if is_top_level:
                    return f" {logical_operator.upper()} ".join(processed_sub_filters)
                else:
                    # wrap in parentheses
                    return f"({f' {logical_operator.upper()} '.join(processed_sub_filters)})"
            elif filter_item["cdf"]["type"] == Cdf.CustomField.value:
                column = filter_item["cdf"]["column"]
                name = next(
                    custom_field_cdf.get("name")
                    for custom_field_cdf in custom_field_cdfs
                    if custom_field_cdf.get("column") == column
                )
                operator = filter_item["operator"]
                operator = OPERATORS_MAP[operator]
                value = filter_item["value"]
                return f"{name} {operator} {value}"
            else:
                column = cdfs[filter_item["cdf"]["column"]]
                operator = filter_item["operator"]
                operator = OPERATORS_MAP[operator]
                value = filter_item["value"]
                if filter_item["cdf"]["column"] in date_cdfs:
                    values = value.split(";")
                    if values[0] in RelativeDate.values():
                        value = RELATIVE_DATES_MAP[values[0]]
                        if len(values) == 2:
                            value = f"{values[1]} {value}"
                return f"{column} {operator} {value}"

        processed_string = process_filter(
            filters,
            is_top_level=True,
            custom_field_cdfs=self.custom_field_cdfs,
        )
        return processed_string

    def __make_period_string(self, period: dict) -> str:
        cdfs = {cdf["column"]: cdf["name"] for cdf in self.dataset.flatten_all_cdfs}
        column = cdfs[period["cdf"]["column"]]

        processed_string = ""
        if period["start"]:
            value = period["start"]
            values = value.split(";")
            if values[0] in RelativeDate.values():
                value = RELATIVE_DATES_MAP[values[0]]
                if len(values) == 2:
                    value = f"{values[1]} {value}"

            processed_string = f"{column} >= {value}"

        if period["start"] and period["end"]:
            processed_string = f"{processed_string} AND"

        if period["end"]:
            value = period["end"]
            values = value.split(";")
            if values[0] in RelativeDate.values():
                value = RELATIVE_DATES_MAP[values[0]]
                if len(values) == 2:
                    value = f"{values[1]} {value}"

            processed_string = f"{processed_string} {column} < {value}"
        return processed_string
