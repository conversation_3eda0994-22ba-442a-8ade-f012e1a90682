from datetime import timezone
from typing import Optional

import pandas as pd
from pandas.core.frame import <PERSON><PERSON>rame

from sqlalchemy.orm.collections import InstrumentedList
from sqlalchemy.orm.query import Query

from app.cdfs.cdf import CDF
from app.common.constants.comparison import COMPARISON_COLUMN_HEADER
from app.common.constants.dynamic_cdf_label import DYNAMIC_CDF_LABEL
from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.constants.period_comparison import PERIOD_COMPARISON_COLUMN_HEADER
from app.common.constants.table_limits import (
    EXPORT_LIMIT,
    SUMMARY_PREVIEW_LIMIT,
    SUMMARY_RUN_LIMIT,
)
from app.datasets.dataset import Dataset
from app.enums import (
    Cdf,
    CdfKind,
    Mode,
)
from app.enums.metric import Metric
from app.enums.multi_level import MultiLevel
from app.reports.report_cdf import ReportCDF
from app.reports.report_interface import ReportInterface
from app.reports.report_metadata import ReportMetadata
from app.reports.report_query import ReportQuery


class ReportListComparison(ReportInterface):
    def __init__(
        self,
        mode: Mode,
        dataset: Dataset,
        custom_cdfs: list,
        custom_field_cdfs: list,
        columns: list,
        property_ids: list,
        organization_id: int,
        filters: dict,
        sort: list,
        format: str,
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        comparisons: list = None,
        formats: Optional[dict] = None,
        aggregate_count: Optional[bool] = True,
        metadata: ReportMetadata = None,
    ):
        self.mode = mode
        self.dataset = dataset
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        columns_with_counts = [dict(d) for d in columns]
        if aggregate_count:
            columns_with_counts += [
                dict(
                    cdf=dict(column=ReportCDF.STAR_CDF, type=Cdf.Default.value),
                    metrics=[Metric.count.name],
                )
            ]
        self.columns = columns_with_counts
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.filters = filters
        self.sort = sort
        self.limit = self.mode
        self.format = format
        self.property_timezone = property_timezone
        self.property_settings = property_settings
        self.comparisons = comparisons
        self.formats = formats
        self.column_header = (
            COMPARISON_COLUMN_HEADER
            if self.comparisons[0].get("filters")
            else PERIOD_COMPARISON_COLUMN_HEADER
        )
        self.metadata = metadata

    def __repr__(self):
        """Representation of ReportComparison
        :return: string
        """
        return (
            f"<ReportListComparison, mode={self.mode}, dataset={self.dataset}, columns={self.columns}, comparisons={self.comparisons}, "
            f"property_ids={self.property_ids}, filters={self.filters}, sort={self.sort}>"
        )

    @property
    def limit(self):
        return self.__limit

    @limit.setter
    def limit(self, mode):
        match (mode):
            case Mode.Preview:
                self.__limit = SUMMARY_PREVIEW_LIMIT
            case Mode.Run:
                self.__limit = SUMMARY_RUN_LIMIT
            case Mode.Export:
                self.__limit = EXPORT_LIMIT
            case _:
                self.__limit = None

    def get_df(self) -> DataFrame:
        raise Exception("List is not supported for this report")

    def get_df_subtotals(self) -> Exception:
        raise Exception("Subtotals not supported for this report")

    def get_df_totals(self) -> DataFrame:
        if not self.has_metrics(self.columns, self.dataset):
            return DataFrame()

        df = self.__get_df_data(aggregate=True)

        if df.empty:
            return DataFrame()

        # Get Column MultiIndex
        df.columns = self.get_df_columns(self.columns, self.dataset)

        # Fill NAs with NA_FILL_STRING
        df = df.astype("object")
        df = df.infer_objects(copy=False).fillna(NA_FILL_STRING)

        # Stack Period level
        df = df.stack(level=self.column_header, future_stack=True)

        # Remove unnecessary level from initial index
        df.index = df.index.droplevel(0)

        # Reindex to match comparison order
        df = df.reindex([comparison["name"] for comparison in self.comparisons])
        df = df.drop(columns="*", level=0)

        return df

    def get_df_columns(self, columns: list, dataset: Dataset) -> list:
        comparison_headers = []
        headers = []
        metrics = []
        if hasattr(self, "custom_cdfs") and self.custom_cdfs:
            if issubclass(type(self.custom_cdfs), InstrumentedList):
                dynamic_custom_cdfs = [
                    custom_cdf.column
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf.kind == CdfKind.Dynamic.name
                ]
            else:
                dynamic_custom_cdfs = [
                    custom_cdf["column"]
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf["kind"] == CdfKind.Dynamic.name
                ]
        else:
            dynamic_custom_cdfs = []
        for comparison in self.comparisons:
            for column in columns:
                name = column["cdf"]["column"]
                multi_level = (
                    MultiLevel(column["cdf"]["multi_level_id"])
                    if "multi_level_id" in column["cdf"]
                    else None
                )
                if "metrics" in column:
                    for metric in column["metrics"]:
                        comparison_headers.append(comparison["name"])
                        headers.append(name)
                        metrics.append(metric)
                elif (
                    column["cdf"]["type"] == Cdf.Default.value
                    and CDF(dataset.kind, name, False, False, multi_level).kind
                    in (CdfKind.DynamicCurrency, CdfKind.DynamicPercentage)
                    or name in dynamic_custom_cdfs
                ):
                    comparison_headers.append(comparison["name"])
                    headers.append(name)
                    metrics.append(DYNAMIC_CDF_LABEL)

        return pd.MultiIndex.from_arrays(
            [comparison_headers, headers, metrics],
            names=[self.column_header, None, None],
        )

    def __get_df_data(self, aggregate: bool = False) -> DataFrame:

        data = self.__get_query(aggregate=aggregate).all()
        return DataFrame(data=data)

    def __get_query(self, aggregate: bool = False) -> Query:
        return ReportQuery(
            self.dataset,
            self.property_ids,
            self.organization_id,
            self.custom_cdfs,
            self.custom_field_cdfs,
            self.columns,
            filters=self.filters,
            sort=self.sort,
            limit=self.limit,
            aggregate=aggregate,
            format=self.format,
            property_timezone=self.property_timezone,
            property_settings=self.property_settings,
            formats=self.formats,
            metadata=self.metadata,
            comparisons=self.comparisons,
        ).get_query()
