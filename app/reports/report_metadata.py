from app.enums.export import Format
from app.enums.mode import Mode
from app.reports.report_type import ReportType


class ReportMetadata:
    def __init__(
        self,
        mode: Mode,
        format: Format,
        type: ReportType,
        page_offset: int = 0,
        page_limit: int = None,
        cte_query: bool = False,
    ):
        self.mode = mode
        self.format = format
        self.type = type
        self.page_offset = page_offset
        self.page_limit = page_limit
        self.cte_query = cte_query
