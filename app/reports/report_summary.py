import warnings
from datetime import timezone
from typing import Optional

import pandas as pd
from pandas.core.frame import DataFrame

from sqlalchemy.orm.query import Query

from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.constants.table_limits import (
    EXPORT_LIMIT,
    SUMMARY_PREVIEW_LIMIT,
    SUMMARY_RUN_LIMIT,
)
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.enums import Mode
from app.enums.cdf import Cdf
from app.enums.format import NumericFormat
from app.enums.metric import Metric
from app.reports.report_cdf import ReportCDF
from app.reports.report_interface import ReportInterface
from app.reports.report_metadata import ReportMetadata
from app.reports.report_query import ReportQuery


class ReportSummary(ReportInterface):
    def __init__(
        self,
        mode: Mode,
        dataset: Dataset,
        custom_cdfs: list,
        custom_field_cdfs,
        columns: list,
        group_rows: list,
        property_ids: list,
        organization_id: int,
        filters: dict,
        sort: list,
        format: str,
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        transpose: bool = False,
        formats: Optional[dict] = None,
        aggregate_count: Optional[bool] = True,
        metadata: ReportMetadata = None,
    ):

        columns_with_counts = [dict(d) for d in columns]
        if aggregate_count:
            columns_with_counts += [
                dict(
                    cdf=dict(column=ReportCDF.STAR_CDF, type=Cdf.Default.value),
                    metrics=[Metric.count.name],
                )
            ]

        self.mode = mode
        self.dataset = dataset
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.columns = columns_with_counts
        self.group_rows = group_rows
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.filters = filters
        self.sort = sort
        self.metadata = metadata
        self.limit = self.mode
        self.format = format
        self.property_timezone = property_timezone
        self.property_settings = property_settings
        self.transpose = transpose
        self.formats = formats
        self.aggregate_count = aggregate_count

    def __repr__(self):
        """Representation of ReportSummary
        :return: string
        """
        return (
            f"<ReportSummary, mode={self.mode}, dataset={self.dataset}, columns={self.columns}, group_rows={self.group_rows}, "
            f"property_ids={self.property_ids}, filters={self.filters}, sort={self.sort}>"
        )

    @property
    def limit(self):
        return self.__limit

    @limit.setter
    def limit(self, mode):
        match (mode):
            case Mode.Preview:
                self.__limit = SUMMARY_PREVIEW_LIMIT
            case Mode.Run:
                self.__limit = SUMMARY_RUN_LIMIT
            case Mode.Export:
                self.__limit = EXPORT_LIMIT
            case _:
                self.__limit = None

    @property
    def offset(self):
        return self.__offset

    @offset.setter
    def offset(self, mode):
        match (mode):
            case Mode.Page:
                self.__offset = self.metadata.page_offset
            case _:
                self.__offset = 0

    def get_df(self) -> DataFrame:
        df = self.__get_df_data()

        if df.empty:
            return DataFrame()

        df = df.astype("object")
        group_rows = self.get_flatten_groups(self.group_rows)
        columns = self.get_df_columns(self.columns, self.dataset)
        df = df.infer_objects(copy=False).fillna(NA_FILL_STRING).set_index(group_rows)
        if isinstance(df.index, pd.MultiIndex):
            levels = [
                df.index.get_level_values(level).astype(str)
                for level in range(df.index.nlevels)
            ]
            df.index = pd.MultiIndex.from_arrays(levels)
        else:
            df.index = df.index.astype(str)

        df.columns = columns

        return df

    def get_df_subtotals(self):
        if not self.has_metrics(self.columns, self.dataset):
            return DataFrame()

        group_rows = self.get_flatten_groups(self.group_rows)[:2]
        columns = self.get_df_columns(self.columns, self.dataset)
        frames = []
        for index, _ in enumerate(self.group_rows[:-1], start=1):
            df = self.__get_df_data(index, True)
            df.insert(0, "level", group_rows[(index - 1)])

            if index == 1:
                df[group_rows[index]] = "_SUBTOTAL_INDEX"

            if not df.empty:
                frames.append(
                    df.astype("object").infer_objects(copy=False).fillna(NA_FILL_STRING)
                )

        if not len(frames):
            return DataFrame()

        if self.format is NumericFormat.Formatted.value:
            df_subtotals = (
                pd.concat(frames)
                .infer_objects(copy=False)
                .fillna(NA_FILL_STRING)
                .set_index(["level"] + group_rows)
            )
        else:
            df_subtotals = (
                pd.concat([frame.dropna() for frame in frames])
                .infer_objects(copy=False)
                .fillna(NA_FILL_STRING)
                .set_index(["level"] + group_rows)
            )
        df_subtotals.columns = columns
        return df_subtotals

    def get_df_totals(self) -> DataFrame:
        if not self.has_metrics(self.columns, self.dataset):
            return DataFrame()

        df_totals = self.__get_df_data()

        if df_totals.empty:
            return DataFrame()

        df_totals.columns = self.get_df_columns(self.columns, self.dataset)

        return df_totals

    def __get_df_data(self, group_levels: int = None, totals=False) -> DataFrame:
        data = self.__get_query(group_levels, totals).all()

        with warnings.catch_warnings(record=True) as warning_list:
            df = DataFrame(data=data).convert_dtypes()

        if warning_list:
            for warning in warning_list:
                logger.warning(f"{warning._category_name}: {str(warning.message)}")

        df = self.translate_df(df, self.group_rows, self.dataset)
        return df

    def __get_query(self, group_levels: int = None, totals=False) -> Query:
        group_rows = self.group_rows[:group_levels] if group_levels else self.group_rows
        sort = [] if totals else self.sort
        return ReportQuery(
            self.dataset,
            self.property_ids,
            self.organization_id,
            self.custom_cdfs,
            self.custom_field_cdfs,
            self.columns,
            groups=group_rows,
            filters=self.filters,
            sort=sort,
            limit=self.limit,
            aggregate=True,
            format=self.format,
            property_timezone=self.property_timezone,
            property_settings=self.property_settings,
            formats=self.formats,
            metadata=self.metadata,
        ).get_query()
