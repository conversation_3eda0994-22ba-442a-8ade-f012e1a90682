from datetime import timezone
from typing import Optional

from pandas.core.frame import Data<PERSON>rame

from sqlalchemy.orm.query import Query

from app.datasets.dataset import Dataset
from app.enums import Cdf, Metric
from app.reports.report_cdf import ReportCDF
from app.reports.report_interface import ReportInterface
from app.reports.report_query import ReportQuery


class ReportMetrics(ReportInterface):
    def __init__(
        self,
        dataset: Dataset,
        custom_cdfs: list,
        custom_field_cdfs: list,
        columns: list,
        property_ids: list,
        organization_id: int,
        filters: dict,
        format: str,
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        formats: Optional[dict] = None,
    ):
        self.dataset = dataset
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.columns = columns
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.filters = filters
        self.format = format
        self.property_timezone = property_timezone
        self.property_settings = property_settings
        self.formats = formats

    def __repr__(self):
        """Representation of ReportMetrics
        :return: string
        """
        return f"<ReportMetrics, dataset={self.dataset}, columns={self.columns}, property_ids={self.property_ids}, filters={self.filters}>"

    @property
    def columns(self):
        return self.__columns

    @columns.setter
    def columns(self, columns):
        total = dict(
            cdf=dict(column=ReportCDF.STAR_CDF, type=Cdf.Default.value),
            metrics=[Metric.count.value],
        )
        self.__columns = [total] + columns

    def get_df(self) -> DataFrame:
        df = self.__get_df_data()

        if df.empty:
            return DataFrame()

        df.columns = self.get_df_columns(self.columns, self.dataset)
        return df

    def get_df_subtotals(self) -> Exception:
        return Exception("Subtotals not supported for this report")

    def get_df_totals(self) -> Exception:
        return Exception("Subtotals not supported for this report")

    def __get_df_data(self) -> DataFrame:
        data = self.__get_query().all()
        return DataFrame(data=data)

    def __get_query(self) -> Query:
        return ReportQuery(
            self.dataset,
            self.property_ids,
            self.organization_id,
            self.custom_cdfs,
            self.custom_field_cdfs,
            self.columns,
            filters=self.filters,
            sort=[],
            limit=None,
            aggregate=True,
            format=self.format,
            property_timezone=self.property_timezone,
            property_settings=self.property_settings,
            formats=self.formats,
            metadata=None,
        ).get_query()
