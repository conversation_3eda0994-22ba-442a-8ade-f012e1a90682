import warnings
from datetime import timezone
from typing import Optional

import pandas as pd
from pandas.core.frame import DataFrame

from sqlalchemy.orm.query import Query

from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.constants.table_limits import (
    EXPORT_LIMIT,
    SUMMARY_PREVIEW_LIMIT,
    SUMMARY_RUN_LIMIT,
)
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.enums import Mode
from app.reports.report_interface import ReportInterface
from app.reports.report_metadata import ReportMetadata
from app.reports.report_query import ReportQuery


class ReportSummaryDetails(ReportInterface):
    def __init__(
        self,
        mode: Mode,
        dataset: Dataset,
        custom_cdfs: list,
        custom_field_cdfs: list,
        columns: list,
        group_rows: list,
        property_ids: list,
        organization_id: int,
        filters: dict,
        sort: list,
        format: str,
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        formats: Optional[dict] = None,
        metadata: ReportMetadata = None,
    ):
        self.mode = mode
        self.dataset = dataset
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.columns = columns
        self.group_rows = group_rows
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.filters = filters
        self.sort = sort
        self.metadata = metadata
        self.limit = self.mode
        self.offset = self.mode
        self.format = format
        self.property_timezone = property_timezone
        self.property_settings = property_settings
        self.formats = formats

    def __repr__(self):
        """Representation of ReportSummaryDetails
        :return: string
        """
        return (
            f"<ReportSummaryDetails, mode={self.mode}, dataset={self.dataset}, columns={self.columns}, group_rows={self.group_rows}, "
            f"property_ids={self.property_ids}, filters={self.filters}, sort={self.sort}>"
        )

    @property
    def limit(self):
        return self.__limit

    @limit.setter
    def limit(self, mode):
        match (mode):
            case Mode.Preview:
                self.__limit = SUMMARY_PREVIEW_LIMIT
            case Mode.Run:
                self.__limit = SUMMARY_RUN_LIMIT
            case Mode.Export:
                self.__limit = EXPORT_LIMIT
            case Mode.Page:
                self.__limit = self.metadata.page_limit
            case _:
                self.__limit = None

    @property
    def offset(self):
        return self.__offset

    @offset.setter
    def offset(self, mode):
        match (mode):
            case Mode.Page:
                self.__offset = self.metadata.page_offset
            case _:
                self.__offset = 0

    def get_df(self) -> DataFrame:
        df = self.__get_df_data()

        if df.empty:
            return DataFrame()

        df = self.translate_df(df, self.columns + self.group_rows, self.dataset)
        group_rows = self.get_flatten_groups(self.group_rows)
        df = (
            df.astype("object")
            .infer_objects(copy=False)
            .fillna(NA_FILL_STRING)
            .set_index(group_rows)
        )
        return df

    def get_df_subtotals(self) -> DataFrame:
        if not self.has_metrics(self.columns, self.dataset):
            return DataFrame()

        group_rows = self.get_flatten_groups(self.group_rows)
        columns = self.get_df_columns(self.columns, self.dataset)
        frames = []
        for index, group_row in enumerate(self.group_rows, start=1):
            df = self.__get_df_data(index, True)
            if not df.empty:
                df[group_row["cdf"]["column"]] = (
                    df[group_row["cdf"]["column"]]
                    .astype("string")
                    .fillna(NA_FILL_STRING)
                )
            df.insert(0, "level", group_rows[(index - 1)])

            if index != len(group_rows):
                df[group_rows[index]] = ""

            if not df.empty:

                frames.append(df.astype("object"))

        if not len(frames):
            return DataFrame()

        df_subtotals = (
            pd.concat(frames)
            .astype("object")
            .infer_objects(copy=False)
            .fillna(NA_FILL_STRING)
            .set_index(["level"] + group_rows)
        )
        df_subtotals.columns = columns
        df_subtotals = df_subtotals.infer_objects(copy=False).fillna(NA_FILL_STRING)

        return df_subtotals

    def get_df_totals(self) -> DataFrame:
        if not self.has_metrics(self.columns, self.dataset):
            return DataFrame()

        df_totals = self.__get_df_data(aggregate=True)

        if df_totals.empty:
            return DataFrame()

        df_totals.columns = self.get_df_columns(self.columns, self.dataset)
        df_totals = (
            df_totals.astype("object").infer_objects(copy=False).fillna(NA_FILL_STRING)
        )
        return df_totals

    def __get_df_data(
        self, group_levels: int = None, aggregate: bool = False
    ) -> DataFrame:
        data = self.__get_query(group_levels, aggregate).all()
        with warnings.catch_warnings(record=True) as warning_list:
            df = DataFrame(data=data).convert_dtypes()

        if warning_list:
            for warning in warning_list:
                logger.warning(f"{warning._category_name}: {str(warning.message)}")

        return df

    def __get_query(self, group_levels: int = None, aggregate: bool = False) -> Query:
        columns = (
            [*self.group_rows, *self.columns] if group_levels is None else self.columns
        )

        group_rows = self.group_rows[:group_levels] if aggregate else []
        if aggregate:
            sort = (
                [
                    sort
                    for sort in self.sort
                    if sort["cdf"]["column"]
                    in [col["cdf"]["column"] for col in (group_rows)]
                ]
                if group_rows and self.sort
                else []
            )
        else:
            sort = self.sort
        return ReportQuery(
            self.dataset,
            self.property_ids,
            self.organization_id,
            self.custom_cdfs,
            self.custom_field_cdfs,
            columns,
            groups=group_rows,
            filters=self.filters,
            sort=sort,
            offset=self.offset,
            limit=self.limit,
            aggregate=aggregate,
            format=self.format,
            property_timezone=self.property_timezone,
            property_settings=self.property_settings,
            formats=self.formats,
            metadata=self.metadata,
        ).get_query()
