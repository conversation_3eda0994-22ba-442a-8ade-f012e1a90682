import warnings
from datetime import datetime
from io import By<PERSON><PERSON>
from typing import Callable, Optional

import numpy as np

from openpyxl.worksheet.worksheet import Worksheet

import pandas as pd
from pandas import DataFrame

from app.cdfs.cdf import CDF
from app.cdfs.cdfs import CDFs
from app.common.constants.duplicate_index_rename_string import (
    DUPLICATE_INDEX_RENAME_STRING,
)
from app.common.constants.dynamic_cdf_label import DYNAMIC_CDF_LABEL
from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.constants.report_formats import EXCHANGE_RATE_SUFFIX
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.enums.export import View
from app.enums.metric import Metric
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.reports.report_export_customization_pdf import ReportExportCustomizationPdf
from app.reports.report_export_customization_xlsx import ReportExportCustomizationXlsx
from app.reports.report_type import ReportType


class ReportExport:
    SUBTOTAL_LABELS = ("Subtotal", "Sub subtotal")
    Z_SUBTOTAL_LABELS = (f"~z{SUBTOTAL_LABELS[0]}", f"~z{SUBTOTAL_LABELS[1]}")
    TOTAL_LABEL = "Total"
    DEFAULT_START_ROW = 5
    START_ROW_WITH_CHARTS = 35
    METRIC_LIST = [metric.name for metric in Metric] + [DYNAMIC_CDF_LABEL]

    def __init__(
        self,
        dataset: Dataset,
        title: str,
        view: View,
        type: ReportType,
        details: bool,
        generated_at: datetime,
        custom_cdfs: list,
        custom_field_cdfs: list,
        records_df: DataFrame,
        subtotals_df: DataFrame,
        totals_df: DataFrame,
        multilevel_cdfs: list,
        transpose: bool = False,
        periods: Optional[list] = None,
        formats: Optional[dict] = None,
        charts: list[bytes] = [],
        comparisons: Optional[dict] = None,
        aggregated_count: Optional[int] = None,
        filters: Optional[dict] = None,
        original_filters: Optional[dict] = None,
    ) -> None:
        self.dataset = dataset
        self.title = title
        self.view = view
        self.type = type
        self.details = details
        self.generated_at = generated_at
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.records_df = records_df
        self.subtotals_df = subtotals_df
        self.totals_df = totals_df
        self.multilevel_cdfs = multilevel_cdfs
        self.transpose = transpose
        self.periods = periods
        self.formats = formats
        self.charts = charts
        self.comparisons = comparisons
        self.aggregated_count = aggregated_count
        self.filters = filters
        self.original_filters = original_filters
        self.START_ROW = (
            self.START_ROW_WITH_CHARTS if len(charts) else self.DEFAULT_START_ROW
        )

    def __repr__(self):
        """Representation of ReportExport
        :return: string
        """
        return (
            f"<ReportExport, dataset={self.dataset}, title={self.title}, view={self.view}, type={self.type}, details={self.details}, generated_at={self.generated_at},"
            f"records_df={self.records_df}, subtotals_df={self.subtotals_df}, totals_df={self.totals_df}, multilevel_cdfs={self.multilevel_cdfs}, filters={self.filters}>"
            f"records_df={self.records_df}, subtotals_df={self.subtotals_df}, totals_df={self.totals_df}, multilevel_cdfs={self.multilevel_cdfs}, filters={self.filters}>"
        )

    @property
    def multilevel_cdfs(self) -> list:
        return self.__multilevel_cdfs

    @multilevel_cdfs.setter
    def multilevel_cdfs(self, cdfs):
        self.__multilevel_cdfs = [
            CDF(
                self.dataset.kind,
                multilevel_cdf["cdf"]["column"],
                False,
                False,
                MultiLevelEnum(multilevel_cdf["cdf"]["multi_level_id"]),
            ).cdf
            for multilevel_cdf in CDFs.get_multi_level_cdfs(cdfs)
        ]

    @property
    def custom_cdfs(self) -> list:
        return self.__custom_cdfs

    @custom_cdfs.setter
    def custom_cdfs(self, custom_cdfs):
        if custom_cdfs is None:
            self.__custom_cdfs = []
        else:
            self.__custom_cdfs = [
                (
                    dict(
                        column=custom_cdf["column"],
                        name=custom_cdf["name"],
                        kind=custom_cdf["kind"],
                    )
                    if isinstance(custom_cdf, dict)
                    else dict(
                        column=custom_cdf.column,
                        name=custom_cdf.name,
                        kind=custom_cdf.kind,
                    )
                )
                for custom_cdf in custom_cdfs
            ]

    def get_df(self) -> DataFrame:
        if self.records_df.empty:
            return self.records_df

        if self.view == View.Formatted.value or self.view == View.Table.value:
            match (self.type):
                case ReportType.List:
                    df = self.__get_df_list()
                case ReportType.Summary:
                    if not self.details:
                        df = self.__get_df_summary()
                    if self.details:
                        df = self.__get_df_summary_details()
                case ReportType.Pivot:
                    df = self.__get_df_pivot()
                case ReportType.PeriodList | ReportType.ListComparison:
                    df = self.__get_df_comparison()
                case ReportType.SummaryComparison | ReportType.PeriodSummary:
                    df = self.__get_df_summary_comparison()

            if self.view == View.Table.value:
                df = df.reset_index()
        else:
            df = self.records_df
            df.columns = self.__get_converted_columns(df.columns.tolist())

        df = self.__rename_duplicate_indexes(df)
        return df

    def get_pdf_customization(
        self, df: DataFrame, pdf_buffer: BytesIO, total_record_count: int
    ) -> Callable:
        return ReportExportCustomizationPdf(
            generated_at=self.generated_at,
            pdf_buffer=pdf_buffer,
            total_record_count=total_record_count,
            df=df,
            title=self.title,
            type=self.type,
            charts=self.charts,
            records_df=self.records_df,
            totals_df=self.totals_df,
            subtotals_df=self.subtotals_df,
        ).apply()

    def get_xlsx_customization(
        self,
        worksheet: Worksheet,
        df: DataFrame,
        total_record_count: int,
    ) -> tuple[Worksheet, list[bytes]]:
        return ReportExportCustomizationXlsx(
            generated_at=self.generated_at,
            worksheet=worksheet,
            df=df,
            title=self.title,
            type=self.type,
            charts=self.charts,
            total_record_count=total_record_count,
            dataset=self.dataset,
            aggregated_count=self.aggregated_count,
            details=self.details,
            custom_cdfs=self.custom_cdfs,
            custom_field_cdfs=self.custom_field_cdfs,
            records_df=self.records_df,
            subtotals_df=self.subtotals_df,
            totals_df=self.totals_df,
            multilevel_cdfs=self.multilevel_cdfs,
            transpose=self.transpose,
            periods=self.periods,
            formats=self.formats,
            comparisons=self.comparisons,
            filters=self.filters,
            original_filters=self.original_filters,
        ).apply()

    def __get_df_list(self) -> DataFrame:
        df = self.records_df

        if not self.totals_df.empty:
            totals_df = self.totals_df
            df.insert(0, "", "")
            totals_df = (
                totals_df.stack(1, future_stack=True)
                .reset_index()
                .rename(columns={"level_1": ""})
                .drop(columns="level_0")
            )
            totals_df[""] = self.TOTAL_LABEL + " - " + totals_df[""]
            df = pd.concat([df, totals_df], ignore_index=True)

        df.columns = self.__get_converted_columns(df.columns.tolist())
        return df

    def __get_df_comparison(self) -> DataFrame:
        df = self.records_df

        df.index.names = self.__get_converted_columns(df.index.names)
        df.columns = [
            f"{self.__get_converted_column(column)} - {metric}"
            for column, metric in df.columns
        ]
        if self.transpose:
            df = df.transpose()

        return df

    def __get_df_summary_comparison(self) -> DataFrame:
        df = self.records_df
        index_names = df.index.names

        with warnings.catch_warnings(record=True) as warning_list:
            if not self.subtotals_df.empty:
                subtotals_df = self.subtotals_df
                subtotals_df = subtotals_df.reset_index(level=0, drop=True)
                subtotals_df = subtotals_df.rename(
                    index={"_SUBTOTAL_INDEX": self.Z_SUBTOTAL_LABELS[0]}
                )

                if len(df.index.names) == 3:
                    subtotals_df = subtotals_df.reset_index()
                    subtotals_df[df.index.names[-1]] = subtotals_df[
                        df.index.names[-2]
                    ].apply(
                        lambda value: (
                            ""
                            if value == self.Z_SUBTOTAL_LABELS[0]
                            else self.Z_SUBTOTAL_LABELS[1]
                        )
                    )
                    subtotals_df = subtotals_df.set_index(df.index.names)

                for subtotal in subtotals_df.iterrows():

                    masks = self.__get_masks(df, subtotal)

                    if not df[masks[0]].empty:
                        # when everything matches, its easy
                        top_part = df.iloc[
                            : df.index.get_indexer_for(df[masks[0]].index)[-1] + 1
                        ]
                    elif not df.index[masks[1]].empty:
                        # checking everything but the last level of the index
                        top_part = df.iloc[
                            : df.index.get_indexer_for(df[masks[1]].index)[-1] + 1
                        ]

                    # the remainder of the df is the bottom part
                    bottom_part = df.iloc[len(top_part) :]

                    # adding the subtotal to the top part
                    top_part = pd.concat(
                        [top_part, pd.DataFrame([subtotal[1]], index=[subtotal[0]])]
                    )
                    # recombining the top and bottom parts
                    df = pd.concat([top_part, bottom_part])

                df.index.names = index_names

        if warning_list:
            for warning in warning_list:
                logger.warning(f"{warning._category_name}: {str(warning.message)}")

        if self.totals_df is not None and not self.totals_df.empty:
            totals_df = self.totals_df
            totals_df = pd.DataFrame(
                self.totals_df.unstack().reorder_levels([2, 0, 1])
            ).transpose()
            totals_df.index = (
                pd.MultiIndex.from_tuples([("Total",) + ("",) * (df.index.nlevels - 1)])
                if df.index.nlevels > 1
                else pd.Index(["Total"])
            )
            df = pd.concat([df, totals_df])

        df = df.rename(index={self.Z_SUBTOTAL_LABELS[0]: self.SUBTOTAL_LABELS[0]})
        df = df.rename(index={self.Z_SUBTOTAL_LABELS[1]: self.SUBTOTAL_LABELS[1]})

        df.index.names = self.__get_converted_columns(index_names)
        df.columns.names = self.__get_converted_columns(df.columns.names)
        df.columns = df.columns.set_levels(
            self.__get_converted_columns(df.columns.levels[-2].tolist()),
            level=int(len(df.columns.names) / 2),
        )
        df.columns.names = [
            "Periods" if self.periods else "Comparisons"
        ] + df.columns.names[1:]

        if self.transpose:
            df = df.transpose()

        return df

    def __get_df_summary(self) -> DataFrame:
        df = self.records_df

        with warnings.catch_warnings(record=True) as warning_list:
            if not self.subtotals_df.empty:
                index_names = df.index.names
                subtotals_df = self.subtotals_df
                subtotals_df = subtotals_df.reset_index(level=0, drop=True)
                subtotals_df = subtotals_df.rename(
                    index={"_SUBTOTAL_INDEX": self.Z_SUBTOTAL_LABELS[0]}
                )

                if len(df.index.names) == 3:
                    subtotals_df = subtotals_df.reset_index()
                    subtotals_df[df.index.names[-1]] = subtotals_df[
                        df.index.names[-2]
                    ].apply(
                        lambda value: (
                            ""
                            if value == self.Z_SUBTOTAL_LABELS[0]
                            else self.Z_SUBTOTAL_LABELS[1]
                        )
                    )
                    subtotals_df = subtotals_df.set_index(df.index.names)

                for subtotal in subtotals_df.iterrows():

                    masks = self.__get_masks(df, subtotal)

                    if not df[masks[0]].empty:
                        # when everything matches, its easy
                        top_part = df.iloc[
                            : df.index.get_indexer_for(df[masks[0]].index)[-1] + 1
                        ]
                    elif not df.index[masks[1]].empty:
                        # checking everything but the last level of the index
                        top_part = df.iloc[
                            : df.index.get_indexer_for(df[masks[1]].index)[-1] + 1
                        ]

                    # the remainder of the df is the bottom part
                    bottom_part = df.iloc[len(top_part) :]

                    # adding the subtotal to the top part
                    top_part = pd.concat(
                        [top_part, pd.DataFrame([subtotal[1]], index=[subtotal[0]])]
                    )
                    # recombining the top and bottom parts
                    df = pd.concat([top_part, bottom_part])

                df.index.names = index_names

        if warning_list:
            for warning in warning_list:
                logger.warning(f"{warning._category_name}: {str(warning.message)}")

        if not self.totals_df.empty:
            totals_df = self.totals_df
            for index, column in enumerate(df.index.names):
                totals_df[column] = self.TOTAL_LABEL if index == 0 else ""

            totals_df = totals_df.set_index(df.index.names)

            df = pd.concat([df, totals_df])

        df = df.rename(
            index={
                self.Z_SUBTOTAL_LABELS[0]: self.SUBTOTAL_LABELS[0],
                self.Z_SUBTOTAL_LABELS[1]: self.SUBTOTAL_LABELS[1],
            }
        )
        df.index.names = self.__get_converted_columns(df.index.names)
        df.columns = [
            f"{self.__get_converted_column(column)} - {metric}"
            for column, metric in df.columns
        ]

        if self.transpose:
            df = df.transpose()

        return df

    def __get_df_summary_details(self) -> DataFrame:
        df = self.records_df
        if not self.subtotals_df.empty:
            index_names = df.index.names
            subtotals_df = self.subtotals_df

            subtotals_df = subtotals_df.stack(1, future_stack=True).reset_index()

            level_dfs = []
            for i, name in enumerate(df.index.names):
                level_df = (
                    subtotals_df[subtotals_df["level"] == name]
                    .rename(columns={f"{'level_'}{len(df.index.names) + 1}": "metric"})
                    .drop(columns=["level"])
                )

                level_df[name] = (
                    level_df[name].astype(str) + " " + level_df["metric"].astype(str)
                )
                level_df["level"] = i
                level_df = level_df.set_index(df.index.names)
                level_dfs.append(level_df)
                # put the levels in reverse order, so the first level gets added last (matters most when there are more records than subtotals)
                level_dfs = level_dfs[::-1]

            subtotals_df = pd.concat(level_dfs)

            for subtotal in subtotals_df.iterrows():
                n = 0
                if df.index.nlevels == 1:
                    x = subtotal[n][: -(len(subtotal[1]["metric"]) + 1)]
                    last_row_index = df.index[
                        df.index.astype(str).isin(
                            [str(x)]
                            + [str(x) + " " + metric for metric in self.METRIC_LIST][
                                : self.METRIC_LIST.index(subtotal[1]["metric"])
                            ]
                        )
                    ]
                    if len(last_row_index) == 0:
                        top_part = df
                    else:
                        last_row_index = last_row_index.tolist()[-1]
                        top_part = df.loc[:last_row_index]

                else:
                    df["metric"] = np.nan
                    masks = self.__get_masks_with_metrics(df, subtotal)

                    if masks[0].any():
                        # when everything matches, its easy
                        top_part = df.iloc[
                            : max(
                                [
                                    i + 1
                                    for i, val in enumerate(masks[0].to_numpy())
                                    if val
                                ]
                            )
                        ]

                    elif df.index.nlevels >= 2 and masks[1].any():
                        # checking everything but the last level of the index
                        top_part = df.iloc[
                            : max(
                                [
                                    i + 1
                                    for i, val in enumerate(masks[1].to_numpy())
                                    if val
                                ]
                            )
                        ]

                    elif df.index.nlevels >= 3 and masks[2].any():
                        # checking only the first level of a 3 level index
                        top_part = df.iloc[
                            : max(
                                [
                                    i + 1
                                    for i, val in enumerate(masks[2].to_numpy())
                                    if val
                                ]
                            )
                        ]

                    else:
                        # when the limit was reached on records, but not subtotals add the subtotal to the end of the frame
                        top_part = df

                # the remainder of the df is the bottom part
                bottom_part = df.iloc[len(top_part) :]

                # adding the subtotal to the top part
                top_part = pd.concat(
                    [top_part, pd.DataFrame([subtotal[1]], index=[subtotal[0]])]
                )
                # recombining the top and bottom parts
                df = pd.concat([top_part, bottom_part])

                # pandas wants a sorted index, but we want it exactly in this order, assign an aux index and sort on it then drop it
                aux_index = pd.RangeIndex(start=0, stop=len(df))
                df["aux_index"] = aux_index
                df = df.sort_index().sort_values("aux_index")
                df = df.drop(columns=["aux_index"])

            df = df.drop(columns=["metric", "level"])
            df.index.names = index_names

        if not self.totals_df.empty:
            totals_df = self.totals_df
            totals_df = (
                totals_df.stack(1, future_stack=True)
                .reset_index()
                .drop(columns="level_0")
                .rename(columns={"level_1": subtotals_df.index.names[0]})
            )
            totals_df[subtotals_df.index.names[0]] = (
                self.TOTAL_LABEL
                + " "
                + totals_df[subtotals_df.index.names[0]].astype(str)
            )
            totals_df[subtotals_df.index.names[1:]] = NA_FILL_STRING
            totals_order = [
                "Total " + metric
                for metric in self.METRIC_LIST
                if "Total " + metric
                in totals_df[subtotals_df.index.names[0]].values.tolist()
            ]
            totals_df = totals_df.set_index(subtotals_df.index.names[0])
            totals_df = totals_df.reindex(totals_order).reset_index()
            totals_df = totals_df.set_index(subtotals_df.index.names)
            df = pd.concat([df, totals_df])

        df.index.names = self.__get_converted_columns(df.index.names)
        df.columns = self.__get_converted_columns(df.columns.tolist())

        return df

    def __get_df_pivot(self) -> DataFrame:
        df = self.records_df

        if not self.subtotals_df.empty:
            subtotals_df = self.subtotals_df
            subtotals_df.index = pd.MultiIndex.from_arrays(
                [
                    subtotals_df.index,
                    [self.SUBTOTAL_LABELS[0]] * len(subtotals_df.index),
                ],
                names=df.index.names,
            )
            for subtotal in subtotals_df.iterrows():
                index_names = df.index.names
                top_part = df.iloc[
                    : max(
                        df.index.droplevel(1)
                        .get_level_values(0)
                        .astype(str)
                        .isin([subtotal[0][0]])
                        .nonzero()[0]
                    )
                    + 1
                ]
                top_part = pd.concat(
                    [top_part, pd.DataFrame([subtotal[1]], index=[subtotal[0]])]
                )
                bottom_part = df.iloc[len(top_part) - 1 :]
                df = pd.concat([top_part, bottom_part])
                df.index.names = index_names

        if not self.totals_df.empty:
            totals_df = self.totals_df
            reorder_levels = list(range(2, len(df.columns.names))) + [0, 1]
            totals_df = (
                totals_df.unstack(level=list(range(int(len(df.columns.names) / 2))))
                .reorder_levels(reorder_levels)
                .sort_index(level=range(len(df.index.names)))
                .to_frame()
                .transpose()
            )
            totals_df.index = (
                pd.Index([self.TOTAL_LABEL], name=df.index.names[0])
                if len(df.index.names) == 1
                else pd.MultiIndex.from_arrays(
                    [[self.TOTAL_LABEL], [""] * (len(df.index.names) - 1)],
                    names=df.index.names,
                )
            )
            df = pd.concat([df, totals_df])

        df = df.rename(index={self.Z_SUBTOTAL_LABELS[0]: self.SUBTOTAL_LABELS[0]})
        df.index.names = self.__get_converted_columns(df.index.names)
        df.columns.names = self.__get_converted_columns(df.columns.names)
        df.columns = df.columns.set_levels(
            self.__get_converted_columns(df.columns.levels[-2].tolist()),
            level=int(len(df.columns.names) / 2),
        )
        return df

    def __get_converted_column(self, column: str) -> str:
        return next(
            self.__get_cdf_name(cdf)
            for cdf in self.dataset.flatten_cdfs
            + self.custom_cdfs
            + self.multilevel_cdfs
            if cdf["column"] == column
        )

    def __get_converted_columns(self, columns: list) -> list:
        column_names = {
            cdf["column"]: self.__get_cdf_name(cdf)
            for cdf in self.dataset.flatten_cdfs
            + self.custom_cdfs
            + self.custom_field_cdfs
            + self.multilevel_cdfs
            if cdf["column"] in columns
        }
        return [
            column_names[column] if column in column_names else column
            for column in columns
        ]

    def __get_cdf_name(self, cdf: dict) -> str:
        return (
            cdf["name"].replace(
                EXCHANGE_RATE_SUFFIX, f"({self.formats.get('currency')})"
            )
            if self.formats
            and self.formats.get("currency")
            and EXCHANGE_RATE_SUFFIX in cdf["name"]
            else cdf["name"]
        )

    def __get_masks_with_metrics(self, df: DataFrame, subtotal: tuple) -> list:
        # given a two or three multiindex dataframe with varying types of datatypes in index
        # and a tuple containing one, two or three values
        # will return a list of masks for up to 3 levels
        # there will be one mask for each level in the df.index
        # the first one will have all levels for either 2 or 3 level index, e.g. ('59.99', '2021-01-01') or ('59.99', '2021-01-01', 'Total')
        # the second one drop the last level to allow matching indexes when the tuple doesn't match on its last value e.g. ('59.99', '') or ('59.99', '2021-01-01', '')
        # the third one will only apply when there are 3 index levels and will drop the last two levels when the tuple only matches on the first value e.g. ('59.99', '', '')
        # these masks will be used to filter the dataframe by values in tuple like '59.99' and values in df.index like 59.99
        # this is how summary with details export subtotals can be inserted without disturbing the original sort of the data
        # setting the index to type str will break sort
        # to ensure metrics appear in the right order, we have to find not only the last occurence of the 'value' without metrics
        # but also the potential last occurence of the value with all the metrics that could come before it
        # e.g. for Value sum, we can build it first, directly under the last occurence of Value
        # for Value count, we have to make sure it comes after every Value row, and every Value sum, Value mean, Value max, Value  min row
        n = 0
        metric = subtotal[1]["metric"]
        level = subtotal[1]["level"]
        match df.index.nlevels:
            case 2:
                if level == 0:
                    x = tuple(
                        [
                            subtotal[n][0][: -(len(metric) + 1)],
                            subtotal[n][1],
                        ]
                    )

                else:
                    x = tuple(
                        [
                            subtotal[n][0],
                            subtotal[n][1][: -(len(metric) + 1)],
                        ]
                    )

            case 3:

                # three cases, metric in last value or second value
                if level == 0:
                    x = tuple(
                        [
                            subtotal[n][0][: -(len(metric) + 1)],
                            subtotal[n][1],
                            subtotal[n][2],
                        ]
                    )

                elif level == 1:
                    x = tuple(
                        [
                            subtotal[n][0],
                            subtotal[n][1][: -(len(metric) + 1)],
                            subtotal[n][2],
                        ]
                    )

                else:
                    x = tuple(
                        [
                            subtotal[n][0],
                            subtotal[n][1],
                            subtotal[n][2][: -(len(metric) + 1)],
                        ]
                    )
        masks = []
        in_list_0 = [str(x[0])] + [
            str(x[0]) + " " + metric
            for metric in self.METRIC_LIST[: self.METRIC_LIST.index(metric)]
        ][: self.METRIC_LIST.index(metric)]
        in_list_1 = [str(x[1])] + [
            str(x[1]) + " " + metric
            for metric in self.METRIC_LIST[: self.METRIC_LIST.index(metric)]
        ][: self.METRIC_LIST.index(metric)]
        in_list_2 = (
            [str(x[2])]
            + [
                str(x[2]) + " " + metric
                for metric in self.METRIC_LIST[: self.METRIC_LIST.index(metric)]
            ][: self.METRIC_LIST.index(metric)]
            if len(x) == 3
            else None
        )

        mask = (
            (
                (df.index.get_level_values(0).astype(str).isin(in_list_0))
                & (df.index.get_level_values(1).astype(str).isin(in_list_1))
                & (
                    (
                        (df.index.get_level_values(0).astype(str).isin(in_list_0))
                        & (df.index.get_level_values(1).astype(str).isin(in_list_1))
                        & df["metric"].isnull()
                    )
                    | (
                        (
                            (~df.index.get_level_values(0).astype(str).isin(in_list_0))
                            | (
                                ~df.index.get_level_values(1)
                                .astype(str)
                                .isin(in_list_1)
                            )
                        )
                        & df["metric"].notnull()
                    )
                )
            )
            if df.index.nlevels == 2
            else (
                (df.index.get_level_values(0).astype(str).isin(in_list_0))
                & (df.index.get_level_values(1).astype(str).isin(in_list_1))
                & (df.index.get_level_values(2).astype(str).isin(in_list_2))
                & (
                    (
                        (df.index.get_level_values(0).astype(str).isin(in_list_0))
                        & (df.index.get_level_values(1).astype(str).isin(in_list_1))
                        & (df.index.get_level_values(2).astype(str).isin(in_list_2))
                        & df["metric"].isnull()
                    )
                    | (
                        (
                            (~df.index.get_level_values(0).astype(str).isin(in_list_0))
                            | (
                                ~df.index.get_level_values(1)
                                .astype(str)
                                .isin(in_list_1)
                            )
                            | (
                                ~df.index.get_level_values(2)
                                .astype(str)
                                .isin(in_list_2)
                            )
                        )
                        & df["metric"].notnull()
                    )
                )
            )
        )

        masks.append(mask)

        # drop last level
        mask = (
            (
                (
                    df.index.droplevel(1)
                    .get_level_values(0)
                    .astype(str)
                    .isin(
                        [str(x[0])]
                        + [str(x[0]) + " " + metric for metric in self.METRIC_LIST][
                            : self.METRIC_LIST.index(metric)
                        ]
                    )
                )
                & (
                    (
                        (df.index.get_level_values(0).astype(str) == str(x[0]))
                        & df["metric"].isnull()
                    )
                    | (
                        (df.index.get_level_values(0).astype(str) != str(x[0]))
                        & df["metric"].notnull()
                    )
                )
            )
            if df.index.nlevels == 2
            else (
                (
                    df.index.droplevel(2)
                    .get_level_values(0)
                    .astype(str)
                    .isin(
                        [str(x[0])]
                        + [str(x[0]) + " " + metric for metric in self.METRIC_LIST][
                            : self.METRIC_LIST.index(metric)
                        ]
                    )
                )
                & (
                    df.index.droplevel(2)
                    .get_level_values(1)
                    .astype(str)
                    .isin(
                        [str(x[1])]
                        + [str(x[1]) + " " + metric for metric in self.METRIC_LIST][
                            : self.METRIC_LIST.index(metric)
                        ]
                    )
                )
                & (
                    (
                        (df.index.get_level_values(0).astype(str) == str(x[0]))
                        & (df.index.get_level_values(1).astype(str) == str(x[1]))
                        & df["metric"].isnull()
                    )
                    | (
                        (
                            (df.index.get_level_values(0).astype(str) != str(x[0]))
                            | (df.index.get_level_values(1).astype(str) != str(x[1]))
                        )
                        & df["metric"].notnull()
                    )
                )
            )
        )

        masks.append(mask)

        # None for 2 levels, first level only for 3
        mask = (
            (
                (
                    df.index.droplevel(2)
                    .droplevel(1)
                    .get_level_values(0)
                    .astype(str)
                    .isin(
                        [str(x[0])]
                        + [str(x[0]) + " " + metric for metric in self.METRIC_LIST][
                            : self.METRIC_LIST.index(metric)
                        ]
                    )
                )
                & (
                    (
                        (df.index.get_level_values(0).astype(str) == str(x[0]))
                        & df["metric"].isnull()
                    )
                    | (
                        (df.index.get_level_values(0).astype(str) != str(x[0]))
                        & df["metric"].notnull()
                    )
                )
            )
            if df.index.nlevels == 3
            else None
        )
        masks.append(mask)

        return masks

    def __get_masks(self, df: DataFrame, subtotal: tuple) -> list:
        masks = []

        match (df.index.nlevels):
            case 2:
                # subtotal level mask, drop the last level and try to match the first level only:
                mask = (
                    df.index.droplevel(1)
                    .get_level_values(0)
                    .astype(str)
                    .isin([str(subtotal[0][0])])
                )
                masks.append(mask)
                # there will be no second mask for 2 level index
                masks.append(None)

            case 3:
                # sub subtotal level mask, drop the last level and try to match the first two levels only:
                mask = (
                    df.index.droplevel(2)
                    .get_level_values(0)
                    .astype(str)
                    .isin([str(subtotal[0][0])])
                ) & (
                    df.index.droplevel(2)
                    .get_level_values(1)
                    .astype(str)
                    .isin([str(subtotal[0][1])])
                )
                masks.append(mask)
                # subtotal level mask, drop the last two levels and try to match the first level only:
                mask = (
                    df.index.droplevel(2)
                    .droplevel(1)
                    .get_level_values(0)
                    .astype(str)
                    .isin([str(subtotal[0][0])])
                )
                masks.append(mask)
        return masks

    def __rename_duplicate_indexes(self, df):
        indexes_to_rename = [index for index in df.index.names if index in df.columns]
        new_index_names = [
            (
                index + DUPLICATE_INDEX_RENAME_STRING
                if index in indexes_to_rename
                else index
            )
            for index in df.index.names
        ]
        df.index.names = new_index_names
        return df
