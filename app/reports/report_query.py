from collections import OrderedDict
from datetime import timezone
from types import SimpleNamespace
from typing import Optional

from sqlalchemy.dialects import postgresql
from sqlalchemy.orm.collections import InstrumentedList
from sqlalchemy.orm.query import Query
from sqlalchemy.sql.expression import nullsfirst


from app.cdfs.cdf import CDF
from app.common.logger import logger
from app.datasets.custom_field import CustomField
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums import Cdf, CdfKind
from app.enums.custom_fields import CustomFields as CustomFieldsEnum
from app.enums.format import NumericFormat
from app.enums.mode import Mode
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.models.report import ReportCustomCdf
from app.reports.cte_cdf import CTECDF
from app.reports.report_cases import ReportCases
from app.reports.report_cdf import ReportCDF
from app.reports.report_filters import ReportFilters
from app.reports.report_metadata import ReportMetadata


from dependencies.sqlalchemy_filters import apply_filters


class ReportQuery:
    def __init__(
        self,
        dataset: Dataset,
        property_ids: list,
        organization_id: int,
        custom_cdfs: list,
        custom_field_cdfs: list,
        columns: list,
        groups: list = [],
        filters: Optional[dict] = None,
        sort: list = [],
        limit: Optional[int] = None,
        aggregate: bool = False,
        format: str = NumericFormat.Raw.value,
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        formats: Optional[dict] = None,
        metadata: ReportMetadata = None,
        include_raw_groups_for_sort: bool = False,
        offset: int = 0,
        comparisons: list = None,
    ):

        self.__configure_cte_meta(
            columns, groups, filters, sort, custom_cdfs, custom_field_cdfs, formats
        )
        self.property_settings = property_settings
        self.format = format
        self.formats = formats
        self.function_args = dict(
            currency=self.formats.get("currency") if self.formats else None
        )
        self.property_timezone = property_timezone
        self.dataset = dataset
        self.aggregate = aggregate
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.metadata = metadata
        self.include_raw_groups_for_sort = include_raw_groups_for_sort
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.groups = groups
        self.group_by = groups
        self.order_by = (sort, columns, groups)
        self.sort = [] if sort is None else sort
        self.limit = limit
        self.offset = offset if offset else 0
        self.comparisons = comparisons

        self.cases = ReportCases(
            self.dataset,
            self.comparisons,
            self.property_timezone,
            function_args=self.function_args,
        )
        if self.cases.report_filters:
            if not filters:
                filters = {"and": []}
            filters["and"] += [self.cases.report_filters]

        self.multi_levels = (
            columns
            + groups
            + ReportFilters(
                self.dataset,
                filters,
                self.property_ids,
                self.organization_id,
                self.property_timezone,
            ).get_rules()
            + self.sort
        )
        self.filters = ReportFilters(
            self.dataset,
            filters,
            self.property_ids,
            self.organization_id,
            self.property_timezone,
            self.multi_levels,
            self.custom_field_cdfs,
            custom_cdfs=(
                [
                    ReportCDF(
                        model=self.dataset.model,
                        cdf=dict(
                            cdf=dict(
                                column=(
                                    custom_cdf["column"]
                                    if isinstance(custom_cdf, (dict, OrderedDict))
                                    else custom_cdf.column
                                ),
                                type=Cdf.Custom.value,
                            )
                        ),
                        dataset_id=self.dataset.kind.value,
                        custom_cdf=(
                            SimpleNamespace(**custom_cdf)
                            if isinstance(custom_cdf, (dict, OrderedDict))
                            else custom_cdf
                        ),
                        dynamic=(
                            custom_cdf.get("dynamic", False)
                            if isinstance(custom_cdf, (dict, OrderedDict))
                            else getattr(custom_cdf, "dynamic", False)
                        ),
                        format=self.format,
                        property_settings=self.property_settings,
                        formats=self.formats,
                        custom_field_cdfs=self.custom_field_cdfs,
                        metadata=self.metadata,
                    ).get_cdf(skip_aggregation=not self.aggregate)
                    for custom_cdf in self.custom_cdfs
                ]
                if self.custom_cdfs
                else []
            ),
            function_args=self.function_args,
        ).get_filters()

        self.columns = columns

    def __repr__(self):
        """Representation of ReportQuery
        :return: string
        """
        return (
            f"<ReportQuery, dataset={self.dataset}, property_ids={self.property_ids}, custom_cdfs={self.custom_cdfs}, columns={self.columns}, "
            f"groups={self.groups}, filters={self.filters}, sort={self.sort}, offset={self.offset}, limit={self.limit}, aggregate={self.aggregate}>"
        )

    @property
    def select(self):
        return self.groups + self.columns

    @property
    def columns(self):
        return self.__columns

    @columns.setter
    def columns(self, columns):
        self.__columns = []
        if self.aggregate:
            columns = self.__get_aggregate_columns(columns)
        if self.comparisons is None:
            self.__columns = [
                ReportCDF(
                    (
                        CustomField(CustomFieldsEnum(self.dataset.kind.value)).model
                        if column["cdf"]["type"] == Cdf.CustomField.value
                        else (
                            MultiLevel(
                                MultiLevelEnum(column["cdf"]["multi_level_id"])
                            ).model
                            if "multi_level_id" in column["cdf"]
                            else self.dataset.model
                        )
                    ),
                    column,
                    self.dataset.kind.value,
                    CDF(
                        self.dataset.kind, column["cdf"]["column"], is_custom_cdf=True
                    ).get_custom_cdf_in_custom_cdfs(self.custom_cdfs),
                    CDF(
                        self.dataset.kind,
                        column["cdf"]["column"],
                        is_custom_field_cdf=True,
                    ).get_custom_field_cdf_in_custom_field_cdfs(self.custom_field_cdfs),
                    column.get("metric"),
                    column.get("modifier"),
                    dynamic=column.get("dynamic", False),
                    format=self.format,
                    property_settings=self.property_settings,
                    formats=self.formats,
                    custom_field_cdfs=self.custom_field_cdfs,
                    metadata=self.metadata,
                ).get_cdf(skip_aggregation=not self.aggregate)
                for column in columns
            ]
        else:
            for comparison in self.comparisons:
                temp_columns = [
                    ReportCDF(
                        (
                            CustomField(CustomFieldsEnum(self.dataset.kind.value)).model
                            if column["cdf"]["type"] == Cdf.CustomField.value
                            else (
                                MultiLevel(
                                    MultiLevelEnum(column["cdf"]["multi_level_id"])
                                ).model
                                if "multi_level_id" in column["cdf"]
                                else self.dataset.model
                            )
                        ),
                        column,
                        self.dataset.kind.value,
                        CDF(
                            self.dataset.kind,
                            column["cdf"]["column"],
                            is_custom_cdf=True,
                        ).get_custom_cdf_in_custom_cdfs(self.custom_cdfs),
                        CDF(
                            self.dataset.kind,
                            column["cdf"]["column"],
                            is_custom_field_cdf=True,
                        ).get_custom_field_cdf_in_custom_field_cdfs(
                            self.custom_field_cdfs
                        ),
                        column.get("metric"),
                        column.get("modifier"),
                        dynamic=column.get("dynamic", False),
                        format=self.format,
                        property_settings=self.property_settings,
                        formats=self.formats,
                        custom_field_cdfs=self.custom_field_cdfs,
                        metadata=self.metadata,
                        comparison_name=comparison["name"],
                        comparison_cases=(self.cases.cases[comparison["name"]]),
                    ).get_cdf(skip_aggregation=not self.aggregate)
                    for column in columns
                ]
                self.__columns += temp_columns

    @property
    def groups(self):
        return self.__group_by if self.include_raw_groups_for_sort else self.__groups

    @groups.setter
    def groups(self, groups):
        self.__groups = [
            ReportCDF(
                (
                    CustomField(CustomFieldsEnum(self.dataset.kind.value)).model
                    if column["cdf"]["type"] == Cdf.CustomField.value
                    else (
                        MultiLevel(
                            MultiLevelEnum(column["cdf"]["multi_level_id"])
                        ).model
                        if "multi_level_id" in column["cdf"]
                        else self.dataset.model
                    )
                ),
                column,
                self.dataset.kind.value,
                CDF(
                    self.dataset.kind, column["cdf"]["column"], is_custom_cdf=True
                ).get_custom_cdf_in_custom_cdfs(self.custom_cdfs),
                CDF(
                    self.dataset.kind, column["cdf"]["column"], is_custom_field_cdf=True
                ).get_custom_field_cdf_in_custom_field_cdfs(self.custom_field_cdfs),
                column.get("metric"),
                column.get("modifier"),
                dynamic=column.get("dynamic", False),
                to_char=True,
                format=self.format,
                property_settings=self.property_settings,
                formats=self.formats,
                custom_field_cdfs=self.custom_field_cdfs,
                metadata=self.metadata,
            ).get_cdf(skip_aggregation=True)
            for column in groups
        ]

    @property
    def filters(self):
        return self.__filters

    @filters.setter
    def filters(self, filters):
        self.__filters = filters

    @property
    def group_by(self):
        return self.__group_by

    @group_by.setter
    def group_by(self, groups):
        raw_groups = []
        # add each group twice, once formatted how columns is with to_char for display and once raw for grouping/sorting
        raw_groups = [
            ReportCDF(
                (
                    CustomField(CustomFieldsEnum(self.dataset.kind.value)).model
                    if column["cdf"]["type"] == Cdf.CustomField.value
                    else (
                        MultiLevel(
                            MultiLevelEnum(column["cdf"]["multi_level_id"])
                        ).model
                        if "multi_level_id" in column["cdf"]
                        else self.dataset.model
                    )
                ),
                column,
                self.dataset.kind.value,
                CDF(
                    self.dataset.kind, column["cdf"]["column"], is_custom_cdf=True
                ).get_custom_cdf_in_custom_cdfs(self.custom_cdfs),
                CDF(
                    self.dataset.kind,
                    column["cdf"]["column"],
                    is_custom_field_cdf=True,
                ).get_custom_field_cdf_in_custom_field_cdfs(self.custom_field_cdfs),
                column.get("metric"),
                column.get("modifier"),
                dynamic=column.get("dynamic", False),
                to_char=False,
                format=NumericFormat.Raw.value,
                property_settings=self.property_settings,
                formats=None,
                custom_field_cdfs=self.custom_field_cdfs,
                metadata=self.metadata,
                raw_group_for_sort=True,
            ).get_cdf(skip_aggregation=True)
            for column in groups
        ]
        formatted_groups = [
            ReportCDF(
                (
                    CustomField(CustomFieldsEnum(self.dataset.kind.value)).model
                    if column["cdf"]["type"] == Cdf.CustomField.value
                    else (
                        MultiLevel(
                            MultiLevelEnum(column["cdf"]["multi_level_id"])
                        ).model
                        if "multi_level_id" in column["cdf"]
                        else self.dataset.model
                    )
                ),
                column,
                self.dataset.kind.value,
                CDF(
                    self.dataset.kind, column["cdf"]["column"], is_custom_cdf=True
                ).get_custom_cdf_in_custom_cdfs(self.custom_cdfs),
                CDF(
                    self.dataset.kind, column["cdf"]["column"], is_custom_field_cdf=True
                ).get_custom_field_cdf_in_custom_field_cdfs(self.custom_field_cdfs),
                column.get("metric"),
                column.get("modifier"),
                dynamic=column.get("dynamic", False),
                to_char=True,
                format=self.format,
                property_settings=self.property_settings,
                formats=self.formats,
                custom_field_cdfs=self.custom_field_cdfs,
                metadata=self.metadata,
            ).get_cdf(skip_aggregation=True)
            for column in groups
        ]

        self.__group_by = formatted_groups + raw_groups

    @property
    def order_by(self):
        return self.__order_by

    @order_by.setter
    def order_by(self, args):
        sort, columns, group_by = args

        if not sort and not self.aggregate:
            sort = self.dataset.default_sorts

        self.__order_by = (
            [
                ReportCDF(
                    (
                        CustomField(CustomFieldsEnum(self.dataset.kind.value)).model
                        if cdf_sort["cdf"]["type"] == Cdf.CustomField.value
                        else (
                            MultiLevel(
                                MultiLevelEnum(cdf_sort["cdf"]["multi_level_id"])
                            ).model
                            if "multi_level_id" in cdf_sort["cdf"]
                            else self.dataset.model
                        )
                    ),
                    cdf_sort,
                    self.dataset.kind.value,
                    CDF(
                        self.dataset.kind, cdf_sort["cdf"]["column"], is_custom_cdf=True
                    ).get_custom_cdf_in_custom_cdfs(self.custom_cdfs),
                    CDF(
                        self.dataset.kind,
                        cdf_sort["cdf"]["column"],
                        is_custom_field_cdf=True,
                    ).get_custom_field_cdf_in_custom_field_cdfs(self.custom_field_cdfs),
                    cdf_sort.get("metric"),
                    next(
                        (
                            cdf_column.get("modifier")
                            for cdf_column in columns + group_by
                            if cdf_column["cdf"]["column"] == cdf_sort["cdf"]["column"]
                        ),
                        None,
                    ),
                    dynamic=cdf_sort.get("dynamic", False),
                    to_char=False,
                    property_settings=self.property_settings,
                    format=NumericFormat.Raw.value,
                    formats=None,
                    custom_field_cdfs=self.custom_field_cdfs,
                    metadata=self.metadata,
                    raw_group_for_sort=True,
                ).get_cdf(skip_aggregation=not self.aggregate)
                for cdf_sort in sort
            ]
            if sort
            else []
        )

    @property
    def multi_levels(self):
        return self.__multi_levels

    @multi_levels.setter
    def multi_levels(self, cdfs):
        joinables = [
            MultiLevel(MultiLevelEnum(multi_level))
            for multi_level in set(
                cdf["cdf"]["multi_level_id"]
                for cdf in cdfs
                if "multi_level_id" in cdf["cdf"]
            )
        ]
        if any(cdf for cdf in cdfs if cdf["cdf"]["type"] == Cdf.CustomField.value):
            joinables += [CustomField(CustomFieldsEnum(self.dataset.kind.value))]
        self.__multi_levels = joinables

    def __get_aggregate_columns(self, columns):
        if issubclass(type(self.custom_cdfs), InstrumentedList):
            dynamic_custom_cdfs = (
                [
                    custom_cdf.column
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf.kind == CdfKind.Dynamic.name
                ]
                if hasattr(self, "custom_cdfs") and self.custom_cdfs
                else []
            )
        else:
            dynamic_custom_cdfs = (
                [
                    custom_cdf["column"]
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf["kind"] == CdfKind.Dynamic.name
                ]
                if hasattr(self, "custom_cdfs") and self.custom_cdfs
                else []
            )

        aggregate_columns = []
        for column in columns:
            multi_level = (
                MultiLevelEnum(column["cdf"]["multi_level_id"])
                if "multi_level_id" in column["cdf"]
                else None
            )
            if "metrics" in column:
                for metric in column["metrics"]:
                    aggregate_columns.append(
                        dict(cdf=column["cdf"], metric=metric, multi_level=multi_level)
                    )
            elif (
                column["cdf"]["type"] == Cdf.Default.value
                and CDF(
                    self.dataset.kind,
                    column["cdf"]["column"],
                    False,
                    False,
                    multi_level,
                ).kind
                in (
                    CdfKind.DynamicCurrency,
                    CdfKind.DynamicPercentage,
                    CdfKind.Dynamic,
                )
                or column["cdf"]["column"] in dynamic_custom_cdfs
            ):
                aggregate_columns.append(dict(cdf=dict(**column["cdf"]), dynamic=True))
        return aggregate_columns

    def get_query(self) -> Query:
        # at this point if there is an aggregated page mode, it is a totals / subtotals query
        if self.aggregate and self.metadata and self.metadata.mode in [Mode.Page]:
            self.metadata.format = NumericFormat.Raw.value
            self.metadata.cte_query = True
            query = ReportQuery(
                self.dataset,
                self.property_ids,
                self.organization_id,
                self.cte_meta.get("custom_cdfs"),
                self.cte_meta.get("custom_field_cdfs"),
                [
                    dict(cdf=column["cdf"])
                    for column in (
                        [
                            c
                            for c in self.cte_meta.get("columns")
                            + [
                                group
                                for group in self.cte_meta.get("groups", [])
                                if group["cdf"]["column"]
                                not in [
                                    col["cdf"]["column"]
                                    for col in self.cte_meta.get("columns")
                                ]
                            ]
                            if c["cdf"]["column"] != "*"
                        ]
                    )
                ],
                groups=[],
                filters=self.cte_meta.get("filters"),
                sort=self.cte_meta.get("sort"),
                offset=self.offset,
                limit=self.limit,
                aggregate=False,
                format=NumericFormat.Raw.value,
                property_timezone=self.property_timezone,
                property_settings=self.property_settings,
                formats=None,
                metadata=self.metadata,
            ).get_query()
            report_cte = query.cte("report_cte")

            self.metadata.format = self.format
            self.metadata.cte_query = False
            cte_columns = [
                CTECDF(
                    report_cte,
                    column,
                    self.dataset.kind.value,
                    CDF(
                        self.dataset.kind, column["cdf"]["column"], is_custom_cdf=True
                    ).get_custom_cdf_in_custom_cdfs(self.cte_meta.get("custom_cdfs")),
                    CDF(
                        self.dataset.kind,
                        column["cdf"]["column"],
                        is_custom_field_cdf=True,
                    ).get_custom_field_cdf_in_custom_field_cdfs(
                        self.cte_meta.get("custom_field_cdfs")
                    ),
                    column.get("metric"),
                    column.get("modifier"),
                    dynamic=column.get("dynamic", False),
                    to_char=True,
                    format=self.format,
                    property_settings=self.property_settings,
                    formats=self.cte_meta.get("formats"),
                    custom_field_cdfs=self.custom_field_cdfs,
                    metadata=self.metadata,
                    cte_meta=self.cte_meta,
                ).get_cdf(skip_aggregation=False)
                for column in self.__get_aggregate_columns(self.cte_meta.get("columns"))
            ]

            cte_groups = [
                CTECDF(
                    report_cte,
                    column,
                    self.dataset.kind.value,
                    CDF(
                        self.dataset.kind, column["cdf"]["column"], is_custom_cdf=True
                    ).get_custom_cdf_in_custom_cdfs(self.cte_meta.get("custom_cdfs")),
                    CDF(
                        self.dataset.kind,
                        column["cdf"]["column"],
                        is_custom_field_cdf=True,
                    ).get_custom_field_cdf_in_custom_field_cdfs(
                        self.cte_meta.get("custom_field_cdfs")
                    ),
                    column.get("metric"),
                    column.get("modifier"),
                    dynamic=column.get("dynamic", False),
                    to_char=True,
                    format=self.format,
                    property_settings=self.property_settings,
                    formats=self.cte_meta.get("formats"),
                    custom_field_cdfs=self.custom_field_cdfs,
                    metadata=self.metadata,
                    cte_meta=self.cte_meta,
                ).get_cdf(skip_aggregation=True)
                for column in self.cte_meta.get("groups")
            ]
            if self.include_raw_groups_for_sort:
                raw_groups = [
                    CTECDF(
                        report_cte,
                        column,
                        self.dataset.kind.value,
                        CDF(
                            self.dataset.kind,
                            column["cdf"]["column"],
                            is_custom_cdf=True,
                        ).get_custom_cdf_in_custom_cdfs(
                            self.cte_meta.get("custom_cdfs")
                        ),
                        CDF(
                            self.dataset.kind,
                            column["cdf"]["column"],
                            is_custom_field_cdf=True,
                        ).get_custom_field_cdf_in_custom_field_cdfs(
                            self.cte_meta.get("custom_field_cdfs")
                        ),
                        column.get("metric"),
                        column.get("modifier"),
                        dynamic=column.get("dynamic", False),
                        to_char=False,
                        format=NumericFormat.Raw.value,
                        property_settings=self.property_settings,
                        formats=None,
                        custom_field_cdfs=self.custom_field_cdfs,
                        metadata=self.metadata,
                        cte_meta=self.cte_meta,
                        raw_group_for_sort=True,
                    ).get_cdf(skip_aggregation=True)
                    for column in self.cte_meta.get("groups")
                ]
                cte_groups += raw_groups
            cte_columns += cte_groups
            cte_sort = [
                CTECDF(
                    report_cte,
                    cdf_sort,
                    self.dataset.kind.value,
                    CDF(
                        self.dataset.kind, cdf_sort["cdf"]["column"], is_custom_cdf=True
                    ).get_custom_cdf_in_custom_cdfs(self.custom_cdfs),
                    CDF(
                        self.dataset.kind,
                        cdf_sort["cdf"]["column"],
                        is_custom_field_cdf=True,
                    ).get_custom_field_cdf_in_custom_field_cdfs(self.custom_field_cdfs),
                    cdf_sort.get("metric"),
                    next(
                        (
                            cdf_column.get("modifier")
                            for cdf_column in self.cte_meta.get("columns")
                            + self.cte_meta.get("groups")
                            if cdf_column["cdf"]["column"] == cdf_sort["cdf"]["column"]
                        ),
                        None,
                    ),
                    dynamic=cdf_sort.get("dynamic", False),
                    to_char=False,
                    property_settings=self.property_settings,
                    format=NumericFormat.Raw.value,
                    formats=None,
                    custom_field_cdfs=self.custom_field_cdfs,
                    metadata=self.metadata,
                ).get_cdf(skip_aggregation=not self.aggregate)
                for cdf_sort in self.cte_meta.get("sort")
            ]
            query = self.dataset.model.query.select_from(report_cte)
            query = query.with_entities(*cte_columns)
            if cte_groups:
                query = query.group_by(*cte_groups)
            if cte_sort:
                query = query.order_by(*cte_sort)
        else:
            query = self.dataset.model.query.select_from(self.dataset.model)

            for multi_level in self.multi_levels:
                join_function = getattr(query, multi_level.join_function)
                query = join_function(multi_level.model, multi_level.join)

            if self.order_by and not self.aggregate:
                query = query.order_by(*self.order_by)

            query = apply_filters(query, self.filters)
            query = query.with_entities(*self.select)

            if self.aggregate:
                query = query.group_by(*self.group_by)

                if self.order_by:
                    query = query.order_by(*self.order_by)
                else:
                    query = query.order_by(
                        *[nullsfirst(group) for group in self.group_by]
                    )
            query = query.offset(self.offset)
            query = query.limit(self.limit)

        logger.info(
            f"Query to be executed: {query.statement.compile(dialect=postgresql.dialect(), compile_kwargs={'literal_binds': True}).string}"
        )

        return query

    def __extract_cdf_fields(self, item):
        """Get every CDF from a custom cdf definition, including nesting in case statements.
        all CDFs are default kind in this context.  Custom fields are not included,
        this method is for building dynamic CTE CDFs to ensure that all source CDFs
        are included in the CTE."""

        results = []
        if isinstance(item, ReportCustomCdf):
            results.extend(self.__extract_cdf_fields(item.formula))
        elif isinstance(item, dict):
            if item.get("kind") == "cdf":
                # Append the 'field' value only if it exists.
                if "value" in item:
                    results.append(item["value"])
            # Recursively search each value in the dictionary.
            for value in item.values():
                results.extend(self.__extract_cdf_fields(value))
        elif isinstance(item, (list, InstrumentedList)):
            # Recursively search each element in the list.
            for elem in item:
                results.extend(self.__extract_cdf_fields(elem))
        else:
            pass
        return list(set(results))

    def __configure_cte_meta(
        self, columns, groups, filters, sort, custom_cdfs, custom_field_cdfs, formats
    ):
        # identify dynamic and custom cdfs
        # build list of cdfs included in the calculations
        source_columns = []
        dynamic_columns = [
            column
            for column in columns
            if column["cdf"]["column"]
            in [
                "adr",
                "revpar",
                "occupancy",
                "mfd_occupancy",
                "unassigned_accommodations",
                "unassigned_accommodation",
            ]
        ]
        for dynamic_column in dynamic_columns:
            match (dynamic_column["cdf"]["column"]):
                case "adr":
                    if not any(
                        column["cdf"]["column"] == "room_revenue" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "room_revenue"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(column="room_revenue", type=Cdf.Default.value),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "rooms_sold" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "rooms_sold"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(column="rooms_sold", type=Cdf.Default.value),
                            )
                        )
                case "revpar":
                    if not any(
                        column["cdf"]["column"] == "room_revenue" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "room_revenue"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(column="room_revenue", type=Cdf.Default.value),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "capacity_count"
                        for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "capacity_count"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(
                                    column="capacity_count", type=Cdf.Default.value
                                ),
                            )
                        )
                case "occupancy":
                    if not any(
                        column["cdf"]["column"] == "rooms_sold" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "rooms_sold"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(column="rooms_sold", type=Cdf.Default.value),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "capacity_count"
                        for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "capacity_count"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(
                                    column="capacity_count", type=Cdf.Default.value
                                ),
                            )
                        )
                case "mfd_occupancy":
                    if not any(
                        column["cdf"]["column"] == "rooms_sold" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "rooms_sold"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(column="rooms_sold", type=Cdf.Default.value),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "capacity_count"
                        for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "capacity_count"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(
                                    column="capacity_count", type=Cdf.Default.value
                                ),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "blocked_room_count"
                        for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "blocked_room_count"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(
                                    column="blocked_room_count", type=Cdf.Default.value
                                ),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "out_of_service_count"
                        for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "out_of_service_count"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(
                                    column="out_of_service_count",
                                    type=Cdf.Default.value,
                                ),
                            )
                        )
                case "unassigned_accommodations":
                    if not any(
                        column["cdf"]["column"] == "room_types" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "room_types"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(column="room_types", type=Cdf.Default.value),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "room_numbers" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "room_numbers"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(column="room_numbers", type=Cdf.Default.value),
                            )
                        )
                case "unassigned_accommodation":
                    if not any(
                        column["cdf"]["column"] == "room_type" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "room_type"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(
                                    column="room_type",
                                    type=Cdf.Default.value,
                                    multi_level_id=MultiLevelEnum.RoomReservations.value,
                                ),
                            )
                        )
                    if not any(
                        column["cdf"]["column"] == "room_number" for column in columns
                    ) and not any(
                        column["cdf"]["column"] == "room_number"
                        for column in source_columns
                    ):
                        source_columns.append(
                            dict(
                                cdf=dict(
                                    column="room_number",
                                    type=Cdf.Default.value,
                                    multi_level_id=MultiLevelEnum.RoomReservations.value,
                                ),
                            )
                        )
        nested_cdfs = self.__extract_cdf_fields(custom_cdfs)
        for nested_cdf in nested_cdfs:
            if not any(column["cdf"]["column"] == nested_cdf for column in columns):
                source_columns.append(
                    dict(
                        cdf=dict(
                            column=nested_cdf,
                            type=Cdf.Default.value,
                        ),
                    )
                )
        self.cte_meta = dict(
            custom_cdfs=[
                (
                    custom_cdf.__dict__
                    if not isinstance(custom_cdf, dict)
                    else custom_cdf
                )
                for custom_cdf in custom_cdfs
            ],
            custom_field_cdfs=custom_field_cdfs,
            columns=columns + source_columns,
            groups=groups,
            filters=filters,
            sort=sort if sort else [],
            formats=formats,
        )
