import re
import uuid
from copy import deepcopy
from datetime import datetime, timezone
from os.path import join
from typing import Optional

import numpy as np

from openpyxl.utils import get_column_letter

import pandas as pd
from pandas import MultiIndex
from pandas.core.frame import DataFrame

from app.common.comparisons import get_comparison_filter
from app.common.constants.limits import MAX_EXCEL_COLUMNS
from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.exceptions import InvalidUsage
from app.common.periods import get_period_filter
from app.datasets.dataset import Dataset
from app.enums import Metric, Mode
from app.enums.export import Format, View
from app.enums.format import NumericFormat
from app.reports.report_cdf import ReportCDF
from app.reports.report_export import ReportExport
from app.reports.report_list import ReportList
from app.reports.report_list_comparison import ReportListComparison
from app.reports.report_metadata import ReportMetadata
from app.reports.report_metrics import ReportMetrics
from app.reports.report_pivot import ReportPivot
from app.reports.report_summary import ReportSummary
from app.reports.report_summary_comparison import ReportSummaryComparison
from app.reports.report_summary_details import ReportSummaryDetails
from app.reports.report_type import ReportType
from app.services.s3_service import S3Service


class Report:
    def __init__(
        self,
        mode: Mode,
        dataset: Dataset,
        organization_id: int,
        format: str = NumericFormat.Raw.value,
        columns: list = [],
        custom_cdfs: list = [],
        custom_field_cdfs: list = [],
        group_rows: list = [],
        group_columns: list = [],
        filters: dict = dict(),
        original_filters: dict = dict(),
        property_ids: list = [],
        sort: dict = dict(),
        settings: dict = dict(),
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        periods: Optional[list[dict]] = [],
        formats: Optional[dict] = None,
        comparisons: Optional[list[dict]] = [],
        offset: Optional[int] = None,
        limit: Optional[int] = None,
    ):
        self.mode = mode
        self.dataset = dataset
        self.columns = columns
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.group_rows = group_rows
        self.group_columns = group_columns
        self.filters = filters
        self.original_filters = original_filters or deepcopy(filters)
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.sort = sort
        self.settings = settings
        self.format = format
        self.property_timezone = property_timezone
        self.property_settings = property_settings
        self.periods = periods
        self.formats = formats
        self.comparisons = comparisons
        self.offset = offset
        self.limit = limit

    @property
    def type(self) -> ReportType:
        return ReportType(
            self.group_rows, self.group_columns, self.periods, self.comparisons
        ).type

    def get_summary(self) -> dict | list[dict]:
        summary = dict(total=0, metrics=dict())
        if self.type in [ReportType.PeriodList, ReportType.PeriodSummary]:
            for i, period in enumerate(self.periods):
                filters = get_period_filter(self, period)
                period_report = Report(
                    self.mode,
                    self.dataset,
                    self.organization_id,
                    self.format,
                    self.columns,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.group_rows,
                    self.group_columns,
                    filters,
                    self.original_filters,
                    self.property_ids,
                    self.sort,
                    self.settings,
                    self.property_timezone,
                    self.property_settings,
                    None,
                    self.formats,
                ).get_summary()
                summary["total"] += period_report["total"]
                summary["metrics"][period["name"]] = period_report
        elif self.type in [ReportType.ListComparison, ReportType.SummaryComparison]:
            for i, comparison in enumerate(self.comparisons):
                filters = get_comparison_filter(self, comparison)
                comparison_report = Report(
                    self.mode,
                    self.dataset,
                    self.organization_id,
                    self.format,
                    self.columns,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.group_rows,
                    self.group_columns,
                    filters,
                    self.original_filters,
                    self.property_ids,
                    self.sort,
                    self.settings,
                    self.property_timezone,
                    self.property_settings,
                    None,
                    self.formats,
                ).get_summary()
                summary["total"] += comparison_report["total"]
                summary["metrics"][comparison["name"]] = comparison_report
        else:
            df_metrics = ReportMetrics(
                self.dataset,
                self.custom_cdfs,
                self.custom_field_cdfs,
                self.columns,
                self.property_ids,
                self.organization_id,
                self.filters,
                self.format,
                self.property_timezone,
                self.property_settings,
                formats=self.formats,
            ).get_df()

            if df_metrics.empty:
                return summary

            df_metrics = df_metrics.to_dict("records")[0]
            for key, value in df_metrics.items():
                if key == (ReportCDF.STAR_CDF, Metric.count.value):
                    summary["total"] = value
                else:
                    k1, k2 = key
                    summary["metrics"].setdefault(k1, {})[k2] = value

        return summary

    def get_data(self) -> dict:
        aggregated_count = None
        records_df = self.__get_records_df()
        subtotals_df = self.__get_subtotals_df()
        totals_df = self.__get_totals_df()

        if not records_df.empty:
            if self.group_columns or (
                (self.comparisons or self.periods) and self.group_rows
            ):
                with pd.option_context("future.no_silent_downcasting", True):
                    aggregated_count = (
                        records_df.xs(
                            (ReportCDF.STAR_CDF, Metric.count.name),
                            level=[-2, -1],
                            axis=1,
                            drop_level=False,
                        )
                        .replace(NA_FILL_STRING, np.nan)
                        .apply(pd.to_numeric, errors="coerce")
                        .sum(axis=1)
                        .sum()
                    )
                    records_df = records_df.drop(
                        columns=records_df.xs(
                            (ReportCDF.STAR_CDF, Metric.count.name),
                            level=[-2, -1],
                            axis=1,
                            drop_level=False,
                        ).columns
                    )
            elif self.type == ReportType.Summary and not self.settings.get("details"):
                aggregated_count = records_df[
                    tuple([ReportCDF.STAR_CDF, Metric.count.name])
                ].sum()
                records_df = records_df.drop(
                    columns=([tuple([ReportCDF.STAR_CDF, Metric.count.name])])
                )

        if self.type in [
            ReportType.Summary,
            ReportType.PeriodList,
            ReportType.PeriodSummary,
            ReportType.ListComparison,
            ReportType.SummaryComparison,
        ] and self.settings.get("transpose"):
            records_df = records_df.transpose()

        headers, index, group_rows, group_columns, records = (
            self.__get_headers(records_df),
            self.__get_index(records_df),
            self.__get_group_rows(records_df),
            self.__get_group_columns(records_df),
            self.__get_records(records_df),
        )
        subtotals = self.__get_subtotals(subtotals_df)
        totals = self.__get_totals(totals_df)
        periods = (
            None if not self.periods else [period["name"] for period in self.periods]
        )
        comparisons = (
            None
            if not self.comparisons
            else [comparison["name"] for comparison in self.comparisons]
        )
        return dict(
            headers=headers,
            index=index,
            group_rows=group_rows,
            group_columns=group_columns,
            records=records,
            subtotals=subtotals,
            totals=totals,
            type=self.type,
            periods=periods,
            comparisons=comparisons,
            aggregated_count=aggregated_count,
        )

    def get_export(
        self,
        title: str,
        view: str,
        format: str,
        charts: list[bytes] = [],
        path: list = ["reports", "exports"],
    ) -> dict:
        generated_at = datetime.now(timezone.utc)

        path = join(
            str(self.organization_id),
            *path,
            view,
            format,
            generated_at.strftime("%Y-%m-%d%H:%M:%S"),
            str(uuid.uuid4()),
        )

        index = (
            view == View.Details.value
            and self.type == ReportType.List
            and format == Format.XLSX.value
        )

        report_export = self.get_export_data(
            title, view, Format(format), index, generated_at, charts
        )

        df = report_export.get_df()

        if len(df.columns) > MAX_EXCEL_COLUMNS:
            raise InvalidUsage.bad_request(
                f"Number of columns must not exceed {MAX_EXCEL_COLUMNS}"
            )

        s3_service = S3Service(
            path,
            title,
            format,
            index,
            (
                report_export.get_pdf_customization
                if format == Format.PDF.value
                else report_export.get_xlsx_customization
            ),
            charts,
        )

        index = self.type != ReportType.List
        s3_service.upload(
            self.get_summary()["total"], df, view == View.Formatted.value, index
        )
        url = s3_service.get_presigned_url()
        return dict(
            url=url,
            format=format,
            view=view,
            filename=title,
            generated_at=generated_at,
            charts=charts,
        )

    def __get_records_df(self, format: str = None) -> DataFrame:
        match (self.type):
            case ReportType.List:
                return ReportList(
                    self.mode,
                    self.dataset,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.columns,
                    self.property_ids,
                    self.organization_id,
                    self.filters,
                    self.sort,
                    self.format,
                    self.property_timezone,
                    self.property_settings,
                    formats=self.formats,
                    metadata=ReportMetadata(
                        self.mode, format, self.type, self.offset, self.limit
                    ),
                ).get_df()
            case ReportType.Summary:
                if not self.settings.get("details"):
                    return ReportSummary(
                        self.mode,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        self.settings.get("transpose"),
                        formats=self.formats,
                        metadata=ReportMetadata(self.mode, format, self.type),
                    ).get_df()

                if self.settings.get("details"):
                    return ReportSummaryDetails(
                        self.mode,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        formats=self.formats,
                        metadata=ReportMetadata(
                            self.mode, format, self.type, self.offset, self.limit
                        ),
                    ).get_df()
            case ReportType.Pivot:
                return ReportPivot(
                    self.mode,
                    self.dataset,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.columns,
                    self.group_rows,
                    self.group_columns,
                    self.property_ids,
                    self.organization_id,
                    self.filters,
                    self.sort,
                    self.format,
                    self.property_timezone,
                    self.property_settings,
                    self.settings.get("transpose"),
                    formats=self.formats,
                    metadata=ReportMetadata(
                        self.mode,
                        format,
                        self.type,
                        page_offset=self.offset,
                        page_limit=self.limit,
                    ),
                ).get_df()
            case ReportType.PeriodList:
                return ReportListComparison(
                    self.mode,
                    self.dataset,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.columns,
                    self.property_ids,
                    self.organization_id,
                    self.filters,
                    self.sort,
                    self.format,
                    self.property_timezone,
                    self.property_settings,
                    self.periods,
                    formats=self.formats,
                    metadata=ReportMetadata(self.mode, format, self.type),
                ).get_df_totals()
            case ReportType.PeriodSummary:
                df = ReportSummaryComparison(
                    self.mode,
                    self.dataset,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.columns,
                    self.group_rows,
                    self.property_ids,
                    self.organization_id,
                    self.filters,
                    self.sort,
                    self.format,
                    self.property_timezone,
                    self.property_settings,
                    self.periods,
                    formats=self.formats,
                    metadata=ReportMetadata(self.mode, format, self.type),
                ).get_df()
            case ReportType.SummaryComparison:
                df = ReportSummaryComparison(
                    self.mode,
                    self.dataset,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.columns,
                    self.group_rows,
                    self.property_ids,
                    self.organization_id,
                    self.filters,
                    self.sort,
                    self.format,
                    self.property_timezone,
                    self.property_settings,
                    self.comparisons,
                    formats=self.formats,
                    metadata=ReportMetadata(self.mode, format, self.type),
                ).get_df()
            case ReportType.ListComparison:
                return ReportListComparison(
                    self.mode,
                    self.dataset,
                    self.custom_cdfs,
                    self.custom_field_cdfs,
                    self.columns,
                    self.property_ids,
                    self.organization_id,
                    self.filters,
                    self.sort,
                    self.format,
                    self.property_timezone,
                    self.property_settings,
                    self.comparisons,
                    formats=self.formats,
                    metadata=ReportMetadata(self.mode, format, self.type),
                ).get_df_totals()
        return df

    def __get_records(self, df: DataFrame) -> dict:
        match (self.type):
            case ReportType.List:
                records = df.to_dict("list")

            case ReportType.Summary:
                if self.settings.get("details"):
                    records = df.to_dict("list")
                else:
                    records = self.__get_data_from_df(
                        df, list(range(len(df.columns.names)))
                    )
            case (
                ReportType.Pivot
                | ReportType.PeriodSummary
                | ReportType.PeriodList
                | ReportType.SummaryComparison
                | ReportType.ListComparison
            ):
                records = self.__get_data_from_df(
                    df, list(range(len(df.columns.names)))
                )
            case _:
                records = {}

        return records

    def __get_subtotals_df(self, format: str = None) -> DataFrame:
        if self.settings.get("totals"):
            match (self.type):
                case ReportType.Summary:
                    if not self.settings.get("details"):
                        return ReportSummary(
                            self.mode,
                            self.dataset,
                            self.custom_cdfs,
                            self.custom_field_cdfs,
                            self.columns,
                            self.group_rows,
                            self.property_ids,
                            self.organization_id,
                            self.filters,
                            self.sort,
                            self.format,
                            self.property_timezone,
                            self.property_settings,
                            self.settings.get("transpose"),
                            formats=self.formats,
                            aggregate_count=False,
                            metadata=ReportMetadata(self.mode, format, self.type),
                        ).get_df_subtotals()

                    if self.settings.get("details"):
                        return ReportSummaryDetails(
                            self.mode,
                            self.dataset,
                            self.custom_cdfs,
                            self.custom_field_cdfs,
                            self.columns,
                            self.group_rows,
                            self.property_ids,
                            self.organization_id,
                            self.filters,
                            self.sort,
                            self.format,
                            self.property_timezone,
                            self.property_settings,
                            formats=self.formats,
                            metadata=ReportMetadata(
                                self.mode, format, self.type, self.offset, self.limit
                            ),
                        ).get_df_subtotals()

                case ReportType.Pivot:
                    return ReportPivot(
                        self.mode,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.group_columns,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        self.settings.get("transpose"),
                        formats=self.formats,
                        aggregate_count=False,
                        metadata=ReportMetadata(self.mode, format, self.type),
                    ).get_df_subtotals()
                case ReportType.PeriodSummary:
                    return ReportSummaryComparison(
                        self.mode,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        self.periods,
                        formats=self.formats,
                        metadata=ReportMetadata(self.mode, format, self.type),
                    ).get_df_subtotals()
                case ReportType.SummaryComparison:
                    return ReportSummaryComparison(
                        self.mode,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        self.comparisons,
                        formats=self.formats,
                        metadata=ReportMetadata(self.mode, format, self.type),
                    ).get_df_subtotals()
        return DataFrame()

    def __get_subtotals(self, df: DataFrame | list[DataFrame]) -> dict:
        subtotals = {}

        if not isinstance(df, list) and df.empty:
            return subtotals

        if self.type == ReportType.Summary:
            subtotals = self.__get_subtotal_data_from_summary_df(df, subtotals)

        if self.type == ReportType.Pivot and self.settings.get("totals"):
            subtotals = self.__get_data_from_df(df, list(range(len(df.columns.names))))

        if self.type in (
            ReportType.PeriodSummary,
            ReportType.SummaryComparison,
        ) and self.settings.get("totals"):
            for level in df.columns.levels[0]:
                subtotals[level] = {}
                subtotals[level] = self.__get_subtotal_data_from_summary_df(
                    df[level], subtotals[level]
                )

        return subtotals

    def __get_totals_df(self, format: str = None) -> DataFrame:
        if self.settings.get("totals"):
            match (self.type):
                case ReportType.List:
                    return ReportList(
                        self.mode,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort if self.mode == Mode.Page else None,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        formats=self.formats,
                        metadata=ReportMetadata(
                            self.mode, format, self.type, self.offset, self.limit
                        ),
                    ).get_df_totals()
                case ReportType.Summary:
                    if not self.settings.get("details"):
                        return ReportSummary(
                            None,
                            self.dataset,
                            self.custom_cdfs,
                            self.custom_field_cdfs,
                            self.columns,
                            [],
                            self.property_ids,
                            self.organization_id,
                            self.filters,
                            None,
                            self.format,
                            self.property_timezone,
                            self.property_settings,
                            self.settings.get("transpose"),
                            formats=self.formats,
                            aggregate_count=False,
                            metadata=ReportMetadata(None, format, self.type),
                        ).get_df_totals()
                    if self.settings.get("details"):
                        return ReportSummaryDetails(
                            self.mode,
                            self.dataset,
                            self.custom_cdfs,
                            self.custom_field_cdfs,
                            self.columns,
                            [],
                            self.property_ids,
                            self.organization_id,
                            self.filters,
                            self.sort,
                            self.format,
                            self.property_timezone,
                            self.property_settings,
                            formats=self.formats,
                            metadata=ReportMetadata(
                                self.mode, format, self.type, self.offset, self.limit
                            ),
                        ).get_df_totals()
                case ReportType.Pivot:
                    return ReportPivot(
                        None,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.group_columns,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        None,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        self.settings.get("transpose"),
                        formats=self.formats,
                        aggregate_count=False,
                        metadata=ReportMetadata(None, format, self.type),
                    ).get_df_totals()
                case ReportType.PeriodSummary:
                    return ReportSummaryComparison(
                        None,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        self.periods,
                        formats=self.formats,
                        metadata=ReportMetadata(None, format, self.type),
                    ).get_df_totals()
                case ReportType.SummaryComparison:
                    return ReportSummaryComparison(
                        None,
                        self.dataset,
                        self.custom_cdfs,
                        self.custom_field_cdfs,
                        self.columns,
                        self.group_rows,
                        self.property_ids,
                        self.organization_id,
                        self.filters,
                        self.sort,
                        self.format,
                        self.property_timezone,
                        self.property_settings,
                        self.comparisons,
                        formats=self.formats,
                        metadata=ReportMetadata(None, format, self.type),
                    ).get_df_totals()
        return DataFrame()

    def __get_totals(self, df: DataFrame) -> dict:
        totals = {}

        if df.empty:
            return totals

        if self.type == ReportType.List or self.type == ReportType.Summary:
            df = df.to_dict("records")[0]
            for key, value in df.items():
                k1, k2 = key
                totals.setdefault(k1, {})[k2] = value

        if self.type == ReportType.Pivot:
            totals = self.__get_data_from_df(df, [0, 1])

        if (
            self.type == ReportType.PeriodSummary
            or self.type == ReportType.SummaryComparison
        ):
            totals = self.__get_data_from_df(df, [0, 1])
        return totals

    def __get_headers(self, df: DataFrame) -> list:
        headers = []

        match (self.type):
            case ReportType.List:
                headers = df.columns.tolist()
            case (
                ReportType.PeriodList
                | ReportType.PeriodSummary
                | ReportType.SummaryComparison
                | ReportType.ListComparison
            ):
                if isinstance(df.columns, MultiIndex):
                    headers = df.columns.tolist()
                else:
                    headers = [[index] for index in df.columns.tolist()]

            case ReportType.Summary:
                if self.settings.get("details") or self.settings.get("transpose"):
                    headers = df.columns.tolist()
                else:
                    headers = list(map(list, df.columns))

            case ReportType.PeriodSummary | ReportType.Pivot:
                headers = list(map(list, df.columns))

        return headers

    def __get_index(self, df: DataFrame) -> list:
        index = []

        if self.type in [
            ReportType.Summary,
            ReportType.Pivot,
            ReportType.PeriodList,
            ReportType.PeriodSummary,
            ReportType.SummaryComparison,
            ReportType.ListComparison,
        ]:
            if isinstance(df.index, MultiIndex):
                index = df.index.tolist()
            else:
                index = [[index] for index in df.index.tolist()]
        return index

    def __get_group_rows(self, df: DataFrame) -> list:
        group_rows = []

        if df.empty:
            return group_rows

        if (
            self.type == ReportType.Summary and not self.settings.get("transpose")
        ) or self.type == ReportType.Pivot:
            group_rows = list(df.index.names)

        if self.type == ReportType.Summary and self.settings.get("transpose"):
            group_rows = list(df.columns.names)

        if self.type in (ReportType.PeriodSummary, ReportType.SummaryComparison):
            group_rows = [group_row["cdf"]["column"] for group_row in self.group_rows]

        return group_rows

    def __get_group_columns(self, df: DataFrame) -> list:
        group_columns = []

        if df.empty:
            return group_columns

        if self.type == ReportType.Pivot:
            group_columns = list(df.columns.names[:-2])

        return group_columns

    def __get_data_from_df(self, df: DataFrame, level: list = [0, 1]) -> dict:
        "Get data from a dataframe and transform it to a proper dict structure which can be readable"
        data = {}

        if df.empty:
            return data

        df = df.stack(level=level, future_stack=True).to_dict()
        for key, value in df.items():
            target = data.setdefault(key[0], {})
            for k in key[1:-1]:
                target = target.setdefault(k, {})
            target[key[-1]] = value
        return data

    def __get_multilevels(self):
        multilevels = self.columns
        if self.group_columns:
            multilevels += self.group_columns
        if self.group_rows:
            multilevels += self.group_rows
        if self.periods:
            multilevels += self.periods
        return multilevels

    def __get_subtotal_data_from_summary_df(
        self, df: DataFrame, subtotals: dict
    ) -> dict:
        for index, name in enumerate(
            list(filter(lambda name: name != "level", df.index.names)), start=1
        ):
            if not self.settings.get("details"):
                level = (
                    [0, len(df.index.names) - 1]
                    if index != len(df.index.names) - 1
                    else [0]
                )
            else:
                if index == 1:
                    level = [0] + [i for i in range(1, len(df.index.names)) if i != 1]
                if index == 2:
                    level = [0, 3] if len(df.index.names) - 1 == 3 else [0]
                if index == 3:
                    level = [0]

            level_df = df[df.index.get_level_values("level") == name].reset_index(
                level=level, drop=True
            )
            subtotals[name] = self.__get_data_from_df(level_df)
        return subtotals

    def __get_subtotal_data_from_summary_comparison_df(
        self, df: DataFrame, subtotals: dict
    ) -> dict:
        for index, name in enumerate(
            list(filter(lambda name: name != "level", df.index.names)), start=1
        ):
            if not self.settings.get("details"):
                level = (
                    [0, len(df.index.names) - 1]
                    if index != len(df.index.names) - 1
                    else [0]
                )
            else:
                if index == 1:
                    level = [0] + [i for i in range(1, len(df.index.names)) if i != 1]
                if index == 2:
                    level = [0, 3] if len(df.index.names) - 1 == 3 else [0]
                if index == 3:
                    level = [0]

            level_df = df[df.index.get_level_values("level") == name].reset_index(
                level=level, drop=True
            )
            subtotals[name] = self.__get_data_from_df(level_df)
        return subtotals

    def add_export_sheet(
        self,
        title: str,
        view: str,
        writer,
        s3_service,
        charts: list[bytes] = [],
        report: "Report" = None,
    ):
        view = View.Formatted.value
        format = Format.XLSX.value
        index = (
            view == View.Details.value
            and self.type == ReportType.List
            and format == Format.XLSX.value
        )
        generated_at = datetime.now(timezone.utc)

        report_export = self.get_export_data(
            title, view, Format(format), index, generated_at, charts
        )
        total_record_count = report.get_summary()["total"]
        df = report_export.get_df()

        if len(df.columns) > MAX_EXCEL_COLUMNS:
            raise InvalidUsage.bad_request(
                f"Number of columns must not exceed {MAX_EXCEL_COLUMNS}"
            )

        title = re.sub(r"\W+", "", title)

        START_ROW = (
            ReportExport.START_ROW_WITH_CHARTS
            if len(charts)
            else ReportExport.DEFAULT_START_ROW
        )

        title = self.get_valid_title(title, writer)

        index = self.type != ReportType.List
        df.to_excel(writer, sheet_name=title, index=index, startrow=START_ROW)

        worksheet = writer.sheets[title]

        worksheet = report_export.get_xlsx_customization(
            worksheet, df, total_record_count
        )

        for idx, _ in enumerate(worksheet.columns, 1):
            worksheet.column_dimensions[get_column_letter(idx)].auto_size = True

        # Upload images to S3
        base_key = re.sub(f"{s3_service.filename}|.xlsx", "", s3_service.key)
        for index, chart in enumerate(charts):
            chart_key = f"{base_key}chart-{index}.png"
            s3_service.s3.put_object(
                Body=chart, Bucket=s3_service.bucket, Key=chart_key
            )

    def get_valid_title(self, title, writer) -> str:
        existing_sheet_names = writer.book.sheetnames
        new_sheet_name = title
        counter = 1

        while new_sheet_name in existing_sheet_names:
            new_sheet_name = f"{title}_{counter}"
            counter += 1

        return new_sheet_name

    def get_export_data(
        self,
        title: str,
        view: str,
        format: Format,
        index: int,
        generated_at: str,
        charts: list[bytes],
    ):
        aggregated_count = None
        totals_df, subtotals_df, totals_df = DataFrame(), DataFrame(), DataFrame()

        if view == View.Details.value:
            groups = self.group_columns if self.group_columns else []
            groups += self.group_rows if self.group_rows else []
            records_df = ReportList(
                Mode.Export,
                self.dataset,
                self.custom_cdfs,
                self.custom_field_cdfs,
                groups + self.columns,
                self.property_ids,
                self.organization_id,
                self.filters,
                self.sort,
                self.format,
                self.property_timezone,
                self.property_settings,
                formats=self.formats,
                metadata=ReportMetadata(Mode.Export, format, self.type),
            ).get_df()
            records_df.index = records_df.index + 1 if index else records_df.index

        else:
            records_df = self.__get_records_df(format)
            subtotals_df = self.__get_subtotals_df(format)
            totals_df = self.__get_totals_df(format)

            if not records_df.empty:
                if self.group_columns or (
                    (self.comparisons or self.periods) and self.group_rows
                ):
                    with pd.option_context("future.no_silent_downcasting", True):
                        aggregated_count = (
                            records_df.xs(
                                (ReportCDF.STAR_CDF, Metric.count.name),
                                level=[-2, -1],
                                axis=1,
                                drop_level=False,
                            )
                            .replace(NA_FILL_STRING, np.nan)
                            .apply(pd.to_numeric, errors="coerce")
                            .sum(axis=1)
                            .sum()
                        )

                        records_df = records_df.drop(
                            columns=records_df.xs(
                                (ReportCDF.STAR_CDF, Metric.count.name),
                                level=[-2, -1],
                                axis=1,
                                drop_level=False,
                            ).columns
                        )

                        if not subtotals_df.empty:
                            subtotals_df = subtotals_df.drop(
                                columns=subtotals_df.columns[
                                    (
                                        (
                                            subtotals_df.columns.get_level_values(-1)
                                            == ReportCDF.STAR_CDF
                                        )
                                        & (
                                            subtotals_df.columns.get_level_values(-2)
                                            == Metric.count.name
                                        )
                                    )
                                ]
                            )

                        if not totals_df.empty:
                            totals_df = totals_df.drop(
                                columns=totals_df.columns[
                                    (
                                        (
                                            totals_df.columns.get_level_values(-1)
                                            == ReportCDF.STAR_CDF
                                        )
                                        & (
                                            totals_df.columns.get_level_values(-2)
                                            == Metric.count.name
                                        )
                                    )
                                ]
                            )

                elif (
                    self.group_rows
                    and not self.settings.get("details")
                    and not self.periods
                    and not self.comparisons
                ):
                    aggregated_count = records_df[
                        tuple([ReportCDF.STAR_CDF, Metric.count.name])
                    ].sum()
                    records_df = records_df.drop(
                        columns=([tuple([ReportCDF.STAR_CDF, Metric.count.name])])
                    )

        multilevels = self.__get_multilevels()
        report_export = ReportExport(
            self.dataset,
            title,
            view,
            self.type,
            self.settings.get("details"),
            generated_at,
            self.custom_cdfs,
            self.custom_field_cdfs,
            records_df,
            subtotals_df,
            totals_df,
            multilevels,
            self.settings.get("transpose"),
            self.periods,
            formats=self.formats,
            charts=charts,
            comparisons=self.comparisons,
            aggregated_count=aggregated_count,
            filters=self.filters,
            original_filters=self.original_filters,
        )

        return report_export
