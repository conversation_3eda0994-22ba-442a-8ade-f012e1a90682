from collections import OrderedDict
from datetime import datetime, timezone

from flask import g

from sqlalchemy import case, func, literal, literal_column

from app.cdfs.cdf import CDF
from app.common.babel import get_locale
from app.common.constants.datetime import DEFAULT_DATETIME_DATE_FORMAT
from app.common.enums import LaunchDarklyFeature
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.translation_keys import (
    get_reverse_translation_map,
    get_translation_keys,
)
from app.datasets.custom_field import CustomField
from app.datasets.dataset import Dataset
from app.datasets.financial import FinancialFFView, FinancialView
from app.datasets.multi_level import MultiLevel
from app.enums import FilterOperator as Operator
from app.enums.cdf import Cdf
from app.enums.cdf_kind import CdfKind
from app.enums.custom_fields import CustomFields as CustomFieldsEnum
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.enums.relative_date import RelativeDate as RelativeDateEnum
from app.filters.relative_date import RelativeDate
from app.multi_levels.occupancy_reservation import (
    OccupancyReservationsFFView,
    OccupancyReservationsView,
)
from app.services.custom_field_service import CustomFieldService
from app.services.launch_darkly_service import LaunchDarklyService


class ReportFilters:
    OPERATORS = {
        Operator.IsNull.value: "is_null",
        Operator.IsNotNull.value: "is_not_null",
        Operator.Equals.value: "eq",
        Operator.IsEmpty.value: "eq",
        Operator.NotEquals.value: "ne",
        Operator.IsNotEmpty.value: "ne",
        Operator.GreaterThan.value: "gt",
        Operator.LessThan.value: "lt",
        Operator.GreaterThanOrEqual.value: "ge",
        Operator.LessThanOrEqual.value: "le",
        Operator.Begins.value: "ilike",
        Operator.Ends.value: "ilike",
        Operator.Contains.value: "ilike",
        Operator.NotBegins.value: "not_ilike",
        Operator.NotEnds.value: "not_ilike",
        Operator.NotContains.value: "not_ilike",
        Operator.ListContains.value: "in",
        Operator.NotListContains.value: "not_in",
    }

    VALUES = {
        Operator.IsNull.value: lambda value: None,
        Operator.IsNotNull.value: lambda value: None,
        Operator.Equals.value: lambda value: value,
        Operator.IsEmpty.value: lambda value: "",
        Operator.NotEquals.value: lambda value: value,
        Operator.IsNotEmpty.value: lambda value: "",
        Operator.GreaterThan.value: lambda value: value,
        Operator.LessThan.value: lambda value: value,
        Operator.GreaterThanOrEqual.value: lambda value: value,
        Operator.LessThanOrEqual.value: lambda value: value,
        Operator.Begins.value: lambda value: f"{value}%",
        Operator.Ends.value: lambda value: f"%{value}",
        Operator.Contains.value: lambda value: f"%{value}%",
        Operator.NotBegins.value: lambda value: f"{value}%",
        Operator.NotEnds.value: lambda value: f"%{value}",
        Operator.NotContains.value: lambda value: f"%{value}%",
        Operator.ListContains.value: lambda value: value,
        Operator.NotListContains.value: lambda value: value,
    }

    def __init__(
        self,
        dataset: Dataset,
        filters: dict,
        property_ids: list,
        organization_id: int,
        property_timezone: timezone = timezone.utc,
        multi_levels: list = [],
        custom_field_cdfs: list = [],
        custom_cdfs: list = [],
        function_args: dict = {},
    ):
        self.dataset = dataset
        self.filters = filters
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.property_timezone = property_timezone
        self.multi_levels = multi_levels
        self.custom_field_cdfs = custom_field_cdfs
        self.custom_cdfs = custom_cdfs
        self.function_args = function_args

    def __repr__(self):
        """Representation of ReportFilters
        :return: string
        """
        return f"<ReportFilters, dataset={self.dataset}, filters={self.filters}, property_ids={self.property_ids}>"

    def get_filters(self) -> list:
        parsed_organization_id = self.__get_parsed_organization_id(self.organization_id)
        parsed_property_ids = self.__get_parsed_property_ids(self.property_ids)
        parsed_static_filters = self.__get_parsed_filters(self.dataset.static_filters)
        parsed_filters = self.__get_parsed_filters(self.filters)

        filters = (
            parsed_organization_id + parsed_property_ids
            if self.dataset.has_organization_id
            else parsed_property_ids
        )

        if len(parsed_static_filters):
            filters.extend(parsed_static_filters)

        if parsed_filters:
            filters.append(parsed_filters)

        filters = [{"and": filters}]
        return filters

    def get_rules(self):
        return [] if not self.filters else self.__get_rules(self.filters)

    def __get_rules(self, filters: dict):
        rules = []
        for key in filters:
            for rule in filters[key]:
                if "and" in rule or "or" in rule:
                    rules += self.__get_rules(rule)
                else:
                    rules.append(rule)

        return rules

    def __get_parsed_property_ids(self, property_ids) -> list[dict]:
        get_filter = lambda model: {
            "model": model,
            "field": "property_id",
            "op": self.OPERATORS[Operator.ListContains.value],
            "value": property_ids,
        }

        property_filter = get_filter(self.dataset.model.__name__)

        return [property_filter]

    def __get_parsed_organization_id(self, organization_id) -> list[dict]:
        if organization_id is None:
            raise InvalidUsage.server_error(
                "This property does not have an Organization ID"
            )

        get_filter = lambda model: {
            "model": model,
            "field": "organization_id",
            "op": self.OPERATORS[Operator.Equals.value],
            "value": organization_id,
        }

        organization_filter = get_filter(self.dataset.model.__name__)

        return [organization_filter]

    def __get_parsed_filters(self, filters=[]):
        if isinstance(filters, list):
            return [self.__get_parsed_filters(filter) for filter in filters]

        if isinstance(filters, dict):
            if "cdf" in filters:
                value_cdf = CDF(
                    self.dataset.kind,
                    filters["cdf"]["column"],
                    any(
                        filters["cdf"]["column"] == cdf.key for cdf in self.custom_cdfs
                    ),
                    filters["cdf"].get("type") == Cdf.CustomField.value,
                    (
                        MultiLevelEnum(filters["cdf"].get("multi_level_id"))
                        if "multi_level_id" in filters["cdf"]
                        else None
                    ),
                    filters["cdf"].get("type"),
                )
                is_multi_level = "multi_level_id" in filters["cdf"]

                # Add booking_id filter for multi-level occupancy reservations
                if is_multi_level and (
                    MultiLevel(
                        MultiLevelEnum(filters["cdf"]["multi_level_id"])
                    ).model.__name__
                    in (
                        OccupancyReservationsFFView.__name__,
                        OccupancyReservationsView.__name__,
                    )
                ):
                    return {
                        "or": [
                            {
                                "model": self.dataset.model.__name__,
                                "field": "booking_id",
                                "op": "is_null",
                                "value": None,
                            },
                            self.__parse_filter(filters, is_multi_level),
                        ]
                    }
                # Add filters for translatable CDFs
                elif value_cdf.is_translate_cdf() and filters["operator"] not in [
                    Operator.IsNull.value,
                    Operator.IsNotNull.value,
                    Operator.IsEmpty.value,
                    Operator.IsNotEmpty.value,
                ]:
                    model = (
                        self.dataset.model
                        if not is_multi_level
                        else MultiLevel(
                            MultiLevelEnum(filters["cdf"]["multi_level_id"])
                        ).model
                    )
                    return {
                        "or": [
                            {
                                "model": model.__name__,
                                "field": filters["cdf"]["column"],
                                "op": self.OPERATORS[Operator.ListContains.value],
                                "value": self.__get_translation_keys_from_value(
                                    filters["value"], filters["operator"]
                                ),
                            },
                            {
                                "and": [
                                    self.__parse_filter(filters, is_multi_level),
                                    {
                                        "model": model.__name__,
                                        "field": filters["cdf"]["column"],
                                        "op": "not_in",
                                        "value": get_translation_keys(),
                                    },
                                ]
                            },
                        ],
                    }
                else:
                    return self.__parse_filter(filters, is_multi_level)

            else:
                return {
                    key: self.__get_parsed_filters(filter)
                    for key, filter in filters.items()
                }

    def __get_field(self, field, model) -> str:
        custom_cdf = next((cdf for cdf in self.custom_cdfs if field == cdf.key), False)
        if custom_cdf is not False:
            field = custom_cdf
        elif "function" in model.__dict__[field].info:
            field = model.__dict__[field].info["function"](
                self.dataset.model, **self.function_args
            )
        return field

    def __get_value(
        self,
        value: str,
        field: str,
        op: str,
        multi_level: MultiLevelEnum = None,
        type: str = None,
    ) -> str:
        value_cdf = CDF(
            self.dataset.kind,
            field,
            any(field == cdf.key for cdf in self.custom_cdfs),
            type == Cdf.CustomField.value,
            multi_level,
            type,
        )
        if (
            field
            not in [
                static_filter["cdf"]["column"]
                for static_filter in self.dataset.static_filters
                if "cdf" in static_filter
            ]
        ) and (value_cdf.is_kind(CdfKind.Date) or value_cdf.is_kind(CdfKind.Timestamp)):
            value, duration = RelativeDate.get_duration_from_value(value)
            if duration is not None:
                return self.__get_converted_relative_date(value, duration)
            elif value in [relative_date.value for relative_date in RelativeDateEnum]:
                return self.__get_converted_relative_date(value)
            elif value_cdf.is_kind(CdfKind.Date):
                return self.__get_formatted_date(value)

        return self.VALUES[op](value)

    def __get_translation_keys_from_value(self, user_input: str, op: str) -> list[str]:
        translations = get_reverse_translation_map(locale=get_locale())
        original_input = user_input

        matches = []

        for translated_text, keys in translations.items():
            translated_text = translated_text.lower()
            user_input = user_input.lower()
            if op == Operator.Equals.value and translated_text == user_input:
                matches += keys
            elif op == Operator.Begins.value and translated_text.startswith(user_input):
                matches += keys
            elif op == Operator.Ends.value and translated_text.endswith(user_input):
                matches += keys
            elif op == Operator.Contains.value and user_input in translated_text:
                matches += keys
            elif op == Operator.NotBegins.value and not translated_text.startswith(
                user_input
            ):
                matches += keys
            elif op == Operator.NotEnds.value and not translated_text.endswith(
                user_input
            ):
                matches += keys
            elif op == Operator.NotContains.value and user_input not in translated_text:
                matches += keys
            elif op == Operator.NotEquals.value and translated_text != user_input:
                matches += keys

        if not matches:
            return [original_input]

        return list(set(matches))

    def __get_converted_relative_date(
        self, relative_date_string: str, duration: int = 0
    ) -> str:
        relative_date_string = relative_date_string.lower()

        return RelativeDate(
            RelativeDateEnum(relative_date_string),
            self.property_timezone,
            duration,
        ).value.strftime(DEFAULT_DATETIME_DATE_FORMAT)

    def __get_formatted_date(self, date_value: str) -> str:
        try:
            return datetime.fromisoformat(date_value).strftime(
                DEFAULT_DATETIME_DATE_FORMAT
            )
        except ValueError:
            try:
                return datetime.fromisoformat(
                    date_value.replace("Z", "+00:00")
                ).strftime(DEFAULT_DATETIME_DATE_FORMAT)
            except ValueError as value_error:
                logger.error(
                    f"Invalid date format for value, '{date_value}', in report filters",
                    exc_info=True,
                    extra={"error": value_error},
                )
                InvalidUsage.server_error()

    def __get_custom_field_filter(self, column: str, operator: str):
        cases = []

        custom_field_cdf = next(
            (
                custom_field_cdf
                for custom_field_cdf in self.custom_field_cdfs
                if (
                    isinstance(custom_field_cdf, (dict, OrderedDict))
                    and custom_field_cdf["column"] == column
                )
                or (
                    not isinstance(custom_field_cdf, (dict, OrderedDict))
                    and custom_field_cdf.column == column
                )
            ),
            None,
        )

        custom_field_cdf = (
            custom_field_cdf
            if isinstance(custom_field_cdf, (dict, OrderedDict))
            else dict(
                name=custom_field_cdf.name,
                column=custom_field_cdf.column,
                properties=[
                    dict(
                        property_id=property.property_id,
                        internal_name=property.internal_name,
                    )
                    for property in custom_field_cdf.properties
                ],
            )
        )
        for property in custom_field_cdf.get("properties", []):
            custom_field = (
                CustomFieldService.get_custom_field_by_dataset_id_and_internal_name(
                    self.dataset.kind.value,
                    property.get("internal_name"),
                    property.get("property_id"),
                )
            )
            if custom_field:
                cases.append(
                    (
                        CustomField(
                            CustomFieldsEnum(self.dataset.kind.value)
                        ).model.property_id
                        == property.get("property_id"),
                        literal_column(f"custom_string_{custom_field.creation_order}"),
                    )
                )
            else:
                logger.warning(
                    f"Custom Field {property.get('internal_name')} was not found for property id {property.get('property_id')}"
                )

        if cases:
            case_statement = case(
                *cases,
                else_=literal(None),
            )

            return case_statement
        else:
            logger.warning(
                f"Custom Field {column} is not present on any property in the report"
            )
            return literal(None)

    def __parse_filter(self, filters: dict, is_multi_level: bool = False):

        model = (
            CustomField(CustomFieldsEnum(self.dataset.kind.value)).model
            if filters["cdf"]["type"] == Cdf.CustomField.value
            else (
                self.dataset.model
                if not is_multi_level
                else MultiLevel(MultiLevelEnum(filters["cdf"]["multi_level_id"])).model
            )
        )
        field = (
            self.__get_field(filters["cdf"]["column"], model)
            if not filters["cdf"]["type"] == Cdf.CustomField.value
            else self.__get_custom_field_filter(
                filters["cdf"]["column"], filters["operator"]
            )
        )

        op = self.OPERATORS[filters["operator"]]
        value = self.__get_value(
            filters["value"],
            filters["cdf"]["column"],
            filters["operator"],
            (
                None
                if not is_multi_level
                else MultiLevelEnum(filters["cdf"]["multi_level_id"])
            ),
            filters["cdf"].get("type"),
        )

        # Handle special case for FinancialView and FinancialFFView room_number
        # when the operator is IsNotNull
        # TODO: Remove the feature flag check when the fix is fully tested
        if (
            LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.FinancialDatasetRoomNumberNotNullFilter,
                g.property_id,
            )
            and model.__name__ in [FinancialView.__name__, FinancialFFView.__name__]
            and field == FinancialView.room_number.name
            and op == self.OPERATORS[Operator.IsNotNull.value]
        ):
            field = func.coalesce(model.room_number, "placeholder.room_number.is_null")
            op = self.OPERATORS[Operator.NotEquals.value]
            value = "placeholder.room_number.is_null"

        return {
            "model": model.__name__,
            "field": field,
            "op": op,
            "value": value,
        }
