from datetime import timezone
from typing import Optional

from pandas.core.frame import DataFrame

from sqlalchemy.orm.query import Query

from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.constants.table_limits import (
    EXPORT_LIMIT,
    TABULAR_PREVIEW_LIMIT,
    TABULAR_RUN_LIMIT,
)
from app.datasets.dataset import Dataset
from app.enums import Mode
from app.reports.report_interface import ReportInterface
from app.reports.report_metadata import ReportMetadata
from app.reports.report_query import ReportQuery


class ReportList(ReportInterface):
    def __init__(
        self,
        mode: Mode,
        dataset: Dataset,
        custom_cdfs: list,
        custom_field_cdfs: list,
        columns: list,
        property_ids: list,
        organization_id: int,
        filters: dict,
        sort: list,
        format: str,
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        formats: Optional[dict] = None,
        metadata: ReportMetadata = None,
    ):
        self.mode = mode
        self.dataset = dataset
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.columns = columns
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.filters = filters
        self.sort = sort
        self.metadata = metadata
        self.limit = self.mode
        self.offset = self.mode
        self.format = format
        self.property_timezone = property_timezone
        self.property_settings = property_settings
        self.formats = formats

    def __repr__(self):
        """Representation of ReportList
        :return: string
        """
        return f"<ReportList, mode={self.mode}, dataset={self.dataset}, columns={self.columns}, property_ids={self.property_ids}, filters={self.filters}, sort={self.sort}>"

    @property
    def limit(self):
        return self.__limit

    @limit.setter
    def limit(self, mode):
        match (mode):
            case Mode.Preview:
                self.__limit = TABULAR_PREVIEW_LIMIT
            case Mode.Run:
                self.__limit = TABULAR_RUN_LIMIT
            case Mode.Export:
                self.__limit = EXPORT_LIMIT
            case Mode.Page:
                self.__limit = self.metadata.page_limit
            case _:
                self.__limit = None

    @property
    def offset(self):
        return self.__offset

    @offset.setter
    def offset(self, mode):
        match (mode):
            case Mode.Page:
                self.__offset = self.metadata.page_offset
            case _:
                self.__offset = 0

    def get_df(self) -> DataFrame:
        df = self.__get_df_data()

        if df.empty:
            return DataFrame()

        df = df.fillna(NA_FILL_STRING)
        df = self.translate_df(df, self.columns, self.dataset)

        return df

    def get_df_subtotals(self) -> Exception:
        return Exception("Subtotals not supported for this report")

    def get_df_totals(self) -> DataFrame:
        if not self.has_metrics(self.columns, self.dataset):
            return DataFrame()

        df_totals = self.__get_df_data(aggregate=True)

        if df_totals.empty:
            return DataFrame()

        df_totals.columns = self.get_df_columns(self.columns, self.dataset)
        return df_totals

    def __get_df_data(self, aggregate: bool = False) -> DataFrame:
        data = self.__get_query(aggregate=aggregate).all()
        return DataFrame(data=data)

    def __get_query(self, aggregate: bool = False) -> Query:
        return ReportQuery(
            self.dataset,
            self.property_ids,
            self.organization_id,
            self.custom_cdfs,
            self.custom_field_cdfs,
            self.columns,
            filters=self.filters,
            sort=self.sort if not aggregate else [],
            offset=self.offset,
            limit=self.limit,
            aggregate=aggregate,
            format=self.format,
            property_timezone=self.property_timezone,
            property_settings=self.property_settings,
            formats=self.formats,
            metadata=self.metadata,
        ).get_query()
