import warnings
from datetime import timezone
from typing import Optional

import pandas as pd
from pandas.api.types import is_string_dtype
from pandas.core.frame import Data<PERSON>rame

from sqlalchemy.orm.query import Query

from app.common.constants.dynamic_cdf_label import DYNAMIC_CDF_LABEL
from app.common.constants.na_fill_string import NA_FILL_STRING
from app.common.constants.raw_group_for_sort import RAW_GROUP_FOR_SORT_SUFFIX
from app.common.constants.table_limits import (
    EXPORT_LIMIT,
    PIVOT_PREVIEW_LIMIT,
    PIVOT_RUN_LIMIT,
)
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.enums import Mode
from app.enums.cdf import Cdf
from app.enums.metric import Metric
from app.reports.report_cdf import ReportCDF
from app.reports.report_interface import ReportInterface
from app.reports.report_metadata import ReportMetadata
from app.reports.report_query import ReportQuery


class ReportPivot(ReportInterface):
    def __init__(
        self,
        mode: Mode,
        dataset: Dataset,
        custom_cdfs: list,
        custom_field_cdfs: list,
        columns: list,
        group_rows: list,
        group_columns: list,
        property_ids: list,
        organization_id: int,
        filters: dict,
        sort: list,
        format: str,
        property_timezone: timezone = timezone.utc,
        property_settings: list[dict] = [],
        transpose: bool = False,
        formats: Optional[dict] = None,
        aggregate_count: Optional[bool] = True,
        metadata: ReportMetadata = None,
    ):
        columns_with_counts = [dict(d) for d in columns]
        if aggregate_count:
            columns_with_counts += [
                dict(
                    cdf=dict(column=ReportCDF.STAR_CDF, type=Cdf.Default.value),
                    metrics=[Metric.count.name],
                )
            ]

        self.mode = mode
        self.dataset = dataset
        self.custom_cdfs = custom_cdfs
        self.custom_field_cdfs = custom_field_cdfs
        self.columns = columns_with_counts
        self.group_rows = group_rows
        self.group_columns = group_columns
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.filters = filters
        self.sort = sort
        self.metadata = metadata
        self.limit = self.mode
        self.offset = self.mode
        self.format = format
        self.property_timezone = property_timezone
        self.property_settings = property_settings
        self.transpose = transpose
        self.formats = formats
        self.aggregate_count = aggregate_count

    def __repr__(self):
        """Representation of ReportPivot
        :return: string
        """
        return (
            f"<ReportPivot, mode={self.mode}, dataset={self.dataset}, columns={self.columns}, group_rows={self.group_rows}, "
            f"group_columns={self.group_columns}, property_ids={self.property_ids}, filters={self.filters}, sort={self.sort}>"
        )

    @property
    def limit(self):
        return self.__limit

    @limit.setter
    def limit(self, mode):
        match (mode):
            case Mode.Preview:
                self.__limit = PIVOT_PREVIEW_LIMIT
            case Mode.Run:
                self.__limit = PIVOT_RUN_LIMIT
            case Mode.Export:
                self.__limit = EXPORT_LIMIT
            case _:
                self.__limit = None

    @property
    def offset(self):
        return self.__offset

    @offset.setter
    def offset(self, mode):
        match (mode):
            case Mode.Page:
                self.__offset = self.metadata.page_offset
            case _:
                self.__offset = 0

    def get_df(self) -> DataFrame:
        if self.transpose:
            self.group_rows, self.group_columns = self.group_columns, self.group_rows
        df = self.__get_df_data(self.group_rows, include_raw_group_for_sort=True)
        if df.empty:
            return DataFrame()
        indexes = []
        groups = self.group_rows + self.group_columns
        for group in groups:

            formatted_column = group["cdf"]["column"]
            raw_column = f"{group['cdf']['column']}{RAW_GROUP_FOR_SORT_SUFFIX}"
            sorted_group_df = (
                pd.DataFrame(data=df[[formatted_column, raw_column]])
                .convert_dtypes()
                .dropna()
            )
            if is_string_dtype(sorted_group_df[raw_column]):
                sorted_group_df = sorted_group_df.sort_values(
                    by=raw_column, key=lambda col: col.str.lower()
                )
            else:
                sorted_group_df = sorted_group_df.sort_values(by=raw_column)

            sorted_list = (
                sorted_group_df.fillna(NA_FILL_STRING)
                .astype("string")[formatted_column]
                .drop_duplicates()
                .tolist()
            )
            sorted_list = [NA_FILL_STRING] + sorted_list
            indexes.append(sorted_list)

        columns_to_remove = []

        # Iterate over all columns in the DataFrame
        for col in df.columns:
            if col.endswith(RAW_GROUP_FOR_SORT_SUFFIX):
                # Extract the base column name by removing the suffix
                base_col = col[: -len(RAW_GROUP_FOR_SORT_SUFFIX)]

                # Check if the base column exists in df
                if base_col in df.columns:
                    # Add the suffixed column to the list of columns to remove
                    columns_to_remove.append(col)

        # Remove the identified columns from df
        df = df.drop(columns=columns_to_remove)

        with warnings.catch_warnings(record=True) as warning_list:
            df = df.convert_dtypes()

        if warning_list:
            for warning in warning_list:
                logger.warning(f"{warning._category_name}: {str(warning.message)}")

        df = df.astype("object")

        index, columns = self.get_flatten_groups(
            self.group_rows
        ), self.get_flatten_groups(self.group_columns)
        levels = self.__get_levels()
        df = df.infer_objects(copy=False).fillna(NA_FILL_STRING)
        df = df.set_index(index + columns)
        if isinstance(df.index, pd.MultiIndex):
            untyped_levels = [
                df.index.get_level_values(level).astype(str)
                for level in range(df.index.nlevels)
            ]
            df.index = pd.MultiIndex.from_arrays(untyped_levels)
        else:
            df.index = df.index.astype(str)

        df = self.__freeze_indexes(df, indexes)
        df.columns = df.columns.str.split(" - ").map(tuple)
        df = (
            df.unstack(columns)
            .infer_objects(copy=False)
            .fillna(NA_FILL_STRING)
            .reorder_levels(levels, axis=1)
        )
        df = self.__sort_column_levels(df)

        # check if there's only one column with only one metric, it has to be sorted differently
        if len(set([column[-2:] for column in df.columns])) > 1:
            df = df.sort_index(
                axis="columns", level=list(range(len(self.group_columns)))
            )
        else:
            sorted_level = df.columns.levels[0].sort_values()
            new_columns = pd.MultiIndex.from_tuples(
                [
                    col
                    for level_0 in sorted_level
                    for col in df.columns
                    if col[0] == level_0
                ]
            )
            columns_names = df.columns.names
            df = df.reindex(columns=new_columns)
            df.columns.names = columns_names

        # all indexes are categorical, so we can sort values
        df = df.sort_values(by=df.index.names, key=lambda x: x)

        return df

    def get_df_subtotals(self) -> DataFrame:
        if self.transpose:
            self.group_rows, self.group_columns = self.group_columns, self.group_rows

        if len(self.group_rows) == 1 or not self.has_metrics(
            self.columns, self.dataset
        ):
            return DataFrame()

        df_subtotals = self.__get_df_data([self.group_rows[0]], totals=True)

        if df_subtotals.empty:
            return DataFrame()

        index, columns = [
            self.get_flatten_groups(self.group_rows)[0]
        ], self.get_flatten_groups(self.group_columns)
        levels = self.__get_levels()
        df_subtotals = df_subtotals.fillna(NA_FILL_STRING)
        df_subtotals = df_subtotals.set_index(index + columns)
        if isinstance(df_subtotals.index, pd.MultiIndex):
            untyped_levels = [
                df_subtotals.index.get_level_values(level).astype(str)
                for level in range(df_subtotals.index.nlevels)
            ]
            df_subtotals.index = pd.MultiIndex.from_arrays(untyped_levels)
        else:
            df_subtotals.index = df_subtotals.index.astype(str)
        df_subtotals.columns = df_subtotals.columns.str.split(" - ").map(tuple)
        df_subtotals = (
            df_subtotals.unstack(columns)
            .infer_objects(copy=False)
            .fillna(NA_FILL_STRING)
            .reorder_levels(levels, axis=1)
            .sort_index(axis="columns", level=list(range(len(self.group_columns))))
        )
        return df_subtotals

    def get_df_totals(self) -> DataFrame:
        if self.transpose:
            self.group_rows, self.group_columns = self.group_columns, self.group_rows

        if not self.has_metrics(self.columns, self.dataset):
            return DataFrame()

        df_totals = self.__get_df_data([])

        if df_totals.empty:
            return DataFrame()

        columns = self.get_flatten_groups(self.group_columns)
        df_totals = df_totals.fillna(NA_FILL_STRING)
        df_totals = df_totals.set_index(columns)
        if isinstance(df_totals.index, pd.MultiIndex):
            untyped_levels = [
                df_totals.index.get_level_values(level).astype(str)
                for level in range(df_totals.index.nlevels)
            ]
            df_totals.index = pd.MultiIndex.from_arrays(untyped_levels)
        else:
            df_totals.index = df_totals.index.astype(str)
        df_totals.columns = df_totals.columns.str.split(" - ").map(tuple)
        return df_totals

    def __get_levels(self) -> list:
        return [2, 0, 1] if len(self.group_columns) == 1 else [2, 3, 0, 1]

    def __get_df_data(
        self, group_rows: list, totals=False, include_raw_group_for_sort=False
    ) -> DataFrame:
        data = self.__get_query(
            group_rows, totals, include_raw_groups_for_sort=include_raw_group_for_sort
        ).all()
        df = DataFrame(data=data)

        # Get columns without star cdf and translate them
        columns = [
            dict(column)
            for column in self.columns
            if column["cdf"]["column"] != ReportCDF.STAR_CDF
        ]
        df = self.translate_df(
            df, columns + self.group_columns + self.group_rows, self.dataset
        )
        return df

    def __get_query(
        self, group_rows: list, totals=False, include_raw_groups_for_sort=False
    ) -> Query:
        groups = group_rows + self.group_columns
        sort = [] if totals else self.sort
        return ReportQuery(
            self.dataset,
            self.property_ids,
            self.organization_id,
            self.custom_cdfs,
            self.custom_field_cdfs,
            self.columns,
            groups,
            filters=self.filters,
            sort=sort,
            offset=self.offset,
            limit=self.limit,
            aggregate=True,
            format=self.format,
            property_timezone=self.property_timezone,
            property_settings=self.property_settings,
            formats=self.formats,
            metadata=self.metadata,
            include_raw_groups_for_sort=include_raw_groups_for_sort,
        ).get_query()

    def __sort_column_levels(self, df: DataFrame) -> DataFrame:
        # in pivot report, there will always be 2 levels in the columns: cdf and metric
        # get the original order of the columns
        cdf_order = [column["cdf"]["column"] for column in self.columns]
        metric_order = [metric.name for metric in Metric]
        # add dynamic to the order, as it is not a metric but it'll show up in the same level and needs to be sorted even tho it'll be by itself
        metric_order.append(DYNAMIC_CDF_LABEL)

        cdf_index = len(self.group_columns)

        df.columns = df.columns.set_levels(
            pd.CategoricalIndex(
                df.columns.levels[cdf_index],
                categories=cdf_order,
                ordered=True,
            ),
            level=cdf_index,
        )
        metric_index = cdf_index + 1
        df.columns = df.columns.set_levels(
            pd.CategoricalIndex(
                df.columns.levels[metric_index],
                categories=metric_order,
                ordered=True,
            ),
            level=metric_index,
        )

        return df

    def __freeze_indexes(self, df: DataFrame, indexes: list) -> DataFrame:
        groups = self.group_rows + self.group_columns
        for i in range(0, len(groups)):

            df.index = df.index.set_levels(
                pd.CategoricalIndex(
                    df.index.levels[i],
                    categories=indexes[i],
                    ordered=True,
                ),
                level=i,
            )

        return df
