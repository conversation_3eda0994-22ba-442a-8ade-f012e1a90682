from abc import ABC, abstractmethod
from typing import Callable


class ReportExportCustomizationInterface(ABC):
    @abstractmethod
    def apply(self) -> Callable:
        """Implement apply method"""

    @abstractmethod
    def get_customization_list(self) -> Callable:
        """Implement get_customization_list method"""

    @abstractmethod
    def get_customization_summary(self) -> Callable:
        """Implement get_customization_summary method"""

    @abstractmethod
    def get_customization_summary_transposed(self) -> Callable:
        """Implement get_customization_summary_transposed method"""

    @abstractmethod
    def get_customization_summary_details(self) -> Callable:
        """Implement get_customization_summary_details method"""

    @abstractmethod
    def get_customization_pivot(self) -> Callable:
        """Implement get_customization_pivot method"""

    @abstractmethod
    def get_customization_list_comparison(self) -> Callable:
        """Implement get_customization_list_comparison method"""

    @abstractmethod
    def get_customization_summary_comparison_transposed(self) -> Callable:
        """Implement get_customization"""

    @abstractmethod
    def get_customization_summary_comparison(self) -> Callable:
        """Implement get_customization_summary_comparison method"""
