import re
from abc import ABC, abstractmethod

from flask_babel import gettext as _

import pandas as pd

from sqlalchemy.orm.collections import InstrumentedList

from app.cdfs.cdf import CDF
from app.common.constants.dynamic_cdf_label import DYNAMIC_CDF_LABEL
from app.common.translation_keys import get_translation_pattern
from app.datasets.dataset import Dataset
from app.enums import CdfKind
from app.enums.cdf import Cdf
from app.enums.multi_level import MultiLevel


class ReportInterface(ABC):
    @abstractmethod
    def get_df(self) -> pd.DataFrame:
        """Implement get_df method"""

    @abstractmethod
    def get_df_subtotals(self) -> pd.DataFrame:
        """Implement get_df_subtotals method"""

    @abstractmethod
    def get_df_totals(self) -> pd.DataFrame:
        """Implement get_df_totals method"""

    def get_df_columns(self, columns: list, dataset: Dataset) -> list:
        headers = []
        metrics = []
        if hasattr(self, "custom_cdfs") and self.custom_cdfs:
            if issubclass(type(self.custom_cdfs), InstrumentedList):
                dynamic_custom_cdfs = [
                    custom_cdf.column
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf.kind == CdfKind.Dynamic.name
                ]
            else:
                dynamic_custom_cdfs = [
                    custom_cdf["column"]
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf["kind"] == CdfKind.Dynamic.name
                ]
        else:
            dynamic_custom_cdfs = []

        for column in columns:
            name = column["cdf"]["column"]
            multi_level = (
                MultiLevel(column["cdf"]["multi_level_id"])
                if "multi_level_id" in column["cdf"]
                else None
            )
            if "metrics" in column:
                for metric in column["metrics"]:
                    headers.append(name)
                    metrics.append(metric)
            elif (
                column["cdf"]["type"] == Cdf.Default.value
                and CDF(dataset.kind, name, False, False, multi_level).kind
                in (
                    CdfKind.DynamicCurrency,
                    CdfKind.DynamicPercentage,
                    CdfKind.Dynamic,
                )
                or name in dynamic_custom_cdfs
            ):
                headers.append(name)
                metrics.append(DYNAMIC_CDF_LABEL)

        return pd.MultiIndex.from_arrays([headers, metrics])

    def get_flatten_groups(self, groups: list) -> list:
        return [group["cdf"]["column"] for group in groups]

    def has_metrics(self, columns: list, dataset: Dataset) -> bool:
        if hasattr(self, "custom_cdfs") and self.custom_cdfs:
            if issubclass(type(self.custom_cdfs), InstrumentedList):
                dynamic_custom_cdfs = [
                    custom_cdf.column
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf.kind == CdfKind.Dynamic.name
                ]
            else:
                dynamic_custom_cdfs = [
                    custom_cdf["column"]
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf["kind"] == CdfKind.Dynamic.name
                ]
        else:
            dynamic_custom_cdfs = []
        return any(
            [
                True
                for column in columns
                if "metrics" in column
                or column["cdf"]["column"] in dynamic_custom_cdfs
                or column["cdf"]["type"] == Cdf.Default.value
                and CDF(
                    dataset.kind,
                    column["cdf"]["column"],
                    False,
                    False,
                    (
                        MultiLevel(column["cdf"]["multi_level_id"])
                        if "multi_level_id" in column["cdf"]
                        else None
                    ),
                ).kind
                in (CdfKind.DynamicCurrency, CdfKind.DynamicPercentage)
            ]
        )

    def translate_df(
        self, df: pd.DataFrame, columns: list, dataset: Dataset
    ) -> pd.DataFrame:
        """Translate dataframe that uses key-based CDFs."""
        # Extract the relevant CDFs that are translatable
        translate_columns = [
            column["cdf"]
            for column in columns
            if CDF(
                dataset.kind,
                column["cdf"]["column"],
                multi_level=(
                    MultiLevel(column["cdf"].get("multi_level_id"))
                    if "multi_level_id" in column["cdf"]
                    else None
                ),
                is_custom_cdf=column["cdf"]["type"] == Cdf.Custom.value,
                is_custom_field_cdf=column["cdf"]["type"] == Cdf.CustomField.value,
            ).is_translate_cdf(
                self.custom_cdfs if hasattr(self, "custom_cdfs") else None
            )
        ]

        # Load and prepare translations if any custom cdf translations are found
        if any(cdf["type"] == Cdf.Custom.value for cdf in translate_columns):
            pattern = get_translation_pattern()

            # Function to replace known translation keys in the string
            def translate_within_string(match):
                key = match.group(0)
                return _(key)

        # Apply translation to appropriate columns
        for cdf in translate_columns:
            col = cdf["column"]
            if col in df.columns:
                if cdf["type"] == Cdf.Custom.value:
                    # Replace known translation keys in the string
                    df[col] = df[col].apply(
                        lambda val: (
                            re.sub(pattern, translate_within_string, val)
                            if isinstance(val, str)
                            else val
                        )
                    )
                else:
                    # Simple translation for string values
                    df[col] = df[col].apply(
                        lambda val: _(val) if isinstance(val, str) else val
                    )

        return df
