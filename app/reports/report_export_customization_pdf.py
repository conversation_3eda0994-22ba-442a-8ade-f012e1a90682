import os
from datetime import datetime
from io import BytesIO
from typing import Callable

import pandas as pd
from pandas.core.frame import DataFrame

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import ParagraphStyle
from reportlab.platypus import (
    BaseDocTemplate,
    Frame,
    Image,
    PageBreak,
    PageTemplate,
    Paragraph,
    Table,
    TableStyle,
)

from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.enums.export import Colors
from app.reports.report_export_customization_interface import (
    ReportExportCustomizationInterface,
)
from app.reports.report_type import ReportType


class ReportExportCustomizationPdf(ReportExportCustomizationInterface):
    LEFT_MARGIN = 36
    TOP_MARGIN = 50
    BOTTOM_MARGIN = 50
    WIDTH = letter[0]
    HEIGHT = letter[1]
    TABLE_CELL_STYLE = ParagraphStyle(
        "cell", fontName="Helvetica", fontSize=10, leading=12
    )
    TABLE_HEADER_STYLE = ParagraphStyle(
        "header",
        fontName="Helvetica-Bold",
        fontSize=12,
        textColor=colors.HexColor(f"#{Colors.Lightning500.value}"),
        leading=14,
    )

    def __init__(
        self,
        generated_at: datetime,
        pdf_buffer: BytesIO,
        df: DataFrame,
        title: str,
        type: ReportType,
        total_record_count: int,
        charts: list[bytes],
        records_df: DataFrame,
        totals_df: DataFrame,
        subtotals_df: DataFrame,
    ):
        self.generated_at = generated_at
        self.pdf_buffer = pdf_buffer
        self.df = df
        self.title = title
        self.type = type
        self.total_record_count = total_record_count
        self.charts = charts
        self.records_df = records_df
        self.totals_df = totals_df
        self.subtotals_df = subtotals_df

        self.table_elements = []

    def __repr__(self):
        """Representation of ReportExportCustomizationPdf
        :return: string
        """
        return f"<ReportExportCustomizationPdf, generated_at={self.generated_at}, path={self.path}, df={self.df}, title={self.title}, type={self.type}>"

    def apply(self):
        """
        Generates a PDF with a header (title and records), footer (timestamp, page number, logo),
        and the main table content in the middle.
        """
        # Basic setup
        record_count = len(self.df)
        generated_at = self.generated_at.strftime("%Y-%m-%d %H:%M:%S %Z")
        logo_path = os.path.join("app", "static", "logo.png")

        # Document Setup
        pdf = BaseDocTemplate(
            self.pdf_buffer,
            pagesize=letter,
            leftMargin=self.LEFT_MARGIN,
            rightMargin=self.LEFT_MARGIN,
            topMargin=self.TOP_MARGIN,
            bottomMargin=self.BOTTOM_MARGIN,
        )

        # Define the content frame (reduce height for less spacing between header and table)
        content_frame = Frame(
            self.LEFT_MARGIN,
            self.BOTTOM_MARGIN + 20,  # Reduced from +30 to +20
            self.WIDTH - 2 * self.LEFT_MARGIN,
            self.HEIGHT
            - self.TOP_MARGIN
            - self.BOTTOM_MARGIN
            - 60,  # Adjusted height to minimize space
        )

        # Page Template with Header/Footer
        page_template = PageTemplate(
            id="custom",
            frames=[content_frame],
            onPage=lambda canvas, doc: self.__add_header_footer(
                canvas,
                doc,
                self.title,
                record_count,
                generated_at,
                logo_path,
            ),
        )
        pdf.addPageTemplates([page_template])

        # Add the Charts before the table
        for _, chart_png in enumerate(self.charts):
            try:
                image = Image(
                    BytesIO(chart_png),
                    width=self.WIDTH - 2 * self.LEFT_MARGIN,
                    height=self.HEIGHT - 2 * self.TOP_MARGIN,
                )
                self.table_elements.append(image)
                self.table_elements.append(PageBreak())

            except Exception as exception:
                logger.error(
                    "There was a problem adding the chart to the pdf export",
                    extra={"error": str(exception)},
                )

        # Add main table content
        if not self.df.empty:
            match (self.type):
                case ReportType.List:
                    self.get_customization_list()
                case _:
                    raise InvalidUsage.bad_request(
                        "PDF Export only supports List reports for now."
                    )

        # Build the PDF
        pdf.build(self.table_elements)

        self.pdf_buffer.seek(0)
        return self.pdf_buffer

    def get_customization_list(self):
        """
        Customizes the PDF output for 'List' type reports, with distinct styling for headers, data rows, and totals.
        """
        # Define page width and column widths dynamically
        page_width = self.WIDTH - 2 * self.LEFT_MARGIN
        num_columns = len(self.records_df.columns)  # Exclude totals for column logic
        col_width = page_width / num_columns

        # Prepare table data
        table_data = []

        # Header row
        table_data.append(
            [Paragraph(header, self.TABLE_HEADER_STYLE) for header in self.df.columns]
        )

        # Data rows
        table_data += [
            [Paragraph(str(cell), self.TABLE_CELL_STYLE) for cell in row]
            for row in self.records_df.values.tolist()
        ]

        NUMBER_OF_TOTALS_ROWS = self.totals_df.columns.size

        if not self.totals_df.empty:
            totals_rows = self.df.iloc[-NUMBER_OF_TOTALS_ROWS:].values.tolist()
            for total_row in totals_rows:
                formatted_row = [
                    Paragraph(
                        str(cell) if pd.notna(cell) else "-",
                        ParagraphStyle(
                            "totals",
                            fontName="Helvetica-Bold",
                            fontSize=10,
                            textColor=colors.HexColor(f"#{Colors.Lightning500.value}"),
                        ),
                    )
                    for cell in total_row
                ]
                table_data.append(formatted_row)

        # Create the table
        table = Table(table_data, colWidths=[col_width] * num_columns)

        # Apply table styles
        style = TableStyle(
            [
                # Header formatting
                ("BACKGROUND", (0, 0), (-1, 0), colors.HexColor("#F1F3F4")),
                ("TEXTCOLOR", (0, 0), (-1, 0), colors.HexColor("#3366FF")),
                ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                ("TOPPADDING", (0, 0), (-1, 0), 8),
                ("BOTTOMPADDING", (0, 0), (-1, 0), 8),
                # Data row formatting
                ("ALIGN", (0, 1), (-1, -NUMBER_OF_TOTALS_ROWS - 1), "LEFT"),
                ("VALIGN", (0, 1), (-1, -NUMBER_OF_TOTALS_ROWS - 1), "TOP"),
                ("TEXTCOLOR", (0, 1), (-1, -NUMBER_OF_TOTALS_ROWS - 1), colors.black),
                ("GRID", (0, 0), (-1, -1), 0.5, colors.grey),
                ("TOPPADDING", (0, 1), (-1, -NUMBER_OF_TOTALS_ROWS - 1), 5),
                ("BOTTOMPADDING", (0, 1), (-1, -NUMBER_OF_TOTALS_ROWS - 1), 5),
                # Totals row formatting
                (
                    "BACKGROUND",
                    (0, -NUMBER_OF_TOTALS_ROWS),
                    (-1, -1),
                    colors.HexColor(f"#{Colors.Fog100.value}"),
                ),
                (
                    "TEXTCOLOR",
                    (0, -NUMBER_OF_TOTALS_ROWS),
                    (-1, -1),
                    colors.HexColor(f"#{Colors.Lightning500.value}"),
                ),
                ("FONTNAME", (0, -NUMBER_OF_TOTALS_ROWS), (-1, -1), "Helvetica-Bold"),
                ("ALIGN", (0, -NUMBER_OF_TOTALS_ROWS), (-1, -1), "RIGHT"),
                ("TOPPADDING", (0, -NUMBER_OF_TOTALS_ROWS), (-1, -1), 5),
                ("BOTTOMPADDING", (0, -NUMBER_OF_TOTALS_ROWS), (-1, -1), 5),
            ]
        )

        table.setStyle(style)

        # Add the table to the elements
        self.table_elements.append(table)

    def get_customization_summary(self) -> Callable:
        pass

    def get_customization_summary_transposed(self) -> Callable:
        pass

    def get_customization_summary_details(self) -> Callable:
        pass

    def get_customization_pivot(self) -> Callable:
        pass

    def get_customization_list_comparison(self) -> Callable:
        pass

    def get_customization_summary_comparison_transposed(self) -> Callable:
        pass

    def get_customization_summary_comparison(self) -> Callable:
        pass

    def __add_header_footer(
        self, canvas, doc, title, record_count, generated_at, logo_path
    ):
        """
        Draws the header and footer for the PDF with proper alignment and styling.
        """
        # Header
        canvas.setFont("Helvetica-Bold", 12)
        canvas.setFillColor(colors.HexColor("#3366FF"))  # Blue for title
        canvas.drawString(self.LEFT_MARGIN, self.HEIGHT - 40, f"Report Title: {title}")
        canvas.setFillColor(colors.black)  # Default color for record count
        canvas.drawString(
            self.LEFT_MARGIN, self.HEIGHT - 60, f"Number of Records: {record_count}"
        )

        # Footer
        canvas.setFont("Helvetica", 10)
        canvas.setFillColor(colors.black)  # Default footer color
        canvas.drawString(
            self.LEFT_MARGIN, self.BOTTOM_MARGIN - 10, f"Generated on: {generated_at}"
        )
        canvas.drawCentredString(
            self.WIDTH / 2, self.BOTTOM_MARGIN - 10, f"Page {doc.page}"
        )

        # Add Logo (bottom-right)
        if logo_path and os.path.exists(logo_path):
            logo_width = 70  # Adjust width for larger logo
            logo_height = 15  # Adjust height for larger logo
            x_position = self.WIDTH - logo_width - 50  # Adjust horizontal alignment
            y_position = self.BOTTOM_MARGIN - 10  # Adjust vertical alignment

            img = Image(logo_path, width=logo_width, height=logo_height)
            img.drawOn(canvas, x_position, y_position)
