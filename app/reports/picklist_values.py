from collections import OrderedDict
from types import SimpleNamespace

from sqlalchemy import select

from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums.cdf import Cdf
from app.enums.dataset import Dataset as DatasetEnum
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.filters.parse_filters import ParseFilters
from app.reports.report_cdf import ReportCDF


class PicklistValues:
    def __init__(
        self,
        organization_id: int,
        property_ids: list[int],
        filters: dict = [],
        custom_cdfs: list = [],
        multi_levels: list = [],
    ):
        self.organization_id = organization_id
        self.property_ids = property_ids
        self.filters = filters
        self.custom_cdfs = custom_cdfs
        self.multi_levels = multi_levels

    @property
    def multi_levels(self):
        return self.__multi_levels

    @multi_levels.setter
    def multi_levels(self, cdfs):
        joinables = [
            MultiLevel(MultiLevelEnum(multi_level))
            for multi_level in set(
                cdf["cdf"]["multi_level_id"]
                for cdf in cdfs
                if "multi_level_id" in cdf["cdf"]
            )
        ]
        self.__multi_levels = joinables

    def get_picklist_values_by_dataset_id_and_cdfs(
        self,
        dataset_id,
        columns: list[str],
    ):
        dataset = Dataset(DatasetEnum(dataset_id))
        custom_cdfs = (  # we get the custom cdf sqlalchemy attribute to use everywhere
            [
                ReportCDF(
                    model=dataset.model,
                    cdf=dict(
                        cdf=dict(
                            column=(
                                custom_cdf["column"]
                                if isinstance(custom_cdf, (dict, OrderedDict))
                                else custom_cdf.column
                            ),
                            type=Cdf.Custom.value,
                        )
                    ),
                    dataset_id=dataset.kind.value,
                    custom_cdf=(
                        SimpleNamespace(**custom_cdf)
                        if isinstance(custom_cdf, (dict, OrderedDict))
                        else custom_cdf
                    ),
                    dynamic=(
                        custom_cdf.get("dynamic", False)
                        if isinstance(custom_cdf, (dict, OrderedDict))
                        else getattr(custom_cdf, "dynamic", False)
                    ),
                ).get_cdf()
                for custom_cdf in self.custom_cdfs
            ]
            if self.custom_cdfs
            else []
        )

        model = dataset.model
        static_filters = dataset.static_filters
        if static_filters:
            static_filters = ParseFilters(
                dataset=dataset,
                organization_id=self.organization_id,
                property_ids=self.property_ids,
                filters=static_filters,
            ).get_filters()
        filters = []
        if self.filters:

            filters = ParseFilters(
                dataset,
                self.filters,
                self.property_ids,
                self.organization_id,
                custom_cdfs=custom_cdfs,
            ).get_filters()
        query = (
            select(
                *[
                    (
                        next(
                            custom_cdf
                            for custom_cdf in custom_cdfs
                            if custom_cdf.name == column["cdf"]["column"]
                        )
                        if column["cdf"]["type"] == Cdf.Custom.value
                        else getattr(
                            (
                                model
                                if not column["cdf"].get("multi_level_id")
                                else MultiLevel(
                                    MultiLevelEnum(column["cdf"]["multi_level_id"])
                                ).model
                            ),
                            column["cdf"]["column"],
                        )
                    )
                    for column in columns
                ]
            )
            .select_from(model)
            .where(
                *static_filters,
                *filters,
                *[
                    (
                        next(
                            custom_cdf
                            for custom_cdf in custom_cdfs
                            if custom_cdf.name == column["cdf"]["column"]
                        ).isnot(None)
                        if column["cdf"]["type"] == Cdf.Custom.value
                        else getattr(
                            (
                                model
                                if not column["cdf"].get("multi_level_id")
                                else MultiLevel(
                                    MultiLevelEnum(column["cdf"]["multi_level_id"])
                                ).model
                            ),
                            column["cdf"]["column"],
                        ).isnot(None)
                    )
                    for column in columns
                ],
                getattr(model, "organization_id") == self.organization_id,
                getattr(model, "property_id").in_(self.property_ids),
            )
            .group_by(
                *[
                    (
                        next(
                            custom_cdf
                            for custom_cdf in custom_cdfs
                            if custom_cdf.name == column["cdf"]["column"]
                        )
                        if column["cdf"]["type"] == Cdf.Custom.value
                        else getattr(
                            (
                                model
                                if not column["cdf"].get("multi_level_id")
                                else MultiLevel(
                                    MultiLevelEnum(column["cdf"]["multi_level_id"])
                                ).model
                            ),
                            column["cdf"]["column"],
                        )
                    )
                    for column in columns
                ]
            )
        )

        for multi_level in self.multi_levels:
            join_function = getattr(query, multi_level.join_function)
            query = join_function(multi_level.model, multi_level.join)
        return query
