ACCOUNT_BALANCES = {
    "title": "Classic Account Balances Report",
    "description": "",
    "dataset_id": 3,
    "columns": [
        {"cdf": {"type": "default", "column": "id"}},
        {"cdf": {"type": "default", "column": "reservation_number"}},
        {"cdf": {"type": "default", "column": "primary_guest_first_name"}},
        {"cdf": {"type": "default", "column": "primary_guest_surname"}},
        {"cdf": {"type": "default", "column": "booking_datetime_property_timezone"}},
        {"cdf": {"type": "default", "column": "checkin_date"}},
        {"cdf": {"type": "default", "column": "checkout_date"}},
        {"cdf": {"type": "default", "column": "reservation_status"}},
        {"cdf": {"type": "custom", "column": "custom_reservation_source"}},
        {
            "cdf": {"type": "default", "column": "grand_total_amount"},
            "metrics": ["sum"],
        },
        {
            "cdf": {"type": "default", "column": "reservation_paid_amount"},
            "metrics": ["sum"],
        },
        {
            "cdf": {"type": "default", "column": "reservation_balance_due_amount"},
            "metrics": ["sum"],
        },
    ],
    "group_rows": None,
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": None,
    "sort": None,
    "settings": {"details": True, "totals": True, "transpose": False},
    "formats": None,
    "custom_cdfs": [
        {
            "name": "Search",
            "description": "",
            "formula": [
                {"kind": "cdf", "value": "primary_guest_full_name"},
                {"kind": "separator", "value": " "},
                {"kind": "cdf", "value": "reservation_number"},
                {"kind": "separator", "value": " "},
                {"kind": "cdf", "value": "room_numbers"},
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Reservation Source",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "and": [
                                    {
                                        "kind": "cdf",
                                        "field": "reservation_source_category",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "OTA",
                                            "kind": "string",
                                        },
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "is_hotel_collect_booking",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "Yes",
                                            "kind": "string",
                                        },
                                    },
                                ]
                            },
                            "then": {
                                "kind": "formula",
                                "value": [
                                    {"kind": "cdf", "value": "reservation_source"},
                                    {
                                        "kind": "separator",
                                        "value": " (Hotel Collect Booking)",
                                    },
                                ],
                            },
                        },
                        {
                            "when": {
                                "and": [
                                    {
                                        "kind": "cdf",
                                        "field": "reservation_source_category",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "OTA",
                                            "kind": "string",
                                        },
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "is_hotel_collect_booking",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "No",
                                            "kind": "string",
                                        },
                                    },
                                ]
                            },
                            "then": {
                                "kind": "formula",
                                "value": [
                                    {"kind": "cdf", "value": "reservation_source"},
                                    {
                                        "kind": "separator",
                                        "value": " (Channel Collect Booking)",
                                    },
                                ],
                            },
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "reservation_source"},
                }
            ],
            "kind": "String",
            "format": None,
        },
    ],
}
