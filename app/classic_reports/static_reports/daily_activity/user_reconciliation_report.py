USER_RECONCILIATION_REPORT = {
    "title": "Classic User Reconciliation Report",
    "description": "",
    "dataset_id": 1,
    "columns": [
        {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]},
    ],
    "group_rows": [
        {"cdf": {"column": "custom_user", "type": "custom"}},
        {"cdf": {"column": "transaction_type", "type": "default"}},
        {"cdf": {"column": "custom_category", "type": "custom"}},  #
        {"cdf": {"column": "custom_revenue_type", "type": "custom"}},
        {"cdf": {"column": "card_type", "type": "default"}},
    ],
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "operator": "greater_than_or_equal",
                "value": "today",
            },
            {
                "cdf": {"type": "default", "column": "transaction_status"},
                "operator": "list_contains",
                "value": ["Posted"],
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "operator": "less_than_or_equal",
                "value": "today",
            },
        ]
    },
    "sort": None,
    "settings": {"details": False, "totals": False, "transpose": False},
    "formats": {"date": "YYYY-MM-DD", "link": True},
    "custom_cdfs": [
        {
            "name": "Transaction Type",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Room Rate",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Revenue"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "transaction_type"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Category",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_category",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {
                                "kind": "cdf",
                                "value": "item_service_category",
                            },
                        }
                    ],
                    "default_case": {"kind": "null", "value": ""},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Revenue Type",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Room Rate",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Rate"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "addon_charge_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "addon_charge_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "fee_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "fee_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "item_service_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "room_revenue_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "room_revenue_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "tax_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "tax_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "payment_method",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "payment_method"},
                        },
                    ],
                    "default_case": {"kind": "null", "value": ""},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "User",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "user",
                                "operator": "is_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "string", "value": "SYSTEM"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "user"},
                }
            ],
            "kind": "String",
            "format": None,
        },
    ],
}
