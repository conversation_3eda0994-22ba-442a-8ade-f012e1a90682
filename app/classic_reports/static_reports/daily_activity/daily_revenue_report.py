DAILY_REVENUE_REPORT = {
    "title": "Classic Daily Revenue Report",
    "description": "",
    "dataset_id": 1,
    "columns": [
        {
            "cdf": {"type": "default", "column": "balance_due_amount"},
            "metrics": ["sum"],
        },
        {"cdf": {"type": "custom", "column": "custom_revenue"}, "metrics": ["sum"]},
        {"cdf": {"type": "custom", "column": "custom_payments"}, "metrics": ["sum"]},
        {
            "cdf": {"type": "custom", "column": "custom_revenue_minus_payments"},
            "metrics": ["sum"],
        },
    ],
    "group_rows": [
        {"cdf": {"type": "custom", "column": "custom_transaction_type"}},
        {"cdf": {"type": "custom", "column": "custom_category"}},
        {"cdf": {"type": "custom", "column": "custom_name"}},
    ],
    "group_columns": None,
    "periods": None,
    "comparisons": [
        {
            "name": "Today",
            "filters": {
                "and": [
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "greater_than_or_equal",
                        "value": "today",
                    },
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "less_than",
                        "value": "tomorrow",
                    },
                ]
            },
        },
        {
            "name": "MTD (Today)",
            "filters": {
                "and": [
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "greater_than_or_equal",
                        "value": "start_current_month",
                    },
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "less_than",
                        "value": "tomorrow",
                    },
                ]
            },
        },
        {
            "name": "YTD (Today)",
            "filters": {
                "and": [
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "greater_than_or_equal",
                        "value": "start_current_year",
                    },
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "less_than",
                        "value": "tomorrow",
                    },
                ]
            },
        },
    ],
    "filters": {
        "and": [
            {
                "cdf": {"type": "default", "column": "transaction_status"},
                "operator": "list_contains",
                "value": ["Posted"],
            }
        ]
    },
    "sort": [
        {
            "cdf": {"type": "custom", "column": "custom_transaction_type"},
            "direction": "asc",
        },
        {
            "cdf": {"type": "custom", "column": "custom_category"},
            "direction": "asc",
        },
        {
            "cdf": {"type": "custom", "column": "custom_name"},
            "direction": "asc",
        },
    ],
    "settings": {"details": False, "totals": True, "transpose": False},
    "formats": None,
    "custom_cdfs": [
        {
            "name": "Transaction Type",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Room Rate",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Revenue"},
                        },
                        {
                            "when": {
                                "or": [
                                    {
                                        "kind": "cdf",
                                        "field": "transaction_type",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "Tax",
                                            "kind": "string",
                                        },
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "transaction_type",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "Fee",
                                            "kind": "string",
                                        },
                                    },
                                ]
                            },
                            "then": {"kind": "string", "value": "Taxes & Fees"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "is_refund",
                                "operator": "equals",
                                "case_value": {
                                    "kind": "string",
                                    "value": "Yes",
                                },
                            },
                            "then": {"kind": "string", "value": "Refund"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "payment_type",
                                "operator": "list_contains",
                                "case_value": {
                                    "kind": "list",
                                    "value": [
                                        "Chargeback",
                                        "ChargebackFee",
                                        "ChargebackReversal",
                                    ],
                                },
                            },
                            "then": {"kind": "string", "value": "Dispute"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "transaction_type"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Category",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "kind": "string",
                                    "value": "Room Rate",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Rate"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "addon_charge_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "null", "value": ""},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "room_revenue_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "kind": "null",
                                    "value": "",
                                },
                            },
                            "then": {"kind": "cdf", "value": "room_revenue_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "pos_charge_category",
                                "operator": "is_not_null",
                                "case_value": {
                                    "kind": "null",
                                    "value": "",
                                },
                            },
                            "then": {"kind": "cdf", "value": "pos_charge_category"},
                        },
                        {
                            "when": {
                                "or": [
                                    {
                                        "kind": "cdf",
                                        "field": "transaction_type",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "Tax",
                                            "kind": "string",
                                        },
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "transaction_type",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "Fee",
                                            "kind": "string",
                                        },
                                    },
                                ]
                            },
                            "then": {"kind": "cdf", "value": "transaction_type"},
                        },
                        {
                            "when": {
                                "and": [
                                    {
                                        "or": [
                                            {
                                                "kind": "cdf",
                                                "field": "is_refund",
                                                "operator": "equals",
                                                "case_value": {
                                                    "value": "Yes",
                                                    "kind": "string",
                                                },
                                            },
                                            {
                                                "kind": "cdf",
                                                "field": "transaction_type",
                                                "operator": "equals",
                                                "case_value": {
                                                    "value": "Payment",
                                                    "kind": "string",
                                                },
                                            },
                                            {
                                                "kind": "cdf",
                                                "field": "payment_type",
                                                "operator": "list_contains",
                                                "case_value": {
                                                    "kind": "list",
                                                    "value": [
                                                        "Chargeback",
                                                        "ChargebackFee",
                                                        "ChargebackReversal",
                                                    ],
                                                },
                                            },
                                        ],
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "card_type",
                                        "operator": "is_not_null",
                                        "case_value": {"value": "", "kind": "null"},
                                    },
                                ]
                            },
                            "then": {"kind": "cdf", "value": "card_type"},
                        },
                        {
                            "when": {
                                "and": [
                                    {
                                        "or": [
                                            {
                                                "kind": "cdf",
                                                "field": "is_refund",
                                                "operator": "equals",
                                                "case_value": {
                                                    "value": "Yes",
                                                    "kind": "string",
                                                },
                                            },
                                            {
                                                "kind": "cdf",
                                                "field": "transaction_type",
                                                "operator": "equals",
                                                "case_value": {
                                                    "value": "Payment",
                                                    "kind": "string",
                                                },
                                            },
                                            {
                                                "kind": "cdf",
                                                "field": "payment_type",
                                                "operator": "list_contains",
                                                "case_value": {
                                                    "kind": "list",
                                                    "value": [
                                                        "Chargeback",
                                                        "ChargebackFee",
                                                        "ChargebackReversal",
                                                    ],
                                                },
                                            },
                                        ],
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "card_type",
                                        "operator": "is_null",
                                        "case_value": {
                                            "value": "",
                                            "kind": "null",
                                        },
                                    },
                                ]
                            },
                            "then": {"kind": "cdf", "value": "payment_method"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_category",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {
                                "kind": "cdf",
                                "value": "item_service_category",
                            },
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Cancellation",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "cdf", "value": "transaction_description"},
                        },
                    ],
                    "default_case": {"kind": "null", "value": ""},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Name",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_type",
                                "operator": "is_not_null",
                                "case_value": {"value": "", "kind": "null"},
                            },
                            "then": {"kind": "cdf", "value": "item_service_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "tax_type",
                                "operator": "is_not_null",
                                "case_value": {"value": "", "kind": "null"},
                            },
                            "then": {"kind": "cdf", "value": "tax_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "fee_type",
                                "operator": "is_not_null",
                                "case_value": {"value": "", "kind": "null"},
                            },
                            "then": {"kind": "cdf", "value": "fee_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "addon_charge_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "addon_charge_type"},
                        },
                    ],
                    "default_case": {"kind": "null", "value": ""},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Revenue",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "not_equals",
                                "case_value": {
                                    "value": "Payment",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "cdf", "value": "balance_due_amount"},
                        }
                    ],
                    "default_case": {"kind": "null", "value": 0},
                }
            ],
            "kind": "Number",
            "format": None,
        },
        {
            "name": "Payments",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Payment",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "cdf", "value": "balance_due_amount"},
                        }
                    ],
                    "default_case": {"kind": "null", "value": 0},
                }
            ],
            "kind": "Number",
            "format": None,
        },
        {
            "name": "Revenue Minus Payments",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "not_equals",
                                "case_value": {
                                    "value": "Payment",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "cdf", "value": "balance_due_amount"},
                        }
                    ],
                    "default_case": {"kind": "null", "value": ""},
                },
                {
                    "kind": "operator",
                    "value": "-",
                },
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {"value": "Payment", "kind": "string"},
                            },
                            "then": {"kind": "cdf", "value": "balance_due_amount"},
                        }
                    ],
                    "default_case": {"kind": "null", "value": ""},
                },
            ],
            "kind": "Number",
            "format": None,
        },
    ],
}
