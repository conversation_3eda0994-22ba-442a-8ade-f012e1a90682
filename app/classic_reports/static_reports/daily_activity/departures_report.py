DEPARTURES_REPORT = {
    "title": "Classic Departures Report - Reservation Level",
    "description": "",
    "dataset_id": 3,
    "columns": [
        {"cdf": {"type": "default", "column": "id"}},
        {"cdf": {"type": "default", "column": "reservation_number"}},
        {"cdf": {"type": "default", "column": "primary_guest_first_name"}},
        {"cdf": {"type": "default", "column": "primary_guest_surname"}},
        {"cdf": {"type": "default", "column": "checkin_date"}},
        {"cdf": {"type": "default", "column": "checkout_date"}},
        {"cdf": {"type": "default", "column": "room_nights_count"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "guest_count"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "room_types"}},
        {"cdf": {"type": "default", "column": "room_numbers"}},
        {"cdf": {"type": "default", "column": "reservation_status"}},
    ],
    "group_rows": None,
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "cdf": {
                    "type": "default",
                    "column": "checkout_date",
                },
                "operator": "greater_than_or_equal",
                "value": "today",
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "checkout_date",
                },
                "operator": "less_than",
                "value": "tomorrow",
            },
            {
                "cdf": {"type": "default", "column": "reservation_status"},
                "operator": "list_contains",
                "value": ["Confirmed", "In-House"],
            },
        ]
    },
    "sort": None,
    "settings": {"details": True, "totals": False, "transpose": False},
    "formats": {"date": "YYYY-MM-DD", "link": True},
    "custom_cdfs": [
        {
            "name": "Search",
            "description": "",
            "formula": [
                {"kind": "cdf", "value": "primary_guest_full_name"},
                {"kind": "separator", "value": " "},
                {"kind": "cdf", "value": "reservation_number"},
                {"kind": "separator", "value": " "},
                {"kind": "cdf", "value": "room_numbers"},
            ],
            "kind": "String",
            "format": None,
        }
    ],
}
