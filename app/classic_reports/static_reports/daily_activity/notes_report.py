NOTES_REPORT = {
    "title": "Classic Notes Report",
    "description": "",
    "dataset_id": 3,
    "columns": [
        {"cdf": {"type": "default", "column": "booking_datetime_property_timezone"}},
        {"cdf": {"type": "default", "column": "checkout_date"}},
        {"cdf": {"type": "default", "column": "reservation_status"}},
        {"cdf": {"type": "default", "column": "id"}},
        {"cdf": {"type": "default", "column": "room_types"}},
        {
            "cdf": {"type": "default", "column": "reservation_balance_due_amount"},
            "metrics": ["sum"],
        },
        {"cdf": {"type": "default", "column": "primary_guest_full_name"}},
        {"cdf": {"type": "default", "column": "primary_guest_email"}},
        {"cdf": {"type": "default", "column": "primary_guest_phone_number"}},
        {"cdf": {"type": "default", "column": "primary_guest_mobile_phone_number"}},
        {"cdf": {"type": "default", "column": "primary_guest_address"}},
        {"cdf": {"type": "default", "column": "primary_guest_address_line_2"}},
        {"cdf": {"type": "default", "column": "primary_guest_city"}},
        {"cdf": {"type": "default", "column": "primary_guest_state"}},
        {"cdf": {"type": "default", "column": "primary_guest_postal_code"}},
        {"cdf": {"type": "default", "column": "primary_guest_residence_country_code"}},
        {"cdf": {"type": "default", "column": "primary_guest_residence_country"}},
        {"cdf": {"type": "default", "column": "third_party_confirmation_number"}},
        {"cdf": {"type": "default", "column": "room_nights_count"}, "metrics": ["sum"]},
        {
            "cdf": {"type": "default", "column": "grand_total_amount"},
            "metrics": ["sum"],
        },
        {"cdf": {"type": "default", "column": "active_primary_guest_notes"}},
        {"cdf": {"type": "default", "column": "active_booking_notes"}},
    ],
    "group_rows": None,
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "or": [
                    {
                        "cdf": {"type": "default", "column": "checkin_date"},
                        "operator": "greater_than_or_equal",
                        "value": "today",
                    },
                    {
                        "cdf": {"type": "default", "column": "checkout_date"},
                        "operator": "greater_than_or_equal",
                        "value": "today",
                    },
                ]
            }
        ]
    },
    "sort": None,
    "settings": {"details": True, "totals": False, "transpose": False},
    "formats": {"date": "YYYY-MM-DD", "link": True},
    "custom_cdfs": [],
}
