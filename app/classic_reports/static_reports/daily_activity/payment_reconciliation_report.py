PAYMENT_RECONCILIATION_REPORT = {
    "title": "Classic Payment Reconciliation Report",
    "description": "",
    "dataset_id": 1,
    "columns": [
        {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]}
    ],
    "group_rows": [
        {"cdf": {"type": "custom", "column": "custom_user"}},
        {"cdf": {"type": "default", "column": "payment_method"}},
        {"cdf": {"type": "default", "column": "card_type"}},
    ],
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "operator": "greater_than_or_equal",
                "value": "start_last_year",
            },
            {
                "cdf": {"type": "default", "column": "transaction_status"},
                "operator": "list_contains",
                "value": ["Posted"],
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "operator": "less_than",
                "value": "start_current_year",
            },
        ]
    },
    "sort": [
        {"cdf": {"type": "custom", "column": "custom_user"}, "direction": "asc"},
        {"cdf": {"type": "default", "column": "payment_method"}, "direction": "asc"},
        {"cdf": {"type": "default", "column": "card_type"}, "direction": "asc"},
    ],
    "settings": {"details": False, "totals": True, "transpose": False},
    "formats": None,
    "custom_cdfs": [
        {
            "name": "User",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "user",
                                "operator": "is_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "string", "value": "SYSTEM"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "user"},
                }
            ],
            "kind": "String",
            "format": None,
        },
    ],
}
