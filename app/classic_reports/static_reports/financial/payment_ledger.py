PAYMENT_LEDGER = {
    "title": "Classic Payment Ledger",
    "description": "",
    "dataset_id": 1,
    "columns": [
        {"cdf": {"type": "custom", "column": "custom_user"}},
        {
            "cdf": {
                "type": "default",
                "column": "service_date",
            }
        },
        {"cdf": {"type": "default", "column": "room_number"}},
        {"cdf": {"type": "default", "column": "primary_guest_first_name"}},
        {"cdf": {"type": "default", "column": "primary_guest_surname"}},
        {"cdf": {"type": "default", "column": "reservation_number"}},
        {"cdf": {"type": "default", "column": "custom_code"}},
        {"cdf": {"type": "default", "column": "checkin_date"}},
        {"cdf": {"type": "default", "column": "checkout_date"}},
        {"cdf": {"type": "default", "column": "reservation_status"}},
        {"cdf": {"type": "default", "column": "transaction_notes"}},
        {"cdf": {"type": "default", "column": "quantity_amount"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "booking_id"}},
        {"cdf": {"type": "default", "column": "guest_id"}},
        {"cdf": {"type": "default", "column": "house_account_id"}},
        {"cdf": {"type": "default", "column": "group_profile_id"}},
        {"cdf": {"type": "default", "column": "house_account_name"}},
        {"cdf": {"type": "default", "column": "group_profile_code"}},
    ],
    "group_rows": None,
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "operator": "greater_than_or_equal",
                "value": "today",
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "operator": "less_than_or_equal",
                "value": "today",
            },
            {
                "cdf": {"type": "default", "column": "transaction_status"},
                "operator": "list_contains",
                "value": ["Posted"],
            },
        ]
    },
    "sort": None,
    "settings": {"details": True, "totals": True, "transpose": False},
    "formats": {"date": "YYYY-MM-DD", "link": True},
    "custom_cdfs": [
        {
            "name": "User",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "user",
                                "operator": "is_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "string", "value": "SYSTEM"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "user"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Search",
            "description": "",
            "formula": [
                {"kind": "cdf", "value": "reservation_number", "metric": None},
                {"kind": "separator", "value": " ", "metric": None},
                {"kind": "cdf", "value": "room_number", "metric": None},
                {"kind": "separator", "value": " ", "metric": None},
                {"kind": "cdf", "value": "primary_guest_full_name", "metric": None},
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Kind",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "debit_amount",
                                "operator": "greater_than",
                                "case_value": {"value": 0, "kind": "number"},
                            },
                            "then": {"kind": "string", "value": "Debits (Charges)"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "credit_amount",
                                "operator": "greater_than",
                                "case_value": {"value": 0, "kind": "number"},
                            },
                            "then": {"kind": "string", "value": "Credits (Payments)"},
                        },
                    ],
                    "default_case": {"kind": "null", "value": ""},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Transaction Type",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Room Rate",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Revenue"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "transaction_type"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Category",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_category",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {
                                "kind": "cdf",
                                "value": "item_service_category",
                            },
                        }
                    ],
                    "default_case": {"kind": "string", "value": "-"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Revenue Type",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Room Rate",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Rate"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "addon_charge_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "addon_charge_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "fee_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "fee_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "item_service_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "room_revenue_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "room_revenue_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "tax_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "tax_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "payment_method",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "payment_method"},
                        },
                    ],
                    "default_case": {"kind": "string", "value": "-"},
                }
            ],
            "kind": "String",
            "format": None,
        },
    ],
}
