ADJUSTMENTS_REPORT = {
    "title": "Classic Adjustments Report",
    "description": "",
    "dataset_id": 1,
    "group_rows": None,
    "group_columns": None,
    "filters": {
        "and": [
            {
                "cdf": {"type": "default", "column": "transaction_status"},
                "value": ["Posted"],
                "operator": "list_contains",
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "value": "start_current_month",
                "operator": "greater_than_or_equal",
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "service_date",
                },
                "value": "today",
                "operator": "less_than_or_equal",
            },
            {
                "cdf": {"type": "default", "column": "is_transaction_adjusted"},
                "value": "Yes",
                "operator": "equals",
            },
        ]
    },
    "columns": [
        {"cdf": {"type": "default", "column": "user"}},
        {
            "cdf": {
                "type": "default",
                "column": "service_date",
            }
        },
        {"cdf": {"type": "default", "column": "room_number"}},
        {"cdf": {"type": "default", "column": "primary_guest_first_name"}},
        {"cdf": {"type": "default", "column": "primary_guest_surname"}},
        {"cdf": {"type": "default", "column": "house_account_name"}},
        {"cdf": {"type": "default", "column": "reservation_number"}},
        {"cdf": {"type": "default", "column": "group_profile_name"}},
        {"cdf": {"type": "default", "column": "custom_code"}},
        {"cdf": {"type": "default", "column": "internal_transaction_code"}},
        {"cdf": {"type": "default", "column": "transaction_type"}},
        {"cdf": {"type": "default", "column": "transaction_description"}},
        {"cdf": {"type": "default", "column": "checkin_date"}},
        {"cdf": {"type": "default", "column": "checkout_date"}},
        {"cdf": {"type": "default", "column": "reservation_status"}},
        {"cdf": {"type": "default", "column": "transaction_notes"}},
        {"cdf": {"type": "default", "column": "quantity_amount"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "debit_amount"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "credit_amount"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "house_account_id"}},
        {"cdf": {"type": "default", "column": "group_profile_id"}},
        {"cdf": {"type": "default", "column": "group_profile_code"}},
        {"cdf": {"type": "default", "column": "payment_id"}},
        {"cdf": {"type": "default", "column": "id"}},
    ],
    "settings": {"details": True, "totals": True, "transpose": False},
    "custom_cdfs": [
        {
            "formula": [
                {"kind": "cdf", "value": "primary_guest_full_name"},
                {"kind": "separator", "value": " "},
                {"kind": "cdf", "value": "room_number"},
            ],
            "kind": "String",
            "name": "Search",
            "description": "",
            "format": None,
        },
        {
            "name": "User",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "user",
                                "operator": "is_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "string", "value": "SYSTEM"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "user"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Kind",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "debit_amount",
                                "operator": "greater_than",
                                "case_value": {"value": 0, "kind": "number"},
                            },
                            "then": {"kind": "string", "value": "Debits (Charges)"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "credit_amount",
                                "operator": "greater_than",
                                "case_value": {"value": 0, "kind": "number"},
                            },
                            "then": {"kind": "string", "value": "Credits (Payments)"},
                        },
                    ],
                    "default_case": {"kind": "null", "value": ""},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Transaction Type",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Room Rate",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Revenue"},
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "transaction_type"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Category",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_category",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {
                                "kind": "cdf",
                                "value": "item_service_category",
                            },
                        }
                    ],
                    "default_case": {"kind": "string", "value": "-"},
                }
            ],
            "kind": "String",
            "format": None,
        },
        {
            "name": "Revenue Type",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "transaction_type",
                                "operator": "equals",
                                "case_value": {
                                    "value": "Room Rate",
                                    "kind": "string",
                                },
                            },
                            "then": {"kind": "string", "value": "Room Rate"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "addon_charge_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "addon_charge_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "fee_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "fee_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "item_service_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "item_service_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "room_revenue_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "room_revenue_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "tax_type",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "tax_type"},
                        },
                        {
                            "when": {
                                "kind": "cdf",
                                "field": "payment_method",
                                "operator": "is_not_null",
                                "case_value": {
                                    "value": "",
                                    "kind": "null",
                                },
                            },
                            "then": {"kind": "cdf", "value": "payment_method"},
                        },
                    ],
                    "default_case": {"kind": "string", "value": "-"},
                }
            ],
            "kind": "String",
            "format": None,
        },
    ],
    "formats": None,
    "periods": None,
    "sort": None,
}
