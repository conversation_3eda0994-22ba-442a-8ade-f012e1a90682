COMMISSION_REPORT = {
    "title": "Classic Commission Report",
    "description": "",
    "dataset_id": 3,
    "columns": [
        {"cdf": {"type": "default", "column": "id"}},
        {"cdf": {"type": "custom", "column": "custom_room_total"}},
        {"cdf": {"type": "default", "column": "reservation_number"}},
        {"cdf": {"type": "default", "column": "primary_guest_full_name"}},
        {"cdf": {"type": "default", "column": "property_name"}},
        {"cdf": {"type": "default", "column": "booking_datetime_property_timezone"}},
        {"cdf": {"type": "default", "column": "checkin_date"}},
        {"cdf": {"type": "default", "column": "checkout_date"}},
        {"cdf": {"type": "default", "column": "room_nights_count"}, "metrics": ["sum"]},
        {
            "cdf": {"type": "default", "column": "grand_total_amount"},
            "metrics": ["sum"],
        },
        {"cdf": {"type": "custom", "column": "custom_commission_percentage"}},
        {"cdf": {"type": "default", "column": "commission_amount"}, "metrics": ["sum"]},
    ],
    "group_rows": [
        {"cdf": {"type": "default", "column": "reservation_source_category"}},
        {"cdf": {"type": "custom", "column": "custom_reservation_source"}},
    ],
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "cdf": {
                    "type": "default",
                    "column": "booking_datetime_property_timezone",
                },
                "operator": "greater_than_or_equal",
                "value": "months_prior;1",
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "booking_datetime_property_timezone",
                },
                "operator": "less_than",
                "value": "tomorrow",
            },
            {
                "cdf": {"type": "default", "column": "reservation_status"},
                "operator": "not_list_contains",
                "value": ["Cancelled", "No Show"],
            },
        ]
    },
    "sort": [
        {
            "cdf": {"type": "default", "column": "reservation_source_category"},
            "direction": "asc",
        },
        {
            "cdf": {"type": "custom", "column": "custom_reservation_source"},
            "direction": "asc",
        },
    ],
    "settings": {"details": True, "totals": True, "transpose": False},
    "formats": {"date": "YYYY-MM-DD", "link": True},
    "custom_cdfs": [
        {
            "name": "Commission Percentage",
            "description": "",
            "formula": [
                {"kind": "cdf", "value": "commission_amount", "metric": "sum"},
                {"kind": "operator", "value": "/", "metric": None},
                {"kind": "cdf", "value": "grand_total_amount", "metric": "sum"},
            ],
            "kind": "Dynamic",
            "format": "number",
        },
        {
            "name": "Room Total",
            "description": "",
            "formula": [
                {"kind": "cdf", "value": "room_revenue_total_amount", "metric": "sum"},
                {"kind": "operator", "value": "+", "metric": None},
                {"kind": "cdf", "value": "taxes_value_amount", "metric": "sum"},
            ],
            "kind": "Dynamic",
            "format": "number",
        },
        {
            "name": "Reservation Source",
            "description": "",
            "formula": [
                {
                    "kind": "case",
                    "value": "",
                    "cases": [
                        {
                            "when": {
                                "and": [
                                    {
                                        "kind": "cdf",
                                        "field": "reservation_source_category",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "OTA",
                                            "kind": "string",
                                        },
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "is_hotel_collect_booking",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "Yes",
                                            "kind": "string",
                                        },
                                    },
                                ]
                            },
                            "then": {
                                "kind": "formula",
                                "value": [
                                    {"kind": "cdf", "value": "reservation_source"},
                                    {
                                        "kind": "separator",
                                        "value": " (Hotel Collect Booking)",
                                    },
                                ],
                            },
                        },
                        {
                            "when": {
                                "and": [
                                    {
                                        "kind": "cdf",
                                        "field": "reservation_source_category",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "OTA",
                                            "kind": "string",
                                        },
                                    },
                                    {
                                        "kind": "cdf",
                                        "field": "is_hotel_collect_booking",
                                        "operator": "equals",
                                        "case_value": {
                                            "value": "No",
                                            "kind": "string",
                                        },
                                    },
                                ]
                            },
                            "then": {
                                "kind": "formula",
                                "value": [
                                    {"kind": "cdf", "value": "reservation_source"},
                                    {
                                        "kind": "separator",
                                        "value": " (Channel Collect Booking)",
                                    },
                                ],
                            },
                        },
                    ],
                    "default_case": {"kind": "cdf", "value": "reservation_source"},
                }
            ],
            "kind": "String",
            "format": None,
        },
    ],
}
