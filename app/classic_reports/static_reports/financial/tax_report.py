TAX_REPORT = {
    "title": "Classic Tax Report",
    "description": "",
    "dataset_id": 1,
    "columns": [
        {"cdf": {"type": "default", "column": "balance_due_amount"}, "metrics": ["sum"]}
    ],
    "group_rows": [
        {"cdf": {"type": "default", "column": "transaction_type"}},
        {"cdf": {"type": "custom", "column": "custom_tax_or_fee_type"}},
    ],
    "group_columns": None,
    "periods": None,
    "comparisons": [
        {
            "name": "Today",
            "filters": {
                "and": [
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "greater_than_or_equal",
                        "value": "today",
                    },
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "less_than",
                        "value": "tomorrow",
                    },
                ]
            },
        },
        {
            "name": "MTD (Today)",
            "filters": {
                "and": [
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "greater_than_or_equal",
                        "value": "start_current_month",
                    },
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "less_than",
                        "value": "tomorrow",
                    },
                ]
            },
        },
        {
            "name": "YTD (Today)",
            "filters": {
                "and": [
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "greater_than_or_equal",
                        "value": "start_current_year",
                    },
                    {
                        "cdf": {
                            "type": "default",
                            "column": "service_date",
                        },
                        "operator": "less_than",
                        "value": "tomorrow",
                    },
                ]
            },
        },
    ],
    "filters": {
        "and": [
            {
                "cdf": {"type": "default", "column": "transaction_status"},
                "operator": "list_contains",
                "value": ["Posted"],
            },
            {
                "or": [
                    {
                        "cdf": {"type": "default", "column": "tax_type"},
                        "operator": "is_not_null",
                        "value": "",
                    },
                    {
                        "cdf": {"type": "default", "column": "fee_type"},
                        "operator": "is_not_null",
                        "value": "",
                    },
                ]
            },
            {
                "or": [
                    {
                        "cdf": {
                            "type": "default",
                            "column": "is_transaction_adjusted",
                        },
                        "operator": "equals",
                        "value": "Yes",
                    },
                    {
                        "cdf": {
                            "type": "default",
                            "column": "is_transaction_adjusted",
                        },
                        "operator": "equals",
                        "value": "No",
                    },
                ]
            },
        ]
    },
    "sort": [
        {"cdf": {"type": "default", "column": "transaction_type"}, "direction": "asc"},
        {
            "cdf": {"type": "custom", "column": "custom_tax_or_fee_type"},
            "direction": "asc",
        },
    ],
    "settings": {"details": False, "totals": True, "transpose": False},
    "formats": {"date": "YYYY-MM-DD", "link": True},
    "custom_cdfs": [
        {
            "name": "Tax or Fee Type",
            "description": "",
            "formula": [
                {"kind": "cdf", "value": "tax_type", "metric": None},
                {"kind": "cdf", "value": "fee_type", "metric": None},
            ],
            "kind": "String",
            "format": None,
        }
    ],
}
