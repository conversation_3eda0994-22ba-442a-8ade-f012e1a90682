CHANNEL_PRODUCTION_REPORT = {
    "title": "Channel Production Report",
    "description": "",
    "dataset_id": 7,
    "columns": [
        {"cdf": {"type": "default", "column": "room_revenue"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "rooms_sold"}, "metrics": ["sum"]},
        {"cdf": {"type": "default", "column": "adr"}},
    ],
    "group_rows": [
        {
            "cdf": {
                "type": "default",
                "column": "reservation_source_category",
                "multi_level_id": 4,
            }
        },
    ],
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "cdf": {"type": "default", "column": "stay_date"},
                "operator": "greater_than_or_equal",
                "value": "start_current_year",
            },
            {
                "cdf": {"type": "default", "column": "stay_date"},
                "operator": "less_than",
                "value": "start_next_year",
            },
            {
                "value": [
                    "Confirmation Pending",
                    "Confirmed",
                    "In-House",
                    "Checked Out",
                ],
                "cdf": {
                    "type": "default",
                    "column": "reservation_status",
                    "multi_level_id": 4,
                },
                "operator": "list_contains",
            },
        ]
    },
    "sort": [
        {
            "cdf": {
                "type": "default",
                "column": "reservation_source_category",
                "multi_level_id": 4,
            },
            "direction": "asc",
        },
    ],
    "settings": {"details": False, "totals": True, "transpose": False},
    "formats": None,
    "custom_cdfs": [],
}
