RESERVATIONS_BY_COUNTRY_REPORT = {
    "title": "Reservations by Country",
    "description": "",
    "dataset_id": 3,
    "columns": [
        {"cdf": {"type": "default", "column": "guest_count"}, "metrics": ["count"]},
    ],
    "group_rows": [
        {"cdf": {"type": "default", "column": "primary_guest_residence_country_code"}},
    ],
    "group_columns": None,
    "periods": None,
    "comparisons": None,
    "filters": {
        "and": [
            {
                "cdf": {
                    "type": "default",
                    "column": "booking_datetime_property_timezone",
                },
                "operator": "greater_than_or_equal",
                "value": "start_current_month",
            },
            {
                "cdf": {
                    "type": "default",
                    "column": "booking_datetime_property_timezone",
                },
                "operator": "less_than",
                "value": "start_next_month",
            },
        ]
    },
    "sort": None,
    "settings": {"details": False, "totals": True, "transpose": False},
    "formats": None,
    "custom_cdfs": [],
}
