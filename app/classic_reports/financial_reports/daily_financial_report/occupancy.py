import calendar
from datetime import date, timedelta

from sqlalchemy import Date, Numeric, Select, cast, func, literal_column, select

from app.common.constants.property_setting import DEFAULT_ROUNDING_PRECISION
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum


class DailyFinancialOccupancy:
    def __init__(self, organization_id: int, property_id: int, report_date: date):
        self.organization_id = organization_id
        self.property_id = property_id
        self.report_date = report_date
        self.occupancy_model = Dataset(DatasetEnum.OccupancyV1).model

    def get_room_revenue_today(self) -> Select:
        select_statement = (
            select(
                *self.__get_select_room_revenue_columns(self.occupancy_model),
            )
            .where(*self.__get_day_filters(self.occupancy_model))
            .group_by(
                self.occupancy_model.organization_id,
                self.occupancy_model.property_id,
            )
        )
        return select_statement

    def get_room_revenue_month_to_date(self) -> Select:
        select_statement = (
            select(
                *self.__get_select_room_revenue_columns(self.occupancy_model),
            )
            .where(*self.__get_month_to_date_filters(self.occupancy_model))
            .group_by(
                self.occupancy_model.organization_id,
                self.occupancy_model.property_id,
            )
        )
        return select_statement

    def get_room_revenue_14_days_forecast(self) -> Select:
        # Generate a date range for the next 14 days
        date_range_cte = select(
            func.cast(
                func.generate_series(
                    literal_column(f"'{self.report_date}'::date"),
                    literal_column(f"'{self.report_date + timedelta(days=13)}'::date"),
                    "1 day",
                ),
                Date,
            ).label("date")
        ).cte("date_range")

        # Select statement with a left join on the date range
        select_statement = (
            select(
                date_range_cte.c.date,
                *self.__get_select_room_revenue_14_days_forecast_columns(
                    self.occupancy_model
                ),
            )
            .select_from(date_range_cte)
            .outerjoin(
                self.occupancy_model,
                (date_range_cte.c.date == self.occupancy_model.stay_date)
                & (self.occupancy_model.organization_id == self.organization_id)
                & (self.occupancy_model.property_id == self.property_id),
            )
            .group_by(
                self.occupancy_model.organization_id,
                self.occupancy_model.property_id,
                date_range_cte.c.date,
            )
        )
        return select_statement

    def get_occupancy_rate_today(self) -> Select:
        select_statement = (
            select(
                *self.__get_select_occupancy_rate_columns(self.occupancy_model),
            )
            .where(*self.__get_day_filters(self.occupancy_model))
            .group_by(
                self.occupancy_model.organization_id,
                self.occupancy_model.property_id,
            )
        )
        return select_statement

    def get_occupancy_rate_month_to_date(self) -> Select:
        select_statement = (
            select(
                *self.__get_select_occupancy_rate_columns(self.occupancy_model),
            )
            .where(*self.__get_month_to_date_filters(self.occupancy_model))
            .group_by(
                self.occupancy_model.organization_id,
                self.occupancy_model.property_id,
            )
        )
        return select_statement

    def get_on_the_books_forecast(self) -> Select:
        select_statement = (
            select(
                *self.__get_select_on_the_books_forecast_columns(self.occupancy_model),
            )
            .where(*self.__get_whole_month_filters(self.occupancy_model))
            .group_by(
                self.occupancy_model.organization_id,
                self.occupancy_model.property_id,
            )
        )
        return select_statement

    def __get_day_filters(self, model) -> Select:
        filters = [
            model.organization_id == self.organization_id,
            model.property_id == self.property_id,
            model.stay_date == self.report_date,
        ]
        return filters

    def __get_month_to_date_filters(self, model) -> Select:
        filters = [
            model.organization_id == self.organization_id,
            model.property_id == self.property_id,
            model.stay_date.between(self.report_date.replace(day=1), self.report_date),
        ]
        return filters

    def __get_whole_month_filters(self, model) -> Select:
        start_date = self.report_date.replace(day=1)
        end_date = self.report_date.replace(
            day=calendar.monthrange(self.report_date.year, self.report_date.month)[1]
        )
        filters = [
            model.organization_id == self.organization_id,
            model.property_id == self.property_id,
            model.stay_date.between(start_date, end_date),
        ]
        return filters

    def __get_select_occupancy_rate_columns(self, model) -> list:
        columns = [
            func.sum(model.rooms_sold).label("occupancy"),
            func.sum(
                model.capacity_count
                - model.rooms_sold
                - model.blocked_room_count
                - model.out_of_service_count
            ).label("availability"),
            func.sum(model.blocked_room_count + model.out_of_service_count).label(
                "blocks_and_holds"
            ),
            func.round(
                cast(
                    (
                        (
                            func.sum(model.rooms_sold)
                            / func.nullif(func.sum(model.capacity_count), 0)
                        )
                        * 100
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("occupancy_percentage"),
            func.round(
                cast(
                    (
                        func.sum(model.blocked_room_count + model.out_of_service_count)
                        / func.nullif(func.sum(model.capacity_count), 0)
                        * 100
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("blocks_and_holds_percentage"),
            func.round(
                cast(
                    (
                        func.sum(
                            model.capacity_count
                            - model.blocked_room_count
                            - model.rooms_sold
                            - model.out_of_service_count
                        )
                        / func.nullif(func.sum(model.capacity_count), 0)
                        * 100
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("availability_percentage"),
        ]
        return columns

    def __get_select_room_revenue_columns(self, model) -> list:
        columns = [
            func.round(
                cast(
                    func.coalesce(
                        func.sum(model.room_revenue)
                        / func.nullif(
                            (func.sum(model.rooms_sold)),
                            0,
                        ),
                        0,
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("adr"),
            func.round(
                cast(
                    func.coalesce(
                        func.sum(model.room_revenue)
                        / func.nullif(
                            (func.sum(model.capacity_count)),
                            0,
                        ),
                        0,
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("revpar"),
            func.round(
                cast(func.coalesce(func.sum(model.room_revenue), 0), Numeric),
                DEFAULT_ROUNDING_PRECISION,
            ).label("room_revenue"),
        ]
        return columns

    def __get_select_room_revenue_14_days_forecast_columns(self, model) -> list:
        columns = [
            func.coalesce(
                func.round(
                    cast(
                        (
                            (
                                func.sum(model.rooms_sold)
                                / func.nullif(func.sum(model.capacity_count), 0)
                            )
                            * 100
                        ),
                        Numeric,
                    ),
                    DEFAULT_ROUNDING_PRECISION,
                ),
                0,
            ).label("occupancy"),
        ] + self.__get_select_room_revenue_columns(model)
        return columns

    def __get_select_on_the_books_forecast_columns(self, model) -> list:
        columns = [
            func.sum(model.rooms_sold).label("total_rooms_sold"),
            func.round(
                cast(
                    (
                        func.sum(model.rooms_sold)
                        / func.nullif(
                            (func.sum(model.capacity_count)),
                            0,
                        )
                        * 100
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("total_rooms_sold_percentage"),
        ] + self.__get_select_room_revenue_columns(model)
        return columns
