from datetime import date
from typing import Type

from sqlalchemy import (
    BIGINT,
    CompoundSelect,
    DATE,
    Numeric,
    case,
    cast,
    func,
    literal,
    select,
    union_all,
)
from sqlalchemy.orm import DeclarativeMeta, Query
from sqlalchemy.sql.functions import coalesce

from app.common.constants.property_setting import DEFAULT_ROUNDING_PRECISION
from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum
from app.models.daily_financial_report import (
    DFRCapacity,
    DFRCheckin,
    DFRCheckout,
    DFRReservations,
    DFRRoomReservation,
    DFRTransactions,
)


class DailyFinancialStatistics:
    def __init__(self, organization_id: int, property_id: int, report_date: date):
        self.organization_id = organization_id
        self.property_id = property_id
        self.report_date = report_date
        self.occupancy_model = Dataset(DatasetEnum.OccupancyV1).model

    def get_today_query(self) -> Query:
        return self.__get_union_query(
            union_all(
                self.__get_transactions_query(),
                self.__get_checkout_query(),
                self.__get_checkin_query(),
                self.__get_capacity_query(),
                self.__get_room_reservation_query(),
                self.__get_los(),
                self.__get_room_revenue_query(),
            )
        )

    def get_month_to_date_query(self) -> Query:
        return self.__get_union_query(
            union_all(
                self.__get_transactions_query(month_to_date=True),
                self.__get_checkout_query(month_to_date=True),
                self.__get_checkin_query(month_to_date=True),
                self.__get_capacity_query(month_to_date=True),
                self.__get_room_reservation_query(month_to_date=True),
                self.__get_los(month_to_date=True),
                self.__get_room_revenue_query(month_to_date=True),
            )
        )

    def __get_union_query(self, union_query: CompoundSelect) -> Query:
        union_query = union_query.alias("union_query")
        return select(
            coalesce(func.sum(coalesce(union_query.c.checkout_today, 0)), 0).label(
                "checkouts"
            ),
            coalesce(func.sum(coalesce(union_query.c.checkin_today, 0)), 0).label(
                "checkins"
            ),
            coalesce(func.sum(coalesce(union_query.c.walkin_today, 0)), 0).label(
                "walkins"
            ),
            coalesce(func.sum(coalesce(union_query.c.noshow_today, 0)), 0).label(
                "no_shows"
            ),
            func.round(
                cast(
                    coalesce(func.sum(coalesce(union_query.c.adjustment_today, 0)), 0),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("adjustments"),
            func.round(
                cast(
                    coalesce(
                        func.sum(coalesce(union_query.c.item_service_today, 0)), 0
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("items_and_services_revenue"),
            func.round(
                cast(
                    coalesce(func.sum(coalesce(union_query.c.payment_today, 0)), 0),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("payments"),
            func.round(
                cast(
                    coalesce(
                        func.sum(coalesce(union_query.c.total_revenue_today, 0)), 0
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("total_revenue"),
            coalesce(func.sum(coalesce(union_query.c.blocked_rooms_today, 0)), 0).label(
                "blocks"
            ),
            coalesce(func.sum(coalesce(union_query.c.comps_today, 0)), 0).label(
                "comps"
            ),
            coalesce(func.sum(coalesce(union_query.c.guests_today, 0)), 0).label(
                "number_of_guests"
            ),
            func.round(
                cast(
                    coalesce(func.sum(coalesce(union_query.c.los_avg, 0)), 0), Numeric
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("avg_los_nights_stayed"),
            func.round(
                cast(
                    coalesce(func.sum(coalesce(union_query.c.los_total, 0)), 0), Numeric
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("avg_los_total_room_nights"),
            func.round(
                cast(
                    coalesce(
                        case(
                            (
                                func.sum(coalesce(union_query.c.guests_today, 0)) > 0,
                                func.sum(coalesce(union_query.c.room_revenue_today, 0))
                                / func.sum(coalesce(union_query.c.guests_today, 0)),
                            ),
                            else_=0,
                        ),
                        0,
                    ),
                    Numeric,
                ),
                DEFAULT_ROUNDING_PRECISION,
            ).label("adr_per_guest"),
        )

    def __get_filters(self, model: Type[DeclarativeMeta], start_date: date):
        filters = [
            model.property_id == self.property_id,
            model.organization_id == self.organization_id,
        ]
        if model == DFRReservations:
            filters += [
                model.checkin_date <= self.report_date,
                model.checkout_date >= start_date,
                model.reservation_status.in_(
                    (
                        (
                            "Confirmed",
                            "Not Confirmed",
                            "Checked In",
                            "Checked Out",
                            "In-House",
                        )
                    )
                ),
                model.is_booking_deleted.is_(False),
            ]
        elif model == self.occupancy_model:
            filters += [model.stay_date.between(start_date, self.report_date)]
        else:
            filters += [model.report_date.between(start_date, self.report_date)]

        return filters

    def __get_group_by(self, model):
        groups = [model.property_id, model.organization_id]
        if model not in (DFRReservations, self.occupancy_model):
            groups += [model.report_date]
        if model == self.occupancy_model:
            groups += [model.stay_date]
        return groups

    def __get_transactions_query(self, month_to_date: bool = False) -> Query:
        return (
            DFRTransactions.query.with_entities(
                cast(0, BIGINT).label("checkout_today"),
                cast(0, BIGINT).label("checkin_today"),
                cast(0, BIGINT).label("walkin_today"),
                cast(0, BIGINT).label("noshow_today"),
                func.sum(coalesce(DFRTransactions.adjustment, 0)).label(
                    "adjustment_today"
                ),
                func.sum(coalesce(DFRTransactions.item_service, 0)).label(
                    "item_service_today"
                ),
                func.sum(coalesce(DFRTransactions.payment, 0)).label("payment_today"),
                func.sum(coalesce(DFRTransactions.total_revenue, 0)).label(
                    "total_revenue_today"
                ),
                cast(0, BIGINT).label("room_revenue_today"),
                cast(0, BIGINT).label("blocked_rooms_today"),
                cast(0, BIGINT).label("comps_today"),
                cast(0, BIGINT).label("guests_today"),
                cast(0, BIGINT).label("los_avg"),
                cast(0, BIGINT).label("los_total"),
                DFRTransactions.property_id.label("property_id"),
                DFRTransactions.organization_id.label("organization_id"),
                DFRTransactions.report_date.label("report_date"),
            )
            .where(
                *self.__get_filters(
                    DFRTransactions,
                    (
                        self.report_date.replace(day=1)
                        if month_to_date
                        else self.report_date
                    ),
                )
            )
            .group_by(*self.__get_group_by(DFRTransactions))
        )

    def __get_room_revenue_query(self, month_to_date: bool = False) -> Query:
        return (
            self.occupancy_model.query.with_entities(
                cast(0, BIGINT).label("checkout_today"),
                cast(0, BIGINT).label("checkin_today"),
                cast(0, BIGINT).label("walkin_today"),
                cast(0, BIGINT).label("noshow_today"),
                cast(0, BIGINT).label("adjustment_today"),
                cast(0, BIGINT).label("item_service_today"),
                cast(0, BIGINT).label("payment_today"),
                cast(0, BIGINT).label("total_revenue_today"),
                func.sum(coalesce(self.occupancy_model.room_revenue, 0)).label(
                    "room_revenue_today"
                ),
                cast(0, BIGINT).label("blocked_rooms_today"),
                cast(0, BIGINT).label("comps_today"),
                cast(0, BIGINT).label("guests_today"),
                cast(0, BIGINT).label("los_avg"),
                cast(0, BIGINT).label("los_total"),
                self.occupancy_model.property_id.label("property_id"),
                self.occupancy_model.organization_id.label("organization_id"),
                self.occupancy_model.stay_date.label("report_date"),
            )
            .where(
                *self.__get_filters(
                    self.occupancy_model,
                    (
                        self.report_date.replace(day=1)
                        if month_to_date
                        else self.report_date
                    ),
                )
            )
            .group_by(*self.__get_group_by(self.occupancy_model))
        )

    def __get_checkout_query(self, month_to_date: bool = False) -> Query:
        return (
            DFRCheckout.query.with_entities(
                func.sum(coalesce(DFRCheckout.checkout, 0)).label("checkout_today"),
                cast(0, BIGINT).label("checkin_today"),
                cast(0, BIGINT).label("walkin_today"),
                cast(0, BIGINT).label("noshow_today"),
                cast(0, BIGINT).label("adjustment_today"),
                cast(0, BIGINT).label("item_service_today"),
                cast(0, BIGINT).label("payment_today"),
                cast(0, BIGINT).label("total_revenue_today"),
                cast(0, BIGINT).label("room_revenue_today"),
                cast(0, BIGINT).label("blocked_rooms_today"),
                cast(0, BIGINT).label("comps_today"),
                cast(0, BIGINT).label("guests_today"),
                cast(0, BIGINT).label("los_avg"),
                cast(0, BIGINT).label("los_total"),
                DFRCheckout.property_id.label("property_id"),
                DFRCheckout.organization_id.label("organization_id"),
                DFRCheckout.report_date.label("report_date"),
            )
            .where(
                *self.__get_filters(
                    DFRCheckout,
                    (
                        self.report_date.replace(day=1)
                        if month_to_date
                        else self.report_date
                    ),
                )
            )
            .group_by(*self.__get_group_by(DFRCheckout))
        )

    def __get_checkin_query(self, month_to_date: bool = False) -> Query:
        return (
            DFRCheckin.query.with_entities(
                cast(0, BIGINT).label("checkout_today"),
                func.sum(coalesce(DFRCheckin.checkin, 0)).label("checkin_today"),
                func.sum(coalesce(DFRCheckin.walkin, 0)).label("walkin_today"),
                func.sum(coalesce(DFRCheckin.noshow, 0)).label("noshow_today"),
                cast(0, BIGINT).label("adjustment_today"),
                cast(0, BIGINT).label("item_service_today"),
                cast(0, BIGINT).label("payment_today"),
                cast(0, BIGINT).label("total_revenue_today"),
                cast(0, BIGINT).label("room_revenue_today"),
                cast(0, BIGINT).label("blocked_rooms_today"),
                cast(0, BIGINT).label("comps_today"),
                cast(0, BIGINT).label("guests_today"),
                cast(0, BIGINT).label("los_avg"),
                cast(0, BIGINT).label("los_total"),
                DFRCheckin.property_id.label("property_id"),
                DFRCheckin.organization_id.label("organization_id"),
                DFRCheckin.report_date.label("report_date"),
            )
            .where(
                *self.__get_filters(
                    DFRCheckin,
                    (
                        self.report_date.replace(day=1)
                        if month_to_date
                        else self.report_date
                    ),
                )
            )
            .group_by(*self.__get_group_by(DFRCheckin))
        )

    def __get_capacity_query(self, month_to_date: bool = False) -> Query:
        return (
            DFRCapacity.query.with_entities(
                cast(0, BIGINT).label("checkout_today"),
                cast(0, BIGINT).label("checkin_today"),
                cast(0, BIGINT).label("walkin_today"),
                cast(0, BIGINT).label("noshow_today"),
                cast(0, BIGINT).label("adjustment_today"),
                cast(0, BIGINT).label("item_service_today"),
                cast(0, BIGINT).label("payment_today"),
                cast(0, BIGINT).label("total_revenue_today"),
                cast(0, BIGINT).label("room_revenue_today"),
                func.sum(coalesce(DFRCapacity.blocked_rooms, 0)).label(
                    "blocked_rooms_today"
                ),
                cast(0, BIGINT).label("comps_today"),
                cast(0, BIGINT).label("guests_today"),
                cast(0, BIGINT).label("los_avg"),
                cast(0, BIGINT).label("los_total"),
                DFRCapacity.property_id.label("property_id"),
                DFRCapacity.organization_id.label("organization_id"),
                DFRCapacity.report_date.label("report_date"),
            )
            .where(
                *self.__get_filters(
                    DFRCapacity,
                    (
                        self.report_date.replace(day=1)
                        if month_to_date
                        else self.report_date
                    ),
                )
            )
            .group_by(*self.__get_group_by(DFRCapacity))
        )

    def __get_room_reservation_query(self, month_to_date: bool = False) -> Query:
        return (
            DFRRoomReservation.query.with_entities(
                cast(0, BIGINT).label("checkout_today"),
                cast(0, BIGINT).label("checkin_today"),
                cast(0, BIGINT).label("walkin_today"),
                cast(0, BIGINT).label("noshow_today"),
                cast(0, BIGINT).label("adjustment_today"),
                cast(0, BIGINT).label("item_service_today"),
                cast(0, BIGINT).label("payment_today"),
                cast(0, BIGINT).label("total_revenue_today"),
                cast(0, BIGINT).label("room_revenue_today"),
                cast(0, BIGINT).label("blocked_rooms_today"),
                func.sum(coalesce(DFRRoomReservation.comp, 0)).label("comps_today"),
                func.sum(coalesce(DFRRoomReservation.guest, 0)).label("guests_today"),
                cast(0, BIGINT).label("los_avg"),
                cast(0, BIGINT).label("los_total"),
                DFRRoomReservation.property_id.label("property_id"),
                DFRRoomReservation.organization_id.label("organization_id"),
                DFRRoomReservation.report_date.label("report_date"),
            )
            .where(
                *self.__get_filters(
                    DFRRoomReservation,
                    (
                        self.report_date.replace(day=1)
                        if month_to_date
                        else self.report_date
                    ),
                )
            )
            .group_by(*self.__get_group_by(DFRRoomReservation))
        )

    def __get_los(self, month_to_date: bool = False) -> Query:
        start_date = (
            self.report_date.replace(day=1) if month_to_date else self.report_date
        )
        report_date_literal = cast(literal(self.report_date), DATE)
        start_date_literal = cast(literal(start_date), DATE)

        return (
            DFRReservations.query.with_entities(
                cast(0, BIGINT).label("checkout_today"),
                cast(0, BIGINT).label("checkin_today"),
                cast(0, BIGINT).label("walkin_today"),
                cast(0, BIGINT).label("noshow_today"),
                cast(0, BIGINT).label("adjustment_today"),
                cast(0, BIGINT).label("item_service_today"),
                cast(0, BIGINT).label("payment_today"),
                cast(0, BIGINT).label("total_revenue_today"),
                cast(0, BIGINT).label("room_revenue_today"),
                cast(0, BIGINT).label("blocked_rooms_today"),
                cast(0, BIGINT).label("comps_today"),
                cast(0, BIGINT).label("guests_today"),
                (
                    func.coalesce(
                        func.sum(
                            case(
                                (
                                    DFRReservations.checkin_date >= start_date_literal,
                                    case(
                                        (
                                            DFRReservations.checkout_date
                                            <= report_date_literal,
                                            DFRReservations.checkout_date
                                            - DFRReservations.checkin_date,
                                        ),
                                        else_=report_date_literal
                                        - DFRReservations.checkin_date
                                        + 1,
                                    ),
                                ),
                                else_=case(
                                    (
                                        DFRReservations.checkout_date
                                        <= report_date_literal,
                                        DFRReservations.checkout_date
                                        - start_date_literal,
                                    ),
                                    else_=report_date_literal - start_date_literal + 1,
                                ),
                            )
                        )
                        / func.nullif(func.cast(func.count("*"), Numeric), 0),
                        0,
                    ).label("los_avg")
                    if month_to_date
                    else func.coalesce(
                        func.sum(
                            case(
                                (
                                    DFRReservations.checkin_date <= report_date_literal,
                                    case(
                                        (
                                            DFRReservations.checkin_date
                                            < start_date_literal,
                                            report_date_literal
                                            - start_date_literal
                                            + 1,
                                        ),
                                        else_=report_date_literal
                                        - DFRReservations.checkin_date
                                        + 1,
                                    ),
                                ),
                                else_=0,
                            )
                        )
                        / func.nullif(
                            func.cast(
                                func.sum(
                                    case(
                                        (
                                            DFRReservations.checkin_date
                                            <= report_date_literal,
                                            1,
                                        ),
                                        else_=0,
                                    )
                                ),
                                Numeric,
                            ),
                            0,
                        ),
                        0,
                    ).label("los_avg")
                ),
                (
                    func.coalesce(
                        func.sum(
                            DFRReservations.checkout_date - DFRReservations.checkin_date
                        )
                        / func.nullif(func.cast(func.count("*"), Numeric), 0),
                        0,
                    ).label("los_total")
                    if month_to_date
                    else func.coalesce(
                        func.sum(
                            case(
                                (
                                    DFRReservations.checkin_date <= report_date_literal,
                                    DFRReservations.checkout_date
                                    - DFRReservations.checkin_date,
                                ),
                                else_=0,
                            )
                        )
                        / func.nullif(
                            func.cast(
                                func.sum(
                                    case(
                                        (
                                            DFRReservations.checkin_date
                                            <= report_date_literal,
                                            1,
                                        ),
                                        else_=0,
                                    )
                                ),
                                Numeric,
                            ),
                            0,
                        ),
                        0,
                    ).label("los_total")
                ),
                DFRReservations.property_id.label("property_id"),
                DFRReservations.organization_id.label("organization_id"),
                literal(self.report_date.strftime("%Y-%m-%d")).label("report_date"),
            )
            .where(
                *self.__get_filters(
                    DFRReservations,
                    (
                        self.report_date.replace(day=1)
                        if month_to_date
                        else self.report_date
                    ),
                )
            )
            .group_by(*self.__get_group_by(DFRReservations))
        )
