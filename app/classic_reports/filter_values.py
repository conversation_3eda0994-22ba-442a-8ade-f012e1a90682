from sqlalchemy import distinct, select

from app.datasets.financial import FinancialFFView as FinancialView


class FilterValues:
    def __init__(
        self,
        organization_id: int,
        property_id: int,
    ):
        self.organization_id = organization_id
        self.property_id = property_id

    def get_filter_values_by_model(self, model, filter_name: str):
        return select(distinct(getattr(FinancialView, filter_name))).where(
            getattr(model, filter_name).isnot(None),
            FinancialView.organization_id == self.organization_id,
            FinancialView.property_id == self.property_id,
        )
