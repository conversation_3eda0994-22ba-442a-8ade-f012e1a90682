from app.enums.dataset import Dataset as DatasetEnum


CLASSIC_REPORT_DATASETS = {
    "Classic Reports.AccountBalances": DatasetEnum.Reservations.value,
    "Classic Reports.AccountBalancesData": DatasetEnum.Reservations.value,
    "Classic Reports.AccountBalancesPicklist": DatasetEnum.Reservations.value,
    "Classic Reports.AccountBalancesSummary": DatasetEnum.Reservations.value,
    "Classic Reports.AdjustmentsReport": DatasetEnum.Financial.value,
    "Classic Reports.AdjustmentsReportData": DatasetEnum.Financial.value,
    "Classic Reports.AdjustmentsReportPicklist": DatasetEnum.Financial.value,
    "Classic Reports.AdjustmentsReportSummary": DatasetEnum.Financial.value,
    "Classic Reports.ArrivalsReport": DatasetEnum.Reservations.value,
    "Classic Reports.ArrivalsReportData": DatasetEnum.Reservations.value,
    "Classic Reports.ArrivalsReportPicklist": DatasetEnum.Reservations.value,
    "Classic Reports.ArrivalsReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.CancellationReport": DatasetEnum.Reservations.value,
    "Classic Reports.CancellationReportData": DatasetEnum.Reservations.value,
    "Classic Reports.CancellationReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.CommissionReport": DatasetEnum.Reservations.value,
    "Classic Reports.CommissionReportData": DatasetEnum.Reservations.value,
    "Classic Reports.CommissionReportPicklist": DatasetEnum.Reservations.value,
    "Classic Reports.CommissionReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.DeparturesReport": DatasetEnum.Reservations.value,
    "Classic Reports.DeparturesReportData": DatasetEnum.Reservations.value,
    "Classic Reports.DeparturesReportPicklist": DatasetEnum.Reservations.value,
    "Classic Reports.DeparturesReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.InHouseReport": DatasetEnum.Reservations.value,
    "Classic Reports.InHouseReportData": DatasetEnum.Reservations.value,
    "Classic Reports.InHouseReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.NoShowReport": DatasetEnum.Reservations.value,
    "Classic Reports.NoShowReportData": DatasetEnum.Reservations.value,
    "Classic Reports.NoShowReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.NotesReport": DatasetEnum.Reservations.value,
    "Classic Reports.NotesReportData": DatasetEnum.Reservations.value,
    "Classic Reports.NotesReportPicklist": DatasetEnum.Reservations.value,
    "Classic Reports.NotesReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.ChannelProductionReport": DatasetEnum.OccupancyV1.value,
    "Classic Reports.ChannelProductionReportData": DatasetEnum.OccupancyV1.value,
    "Classic Reports.ChannelProductionReportPicklist": DatasetEnum.OccupancyV1.value,
    "Classic Reports.ChannelProductionReportSummary": DatasetEnum.OccupancyV1.value,
    "Classic Reports.PaymentLedger": DatasetEnum.Financial.value,
    "Classic Reports.PaymentLedgerData": DatasetEnum.Financial.value,
    "Classic Reports.PaymentLedgerPicklist": DatasetEnum.Financial.value,
    "Classic Reports.PaymentLedgerSummary": DatasetEnum.Financial.value,
    "Classic Reports.PaymentReconciliationReport": DatasetEnum.Financial.value,
    "Classic Reports.PaymentReconciliationReportData": DatasetEnum.Financial.value,
    "Classic Reports.PaymentReconciliationReportPicklist": DatasetEnum.Financial.value,
    "Classic Reports.PaymentReconciliationReportSummary": DatasetEnum.Financial.value,
    "Classic Reports.ReservationsByCountryReport": DatasetEnum.Reservations.value,
    "Classic Reports.ReservationsByCountryReportData": DatasetEnum.Reservations.value,
    "Classic Reports.ReservationsByCountryReportPicklist": DatasetEnum.Reservations.value,
    "Classic Reports.ReservationsByCountryReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.ReservationsByRatePlanReport": DatasetEnum.Reservations.value,
    "Classic Reports.ReservationsByRatePlanReportData": DatasetEnum.Reservations.value,
    "Classic Reports.ReservationsByRatePlanReportPicklist": DatasetEnum.Reservations.value,
    "Classic Reports.ReservationsByRatePlanReportSummary": DatasetEnum.Reservations.value,
    "Classic Reports.UserReconciliationReport": DatasetEnum.Financial.value,
    "Classic Reports.UserReconciliationReportData": DatasetEnum.Financial.value,
    "Classic Reports.UserReconciliationReportPicklist": DatasetEnum.Financial.value,
    "Classic Reports.UserReconciliationReportSummary": DatasetEnum.Financial.value,
    "Classic Reports.DailyRevenueReport": DatasetEnum.Financial.value,
    "Classic Reports.DailyRevenueReportData": DatasetEnum.Financial.value,
    "Classic Reports.DailyRevenueReportPicklist": DatasetEnum.Financial.value,
    "Classic Reports.DailyRevenueReportSummary": DatasetEnum.Financial.value,
    "Classic Reports.TaxReport": DatasetEnum.Financial.value,
    "Classic Reports.TaxReportData": DatasetEnum.Financial.value,
    "Classic Reports.TaxReportPicklist": DatasetEnum.Financial.value,
    "Classic Reports.TaxReportSummary": DatasetEnum.Financial.value,
    "Classic Reports.TransactionsReport": DatasetEnum.Financial.value,
    "Classic Reports.TransactionsReportData": DatasetEnum.Financial.value,
    "Classic Reports.TransactionsReportPicklist": DatasetEnum.Financial.value,
    "Classic Reports.TransactionsReportSummary": DatasetEnum.Financial.value,
}
