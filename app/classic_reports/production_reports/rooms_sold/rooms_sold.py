from sqlalchemy import (
    Select,
    or_,
    select,
)


from app.classic_reports.production_reports.compare_occupancy import (
    CompareOccupancyReport,
)


class RoomsSold(CompareOccupancyReport):
    def get_rooms_sold_report(self) -> Select:
        """Get the Rooms Sold/Occupancy report for the given property and period."""
        select_columns = [
            self.stay_date.label("date"),
            self._occupancy(self.report_year_condition).label("occupancy"),
            self._rooms_sold(self.report_year_condition).label("accommodations_booked"),
            self._room_revenue(self.report_year_condition).label("revenue"),
        ]

        if self.compare_years:
            select_columns.extend(
                [
                    self._occupancy(or_(*self.compare_years_conditions)).label(
                        "compare_occupancy"
                    ),
                    self._rooms_sold(or_(*self.compare_years_conditions)).label(
                        "compare_accommodations_booked"
                    ),
                    self._room_revenue(or_(*self.compare_years_conditions)).label(
                        "compare_revenue"
                    ),
                    self._percent_change(
                        self._occupancy(self.report_year_condition),
                        self._occupancy(or_(*self.compare_years_conditions)),
                    ).label("percentage_change"),
                ]
            )

        return (
            select(
                *select_columns,
            )
            .select_from(self.date_range_cte)
            .outerjoin(
                *self.date_cte_join,
            )
            .outerjoin(
                *self.multi_level_join,
            )
            .where(*self.where_clauses)
            .group_by(
                self.stay_date,
            )
            .order_by(
                self.stay_date,
            )
        )
