from datetime import date
from typing import Optional

from sqlalchemy import (
    BinaryExpression,
    Date,
    and_,
    case,
    func,
    literal,
    literal_column,
    or_,
    select,
    union_all,
)

from app.datasets.dataset import Dataset
from app.enums.dataset import Dataset as DatasetEnum
from app.multi_levels.occupancy_reservation import OccupancyReservationsView


class CompareOccupancyReport:
    def __init__(
        self,
        organization_id: str,
        property_id: str,
        report_year: int,
        period_start: date,
        period_end: date,
        grouping: str,
        compare_years: Optional[list[int]] = None,
        room_types: Optional[list[str]] = None,
        reservation_sources: Optional[list[str]] = None,
        reservation_source_categories: Optional[list[str]] = None,
    ):
        self.organization_id = organization_id
        self.property_id = property_id
        self.report_year = report_year
        self.period_start = period_start
        self.period_end = period_end
        self.grouping = grouping
        self.compare_years = compare_years or []
        self.room_types = room_types
        self.reservation_sources = reservation_sources
        self.reservation_source_categories = reservation_source_categories
        self.grouping_format = grouping
        self.occupancy_model = Dataset(DatasetEnum.OccupancyV1).model

        report_years = (
            [self.report_year] + self.compare_years
            if self.compare_years
            else [self.report_year]
        )
        self.date_range_cte = report_years

    @property
    def date_range_cte(self):
        return self.__date_range_cte

    @date_range_cte.setter
    def date_range_cte(self, report_years):
        self.__date_range_cte = union_all(
            *[
                select(
                    func.cast(
                        func.generate_series(
                            literal_column(f"'{year}-{self.period_start}'::date"),
                            literal_column(f"'{year}-{self.period_end}'::date"),
                            "1 day",
                        ),
                        Date,
                    ).label("stay_date"),
                )
                for year in report_years
            ]
        ).cte("date_range")

    @property
    def grouping_format(self) -> str:
        return self.__grouping_format

    @grouping_format.setter
    def grouping_format(self, grouping: str):
        match grouping:
            case "week":
                self.__grouping_format = "ww"
            case "month":
                self.__grouping_format = "MM"
            case "day" | _:
                self.__grouping_format = "MM-DD"

    @property
    def room_type_condition(self):
        return (
            self.occupancy_model.room_type.in_(self.room_types)
            if self.room_types
            else literal(True)
        )

    @property
    def reservation_sources_condition(self) -> BinaryExpression:
        return (
            or_(
                OccupancyReservationsView.reservation_source.in_(
                    self.reservation_sources
                ),
                self.occupancy_model.booking_id.is_(None),
            )
            if self.reservation_sources
            else literal(True)
        )

    @property
    def reservation_source_categories_condition(self):
        return (
            or_(
                OccupancyReservationsView.reservation_source.in_(
                    self.reservation_source_categories
                ),
                self.occupancy_model.booking_id.is_(None),
            )
            if self.reservation_source_categories
            else literal(True)
        )

    @property
    def report_year_condition(self) -> BinaryExpression:
        return self.occupancy_model.stay_date.between(
            f"{self.report_year}-{self.period_start}",
            f"{self.report_year}-{self.period_end}",
        )

    @property
    def compare_years_conditions(self) -> list[BinaryExpression]:
        return [
            self.occupancy_model.stay_date.between(
                f"{year}-{self.period_start}",
                f"{year}-{self.period_end}",
            )
            for year in self.compare_years
        ]

    @property
    def where_clauses(self):
        date_filters = or_(
            self.report_year_condition,
            *self.compare_years_conditions,
        )
        where_clauses = [
            self.occupancy_model.organization_id == self.organization_id,
            self.occupancy_model.property_id == self.property_id,
            date_filters,
        ]
        if self.room_types:
            where_clauses.append(self.room_type_condition)
        if self.reservation_sources:
            where_clauses.append(self.reservation_sources_condition)
        if self.reservation_source_categories:
            where_clauses.append(self.reservation_source_categories_condition)
        return where_clauses

    @property
    def multi_level_join(self):
        return (
            OccupancyReservationsView,
            and_(
                OccupancyReservationsView.organization_id
                == self.occupancy_model.organization_id,
                OccupancyReservationsView.property_id
                == self.occupancy_model.property_id,
                OccupancyReservationsView.room_type_id
                == self.occupancy_model.room_type_id,
                OccupancyReservationsView.booking_id == self.occupancy_model.booking_id,
                OccupancyReservationsView.booking_room_id
                == self.occupancy_model.booking_room_id,
                OccupancyReservationsView.stay_date == self.occupancy_model.stay_date,
            ),
        )

    @property
    def date_cte_join(self):
        return (
            self.occupancy_model,
            and_(
                self.occupancy_model.organization_id == self.organization_id,
                self.occupancy_model.property_id == self.property_id,
                self.occupancy_model.stay_date == self.date_range_cte.c.stay_date,
            ),
        )

    @property
    def stay_date(self):
        return func.to_char(self.occupancy_model.stay_date, self.grouping_format).label(
            "stay_date"
        )

    def _revpar(self, condition):
        """Get the RevPar with a case statement built from the condition"""
        return func.coalesce(
            func.sum(
                case(
                    (
                        condition,
                        self.occupancy_model.room_revenue,
                    ),
                    else_=None,
                )
            )
            / func.nullif(
                func.sum(
                    case(
                        (
                            condition,
                            self.occupancy_model.capacity_count,
                        ),
                        else_=None,
                    )
                ),
                0,
            ),
            0,
        )

    def _occupancy(self, condition):
        return (
            func.coalesce(
                func.sum(
                    case(
                        (
                            condition,
                            self.occupancy_model.rooms_sold,
                        ),
                        else_=None,
                    )
                )
                / func.nullif(
                    func.sum(
                        case(
                            (
                                condition,
                                self.occupancy_model.capacity_count,
                            ),
                            else_=None,
                        )
                    ),
                    0,
                ),
                0,
            )
            * 100
        )

    def _rooms_sold(self, condition):
        """Get the accommodations booked for the given model."""
        return func.coalesce(
            func.sum(
                case(
                    (
                        condition,
                        self.occupancy_model.rooms_sold,
                    ),
                    else_=0,
                )
            ),
            0,
        )

    def _room_revenue(self, condition):
        """Get the revenue for the given model."""
        return func.coalesce(
            func.sum(
                case(
                    (
                        condition,
                        self.occupancy_model.room_revenue,
                    ),
                    else_=0,
                )
            ),
            0,
        )

    def _adr(self, condition):
        """Get the ADR for the given model."""
        return func.coalesce(
            func.sum(
                case(
                    (
                        condition,
                        self.occupancy_model.room_revenue,
                    ),
                    else_=0,
                )
            )
            / func.nullif(
                func.sum(
                    case(
                        (
                            condition,
                            self.occupancy_model.rooms_sold,
                        ),
                        else_=0,
                    )
                ),
                0,
            ),
            0,
        )

    def _percent_change(self, primary_year_column, comparison_year_column):
        """Get the percent change between the primary and comparison year values."""
        return (
            (primary_year_column - comparison_year_column)
            / func.nullif(comparison_year_column, 0)
            * 100
        )
