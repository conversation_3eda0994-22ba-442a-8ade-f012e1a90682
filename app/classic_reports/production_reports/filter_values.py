from sqlalchemy import distinct, select

from app.datasets.occupancy_v1 import OccupancyV1View
from app.multi_levels.occupancy_reservation import OccupancyReservationsView


class FilterValues:
    def __init__(
        self,
        organization_id: int,
        property_id: int,
    ):
        self.organization_id = organization_id
        self.property_id = property_id

    def get_room_type_filter_values(self):
        return select(distinct(OccupancyV1View.room_type)).where(
            OccupancyV1View.organization_id == self.organization_id,
            OccupancyV1View.property_id == self.property_id,
        )

    def get_reservation_source_filter_values(self):
        return select(distinct(OccupancyReservationsView.reservation_source),).where(
            OccupancyReservationsView.organization_id == self.organization_id,
            OccupancyReservationsView.property_id == self.property_id,
        )

    def get_reservation_source_category_filter_values(self):
        return select(
            distinct(OccupancyReservationsView.reservation_source_category),
        ).where(
            OccupancyReservationsView.organization_id == self.organization_id,
            OccupancyReservationsView.property_id == self.property_id,
        )
