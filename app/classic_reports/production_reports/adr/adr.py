from sqlalchemy import (
    Select,
    or_,
    select,
)


from app.classic_reports.production_reports.compare_occupancy import (
    CompareOccupancyReport,
)


class Adr(CompareOccupancyReport):
    def get_adr_report(self) -> Select:
        """Get the ADR report for the given property and period."""
        select_columns = [
            self.stay_date.label("date"),
            self._adr(self.report_year_condition).label("adr"),
            self._rooms_sold(self.report_year_condition).label("accommodations_booked"),
            self._room_revenue(self.report_year_condition).label("revenue"),
        ]

        if self.compare_years:
            select_columns.extend(
                [
                    self._adr(or_(*self.compare_years_conditions)).label("compare_adr"),
                    self._rooms_sold(or_(*self.compare_years_conditions)).label(
                        "compare_accommodations_booked"
                    ),
                    self._room_revenue(or_(*self.compare_years_conditions)).label(
                        "compare_revenue"
                    ),
                    self._percent_change(
                        self._adr(self.report_year_condition),
                        self._adr(or_(*self.compare_years_conditions)),
                    ).label("percentage_change"),
                ]
            )

        return (
            select(
                *select_columns,
            )
            .select_from(self.date_range_cte)
            .outerjoin(
                *self.date_cte_join,
            )
            .outerjoin(
                *self.multi_level_join,
            )
            .where(*self.where_clauses)
            .group_by(
                self.stay_date,
            )
            .order_by(
                self.stay_date,
            )
        )
