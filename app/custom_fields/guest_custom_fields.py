from sqlalchemy import BIGINT, Column, VARCHAR

from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class GuestCustomFieldsViewMixin(object):
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property.",
            kind=CdfKind.Identifier,
        ),
    )
    customer_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Customer ID",
            description="The unique identifier for the guest.",
            kind=CdfKind.String,
        ),
        primary_key=True,
    )
    for i in range(1, 61):
        exec(
            f"""
custom_string_{i} = Column(
    VARCHAR,
    info=dict(
        category=CdfCategory.Property,
        name="Custom String {i}",
        description="The value for custom string {i}.",
        kind=CdfKind.String,
    ),
)
        """
        )


class GuestCustomFieldsView(GuestCustomFieldsViewMixin, db.Model):
    __tablename__ = "guest_custom_fields_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"

    name = "GuestCustomFieldsView"


class GuestCustomFieldsFFView(GuestCustomFieldsViewMixin, db.Model):
    __tablename__ = "guest_custom_fields_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"

    name = "GuestCustomFieldsFFView"
