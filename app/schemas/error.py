from marshmallow import Schema, fields


class ErrorContentSchema(Schema):
    class Meta:
        ordered = True

    code = fields.Integer(metadata={"description": "Error code"})
    status = fields.String(metadata={"description": "Error status"})
    description = fields.String(metadata={"description": "Error description"})
    message = fields.Dict(metadata={"description": "Error message"})


class ErrorSchema(Schema):
    """Schema describing the error payload
    Not actually used to dump payload, but only for documentation purposes
    """

    error = fields.Nested(ErrorContentSchema)
