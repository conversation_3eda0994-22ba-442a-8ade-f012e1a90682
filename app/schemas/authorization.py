from marshmallow import Schema, fields, post_load
from marshmallow.decorators import validates

from app.common.constants.languages import DEFAULT_LANGUAGE, LANGUAGES
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.openapi import X_PROPERTY_ID
from app.services.property_service import PropertyService


def X_PROPERTY_ID_FIELD(required: bool = True) -> fields.Integer:
    DESCRIPTION = "Property id of the client"
    return fields.Integer(
        as_string=True,
        required=required,
        load_only=True,
        metadata={"description": DESCRIPTION},
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
    )


class QueryPropertyIdSchema(Schema):
    property_id = fields.Integer(
        as_string=True,
        required=True,
        validate=lambda x: int(x) > 0
        or InvalidUsage.bad_request("Value should be greater than 0."),
        metadata={"description": "Property id of the client"},
    )

    @validates("property_id")
    def validate_property_id(self, property_id):
        PropertyService.are_valid_property_ids([property_id])


class HeaderAcceptLanguageSchema(Schema):
    class Meta:
        include = {
            "ACCEPT-LANGUAGE": fields.String(
                metadata={
                    "description": f"Accept language where valid are {','.join(LANGUAGES)}, if not defaults to en"
                },
            )
        }

    @post_load
    def accept_language(self, data, **kwargs):
        if "ACCEPT-LANGUAGE" in data and data["ACCEPT-LANGUAGE"] in LANGUAGES:
            return data

        logger.warning("Invalid accept language", extra=data)
        data["ACCEPT-LANGUAGE"] = DEFAULT_LANGUAGE
        return data


class HeaderPropertyIdSchema(Schema):
    class Meta:
        include = {X_PROPERTY_ID: X_PROPERTY_ID_FIELD()}

    @post_load
    def post_load_data(self, data, **kwargs):
        property_id = data.pop(X_PROPERTY_ID, None)

        if property_id:
            data["property_id"] = int(property_id)

        return data

    @validates(X_PROPERTY_ID)
    def validate_property_id(self, property_id):
        PropertyService.are_valid_property_ids([property_id])


class HeaderPropertyIdApiKeySchema(HeaderPropertyIdSchema):
    class Meta:
        include = {X_PROPERTY_ID: X_PROPERTY_ID_FIELD(False)}


class HeaderPropertyIdSchemaWithoutValidation(HeaderPropertyIdSchema):
    """Schema use only on authorization/token"""

    class Meta:
        include = {X_PROPERTY_ID: X_PROPERTY_ID_FIELD(True)}

    @validates(X_PROPERTY_ID)
    def validate_property_id(self, property_id):
        return None
