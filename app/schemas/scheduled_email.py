from flask import render_template

from marshmallow import Schema, fields, post_load


class EmailChartSchema(Schema):
    report_title = fields.Str(
        required=True,
        metadata=dict(
            description="Name of the report",
            example="Report Name",
        ),
    )
    chart = fields.Raw(
        required=True,
        metadata=dict(description="Chart PNG image"),
    )


class ReportLinksSchema(Schema):
    report_title = fields.Str(
        required=True,
        metadata=dict(
            description="Name of the report",
            example="Report Name",
        ),
    )
    report_url = fields.Str(
        required=True,
        metadata=dict(
            description="My Front Desk URL link of the report",
            example="https://www.example.com/",
        ),
    )


class ScheduledEmailSchema(Schema):
    class Meta:
        ordered = True

    lang = fields.Str(
        required=False,
        dump_default="en",
        metadata=dict(
            description="Language of the email",
            example="en",
        ),
    )
    title = fields.Str(
        required=False,
        dump_default="",
        metadata=dict(
            description="Title of the report",
            example="Report Title",
        ),
    )
    description = fields.Str(
        required=False,
        dump_default="",
        metadata=dict(
            description="Description of the report being sent via email",
            example="This is a description of a fantastic report",
        ),
    )
    download_link = fields.Str(
        required=False,
        dump_default="",
        metadata=dict(
            description="Download link of the report",
            example="https://www.example.com/report.xlsx",
        ),
    )
    created_at = fields.Str(
        required=False,
        dump_default="",
        metadata=dict(
            description="Date the report was created",
            example="2021-01-01 00:00:00",
        ),
    )
    charts = fields.Nested(
        EmailChartSchema,
        many=True,
        required=False,
        dump_default=[],
        metadata=dict(
            description="List of PNG images of charts",
        ),
    )
    recipients = fields.List(
        fields.Str(),
        required=False,
        dump_default=[],
        metadata=dict(
            description="List of emails that will receive the report",
            example="<EMAIL>",
        ),
    )
    report_links = fields.Nested(
        ReportLinksSchema,
        many=True,
        required=False,
        dump_default=[],
        metadata=dict(
            description="List of report links",
        ),
    )
    schedule_id = fields.Int(
        required=False,
        allow_none=True,
        dump_default=None,
        metadata=dict(
            description="ID of the scheduled email",
            example=1,
        ),
    )
    schedule_user_email = fields.Str(
        required=False,
        allow_none=True,
        dump_default=None,
        metadata=dict(
            description="Email of the user that scheduled the email",
            example="<EMAIL>",
        ),
    )
    schedule_url = fields.Str(
        required=False,
        allow_none=True,
        dump_default=None,
        metadata=dict(
            description="URL of the subscription page",
            example="https://www.example.com/",
        ),
    )
    property_name = fields.Str(
        required=False,
        allow_none=True,
        dump_default=None,
        metadata=dict(
            description="Name of the property",
            example="Example Property",
        ),
    )
    expiration_days = fields.Int(
        required=False,
        dump_default=7,
        metadata=dict(
            description="Number of days the email will be valid",
            example=7,
        ),
    )

    html = fields.Str(
        dump_only=True,
        metadata=dict(description="HTML of the email"),
    )

    @post_load
    def build_html(self, data, **kwargs):
        data["html"] = render_template("scheduled_email.html", **data)
        return data
