from marshmallow import Schema, fields, validate

from app.enums.sse import SSE<PERSON>ind
from app.enums.task import TaskNames, TaskStatus


class SSEPublishSchema(Schema):
    class Meta:
        ordered = True

    kind = fields.String(validate=validate.OneOf(SSEKind.values()))
    name = fields.String(validate=validate.OneOf(TaskNames.values()))
    task_id = fields.String(
        validate=validate.Regexp(
            regex=r"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$",
            error="Not a valid UUID format",
        )
    )
    status = fields.String(validate=validate.OneOf(TaskStatus.values()))
    result = fields.Raw(required=False, load_default=None, metadata={"nullable": True})
