from datetime import datetime, timezone

from marshmallow import ValidationError, fields

from app.common.constants.datetime import DEFAULT_DATETIME_FORMAT


class CustomDateTimeUtc(fields.Field):
    """A custom DateTime field that serializes to ISO 8601 with a 'Z' suffix for UTC."""

    def __init__(self, **kwargs):
        # Set default metadata for OpenAPI and allow overrides
        metadata = kwargs.get("metadata", {})
        metadata.setdefault(
            "description", kwargs.pop("description", "A custom datetime field")
        )
        metadata.setdefault("example", kwargs.pop("example", "2024-01-01T00:00:00Z"))
        metadata.setdefault("format", "date-time")
        kwargs["metadata"] = metadata

        # Set default dump_only and required values if not explicitly provided
        kwargs["dump_only"] = kwargs.get("dump_only", False)
        kwargs["required"] = kwargs.get("required", False)

        super().__init__(**kwargs)

    def _serialize(self, value, attr, obj, **kwargs):
        if value is None:
            return None
        if not isinstance(value, datetime):
            raise ValidationError("Invalid datetime value.")
        # Ensure the datetime is in UTC
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        elif value.tzinfo != timezone.utc:
            value = value.astimezone(timezone.utc)
        # Return the ISO 8601 string with 'Z'
        return value.strftime(DEFAULT_DATETIME_FORMAT)

    def _deserialize(self, value, attr, data, **kwargs):
        if not isinstance(value, str):
            raise ValidationError("Expected a string for datetime.")
        try:
            # Parse the datetime string and enforce UTC
            dt = datetime.strptime(value, DEFAULT_DATETIME_FORMAT)
            return dt.replace(tzinfo=timezone.utc)
        except ValueError:
            raise ValidationError(
                "Invalid datetime format. Expected ISO 8601 with 'Z'."
            )
