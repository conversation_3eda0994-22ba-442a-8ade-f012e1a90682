from datetime import datetime, timezone

from sqlalchemy import and_, literal, not_, or_
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.sql import ColumnElement

from app.cdfs.cdf import CDF
from app.common.constants.datetime import DEFAULT_DATETIME_DATE_FORMAT
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums.cdf_kind import CdfKind
from app.enums.filter_operator import FilterOperator
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.enums.relative_date import RelativeDate as RelativeDateEnum
from app.filters.relative_date import RelativeDate


class ParseFilters:
    OPERATORS = {
        FilterOperator.IsNull.value: "is_null",
        FilterOperator.IsNotNull.value: "is_not_null",
        FilterOperator.Equals.value: "eq",
        FilterOperator.IsEmpty.value: "eq",
        FilterOperator.NotEquals.value: "ne",
        FilterOperator.IsNotEmpty.value: "ne",
        FilterOperator.GreaterThan.value: "gt",
        FilterOperator.LessThan.value: "lt",
        FilterOperator.GreaterThanOrEqual.value: "ge",
        FilterOperator.LessThanOrEqual.value: "le",
        FilterOperator.Begins.value: "ilike",
        FilterOperator.Ends.value: "ilike",
        FilterOperator.Contains.value: "ilike",
        FilterOperator.NotBegins.value: "not_ilike",
        FilterOperator.NotEnds.value: "not_ilike",
        FilterOperator.NotContains.value: "not_ilike",
        FilterOperator.ListContains.value: "in",
        FilterOperator.NotListContains.value: "not_in",
    }

    VALUES = {
        FilterOperator.IsNull.value: lambda value: None,
        FilterOperator.IsNotNull.value: lambda value: None,
        FilterOperator.Equals.value: lambda value: value,
        FilterOperator.IsEmpty.value: lambda value: "",
        FilterOperator.NotEquals.value: lambda value: value,
        FilterOperator.IsNotEmpty.value: lambda value: "",
        FilterOperator.GreaterThan.value: lambda value: value,
        FilterOperator.LessThan.value: lambda value: value,
        FilterOperator.GreaterThanOrEqual.value: lambda value: value,
        FilterOperator.LessThanOrEqual.value: lambda value: value,
        FilterOperator.Begins.value: lambda value: f"{value}%",
        FilterOperator.Ends.value: lambda value: f"%{value}",
        FilterOperator.Contains.value: lambda value: f"%{value}%",
        FilterOperator.NotBegins.value: lambda value: f"{value}%",
        FilterOperator.NotEnds.value: lambda value: f"%{value}",
        FilterOperator.NotContains.value: lambda value: f"%{value}%",
        FilterOperator.ListContains.value: lambda value: value,
        FilterOperator.NotListContains.value: lambda value: value,
    }

    def __init__(
        self,
        dataset: Dataset,
        filters: dict,
        property_ids: list,
        organization_id: int,
        property_timezone: timezone = timezone.utc,
        multi_levels: list = [],
        custom_field_cdfs: list = [],
        custom_cdfs: list = [],
    ):
        self.dataset = dataset
        self.filters = (
            filters if isinstance(filters, list) else [filters] if filters else []
        )
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.property_timezone = property_timezone
        self.multi_levels = multi_levels
        self.custom_field_cdfs = custom_field_cdfs
        self.custom_cdfs = custom_cdfs

    def __repr__(self):
        """Representation of ParseFilters
        :return: string
        """
        return f"<ParseFilters, dataset={self.dataset}, filters={self.filters}, property_ids={self.property_ids}>"

    def get_filters(self) -> list:
        """
        Convert a list of FilterSchema into
        a list of SQLAlchemy2 expressions that can be used in a query.
        """

        parsed_filters = self.build_filters(self.filters)
        parsed_static_filters = self.build_filters(self.dataset.static_filters)
        property_ids_filter = self.__get_parsed_property_ids()
        organization_id_filter = self.__get_parsed_organization_id()
        filters = (
            parsed_filters
            + parsed_static_filters
            + [property_ids_filter]
            + [organization_id_filter]
        )
        return and_(*filters)

    def build_filters(self, filters) -> list:
        """
        Convert a list of FilterSchema into
        a list of SQLAlchemy2 expressions that can be used in a query.
        """
        parsed_filters = []
        for f in filters:
            parsed_filters.append(self.__parse_filter(f))
        return parsed_filters

    def __parse_filter(self, filter: dict) -> ColumnElement:
        """
        Recursively parse a single filter (which may be a logical grouping or an atomic condition)
        into a valid SQLAlchemy expression.
        """
        if "or" in filter:
            or_subfilters = []
            for sub in filter["or"]:
                or_subfilters.append(self.__parse_filter(sub))
            return or_(*or_subfilters)

        if "and" in filter:
            and_subfilters = []
            for sub in filter["and"]:
                and_subfilters.append(self.__parse_filter(sub))
            return and_(*and_subfilters)

        cdf_info = filter.get("cdf", {})
        column_name = cdf_info.get("column")
        operator = filter.get("operator")
        value = filter.get("value")

        value = self.__get_value(
            value,
            column_name,
            operator,
            (
                None
                if not cdf_info.get("multi_level_id")
                else MultiLevelEnum(cdf_info["multi_level_id"])
            ),
            cdf_info.get("type"),
        )
        column_expr = self.__get_field(column_name, cdf_info)

        match (operator):
            case FilterOperator.Begins.value:
                filter = column_expr.ilike(f"{value}%")
            case FilterOperator.NotBegins.value:
                return not_(column_expr.ilike(f"{value}%"))
            case FilterOperator.Ends.value:
                filter = column_expr.ilike(f"%{value}")
            case FilterOperator.NotEnds.value:
                return not_(column_expr.ilike(f"%{value}"))
            case FilterOperator.IsNull.value:
                filter = column_expr.is_(None)
            case FilterOperator.IsNotNull.value:
                filter = column_expr.isnot(None)
            case FilterOperator.IsEmpty.value:
                filter = column_expr == ""
            case FilterOperator.IsNotEmpty.value:
                filter = column_expr != ""
            case FilterOperator.Contains.value:
                filter = column_expr.ilike(f"%{value}%")
            case FilterOperator.NotContains.value:
                return not_(column_expr.ilike(f"%{value}%"))
            case FilterOperator.ListContains.value:
                filter = column_expr.in_(value)
            case FilterOperator.NotListContains.value:
                filter = column_expr.notin_(value)
            case FilterOperator.Equals.value:
                filter = column_expr == value
            case FilterOperator.NotEquals.value:
                filter = column_expr != value
            case FilterOperator.GreaterThan.value:
                filter = column_expr > value
            case FilterOperator.LessThan.value:
                filter = column_expr < value
            case FilterOperator.LessThanOrEqual.value:
                filter = column_expr <= value
            case FilterOperator.GreaterThanOrEqual.value:
                filter = column_expr >= value
            case _:
                raise ValueError(f"Unsupported operator: {operator}")

        if cdf_info.get("multi_level_id") == MultiLevelEnum.OccupancyReservation.value:
            filter = or_(
                filter,
                getattr(self.dataset.model, self.dataset.model.booking_id.key).is_(
                    None
                ),
            )

        if (
            cdf_info.get("multi_level_id")
            == MultiLevelEnum.BedOccupancyReservation.value
        ):
            filter = or_(
                filter,
                getattr(self.dataset.model, self.dataset.model.reservation_id.key).is_(
                    None
                ),
            )

        return filter

    def __get_parsed_property_ids(self) -> list[dict]:
        return getattr(self.dataset.model, "property_id").in_(self.property_ids)

    def __get_parsed_organization_id(self) -> list[dict]:
        if hasattr(self.dataset.model, "organization_id"):
            return (
                getattr(self.dataset.model, "organization_id") == self.organization_id
            )
        return literal(True)

    def __get_value(
        self,
        value: str,
        field: str,
        op: str,
        multi_level: MultiLevelEnum = None,
        type: str = None,
    ) -> str:
        value_cdf = CDF(
            self.dataset.kind,
            field,
            (
                any(field == cdf.key for cdf in self.custom_cdfs)
                if self.custom_cdfs
                else False
            ),
            False,
            multi_level,
            type,
        )
        if (
            field
            not in [
                static_filter["cdf"]["column"]
                for static_filter in self.dataset.static_filters
                if "cdf" in static_filter
            ]
        ) and (value_cdf.is_kind(CdfKind.Date) or value_cdf.is_kind(CdfKind.Timestamp)):
            value, duration = RelativeDate.get_duration_from_value(value)
            if duration is not None:
                return self.__get_converted_relative_date(value, duration)
            elif value in [relative_date.value for relative_date in RelativeDateEnum]:
                return self.__get_converted_relative_date(value)
            elif value_cdf.is_kind(CdfKind.Date):
                return self.__get_formatted_date(value)

        return self.VALUES[op](value)

    def __get_converted_relative_date(
        self, relative_date_string: str, duration: int = 0
    ) -> str:
        relative_date_string = relative_date_string.lower()

        return RelativeDate(
            RelativeDateEnum(relative_date_string),
            self.property_timezone,
            duration,
        ).value.strftime(DEFAULT_DATETIME_DATE_FORMAT)

    def __get_formatted_date(self, date_value: str) -> str:
        try:
            return datetime.fromisoformat(date_value).strftime(
                DEFAULT_DATETIME_DATE_FORMAT
            )
        except ValueError:
            try:
                return datetime.fromisoformat(
                    date_value.replace("Z", "+00:00")
                ).strftime(DEFAULT_DATETIME_DATE_FORMAT)
            except ValueError as value_error:
                logger.error(
                    f"Invalid date format for value, '{date_value}', in report filters",
                    exc_info=True,
                    extra={"error": value_error},
                )
                InvalidUsage.server_error()

    def __get_field(self, field: str, cdf_info: dict) -> InstrumentedAttribute:
        custom_cdf = (
            next(
                (
                    custom_cdf
                    for custom_cdf in self.custom_cdfs
                    if custom_cdf.key == field
                ),
                False,
            )
            if self.custom_cdfs
            else False
        )
        if custom_cdf is not False:
            field = custom_cdf
        else:
            model = (
                self.dataset.model
                if not cdf_info.get("multi_level_id")
                else MultiLevel(MultiLevelEnum(cdf_info["multi_level_id"])).model
            )
            field = getattr(model, field)
        return field
