from datetime import date, datetime, timezone

from dateutil.parser import parse
from dateutil.relativedelta import relativedelta

from app.common.cache import TIMEOUT_DAY, cache
from app.enums.relative_date import RelativeDate as RelativeDateEnum


class RelativeDate:
    def __init__(
        self,
        relative_date: RelativeDateEnum,
        property_timezone: timezone = timezone.utc,
        duration: int = 0,
        today: str | None = None,
    ):
        self.relative_date = relative_date
        self.duration = duration
        self.property_timezone = property_timezone
        self.today = today

    def __repr__(self):
        """Representation of Relative Date class
        :return: string
        """
        return (
            f"<RelativeDate, relative_date={self.relative_date}, "
            f"property_timezone={self.property_timezone}, "
            f"duration={self.duration}>"
        )

    @property
    def value(self):
        if self.today:
            today = parse(self.today)
        else:
            today = datetime.now(tz=self.property_timezone).date()

        match self.relative_date:
            case RelativeDateEnum.Tomorrow:
                return today + relativedelta(days=1)

            case RelativeDateEnum.Yesterday:
                return today - relativedelta(days=1)

            case RelativeDateEnum.StartCurrentWeek:
                return today - relativedelta(days=today.weekday())

            case RelativeDateEnum.StartLastWeek:
                return today - relativedelta(days=7 + today.weekday())

            case RelativeDateEnum.StartNextWeek:
                return today + relativedelta(days=7 - today.weekday())

            case RelativeDateEnum.StartCurrentMonth:
                return today.replace(day=1)

            case RelativeDateEnum.StartLastMonth:
                return today.replace(day=1) - relativedelta(months=1)

            case RelativeDateEnum.StartNextMonth:
                return today.replace(day=1) + relativedelta(months=1)

            case RelativeDateEnum.StartCurrentQuarter:
                return date(today.year, 3 * ((today.month - 1) // 3) + 1, 1)

            case RelativeDateEnum.StartLastQuarter:
                quarter_month = 3 * ((today.month - 4) // 3)
                quarter_year = today.year if (today.month > 3) else today.year - 1
                quarter_month = (
                    quarter_month if quarter_month >= 0 else quarter_month + 12
                )
                return date(quarter_year, quarter_month + 1, 1)

            case RelativeDateEnum.StartNextQuarter:
                quarter_month = 3 * ((today.month + 2) // 3)
                quarter_year = today.year if (today.month < 9) else today.year + 1
                quarter_month = (
                    quarter_month if quarter_month < 12 else quarter_month - 12
                )
                return date(quarter_year, quarter_month + 1, 1)

            case RelativeDateEnum.StartCurrentYear:
                return today.replace(month=1, day=1)

            case RelativeDateEnum.StartLastYear:
                return today.replace(month=1, day=1) - relativedelta(years=1)

            case RelativeDateEnum.StartNextYear:
                return today.replace(month=1, day=1) + relativedelta(years=1)

            case RelativeDateEnum.DaysLater:
                return today + relativedelta(days=self.duration)

            case RelativeDateEnum.DaysPrior:
                return today - relativedelta(days=self.duration)

            case RelativeDateEnum.WeeksLater:
                return today + relativedelta(weeks=self.duration)

            case RelativeDateEnum.WeeksPrior:
                return today - relativedelta(weeks=self.duration)

            case RelativeDateEnum.MonthsLater:
                return today + relativedelta(months=self.duration)

            case RelativeDateEnum.MonthsPrior:
                return today - relativedelta(months=self.duration)

            case RelativeDateEnum.YearsLater:
                return today + relativedelta(years=self.duration)

            case RelativeDateEnum.YearsPrior:
                return today - relativedelta(years=self.duration)
        return today

    @staticmethod
    @cache.memoize(TIMEOUT_DAY)
    def get_duration_from_value(raw_value: str) -> list[str, int]:
        """Using the filter value field, determine if the duration modifier is present and return
        the value and duration
        """
        split_values = raw_value.split(";")
        value = split_values[0] if len(split_values) >= 1 else None
        if (
            value in [relative_date.value for relative_date in RelativeDateEnum]
            and len(split_values) == 2
        ):
            try:
                duration = int(split_values[1]) if split_values[1] else None
            except ValueError:
                duration = None
                value = raw_value
            return [value, duration]
        return [raw_value, None]
