from sqlalchemy import BIGINT, Column, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.common.database import db


class Card(db.Model):
    __tablename__ = "card"
    __table_args__ = (
        UniqueConstraint("hub_id", "chart_id", name="hub_chart_unique"),
        {"comment": "Card table with user-defined cards"},
    )

    id = Column(BIGINT, primary_key=True, comment="Primary key for each card")
    hub_id = Column(BIGINT, ForeignKey("hub.id"), nullable=True, comment="Hub Id")
    chart_id = Column(BIGINT, ForeignKey("chart.id"), nullable=True, comment="Chart Id")
    hub = relationship("Hub", back_populates="cards")
