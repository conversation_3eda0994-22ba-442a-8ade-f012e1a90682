import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.orm import aliased
from sqlalchemy.sql.expression import func, literal
from sqlalchemy.sql.schema import ForeignKey
from sqlalchemy.sql.sqltypes import DateTime

from app.common.database import db
from app.common.exceptions import InvalidUsage
from app.services.user_service import UserService


class Folder(db.Model):
    __tablename__ = "folder"
    __table_args__ = {"comment": "Folder table with all folders"}

    id = Column(BIGINT, primary_key=True, comment="Primary key for each folder")
    name = Column(String(length=25), nullable=False, comment="Name of each folder")
    description = Column(
        String(length=200), nullable=False, comment="Description of each folder"
    )
    parent_id = Column(
        BIGINT,
        ForeignKey("folder.id"),
        nullable=True,
        comment="Folder may belong to a parent - It can be nullable",
    )
    property_id = Column(
        BIGINT, nullable=False, comment="Property that created the folder"
    )
    user_id = Column(BIGINT, nullable=False, comment="User that created the folder")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the folder",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the folder",
    )

    @property
    def created_by(self):
        return UserService.get_created_by(self.user_id)

    @classmethod
    def get_by_id_and_property_id(cls, id, property_id):
        folder = cls.query.filter(
            Folder.id == id, Folder.property_id == property_id
        ).first()

        if not folder:
            raise InvalidUsage.not_found(f"A Folder with id: {id} does not exist")

        return folder

    @classmethod
    def get_all_by_property_id(cls, property_id: int):
        return cls.query.filter(Folder.property_id == property_id).order_by(
            Folder.name.asc()
        )

    @classmethod
    def get_by_name_and_property_id(cls, name: str, property_id: int):
        return cls.query.filter(
            Folder.name == name, Folder.property_id == property_id
        ).first()

    @classmethod
    def get_by_id(cls, id: int):
        return cls.query.filter(Folder.id == id).first()

    @classmethod
    def is_max_level_depth(cls, parent_id, folder_id=None) -> bool:
        """
        Returns True if the sum of the depth level of the current folder
        and the depth level of the parent folder is greater than 2.

        If Folder Id is passed it means that we need to check the depth level of this folder.
        For example if the folder will be moved inside another folder we need to get the number of childs that the folder has and
        then we need to verify the actual level of the folder that will be the new parent


            Parameters:
                    parent_id (int): Id of the new parent folder
                    folder_id (int): Id of the folder

            Returns:
                    max_level_depth (bool): True if is greater than 2
        """
        current_folder_depth = 0

        if folder_id:
            folders_CTE = (
                Folder.query.with_entities(
                    Folder.id, Folder.parent_id, literal(1).label("level")
                )
                .filter(Folder.id == folder_id)
                .cte(name="folders_CTE", recursive=True)
            )

            folders_cte_alias = aliased(folders_CTE, name="folders_cte_alias")
            folders_recursive_alias = aliased(Folder, name="folders_recursive_alias")

            folders_CTE = folders_CTE.union_all(
                Folder.query.with_entities(
                    folders_recursive_alias.id,
                    folders_recursive_alias.parent_id,
                    (folders_cte_alias.c.level + 1),
                ).filter(folders_recursive_alias.parent_id == folders_cte_alias.c.id)
            )

            current_folder_depth = Folder.query.with_entities(
                func.max(folders_CTE.c.level)
            ).first()[0]

        folders_CTE = (
            Folder.query.with_entities(
                Folder.id, Folder.parent_id, literal(1).label("level")
            )
            .filter(Folder.id == parent_id)
            .cte(name="folders_CTE", recursive=True)
        )

        folders_cte_alias = aliased(folders_CTE, name="folders_cte_alias")
        folders_recursive_alias = aliased(Folder, name="folders_recursive_alias")

        folders_CTE = folders_CTE.union_all(
            Folder.query.with_entities(
                folders_recursive_alias.id,
                folders_recursive_alias.parent_id,
                (folders_cte_alias.c.level + 1),
            ).filter(folders_recursive_alias.id == folders_cte_alias.c.parent_id)
        )

        parent_depth = Folder.query.with_entities(
            func.max(folders_CTE.c.level)
        ).first()[0]

        return (parent_depth + current_folder_depth) > 3

    @classmethod
    def are_folders_assign_to_folder(cls, folder_id) -> bool:
        return bool(cls.query.filter(Folder.parent_id == folder_id).count())
