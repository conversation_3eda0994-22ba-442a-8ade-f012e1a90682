import datetime

from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>an,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    JSON,
    String,
    UniqueConstraint,
    desc,
    func,
    select,
)

from app.common.constants.limits import MAX_STOCK_REPORT_REVISIONS
from app.common.database import db
from app.common.exceptions import InvalidUsage


class StockReportRevision(db.Model):
    __tablename__ = "stock_report_revision"
    __table_args__ = (
        UniqueConstraint("stock_report_id", "revision_id"),
        {"comment": "Stock Report Revision Table"},
    )
    stock_report_id = Column(
        BIGINT, nullable=False, comment="Stock Report ID", primary_key=True
    )
    revision_id = Column(BIGINT, nullable=False, comment="Version ID", primary_key=True)
    title = Column(String, nullable=False, comment="Title of each stock report")
    description = Column(String, comment="Description of each stock report")
    columns = Column(
        JSON, nullable=False, comment="Columns of stock report as list of objects"
    )
    filters = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Filters of stock report as list of objects",
    )
    group_rows = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Group rows of stock report as list of objects",
    )
    group_columns = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Group columns of stock report as list of objects",
    )
    sort = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Sort of stock report as list of objects",
    )
    settings = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Settings of stock report as object",
    )
    dataset_id = Column(
        Integer,
        ForeignKey("dataset.id"),
        nullable=False,
        comment="Foreign key to the dataset table id",
    )
    rules = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Stock Report Rules JSON",
    )
    custom_cdfs = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Custom Cdfs of Stock Report as list of objects",
    )
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the stock report",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the report",
    )
    user_id = Column(
        BIGINT,
        nullable=False,
        comment="User ID that created the stock report revision",
    )

    published = Column(
        Boolean, nullable=False, default=True, comment="Published status of the report"
    )

    periods = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Periods of report as list of objects",
    )

    formats = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Formatting options of the report",
    )

    comparisons = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Comparisons of report as list of objects",
    )

    @classmethod
    def get_all_by_id(cls, stock_report_id):
        revisions = (
            cls.query.with_entities(
                StockReportRevision.stock_report_id,
                StockReportRevision.revision_id,
                StockReportRevision.user_id,
                StockReportRevision.updated_at,
            )
            .filter(StockReportRevision.stock_report_id == stock_report_id)
            .order_by(StockReportRevision.revision_id.desc())
        )

        return revisions

    @classmethod
    def prune(cls, stock_report_id):
        columns = [
            cls.__table__.columns[column_name]
            for column_name in cls.__table__.columns.keys()
        ]

        subquery = (
            select(
                *columns,
                func.row_number()
                .over(
                    partition_by=StockReportRevision.stock_report_id,
                    order_by=desc(StockReportRevision.revision_id),
                )
                .label("row_number"),
            )
            .select_from(cls)
            .where(StockReportRevision.stock_report_id == stock_report_id)
            .alias()
        )

        # Use the subquery in another query to filter
        rows_to_delete = db.session.query(subquery.c).filter(
            subquery.c.row_number > MAX_STOCK_REPORT_REVISIONS
        )

        revision_ids_to_delete = [row.revision_id for row in rows_to_delete.all()]

        cls.query.filter(StockReportRevision.stock_report_id == stock_report_id).filter(
            StockReportRevision.revision_id.in_(revision_ids_to_delete)
        ).delete()

    @classmethod
    def get_revision(cls, stock_report_id, revision_id):
        revision = (
            cls.query.filter(StockReportRevision.stock_report_id == stock_report_id)
            .filter(StockReportRevision.revision_id == revision_id)
            .first()
        )

        if not revision:
            return InvalidUsage.not_found(
                message=f"A Stock report revision with id: {revision_id} does not exist for stock report {stock_report_id}"
            )
        return revision
