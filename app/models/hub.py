from datetime import datetime

from sqlalchemy import BIGINT, Column, DateTime, JSON, String
from sqlalchemy.orm import relationship

from app.common.database import db
from app.common.mixins.sort_mixin import SortMixin
from app.services.user_service import UserService


class Hub(db.Model, SortMixin):
    __tablename__ = "hub"
    __table_args__ = {"comment": "Hub table with user-defined hubs"}

    id = Column(BIGINT, primary_key=True, comment="Primary key for each hub")
    title = Column(String, comment="Title of the hub")
    description = Column(String, comment="Description of the hub")
    cards = relationship("Card", back_populates="hub")
    settings = Column(JSON, comment="Hub settings")
    property_id = Column(BIGINT, comment="Property ID of the hub")
    user_id = Column(BIGINT, comment="User ID of the hub")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        comment="Created at date of the chart",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="Updated at date of the chart",
    )
    favorites = relationship("Favorite", cascade="all, delete")

    @property
    def created_by(self):
        return UserService.get_created_by(self.user_id)
