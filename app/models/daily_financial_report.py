from sqlalchemy import B<PERSON>IN<PERSON>, BOOLEAN, Column, DATE, FLOAT, VARCHAR


from app.common.database import db


class DFRMixin(object):
    property_id = Column(BIGINT, primary_key=True)
    organization_id = Column(BIGINT, primary_key=True)
    report_date = Column(DATE, primary_key=True)


class DFRTransactions(DFRMixin, db.Model):
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "aurora"
    __tablename__ = "dfr_transactions_vue"

    adjustment = Column(FLOAT)
    total_revenue = Column(FLOAT)
    item_service = Column(FLOAT)
    payment = Column(FLOAT)


class DFRCheckout(DFRMixin, db.Model):
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "aurora"
    __tablename__ = "dfr_checkout_vue"

    checkout = Column(BIGINT)


class DFRCheckin(DFRMixin, db.Model):
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "aurora"
    __tablename__ = "dfr_checkin_walkin_noshow_vue"

    checkin = Column(BIGINT)
    walkin = Column(BIGINT)
    noshow = Column(BIGINT)


class DFRCapacity(DFRMixin, db.Model):
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "aurora"
    __tablename__ = "dfr_occupancy_capacity_vue"

    blocked_rooms = Column(BIGINT)


class DFRRoomReservation(DFRMixin, db.Model):
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "aurora"
    __tablename__ = "dfr_reservation_room_reservation_vue"

    comp = Column(BIGINT)
    guest = Column(BIGINT)


class DFRReservations(db.Model):
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "aurora"
    __tablename__ = "dfr_reservations_vue"
    property_id = Column(BIGINT, primary_key=True)
    organization_id = Column(BIGINT, primary_key=True)
    checkout_date = Column(DATE)
    checkin_date = Column(DATE)
    reservation_status = Column(VARCHAR)
    is_booking_deleted = Column(BOOLEAN)
