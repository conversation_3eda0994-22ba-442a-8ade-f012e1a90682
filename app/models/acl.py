from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, String


from app.common.database import db


class TblAclRoles(db.Model):
    __tablename__ = "tbl_aclroles"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    name = Column(String)
    scope = Column(String)
    is_active = Column(Boolean)


class TblAcl(db.Model):
    __tablename__ = "tbl_acl"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    type = Column(String)
    type_id = Column(BIGINT)
    resource_id = Column(BIGINT)
    action = Column(String)


class TblAclResources(db.Model):
    __tablename__ = "tbl_aclresources"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    resource = Column(String)


class RoleAssociation(db.Model):
    __tablename__ = "role_association"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    role_id = Column(BIGINT)
    association_id = Column(BIGINT)


class AHTLAssociationProperty(db.Model):
    __tablename__ = "a_htl_association_property"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    association_id = Column(BIGINT)
    deleted = Column(Boolean)
    property_id = Column(BIGINT)


class AHTLAssociation(db.Model):
    __tablename__ = "a_htl_association"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    is_property_wrapper = Column(Boolean)


class UserRoleAssociation(db.Model):
    __tablename__ = "user_role_association"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    user_id = Column(BIGINT)
    role_association_id = Column(BIGINT)
    active = Column(Boolean)


class RoleAssociationProperty(db.Model):
    __tablename__ = "role_association_property"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    role_id = Column(BIGINT)
    association_property_id = Column(BIGINT)


class UserRoleAssociationProperty(db.Model):
    __tablename__ = "user_role_association_property"
    __table_args__ = {"schema": "mfd"}
    __bind_key__ = "aurora"

    id = Column(BIGINT, primary_key=True)
    user_id = Column(BIGINT)
    role_association_property_id = Column(BIGINT)
    active = Column(Boolean)
