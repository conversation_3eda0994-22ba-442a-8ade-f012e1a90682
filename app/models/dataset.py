from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String


from app.common.database import db


class Dataset(db.Model):
    __tablename__ = "dataset"
    __table_args__ = {"comment": "Dataset table with supported datasets"}

    id = Column(Integer, primary_key=True, comment="Primary key for each dataset")
    name = Column(
        String,
        comment="Name of the dataset which has relation to the materialized views",
    )


class DatasetMultiLevel(db.Model):
    __tablename__ = "dataset_multi_level"
    __table_args__ = {"comment": "Table with relation between dataset and multi-levels"}

    dataset_id = Column(
        Integer,
        ForeignKey("dataset.id"),
        primary_key=True,
        nullable=False,
        comment="Dataset ID",
    )
    multi_level_id = Column(
        Integer,
        ForeignKey("multi_level.id"),
        primary_key=True,
        nullable=False,
        comment="Multi-Level ID",
    )
