import datetime
from typing import <PERSON><PERSON>

from flask_sqlalchemy.query import Query as BaseQuery

from sqlalchemy import Column, String
from sqlalchemy.sql.sqltypes import BIGINT, DateTime

from app.common.database import db


class StockReportFolder(db.Model):
    __tablename__ = "stock_report_folder"
    __table_args__ = {
        "comment": "Stock Report Folder table to manage all the folders for stock reports"
    }

    id = Column(
        BIGINT, primary_key=True, autoincrement=False, comment="Id of the folder"
    )
    name = Column(
        String,
        unique=True,
        comment="Name of the folder that will be used as key for the translation",
    )
    user_id = Column(BIGINT, nullable=False, comment="User that created the folder")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the folder",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the folder",
    )

    @classmethod
    def get_by_id(cls, id: int) -> Tuple[int, str]:
        return (
            cls.query.with_entities(StockReportFolder.id, StockReportFolder.name)
            .filter(StockReportFolder.id == id)
            .first()
        )

    @classmethod
    def get_all(cls) -> BaseQuery:
        return cls.query.with_entities(
            StockReportFolder.id, StockReportFolder.name
        ).order_by(StockReportFolder.created_at)
