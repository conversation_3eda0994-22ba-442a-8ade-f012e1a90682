import datetime

from flask import g

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.sql.sqltypes import DateTime

from app.common.database import db


class Task(db.Model):
    __tablename__ = "task"
    __table_args__ = {"comment": "Task table with recent tasks"}

    id = Column(
        String, primary_key=True, nullable=False, comment="Unique ID of the task"
    )
    name = Column(String, nullable=False, comment="Name of task to process task")
    property_id = Column(
        BIGINT, nullable=False, comment="Property that created the task"
    )
    property_ids = Column(
        ARRAY(BIGINT),
        nullable=True,
        comment="Property that the report belongs to for multi property",
    )
    user_id = Column(BIGINT, nullable=False, comment="User that created the task")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the task",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the task",
    )

    @classmethod
    def get_all(cls, user_id: int, property_id: int):
        query = cls.query.filter(Task.property_id == property_id)

        if not g.user.admin:
            query = query.filter(Task.user_id == user_id)

        return query

    @classmethod
    def get_task_by_id(cls, id):
        if g.api_key:
            return cls.query.filter(Task.id == id).first()

        query = cls.query.filter(Task.property_id == g.property_id, Task.id == id)
        if not g.user.admin:
            query = query.filter(Task.user_id == g.user.id)

        result = query.first()
        return result

    @classmethod
    def get_task_by_id_or_404(cls, id):
        return cls.query.filter(Task.id == id).first_or_404()
