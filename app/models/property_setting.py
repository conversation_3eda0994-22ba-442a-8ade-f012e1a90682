import datetime

from sqlalchemy import BIGIN<PERSON>, Column, DateTime, String


from app.common.database import db
from app.enums.property_setting import PropertySetting as PropertySettingName, WeekDays


class PropertySetting(db.Model):
    __tablename__ = "property_setting"
    __table_args__ = {"comment": "Property Settings Table"}

    property_id = Column(
        BIGINT,
        primary_key=True,
        nullable=False,
        comment="Property that the setting belongs",
    )
    name = Column(
        String(length=60),
        primary_key=True,
        nullable=False,
        comment="Name of the property setting",
    )
    value = Column(
        String(length=200),
        nullable=False,
        comment="Value of the property setting",
    )
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the setting",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the setting",
    )
    user_id = Column(BIGINT, nullable=False, comment="User that created the setting")

    @classmethod
    def get_all_by_property_id(cls, property_id: int):
        return cls.query.filter(PropertySetting.property_id == property_id).all()

    @classmethod
    def get_by_name_and_property_id(cls, property_id: int, name: str):
        return (
            cls.query.filter(PropertySetting.property_id == property_id)
            .filter(PropertySetting.name == name)
            .first()
        )

    @classmethod
    def get_property_start_of_week(cls, property_id: int) -> str:
        """Returns a property's start_of_week setting value, or defaults to day with no offset (monday)

        Args:
            property_id int: a unique property_id

        Returns:
            str: a capitalized day of the week
        """
        start_of_week = cls.get_by_name_and_property_id(
            property_id, PropertySettingName.START_OF_WEEK.value
        )
        start_of_week = start_of_week.value if start_of_week else WeekDays.Monday.name
        return start_of_week
