import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.sql.sqltypes import DateTime

from app.common.database import CRUDMixin, db


class StockTag(db.Model, CRUDMixin):
    __tablename__ = "stock_tag"
    __table_args__ = {"comment": "Stock Tag table with cloudbeds-defined Tags"}

    id = Column(BIGINT, primary_key=True, comment="Primary key for each tag")
    name = Column(String(255), nullable=False, comment="Name of each tag", unique=True)
    user_id = Column(BIGINT, nullable=False, comment="User that created the tag")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.now(datetime.timezone.utc),
        comment="Created at date of the tag",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
        comment="Updated at date of the tag",
    )

    @classmethod
    def get_all(cls):
        return cls.query.order_by(StockTag.updated_at.desc()).all()

    @classmethod
    def get_by_id_or_404(cls, id: int):
        return cls.query.filter(StockTag.id == id).first_or_404()
