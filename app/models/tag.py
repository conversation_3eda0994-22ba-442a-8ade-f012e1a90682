import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.sql.sqltypes import DateTime

from app.common.database import db
from app.common.exceptions import InvalidUsage
from app.services.property_service import PropertyService
from app.services.user_service import UserService


class Tag(db.Model):
    __tablename__ = "tag"
    __table_args__ = {"comment": "Tag table with all tags"}

    id = Column(BIGINT, primary_key=True, comment="Primary key for each tag")
    name = Column(String, nullable=False, comment="Name of each tag")
    property_id = Column(
        BIGINT, nullable=False, comment="Property that created the tag"
    )
    user_id = Column(BIGINT, nullable=False, comment="User that created the tag")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the tag",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the tag",
    )

    @property
    def created_by(self):
        return UserService.get_created_by(self.user_id)

    @classmethod
    def get_by_id_with_permissions(self, id, access_token):
        tag = self.get_by_id(id)

        if tag is None:
            InvalidUsage.not_found(
                message=f"Tag id: {id} does not exist for this property"
            )

        properties = PropertyService.get_all(ids=True)

        if tag.property_id in properties:
            return tag

        return InvalidUsage.not_authorized(
            message="This user does not have access to get this tag"
        )

    @classmethod
    def get_all_by_property_id(cls, property_id: int):
        return cls.query.filter(Tag.property_id == property_id).order_by(
            Tag.updated_at.desc()
        )

    @classmethod
    def get_by_name_and_property_id(cls, property_id: int, name: int):
        return cls.query.filter(
            Tag.property_id == property_id, Tag.name == name
        ).first()

    @classmethod
    def get_by_id_and_property_id(cls, property_id: int, id: int):
        return cls.query.filter(Tag.property_id == property_id, Tag.id == id).first()

    @classmethod
    def get_by_id(cls, id: int):
        return cls.query.filter(Tag.id == id).first()
