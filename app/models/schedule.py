import datetime

from flask import g

from sqlalchemy import ARRAY, BIGINT, Column, DateTime, ForeignKey, JSON, String
from sqlalchemy.orm import relationship

from app.common.database import db
from app.common.mixins.sort_mixin import SortMixin


schedule_report_association = db.Table(
    "schedule_report_association",
    db.metadata,
    Column(
        "schedule_id",
        BIGINT,
        ForeignKey("schedule.id"),
        primary_key=True,
        comment="Primary key for each schedule",
    ),
    Column(
        "report_id",
        BIGINT,
        ForeignKey("report.id"),
        primary_key=True,
        comment="Primary key for each report",
    ),
)


class Schedule(SortMixin, db.Model):
    __tablename__ = "schedule"
    __table_args__ = {"comment": "Schedule table with all schedules"}

    id = Column(BIGINT, primary_key=True, comment="Primary key for each schedule")
    reports = relationship(
        "Report",
        secondary=schedule_report_association,
        back_populates="schedules",
        uselist=True,
    )
    property_id = Column(
        BIGINT, nullable=False, comment="Property that created the schedule"
    )
    frequency = Column(
        String, nullable=False, comment="Frequency of the schedule as a cron expression"
    )
    view = Column(
        String, nullable=False, comment="View which is targeted for the schedule"
    )
    format = Column(
        String, nullable=False, comment="Format which is targeted for the schedule"
    )
    subject = Column(String, comment="Subject of the schedule")
    recipients = Column(
        ARRAY(String),
        default=[],
        nullable=False,
        comment="Recipients list of the schedule",
    )
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the schedule",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the schedule",
    )
    user_id = Column(BIGINT, nullable=False, comment="User that created the schedule")

    settings = Column(JSON, nullable=True)

    @classmethod
    def get_all(
        cls, property_id: dict, query: dict, sort: dict, ignore_properties: list = []
    ) -> list:
        schedules = cls.query

        if (not hasattr(g, "user") or not g.user.admin) and len(ignore_properties):
            schedules = schedules.filter(~Schedule.property_id.in_(ignore_properties))

        if "match" in query:
            match = query["match"]
            query_match_removed = {key: query[key] for key in query if key != "match"}
            date = datetime.datetime.strptime(match, "%Y-%m-%dT%H:00:00Z")
            hour, day, month, weekday = (
                date.hour,
                date.day,
                date.month,
                [0, 1, 2, 3, 4, 5, 6, 0][date.isoweekday()],
            )
            pattern = f"(0) (\*|({hour})) (\*|({day}|.*,{day}|{day},.*|.*,{day},.*)) (\*|({month}|.*,{month}|{month},.*|.*,{month},.*)) (\*|({weekday}|.*,{weekday}|{weekday},.*|.*,{weekday},.*))$"
            return (
                schedules.filter(Schedule.frequency.op("~")(pattern))
                .filter_by(**property_id, **query_match_removed)
                .order_by(cls.get_order_by(sort))
            )

        return schedules.filter_by(**property_id, **query).order_by(
            cls.get_order_by(sort)
        )

    @classmethod
    def get_all_by_property_id(cls, property_id: int):
        return cls.query.filter(Schedule.property_id == property_id).order_by(
            Schedule.updated_at.desc()
        )

    @classmethod
    def get_by_id_and_property_id(cls, property_id: int, id: int):
        return cls.query.filter(
            Schedule.property_id == property_id, Schedule.id == id
        ).first_or_404()

    @classmethod
    def get_by_id(cls, id: int):
        return cls.query.filter(Schedule.id == id).first_or_404()

    @classmethod
    def get_all_property_ids(cls) -> list:
        return cls.query.with_entities(Schedule.property_id).distinct().all()
