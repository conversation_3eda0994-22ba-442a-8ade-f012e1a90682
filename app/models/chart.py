from datetime import datetime
from operator import or_

from flask import g

from sqlalchemy import (
    BIGINT,
    Column,
    DateTime,
    ForeignKey,
    JSON,
    String,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.sql import intersect, union

from app.common.database import db
from app.common.enums.search import ChartSearchColumns, ReportSearchColumns
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.mixins.sort_mixin import SortMixin
from app.enums.report import ReportKind
from app.models.report import Report
from app.models.stock_report import StockReport


class Chart(db.Model, SortMixin):
    __tablename__ = "chart"
    __table_args__ = {
        "comment": "Chart table containing user-defined chart configurations"
    }

    id = Column(BIGINT, primary_key=True, comment="Primary key for each report chart")
    report_id = Column(
        BIGINT, ForeignKey("report.id"), nullable=True, comment="Report Id"
    )
    stock_report_id = Column(
        BIGINT, ForeignKey("stock_report.id"), nullable=True, comment="Stock Report ID"
    )
    title = Column(String, nullable=False, comment="Title of each chart")
    kind = Column(String, nullable=False, comment="Kind of each chart")
    categories = Column(JSON, nullable=True, comment="List of categories")
    metrics = Column(JSON, nullable=True, comment="List of metrics")

    settings = Column(JSON, nullable=True, comment="Chart settings")

    user_id = Column(BIGINT, nullable=False, comment="User that created the chart")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        comment="Created at date of the chart",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="Updated at date of the chart",
    )
    report = relationship("Report", back_populates="charts")
    stock_report = relationship("StockReport", back_populates="charts")

    @property
    def datasource_title(self):
        match (self.datasource_kind):
            case ReportKind.Report.value:
                return self.report.title
            case ReportKind.StockReport.value | _:
                return self.stock_report.title

    @property
    def datasource_description(self):
        match (self.datasource_kind):
            case ReportKind.Report.value:
                return self.report.description
            case ReportKind.StockReport.value | _:
                return self.stock_report.description

    @classmethod
    def get_all_by_datasource_kind_and_datasource_id(
        cls, datasource_kind: str, datasource_id: int
    ) -> list["Chart"]:
        filter_column = cls.get_filter_column(datasource_kind)

        charts = (
            cls.query.filter(filter_column == datasource_id)
            .order_by(Chart.id.desc())
            .all()
        )
        return charts

    @classmethod
    def get_filter_column(cls, datasource_kind: str) -> InstrumentedAttribute:
        match (datasource_kind):
            case ReportKind.Report.value:
                filter_column = Chart.report_id
            case ReportKind.StockReport.value:
                filter_column = Chart.stock_report_id
            case _:
                InvalidUsage.bad_request(
                    f"Invalid datasource kind for chart: {datasource_kind}"
                )
        return filter_column

    @classmethod
    def get_by_id_datasource_kind_and_id(
        cls, chart_id: int, datasource_kind: str, datasource_id: int
    ) -> "Chart":
        filter_column = cls.get_filter_column(datasource_kind)
        return cls.query.filter(
            Chart.id == chart_id,
            filter_column == datasource_id,
        ).first()

    @classmethod
    def get_all(cls, filter, sort, property_id):
        chart_ids = filter.pop("ids", None)
        datasource_kind = filter.pop("datasource_kind", None)
        chart_kind = filter.pop("chart_kind", None)
        search_columns = filter.pop("search_columns", [])
        search_term = filter.pop("search_term", None)
        report_search_columns = []
        chart_search_columns = []
        for search_column in search_columns:
            match search_column:
                case ChartSearchColumns.DatasourceTitle.value:
                    report_search_columns.append(ReportSearchColumns.Title.value)
                case ChartSearchColumns.DatasourceDescription.value:
                    report_search_columns.append(ReportSearchColumns.Description.value)
                case ChartSearchColumns.Title.value:
                    chart_search_columns.append(ChartSearchColumns.Title.value)
                case _:
                    pass

        # Charts, Reports, and Stock Reports search queries
        union_queries = []
        # Chart IDs, Chart Kind, Report Tags, Report Kind
        intersect_queries = []

        query = cls.query

        published_stock_reports = [
            stock_report.id
            for stock_report in StockReport.get_all_with_rules(
                g.user, property_id, filter, ids_only=True
            ).all()
        ]

        property_reports = [
            report.id
            for report in Report.get_all(
                property_id, filter, g.user, ids_only=True
            ).all()
        ]

        match datasource_kind:
            case ReportKind.Report.value:
                query = query.filter(Chart.report_id.in_(property_reports))
            case ReportKind.StockReport.value:
                query = query.filter(Chart.stock_report_id.in_(published_stock_reports))
            case None | _:
                query = query.filter(
                    or_(
                        Chart.report_id.in_(property_reports),
                        Chart.stock_report_id.in_(published_stock_reports),
                    )
                )

        if chart_ids or chart_kind:
            chart_filters_query = Chart.query.with_entities(Chart.id.label("id"))
            if chart_ids:
                chart_filters_query = chart_filters_query.filter(
                    Chart.id.in_(chart_ids)
                )

            if chart_kind:
                chart_filters_query = chart_filters_query.filter(
                    Chart.kind.in_(chart_kind)
                )

            intersect_queries.append(chart_filters_query)

        if ChartSearchColumns.Title.value in chart_search_columns:
            chart_subquery = Chart.query.filter(
                Chart.title.ilike(f"%{search_term}%")
            ).subquery()

            chart_query = cls.query.with_entities(Chart.id.label("id")).join(
                chart_subquery, chart_subquery.c.id == Chart.id
            )

            union_queries.append(chart_query)

        if not report_search_columns and not filter:
            if intersect_queries or union_queries:
                query = query.filter(
                    Chart.id.in_(intersect(*intersect_queries, *union_queries))
                )

            query = query.order_by(cls.get_order_by(sort))

            logger.debug(
                f"Chart Search Query: "
                f"{query.statement.compile(compile_kwargs=dict(literal_binds=True, dialect=postgresql)).string}"
            )

            return query

        match (datasource_kind):
            case ReportKind.Report.value:
                if report_search_columns:
                    filter["search_columns"] = report_search_columns
                    filter["search_term"] = search_term
                    report_subquery = Report.get_all_search_results(
                        property_id,
                        dict(
                            search_columns=report_search_columns,
                            search_term=search_term,
                        ),
                        g.user,
                    ).subquery()
                    report_filter_query = Report.get_all(
                        property_id,
                        {
                            key: value
                            for key, value in filter.items()
                            if key not in ["search_columns", "search_term"]
                        },
                        g.user,
                    ).subquery()
                else:
                    report_subquery = Report.get_all(
                        property_id,
                        dict(
                            search_columns=report_search_columns,
                            search_term=search_term,
                        ),
                        g.user,
                    ).subquery()
                    report_filter_query = Report.get_all(
                        property_id, filter, g.user
                    ).subquery()
                report_union_query = cls.query.with_entities(Chart.id.label("id")).join(
                    report_subquery, report_subquery.c.id == Chart.report_id
                )
                report_intersect_query = cls.query.with_entities(
                    Chart.id.label("id")
                ).join(report_filter_query, report_filter_query.c.id == Chart.report_id)
                union_queries.append(report_union_query)
                intersect_queries.append(report_intersect_query)
                combined_reports_query = (
                    union(*union_queries)
                    if len(union_queries) > 1
                    else report_union_query
                )
                combined_query = (
                    intersect(*intersect_queries, combined_reports_query)
                    if intersect_queries
                    else combined_reports_query
                )

            case ReportKind.StockReport.value:
                if report_search_columns:
                    filter["search_columns"] = report_search_columns
                    filter["search_term"] = search_term
                    stock_report_subquery = (
                        StockReport.get_all_search_results_with_rules(
                            g.user,
                            property_id,
                            dict(
                                search_columns=report_search_columns,
                                search_term=search_term,
                            ),
                        ).subquery()
                    )
                    stock_report_filter_query = StockReport.get_all_with_rules(
                        g.user,
                        property_id,
                        {
                            key: value
                            for key, value in filter.items()
                            if key != "search_columns" and key != "search_term"
                        },
                    ).subquery()
                else:
                    stock_report_subquery = StockReport.query.subquery()
                    stock_report_filter_query = StockReport.get_all_with_rules(
                        g.user, property_id, filter
                    ).subquery()
                stock_report_union_query = cls.query.with_entities(
                    Chart.id.label("id")
                ).join(
                    stock_report_subquery,
                    stock_report_subquery.c.id == Chart.stock_report_id,
                )
                stock_report_intersect_query = cls.query.with_entities(
                    Chart.id.label("id")
                ).join(
                    stock_report_filter_query,
                    stock_report_filter_query.c.id == Chart.stock_report_id,
                )
                union_queries.append(stock_report_union_query)
                intersect_queries.append(stock_report_intersect_query)
                combined_reports_query = (
                    union(*union_queries)
                    if len(union_queries) > 1
                    else stock_report_union_query
                )
                combined_query = (
                    intersect(*intersect_queries, combined_reports_query)
                    if intersect_queries
                    else combined_reports_query
                )

            case None | _:
                if report_search_columns:
                    filter["search_columns"] = report_search_columns
                    filter["search_term"] = search_term
                    report_subquery = Report.get_all_search_results(
                        property_id,
                        filter,
                        g.user,
                    ).subquery()
                    stock_report_subquery = (
                        StockReport.get_all_search_results_with_rules(
                            g.user,
                            property_id,
                            filter,
                        ).subquery()
                    )
                else:
                    report_subquery = Report.get_all(
                        property_id, filter, g.user
                    ).subquery()
                    stock_report_subquery = StockReport.get_all_with_rules(
                        g.user, property_id, filter
                    ).subquery()

                report_union_query = cls.query.with_entities(Chart.id.label("id")).join(
                    report_subquery, report_subquery.c.id == Chart.report_id
                )

                stock_report_union_query = cls.query.with_entities(
                    Chart.id.label("id")
                ).join(
                    stock_report_subquery,
                    stock_report_subquery.c.id == Chart.stock_report_id,
                )

                union_queries.append(report_union_query)
                union_queries.append(stock_report_union_query)
                combined_reports_union_query = union(*union_queries)

                combined_query = (
                    intersect(
                        *intersect_queries,
                        combined_reports_union_query,
                    )
                    if intersect_queries
                    else combined_reports_union_query
                )

        query = query.filter(Chart.id.in_(combined_query))

        query = query.order_by(cls.get_order_by(sort))

        logger.debug(
            f"Chart Search Query: "
            f"{query.statement.compile(compile_kwargs=dict(literal_binds=True, dialect=postgresql)).string}"
        )

        return query

    @classmethod
    def get_search_results(cls, query_args, sort_query, property_id):
        query = cls.get_all(query_args, sort_query, property_id)

        return query
