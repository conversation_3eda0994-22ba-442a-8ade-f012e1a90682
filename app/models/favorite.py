from datetime import datetime
from typing import List


from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Check<PERSON>onstraint, Column, Integer, String
from sqlalchemy.sql.schema import ForeignKey
from sqlalchemy.sql.sqltypes import DateTime


from app.common.constants.favorite_limits import MAX_FAVORITES
from app.common.database import db
from app.common.exceptions import InvalidUsage
from app.enums.favorite import FavoriteKind
from app.models.hub import Hub
from app.models.report import Report
from app.models.stock_report import StockReport


class Favorite(db.Model):
    __tablename__ = "favorite"
    __table_args__ = {"comment": "Favorite table with all the favorites"}

    id = Column(Integer, primary_key=True, comment="Primary key for each favorite")
    rank = Column(
        Integer,
        nullable=False,
        comment="Rank associated to the favorite in order to display the reports",
    )
    report_id = Column(
        BIGINT, ForeignKey("report.id"), nullable=True, comment="Report Id"
    )
    stock_report_id = Column(
        BIGINT, ForeignKey("stock_report.id"), nullable=True, comment="Stock Report ID"
    )
    hub_id = Column(BIGINT, ForeignKey("hub.id"), nullable=True, comment="Hub ID")
    kind = Column(
        String,
        nullable=False,
        comment="favorite kind, to know if the favorite is a hub or a report or a stock report",
    )
    property_id = Column(
        BIGINT, nullable=False, comment="Property that created the favorite"
    )
    user_id = Column(BIGINT, nullable=False, comment="User that created the favorite")
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        comment="Created at date of the favorite",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="Updated at date of the favorite",
    )

    CheckConstraint("0 < rank < 13", name="rank_between_1_and_12")
    CheckConstraint(
        (
            "(report_id is NULL AND stock_report_id IS NOT NULL AND hub_id IS NULL AND kind = 'stock_report')"
            "OR (report_id IS NOT NULL AND kind = 'report' AND stock_report_id IS NULL and hub_id IS NULL)"
            "OR (hub_id IS NOT NULL AND kind = 'hub' AND report_id IS NULL AND stock_report_id IS NULL)"
        ),
        name="check_entity_id_and_kind",
    )

    @classmethod
    def get_by_id_property_id_and_user_id(
        cls, id: int, property_id: int, user_id: int
    ) -> "Favorite":
        favorite = cls.query.filter(
            Favorite.id == id,
            Favorite.property_id == property_id,
            Favorite.user_id == user_id,
        ).first()

        if favorite is None:
            InvalidUsage.not_found(
                message=f"Favorite id: {id} does not exist for this property and user"
            )

        return favorite

    @classmethod
    def get_by_rank_property_id_and_user_id(
        cls, rank: int, property_id: int, user_id: int
    ) -> "Favorite":
        return cls.query.filter(
            Favorite.rank == rank,
            Favorite.property_id == property_id,
            Favorite.user_id == user_id,
        ).first()

    @classmethod
    def get_all_with_report_title_by_property_id_and_user_id(
        cls, property_id: int, user_id: int
    ) -> List["Favorite"]:
        return (
            cls.query.with_entities(
                Favorite.id,
                Favorite.rank,
                Favorite.report_id,
                Favorite.kind,
                Favorite.stock_report_id,
                Favorite.hub_id,
                StockReport.title.label("stock_report_title"),
                Report.title.label("report_title"),
                Hub.title.label("hub_title"),
            )
            .filter(Favorite.property_id == property_id, Favorite.user_id == user_id)
            .outerjoin(
                Report,
                Report.id == Favorite.report_id,
            )
            .outerjoin(
                StockReport,
                StockReport.id == Favorite.stock_report_id,
            )
            .outerjoin(
                Hub,
                Hub.id == Favorite.hub_id,
            )
            .order_by(Favorite.rank.asc(), Favorite.updated_at.desc())
            .all()
        )

    @classmethod
    def get_all_by_property_id_and_user_id(
        cls, property_id: int, user_id: int
    ) -> List["Favorite"]:
        return (
            cls.query.filter(
                Favorite.property_id == property_id, Favorite.user_id == user_id
            )
            .order_by(Favorite.rank.asc(), Favorite.updated_at.desc())
            .all()
        )

    @classmethod
    def is_entity_favorited(
        cls, entity_id: int, kind: str, property_id: int, user_id: int
    ) -> bool:
        match (kind):
            case FavoriteKind.StockReport.value:
                return bool(
                    cls.query.filter(
                        Favorite.stock_report_id == entity_id,
                        Favorite.kind == kind,
                        Favorite.property_id == property_id,
                        Favorite.user_id == user_id,
                    ).count()
                )
            case FavoriteKind.Report.value:
                return bool(
                    cls.query.filter(
                        Favorite.report_id == entity_id,
                        Favorite.kind == kind,
                        Favorite.property_id == property_id,
                        Favorite.user_id == user_id,
                    ).count()
                )
            case FavoriteKind.Hub.value:
                return bool(
                    cls.query.filter(
                        Favorite.hub_id == entity_id,
                        Favorite.kind == kind,
                        Favorite.property_id == property_id,
                        Favorite.user_id == user_id,
                    ).count()
                )

    @classmethod
    def is_max_favorited(cls, property_id: int, user_id: int) -> bool:
        return (
            cls.query.filter(
                Favorite.property_id == property_id, Favorite.user_id == user_id
            ).count()
            == MAX_FAVORITES
        )
