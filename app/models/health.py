from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String


from app.common.database import db


class Health(db.Model):
    __tablename__ = "health"
    __table_args__ = {"comment": "Health variables table"}

    name = Column(
        String(length=60),
        primary_key=True,
        nullable=False,
        comment="Name of health check",
    )
    status = Column(
        Boolean,
        nullable=False,
        comment="Status (enabled=true/disabled=false) for each check",
    )

    @classmethod
    def get_by_name(cls, name: str):
        return cls.query.filter(Health.name == name).first()
