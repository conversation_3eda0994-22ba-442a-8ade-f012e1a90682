import datetime
from typing import Optional

from flask import g

from sqlalchemy import (
    ARRAY,
    BIGINT,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    JSON,
    String,
    UniqueConstraint,
)
from sqlalchemy.orm import aliased, relationship
from sqlalchemy.sql.schema import Table

from app.common.cache import TIMEOUT_MINUTE, cache
from app.common.database import db
from app.common.exceptions import InvalidUsage
from app.common.keys import unpermitted_report_ids_key
from app.common.mixins.build_search_query import BuildSearchQuery
from app.common.mixins.custom_cdf_mixin import CustomCdfMixin
from app.common.mixins.sort_mixin import SortMixin
from app.common.user import User
from app.models.schedule import schedule_report_association
from app.reports.report_type import ReportType
from app.services.user_service import UserService


ReportTag = Table(
    "report_tag",
    db.Model.metadata,
    Column(
        "report_id",
        ForeignKey("report.id"),
        primary_key=True,
        comment="Foreign key to the report table id",
    ),
    <PERSON>umn(
        "tag_id",
        <PERSON><PERSON><PERSON>("tag.id"),
        primary_key=True,
        comment="Foreign key to the tag table id",
    ),
    comment="Report tag with the many-to-many relation between the table report and tag",
)


class Report(BuildSearchQuery, SortMixin, db.Model):
    __tablename__ = "report"
    __table_args__ = {"comment": "Report table with all reports"}

    id = Column(
        BIGINT,
        primary_key=True,
        comment="Primary key for each report",
        autoincrement=True,
    )
    title = Column(String, nullable=False, comment="Title of each report")
    description = Column(String, comment="Description of each report")
    user_id = Column(BIGINT, nullable=False, comment="User that created the report")
    property_id = Column(
        BIGINT, nullable=False, comment="Property that created the report"
    )
    property_ids = Column(
        ARRAY(BIGINT),
        nullable=True,
        comment="Property that the report belongs to for multi property",
    )
    columns = Column(
        JSON, nullable=False, comment="Columns of report as list of objects"
    )
    filters = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Filters of report as list of objects",
    )
    group_rows = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Group rows of report as list of objects",
    )
    group_columns = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Group columns of report as list of objects",
    )
    sort = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Sort of report as list of objects",
    )
    settings = Column(
        JSON(none_as_null=True), nullable=True, comment="Settings of report as object"
    )
    dataset_id = Column(
        Integer,
        ForeignKey("dataset.id"),
        nullable=False,
        comment="Foreign key to the dataset table id",
    )
    folder_id = Column(
        BIGINT,
        ForeignKey("folder.id"),
        nullable=True,
        comment="Foreign key to the folder table id",
    )
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the report",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the report",
    )

    dataset = relationship("Dataset", uselist=False, backref="reports")
    custom_cdfs = relationship(
        "ReportCustomCdf", backref="report", cascade="all, delete"
    )
    custom_field_cdfs = relationship(
        "ReportCustomFieldCdf", backref="report", cascade="all, delete"
    )
    tags = relationship("Tag", backref="reports", secondary=ReportTag)
    schedules = relationship(
        "Schedule",
        secondary=schedule_report_association,
        back_populates="reports",
        uselist=True,
    )
    favorites = relationship("Favorite", cascade="all, delete")
    periods = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Periods of report as list of objects",
    )
    formats = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Formatting options of the report",
    )
    charts = relationship("Chart", back_populates="report")
    comparisons = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Comparisons of report as list of objects",
    )

    def __init__(self, user_id, **kwargs):
        db.Model.__init__(self, user_id=user_id, **kwargs)

    @property
    def created_by(self):
        return UserService.get_created_by(self.user_id)

    @property
    def type(self):
        return ReportType(
            self.group_rows, self.group_columns, self.periods, self.comparisons
        ).type

    @classmethod
    def get_by_id(cls, report_id: int):
        return cls.query.get_or_404(report_id)

    @classmethod
    def get_by_id_and_property_id(cls, report_id: int, property_id: int):
        report = cls.query.filter(
            Report.id == report_id, Report.property_id == property_id
        ).first()

        if report is None:
            InvalidUsage.not_found(
                message=f"Report id: {report_id} does not exist for this property"
            )

        return report

    @classmethod
    def is_report_tagged(cls, report_id, tag_id) -> bool:
        return bool(
            cls.query.filter(
                ReportTag.c.report_id == report_id,
                ReportTag.c.tag_id == tag_id,
                Report.id == ReportTag.c.report_id,
            ).count()
        )

    @classmethod
    def are_reports_assign_to_folder(cls, folder_id) -> bool:
        return bool(cls.query.filter(Report.folder_id == folder_id).count())

    @classmethod
    def get_all_sorted(
        cls,
        property_id: int,
        filters: dict,
        sort: dict,
        user: User,
        partial_search: bool = False,
    ):
        reports = cls.get_all(property_id, filters, user, partial_search)
        return reports.order_by(cls.get_order_by(sort))

    @classmethod
    def get_all(
        cls,
        property_id: int,
        filters: dict,
        user: User,
        partial_search: bool = False,
        ids_only=False,
    ):
        """List of reports per property id

        Get a list of all the created reports on a property. If partial_search is True
        it will look using a ILIKE query over the supported resources
        """
        ids = filters.get("ids")
        title = filters.get("title")
        description = filters.get("description")
        dataset_ids = filters.get("dataset_ids")
        folder_ids = filters.get("folder_ids")
        tag_ids = filters.get("tag_ids")
        user_ids = filters.get("user_ids")

        if ids_only:
            reports = cls.query.with_entities(Report.id.label("id")).filter(
                Report.property_id == property_id
            )

        else:
            reports = cls.query.filter(Report.property_id == property_id)

        if not g.user.admin:
            ids_to_omit = cls.get_unpermitted_report_ids_for_user_property_ids(
                property_id, g.property_ids
            )
            reports = reports.filter(~Report.id.in_(ids_to_omit)).filter(
                Report.dataset_id.in_(user.enabled_datasets)
            )

        if ids:
            reports = reports.filter(Report.id.in_(ids))

        if title:
            reports = (
                reports.filter(Report.title.ilike(f"%{title}%"))
                if partial_search
                else reports.filter(Report.title == title)
            )

        if description is not None:
            reports = reports.filter(Report.description == description)

        if dataset_ids:
            reports = reports.filter(Report.dataset_id.in_(dataset_ids))

        if folder_ids:
            reports = reports.filter(Report.folder_id.in_(folder_ids))

        if user_ids:
            reports = reports.filter(Report.user_id.in_(user_ids))

        if tag_ids:
            tag_id_subquery = (
                cls.query.with_entities(Report.id)
                .join(ReportTag, ReportTag.c.report_id == Report.id)
                .filter(ReportTag.c.tag_id.in_(tag_ids))
                .group_by(Report.id)
                .subquery()
            )

            reports = reports.join(tag_id_subquery, tag_id_subquery.c.id == Report.id)

        return reports

    @classmethod
    def get_all_search_results(
        cls, property_id: int, params: dict, user: User, sort: Optional[str] = None
    ):
        search_columns = params.get("search_columns")
        search_term = params.get("search_term")
        report_ids = None
        for search_column in search_columns:
            search_column_report = Report.get_all(property_id, params, user, True, True)
            search_column_report = search_column_report.filter(
                getattr(Report, search_column).ilike(f"%{search_term}%")
            )
            if not report_ids:
                report_ids = search_column_report
            else:
                report_ids = report_ids.union(search_column_report)

        report_ids_aliased = aliased(report_ids.subquery())
        reports = cls.query.join(
            report_ids_aliased, Report.id == report_ids_aliased.c.id
        )

        if sort:
            reports = reports.order_by(cls.get_order_by(sort))
        return reports

    @classmethod
    def get_search_results(cls, property_id: int, params: dict, user: User):
        """Get reports by search criteria"""
        reports = cls.query.filter(Report.property_id == property_id)
        if not user.admin:
            ids_to_omit = cls.get_unpermitted_report_ids_for_user_property_ids(
                property_id, g.property_ids
            )
            reports = reports.filter(~Report.id.in_(ids_to_omit)).filter(
                Report.dataset_id.in_(user.enabled_datasets)
            )
        reports = cls.build_search_query(reports, params, Report)

        return reports

    @classmethod
    def get_unpermitted_report_ids_for_user_property_ids(
        cls, property_id: int, user_property_ids: list[int]
    ) -> list[int]:
        """Gets a list of report ids for this property_id that have property_ids the user is not permitted to view

        <@: Operator on PostgreSQL that will check if the first array is contained by the second
        https://www.postgresql.org/docs/current/functions-array.html

        Args:
            property_id (int): user x_property_id header
            user_property_ids (list[int]): list of property_ids from users jwt token

        Returns:
            list[int]: list of report ids for with property_id equal to uses x_property_id, but property_ids not in user_property_ids
        """
        cache_key = unpermitted_report_ids_key(property_id, user_property_ids)
        unpermitted_report_ids = cache.get(cache_key)
        unpermitted_report_ids = None

        if unpermitted_report_ids is not None:
            return unpermitted_report_ids

        results = (
            db.session.query(Report)
            .filter(
                Report.property_id == property_id,
                ~Report.property_ids.op("<@")(user_property_ids),
            )
            .all()
        )

        ids = [report.id for report in results]
        cache.set(cache_key, ids, timeout=TIMEOUT_MINUTE)
        return ids


class ReportCustomCdf(CustomCdfMixin, db.Model):
    __tablename__ = "report_custom_cdf"
    __table_args__ = (
        UniqueConstraint("report_id", "column"),
        {"comment": "Report Custom CDF table with supported custom cdfs per report"},
    )

    report_id = Column(
        Integer, ForeignKey("report.id"), comment="Foreign key to the report table id"
    )
    format = Column(String, nullable=True, comment="Format of the cdf")


class ReportCustomFieldCdf(db.Model):
    __tablename__ = "report_custom_field_cdf"
    __table_args__ = (
        UniqueConstraint("report_id", "column"),
        {
            "comment": "Report Custom Field CDF table with supported custom cdfs per report"
        },
    )

    # Single primary key (if intended, otherwise keep composite PK)
    id = Column(
        BIGINT,
        primary_key=True,
        autoincrement=True,
        comment="Primary key for each report custom cdf",
    )
    name = Column(String, comment="Name of the custom field")
    column = Column(
        String, comment="Column of the custom field"
    )  # Remove primary_key=True if not needed
    report_id = Column(
        Integer, ForeignKey("report.id"), comment="Foreign key to the report table id"
    )

    # Relationship to ReportCustomFieldCdfProperty
    properties = relationship(
        "ReportCustomFieldCdfProperty",
        back_populates="report_custom_field_cdf",
        cascade="all, delete-orphan, save-update",
    )


class ReportCustomFieldCdfProperty(db.Model):
    __tablename__ = "report_custom_field_cdf_property"
    __table_args__ = (
        UniqueConstraint("report_custom_field_cdf_id", "property_id"),
        {
            "comment": "Report Custom Field CDF Property table with custom field mappings for each property"
        },
    )

    internal_name = Column(
        String,
        nullable=False,
        comment="Internal name of the custom field for the property",
    )
    property_id = Column(
        BIGINT, nullable=False, comment="Property id of the property", primary_key=True
    )

    report_custom_field_cdf_id = Column(
        BIGINT,
        ForeignKey("report_custom_field_cdf.id"),
        nullable=False,
        primary_key=True,  # Assuming this is part of a composite primary key
        comment="Foreign key to the report_custom_field_cdf table id",
    )

    report_custom_field_cdf = relationship(
        ReportCustomFieldCdf, back_populates="properties"
    )
