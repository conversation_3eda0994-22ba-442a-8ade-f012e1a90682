import datetime
from operator import or_
from typing import Optional

from sqlalchemy import (
    BIGIN<PERSON>,
    Boolean,
    Column,
    Date<PERSON>ime,
    Foreign<PERSON>ey,
    Integer,
    JSON,
    String,
    Table,
    UniqueConstraint,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import aliased, relationship

from app.common.database import db
from app.common.mixins.build_search_query import BuildSearchQuery
from app.common.mixins.custom_cdf_mixin import CustomCdfMixin
from app.common.mixins.sort_mixin import SortMixin
from app.common.user import User
from app.models.stock_tag import StockTag
from app.reports.report_type import ReportType
from app.services.property_feature_service import PropertyFeatureService
from app.services.property_service import PropertyService
from app.services.user_service import UserService

StockReportTag = Table(
    "stock_report_tag",
    db.Model.metadata,
    Column(
        "stock_report_id",
        ForeignKey("stock_report.id"),
        primary_key=True,
        comment="Foreign key to the stock report table id",
    ),
    Column(
        "stock_tag_id",
        <PERSON><PERSON><PERSON>("stock_tag.id"),
        primary_key=True,
        comment="Foreign key to the stock_tag table id",
    ),
    comment="Stock Report tag with the many-to-many relation between the table stock_report and stock_tag",
)


class StockReportFeature(db.Model):
    __tablename__ = "stock_report_feature"
    __table_args__ = {
        "comment": "Stock Report Feature table with the many-to-many relation between the stock_report and feature"
    }

    stock_report_id = Column(
        ForeignKey("stock_report.id"),
        primary_key=True,
        comment="Foreign key to the stock report table id",
    )
    feature_id = Column(
        String,
        primary_key=True,
        comment="Foreign key to the feature table id",
    )


class StockReportProperty(db.Model):
    __tablename__ = "stock_report_property"
    __table_args__ = {
        "comment": "Stock Report Property table with one-to-many relation between the stock_report and property"
    }

    stock_report_id = Column(
        ForeignKey("stock_report.id"),
        primary_key=True,
        comment="Foreign key to the stock report table id",
    )
    property_id = Column(
        BIGINT,
        primary_key=True,
        comment="Property ID",
    )


class StockReportCountry(db.Model):
    __tablename__ = "stock_report_country"
    __table_args__ = {
        "comment": "Stock Report Country table with one-to-many relation between the stock_report and country"
    }

    stock_report_id = Column(
        ForeignKey("stock_report.id"),
        primary_key=True,
        comment="Foreign key to the stock report table id",
    )
    country_code = Column(
        String,
        primary_key=True,
        comment="Country",
    )


class StockReportPropertyType(db.Model):
    __tablename__ = "stock_report_property_type"
    __table_args__ = {
        "comment": "Stock Report Property Type table with one-to-many relation between the stock_report and the property type"
    }

    stock_report_id = Column(
        ForeignKey("stock_report.id"),
        primary_key=True,
        comment="Foreign key to the stock report table id",
    )
    property_type = Column(String, primary_key=True, comment="Property Type")


class StockReport(BuildSearchQuery, SortMixin, db.Model):
    __tablename__ = "stock_report"
    __table_args__ = {"comment": "Stock Report table with all stock reports"}

    id = Column(BIGINT, primary_key=True, comment="Primary key for each stock report")
    title = Column(String, nullable=False, comment="Title of each stock report")
    description = Column(String, comment="Description of each stock report")
    columns = Column(
        JSON, nullable=False, comment="Columns of stock report as list of objects"
    )
    filters = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Filters of stock report as list of objects",
    )
    group_rows = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Group rows of stock report as list of objects",
    )
    group_columns = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Group columns of stock report as list of objects",
    )
    sort = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Sort of stock report as list of objects",
    )
    settings = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Settings of stock report as object",
    )
    dataset_id = Column(
        Integer,
        ForeignKey("dataset.id"),
        nullable=False,
        comment="Foreign key to the dataset table id",
    )
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        comment="Created at date of the stock report",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
        comment="Updated at date of the report",
    )

    user_id = Column(
        BIGINT,
        nullable=False,
        default=0,
        comment="User that created the current stock report",
    )

    published = Column(
        Boolean, nullable=False, default=True, comment="Published status of the report"
    )

    dataset = relationship("Dataset", uselist=False, backref="stock_reports")

    features = relationship(
        "StockReportFeature",
        backref="stock_report",
        uselist=True,
        cascade="all, delete",
    )
    properties = relationship(
        "StockReportProperty",
        uselist=True,
        backref="stock_report",
        cascade="all, delete",
    )
    countries = relationship(
        "StockReportCountry",
        uselist=True,
        backref="stock_report",
        cascade="all, delete",
    )

    property_types = relationship(
        "StockReportPropertyType",
        uselist=True,
        backref="stock_report",
        cascade="all, delete",
    )
    favorites = relationship("Favorite", cascade="all, delete")
    custom_cdfs = relationship(
        "StockReportCustomCdf", backref="stock_report", cascade="all, delete"
    )

    periods = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Periods of report as list of objects",
    )

    formats = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Formatting options of the report",
    )

    folder_id = Column(
        BIGINT,
        ForeignKey("stock_report_folder.id"),
        nullable=True,
        comment="Foreign key to the Stock Report folder table id",
    )
    charts = relationship("Chart", back_populates="stock_report")
    comparisons = Column(
        JSON(none_as_null=True),
        nullable=True,
        comment="Comparisons of report as list of objects",
    )
    tags = relationship("StockTag", backref="reports", secondary=StockReportTag)

    @property
    def type(self):
        return ReportType(
            self.group_rows, self.group_columns, self.periods, self.comparisons
        ).type

    @property
    def property_ids(self):
        return self.__property_ids

    @property_ids.setter
    def property_ids(self, property_ids):
        self.__property_ids = property_ids

    @classmethod
    def get_by_id(cls, report_id: int) -> "StockReport":
        stock_report = cls.query.filter(StockReport.id == report_id).first()
        return stock_report

    @classmethod
    def get_by_id_with_rules(
        cls, report_id: int, user: User, property_id: int
    ) -> "StockReport":
        stock_report = cls.get_query_with_rules(user, property_id)
        stock_report = stock_report.filter(StockReport.id == report_id).first()
        return stock_report

    @classmethod
    def get_all_sorted(
        cls,
        filters: dict,
        sort: str,
        partial_search: bool = False,
        enabled_datasets: list[int] = [],
    ):
        stock_reports = cls.get_all(filters, partial_search, False, enabled_datasets)
        return stock_reports.order_by(cls.get_order_by(sort))

    @classmethod
    def get_all(
        cls,
        filters: dict,
        partial_search: bool = False,
        ids_only: bool = False,
        enabled_datasets: list[int] = [],
    ):
        if filters is not None:
            ids = filters.get("ids")
            title = filters.get("title")
            description = filters.get("description")
            dataset_ids = [
                dataset_id
                for dataset_id in enabled_datasets
                if dataset_id in filters.get("dataset_ids", [])
            ]
            folder_ids = filters.get("folder_ids")
            stock_tag_ids = filters.get("stock_tag_ids")
            published = filters.get("published")

        stock_reports = cls.query
        if ids_only:
            stock_reports = stock_reports.with_entities(StockReport.id.label("id"))
        if ids:
            stock_reports = stock_reports.filter(StockReport.id.in_(ids))

        if title:
            stock_reports = (
                stock_reports.filter(StockReport.title.ilike(f"%{title}%"))
                if partial_search
                else stock_reports.filter(StockReport.title == title)
            )

        if description is not None:
            stock_reports = stock_reports.filter(StockReport.description == description)

        if dataset_ids:
            stock_reports = stock_reports.filter(
                StockReport.dataset_id.in_(dataset_ids)
            )

        if folder_ids:
            stock_reports = stock_reports.filter(StockReport.folder_id.in_(folder_ids))

        if stock_tag_ids:
            stock_reports = stock_reports.filter(
                StockReport.tags.any(StockTag.id.in_(stock_tag_ids))
            )

        if published is not None:
            stock_reports = stock_reports.filter(StockReport.published == published)

        return stock_reports

    @classmethod
    def get_all_sorted_with_rules(
        cls,
        user: User,
        property_id: int,
        filters: dict,
        sort: str,
        partial_search: bool = False,
        property_country_only: bool = False,
    ):
        stock_reports = cls.get_all_with_rules(
            user, property_id, filters, partial_search, property_country_only
        )
        return stock_reports.order_by(cls.get_order_by(sort))

    @classmethod
    def get_all_with_rules(
        cls,
        user: User,
        property_id: int,
        filters: dict,
        partial_search: bool = False,
        property_country_only: bool = False,
        ids_only: bool = False,
    ):
        ids = filters.get("ids")
        title = filters.get("title")
        description = filters.get("description")
        dataset_ids = filters.get("dataset_ids")
        folder_ids = filters.get("folder_ids")
        stock_tag_ids = filters.get("stock_tag_ids")

        stock_reports = cls.get_query_with_rules(
            user, property_id, property_country_only, ids_only
        )

        if ids:
            stock_reports = stock_reports.filter(StockReport.id.in_(ids))

        if title:
            stock_reports = (
                stock_reports.filter(StockReport.title.ilike(f"%{title}%"))
                if partial_search
                else stock_reports.filter(StockReport.title == title)
            )

        if description is not None:
            stock_reports = stock_reports.filter(StockReport.description == description)

        if dataset_ids:
            stock_reports = stock_reports.filter(
                StockReport.dataset_id.in_(dataset_ids)
            )

        if user.enabled_datasets:
            stock_reports = stock_reports.filter(
                StockReport.dataset_id.in_(user.enabled_datasets)
            )

        if folder_ids:
            stock_reports = stock_reports.filter(StockReport.folder_id.in_(folder_ids))

        if stock_tag_ids:
            stock_reports = stock_reports.filter(
                StockReport.tags.any(StockTag.id.in_(stock_tag_ids))
            )

        return stock_reports

    @classmethod
    def get_search_results(cls, user: User, property_id: int, params: dict):
        """Get reports by search criteria"""
        stock_reports = cls.get_query_with_rules(user, property_id)
        stock_reports = cls.build_search_query(stock_reports, params, StockReport)

        return stock_reports

    @classmethod
    def get_all_search_results(
        cls,
        property_id: int,
        filters: dict,
        sort: dict,
        enabled_datasets: list[int] = [],
    ):
        search_columns = filters.get("search_columns")
        search_term = filters.get("search_term")
        stock_report_ids = None
        for search_column in search_columns:
            search_column_report = StockReport.get_all(
                filters,
                partial_search=True,
                ids_only=True,
                enabled_datasets=enabled_datasets,
            )
            search_column_report = search_column_report.filter(
                getattr(StockReport, search_column).ilike(f"%{search_term}%")
            )
            if not stock_report_ids:
                stock_report_ids = search_column_report
            else:
                stock_report_ids = stock_report_ids.union(search_column_report)

        stock_report_ids_aliased = aliased(stock_report_ids.subquery())
        stock_reports = cls.query.join(
            stock_report_ids_aliased, StockReport.id == stock_report_ids_aliased.c.id
        )

        return stock_reports.order_by(cls.get_order_by(sort))

    @classmethod
    def get_all_search_results_with_rules(
        cls,
        user: User,
        property_id: int,
        params: dict,
        sort: Optional[str] = None,
        partial_search: bool = False,
        property_country_only: bool = False,
    ):
        search_columns = params.get("search_columns")
        search_term = params.get("search_term")
        stock_report_ids = None
        for search_column in search_columns:
            search_column_report = StockReport.get_all_with_rules(
                user,
                property_id,
                params,
                partial_search,
                property_country_only,
                ids_only=True,
            )
            search_column_report = search_column_report.filter(
                getattr(StockReport, search_column).ilike(f"%{search_term}%")
            )
            if not stock_report_ids:
                stock_report_ids = search_column_report
            else:
                stock_report_ids = stock_report_ids.union(search_column_report)

        stock_report_ids_aliased = aliased(stock_report_ids.subquery())
        stock_reports = cls.query.join(
            stock_report_ids_aliased, StockReport.id == stock_report_ids_aliased.c.id
        )

        if sort:
            stock_reports = stock_reports.order_by(cls.get_order_by(sort))
        return stock_reports

    @classmethod
    def get_query_with_rules(
        cls,
        user: User,
        property_id: int,
        property_country_only: bool = False,
        ids_only: bool = False,
    ):
        property_feature_ids = PropertyFeatureService.get_all_by_property_id(
            property_id
        )
        property_type = PropertyService.get_property_type(property_id)

        # get the country for this property
        country_code = PropertyService.get_property_country_code(property_id)

        stock_report_ids = (
            cls.query.with_entities(StockReport.id.label("id"))
            .filter(StockReport.published)
            .outerjoin(
                StockReportProperty,
                StockReportProperty.stock_report_id == StockReport.id,
            )
            .outerjoin(
                StockReportFeature,
                StockReportFeature.stock_report_id == StockReport.id,
            )
            .outerjoin(
                StockReportCountry,
                StockReportCountry.stock_report_id == StockReport.id,
            )
            .outerjoin(
                StockReportPropertyType,
                StockReportPropertyType.stock_report_id == StockReport.id,
            )
            .filter(
                or_(
                    StockReportProperty.property_id.__eq__(property_id),
                    StockReportProperty.property_id.is_(None),
                )
            )
            .filter(
                or_(
                    StockReportFeature.feature_id.in_(property_feature_ids),
                    StockReportFeature.feature_id.is_(None),
                )
            )
            .filter(
                or_(
                    StockReportPropertyType.property_type.__eq__(property_type),
                    StockReportPropertyType.property_type.is_(None),
                )
            )
        )

        if property_country_only:
            stock_report_ids = stock_report_ids.filter(
                StockReportCountry.country_code.__eq__(country_code),
            )
        else:
            stock_report_ids = stock_report_ids.filter(
                or_(
                    StockReportCountry.country_code.__eq__(country_code),
                    StockReportCountry.country_code.is_(None),
                )
            )

        if user.enabled_datasets:
            stock_report_ids = stock_report_ids.filter(
                StockReport.dataset_id.in_(user.enabled_datasets)
            )

        if ids_only:
            return stock_report_ids

        stock_report_query = cls.query.filter(StockReport.id.in_(stock_report_ids))

        return stock_report_query

    @classmethod
    def are_reports_assign_to_folder(cls, folder_id) -> bool:
        return bool(cls.query.filter(StockReport.folder_id == folder_id).count())

    @classmethod
    def is_stock_report_tagged(cls, stock_report_id, stock_tag_id) -> bool:
        return bool(
            cls.query.filter(
                StockReportTag.c.stock_report_id == stock_report_id,
                StockReportTag.c.stock_tag_id == stock_tag_id,
                StockReport.id == StockReportTag.c.stock_report_id,
            ).count()
        )

    @classmethod
    def get_by_stock_tag_id(cls, stock_tag_id):
        return cls.query.filter(StockReport.tags.any(StockTag.id == stock_tag_id)).all()


class StockReportCustomCdf(CustomCdfMixin, db.Model):
    __tablename__ = "stock_report_custom_cdf"
    __table_args__ = (
        UniqueConstraint("report_id", "column"),
        {
            "comment": "Stock Report Custom CDF table with supported custom cdfs per report"
        },
    )

    report_id = Column(
        Integer,
        ForeignKey("stock_report.id"),
        comment="Foreign key to the stock report table id",
    )

    format = Column(
        String,
        nullable=True,
        comment="Format of the cdf",
    )


class StockReportAdmin(StockReport):
    __tablename__ = "stock_report"

    @hybrid_property
    def created_by(self):
        return UserService.get_created_by(self.user_id)
