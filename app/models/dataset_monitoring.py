from sqlalchemy import Column, Integer, String, TIMESTAMP

from app.common.database import db


class DatasetMonitoring(db.Model):
    __bind_key__ = "updated_at_views"
    __tablename__ = "audit_log_vue"
    __table_args__ = {"schema": "insights"}

    property_id = Column(Integer, primary_key=True, comment="Property ID")
    organization_id = Column(Integer, comment="Organization ID")
    reporting_table = Column(String, comment="Name of the dataset")
    freshness_utc = Column(TIMESTAMP, comment="Latest updated at")
