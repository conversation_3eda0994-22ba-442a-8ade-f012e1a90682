from sqlalchemy import BIGINT, Column, INTEGER, VARCHAR

from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class GuestCustomFieldsMetadataVueMixin(object):

    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )

    internal_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Internal Name",
            description="The CDF column a user will use in an api request for this custom field    .",
            kind=CdfKind.String,
        ),
        primary_key=True,
    )
    field_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Field Name",
            description="The name a user will use in an api request for this custom field    .",
            kind=CdfKind.String,
        ),
    )
    creation_order = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Property,
            name="Creation order",
            description="The creation order of this custom field    .",
            kind=CdfKind.String,
        ),
    )


class GuestCustomFieldsMetadataVue(GuestCustomFieldsMetadataVueMixin, db.Model):
    __tablename__ = "guest_custom_fields_metadata_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class GuestCustomFieldsMetadataVueFF(GuestCustomFieldsMetadataVueMixin, db.Model):
    __tablename__ = "guest_custom_fields_metadata_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
