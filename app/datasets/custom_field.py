from flask import g

from sqlalchemy.orm import class_mapper

from app.common.constants.custom_fields import (
    GUEST_CUSTOM_FIELDS_LABEL,
    RESERVATION_CUSTOM_FIELDS_LABEL,
)
from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.multi_level import Join<PERSON>unction
from app.custom_fields.guest_custom_fields import (
    GuestCustomFieldsFFView,
    GuestCustomFieldsView,
)
from app.custom_fields.reservation_custom_fields import (
    ReservationCustomFieldsFFView,
    ReservationCustomFieldsView,
)
from app.datasets.guests import GuestsFFView, GuestsView
from app.datasets.reservations import ReservationsFFView, ReservationsView
from app.enums.custom_fields import CustomFields as CustomFieldsEnum
from app.services.launch_darkly_service import LaunchDarklyService


class CustomField:
    def __init__(self, kind: CustomFieldsEnum):
        self.kind = kind

    def __repr__(self):
        """Representation of Custom Fields class
        :return: string
        """
        return f"<Custom Field, kind={self.kind}>"

    @property
    def id(self):
        return self.kind.value

    @property
    def name(self):
        name = dict()
        name[CustomFieldsEnum.GuestCustomFieldsView] = GUEST_CUSTOM_FIELDS_LABEL
        name[
            CustomFieldsEnum.ReservationCustomFieldsView
        ] = RESERVATION_CUSTOM_FIELDS_LABEL
        return name[self.kind]

    @property
    def cdfs(self):
        cdfs = [
            dict(
                column=column.key,
                name=column.info["name"],
                description=column.info["description"],
                kind=column.info["kind"],
            )
            for column in list(self.model.__table__.columns)
            + list(class_mapper(self.model).iterate_properties)
            if bool(column.info)
            and not column.info.get("hide", False)
            and (
                "feature_flag" not in column.info
                or (
                    LaunchDarklyService.has_feature_flag(
                        column.info["feature_flag"], g.property_id
                    )
                    if hasattr(g, "property_id")
                    else False
                )
            )
        ]
        cdfs = [cdf for cdf in sorted(cdfs, key=lambda cdf: cdf["name"])]

        return cdfs

    @property
    def model(self):
        try:
            if (
                self.kind == CustomFieldsEnum.GuestCustomFieldsView
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.GuestDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    GuestCustomFieldsFFView
                    if LaunchDarklyService.has_feature_flag(
                        LaunchDarklyFeature.CustomFieldsCDFsDataFFView, g.property_id
                    )
                    else GuestCustomFieldsView
                )
            if (
                self.kind == CustomFieldsEnum.ReservationCustomFieldsView
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.ReservationDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    ReservationCustomFieldsFFView
                    if LaunchDarklyService.has_feature_flag(
                        LaunchDarklyFeature.CustomFieldsCDFsDataFFView, g.property_id
                    )
                    else ReservationCustomFieldsView
                )
        except:
            pass

        models = dict()
        models[CustomFieldsEnum.GuestCustomFieldsView] = (
            GuestCustomFieldsFFView
            if LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.CustomFieldsCDFsDataFFView, g.property_id
            )
            else GuestCustomFieldsView
        )
        models[CustomFieldsEnum.ReservationCustomFieldsView] = (
            ReservationCustomFieldsFFView
            if LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.CustomFieldsCDFsDataFFView, g.property_id
            )
            else ReservationCustomFieldsView
        )

        return models[self.kind]

    @property
    def join(self):
        try:
            if (
                self.kind == CustomFieldsEnum.GuestCustomFieldsView
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.GuestDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    (self.model.organization_id == GuestsFFView.organization_id)
                    & (self.model.property_id == GuestsFFView.property_id)
                    & (self.model.customer_id == GuestsFFView.customer_id)
                )
            if (
                self.kind == CustomFieldsEnum.ReservationCustomFieldsView
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.ReservationDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    (self.model.organization_id == ReservationsFFView.organization_id)
                    & (self.model.property_id == ReservationsFFView.property_id)
                    & (self.model.booking_id == ReservationsFFView.id)
                )

        except:
            pass

        join = dict()

        match self.kind:
            case CustomFieldsEnum.GuestCustomFieldsView:
                join[CustomFieldsEnum.GuestCustomFieldsView] = (
                    (self.model.organization_id == GuestsView.organization_id)
                    & (self.model.property_id == GuestsView.property_id)
                    & (self.model.customer_id == GuestsView.customer_id)
                )
            case CustomFieldsEnum.ReservationCustomFieldsView:
                join[CustomFieldsEnum.ReservationCustomFieldsView] = (
                    (self.model.organization_id == ReservationsView.organization_id)
                    & (self.model.property_id == ReservationsView.property_id)
                    & (self.model.booking_id == ReservationsView.id)
                )
        return join[self.kind]

    @property
    def join_function(self):
        join_functions = dict()
        join_functions[CustomFieldsEnum.GuestCustomFieldsView] = JoinFunction.LEFT.value
        join_functions[
            CustomFieldsEnum.ReservationCustomFieldsView
        ] = JoinFunction.LEFT.value

        return join_functions[self.kind]
