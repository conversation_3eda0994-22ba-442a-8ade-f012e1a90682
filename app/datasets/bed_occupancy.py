from sqlalchemy import (
    BIGINT,
    Case,
    Column,
    DATE,
    FLOAT,
    INTEGER,
    NUMERIC,
    VARCHAR,
    and_,
    case,
    cast,
    func,
    literal,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.orm import column_property, declared_attr

from app.common.constants.report_formats import EXCHANGE_RATE_SUFFIX
from app.common.constants.room_type_category import ROOM_TYPE_CATEGORIES
from app.common.database import db
from app.common.enums.features import LaunchDarklyFeature
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class BedOccupancyViewMixin(object):
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="A unique identifier assigned to the organization for tracking.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="A unique identifier assigned to the property for tracking.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property.",
            kind=CdfKind.String,
        ),
    )
    stay_date = Column(
        DATE,
        primary_key=True,
        info=dict(
            category=CdfCategory.Booking,
            name="Stay Date",
            description="The calendar date being observed for analysis.",
            kind=CdfKind.Date,
        ),
    )
    room_type_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type ID",
            description="The database ID associated with a room type. This can be useful when merging Cloudbeds data with data from other systems.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    room_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Type",
            description="The type of room, as categorized in MyFrontDesk.",
            kind=CdfKind.String,
        ),
    )
    room_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Name",
            description="The display name of a room.",
            kind=CdfKind.String,
        ),
    )
    room_type_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type Category",
            description="Shows if the room booked was either a private room or a shared room type.",
            kind=CdfKind.PickList,
            options=ROOM_TYPE_CATEGORIES,
        ),
    )
    booking_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation ID",
            description="Unique identifier for each record in the dataset.",
            kind=CdfKind.Identifier,
            hide=True,
        ),
    )
    booking_room_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Room ID",
            description="The ID of the room associated with the reservation.",
            kind=CdfKind.Number,
            hide=True,
        ),
    )
    adults_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Reservation,
            name="Room Adult Count",
            description="Total number of adults associated with the room.",
            kind=CdfKind.Number,
        ),
    )
    children_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Reservation,
            name="Room Child Count",
            description="Total number of children associated with the room.",
            kind=CdfKind.Number,
        ),
    )
    guest_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Guest Count",
            description="Total number of guests including adults and children.",
            kind=CdfKind.Number,
        ),
    )
    beds_capacity = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Bed Capacity",
            description="Total number of physical bed spaces that are available for sale at the property.",
            kind=CdfKind.Number,
        ),
    )
    out_of_service_beds_capacity = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Out of Service Bed Spaces",
            description="Total number of bed spaces marked as Out of Service due to the room marked as Out of Service.",
            kind=CdfKind.Number,
        ),
    )
    blocked_beds_capacity = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Blocked Bed Spaces",
            description="Total number of bed spaces marked as Blocked due to the room marked as Blocked.",
            kind=CdfKind.Number,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Number",
            description="The confirmation number that the CB System automatically assigns to a reservation and that uniquely identifies a reservation. "
            "Sometimes referred to as Reservation ID",
            kind=CdfKind.ReservationLink,
        ),
    )
    original_currency = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Original Currency",
            description="Currency used to complete the transaction.",
            kind=CdfKind.String,
        ),
    )
    room_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Room Rate",
            description="Total of all Room Rate transactions posted automatically for reservations per stay date.",
            kind=CdfKind.Currency,
        ),
    )
    additional_room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Other Room Revenue",
            description="Other revenue associated with the room posted by the user, including no show fees.",
            kind=CdfKind.Currency,
        ),
    )
    additional_room_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Additional Room Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of additional room revenue based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.additional_room_revenue
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.additional_room_revenue
            ),
        ),
    )
    total_room_revenue_adjustments = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Room Revenue Adjustments",
            description="Total of room revenue adjustments including manual room revenue adjustments.",
            kind=CdfKind.Currency,
        ),
    )
    total_room_revenue_adjustments_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total Room Revenue Adjustments {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of total room revenue adjustments based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.total_room_revenue_adjustments
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.total_room_revenue_adjustments
            ),
        ),
    )
    room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Room Revenue",
            description="Room Rate plus Other Room Revenue plus Room Revenue Adjustments.",
            kind=CdfKind.Currency,
        ),
    )
    room_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Room Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of room revenue based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.room_revenue
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.room_revenue
            ),
        ),
    )
    misc_income = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Miscellaneous Income",
            description="Total of miscellaneous income including cancellation fees and their adjustments.",
            kind=CdfKind.Currency,
        ),
    )
    misc_income_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Misc Income {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of misc income based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.misc_income
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.misc_income
            ),
        ),
    )
    non_room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Other Revenue",
            description="Revenue from items and services, add-ons and adjustments (excludes taxes and fees).",
            kind=CdfKind.Currency,
        ),
    )
    non_room_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Non Room Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of non room revenue based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.non_room_revenue
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.non_room_revenue
            ),
        ),
    )
    other_revenue_adjustments = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Other Revenue Adjustments",
            description="Adjustments for revenue from items and services, add-ons, etc.",
            kind=CdfKind.Currency,
        ),
    )
    other_revenue_adjustments_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Other Revenue Adjustments {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of other revenue adjustments based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.other_revenue_adjustments
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.other_revenue_adjustments
            ),
        ),
    )
    total_other_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Other Revenue",
            description="Total of other revenue not categorized as room revenue.",
            kind=CdfKind.Currency,
        ),
    )
    total_other_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total Other Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of total other revenue based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.total_other_revenue
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.total_other_revenue
            ),
        ),
    )
    total_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Revenue",
            description="Sum of total room revenue and total other revenue.",
            kind=CdfKind.Currency,
        ),
    )
    total_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of total revenue based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.total_revenue
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.total_revenue
            ),
        ),
    )
    room_taxes = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Taxes",
            description="Total of all taxes, inclusive and exclusive, and any tax adjustments.",
            kind=CdfKind.Currency,
        ),
    )
    room_taxes_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Room Taxes {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of room taxes based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.room_taxes
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.room_taxes
            ),
        ),
    )
    room_fees = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Fees",
            description="Total of all fees, inclusive and exclusive. Excludes cancellation or no-show fees.",
            kind=CdfKind.Currency,
        ),
    )
    room_fees_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Room Fees {EXCHANGE_RATE_SUFFIX}",
            description="Converted value of room fees based on historical exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.room_fees
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.room_fees
            ),
        ),
    )
    accommodation_kind = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Accommodation Kind",
            description="Indicates if the room is 'Physical' or 'Virtual' for properties using split inventory.",
            kind=CdfKind.PickList,
            options=("Physical", "Virtual"),
        ),
    )

    @declared_attr
    def average_bed_rate(cls):
        return column_property(
            cast(
                cast(cls.total_revenue, NUMERIC)
                / func.nullif(cast(cls.guest_count, NUMERIC), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="Average Bed Rate",
                description="Average rate per occupied bed, calculated as Total Room Revenue divided by Total Guest Count.",
                kind=CdfKind.DynamicCurrency,
            ),
        )

    @declared_attr
    def average_bed_rate_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.total_revenue, NUMERIC))
                / (
                    func.nullif(
                        (func.sum(cast(cls.guest_count, NUMERIC))),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="Average Bed Rate Summary",
                description="Average rate per occupied bed, calculated as Total Room Revenue divided by Total Guest Count.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def average_bed_rate_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    case(
                        (and_(*cases), cls.total_revenue),
                        else_=None,
                    ),
                    NUMERIC,
                )
                / func.nullif(
                    cast(
                        case(
                            (and_(*cases), cls.guest_count),
                            else_=None,
                        ),
                        NUMERIC,
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="Average Bed Rate Cases",
                description="Average rate per occupied bed, calculated as Total Room Revenue divided by Total Guest Count.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def average_bed_rate_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast((case((and_(*cases), cls.room_revenue), else_=None)), NUMERIC)
                )
                / func.nullif(
                    func.sum(
                        cast(
                            (case((and_(*cases), cls.guest_count), else_=None)),
                            NUMERIC,
                        )
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="Average Bed Rate Summary Cases",
                description="Average rate per occupied bed, calculated as Total Room Revenue divided by Total Guest Count.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    average_bed_rate_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Average Bed Rate {EXCHANGE_RATE_SUFFIX}",
            description="Average rate per occupied bed, adjusted by exchange rate.",
            kind=CdfKind.DynamicCurrency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                (
                    (
                        cast(cls.total_revenue, NUMERIC)
                        * func.cast(
                            cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                        )
                        if kwargs.get("currency")
                        else cast(cls.total_revenue, NUMERIC)
                    )
                    / func.nullif(cast(cls.guest_count, NUMERIC), 0)
                ),
                FLOAT,
            ),
        ),
    )

    average_bed_rate_converted_rate_summary = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Average Bed Rate Summary {EXCHANGE_RATE_SUFFIX}",
            description="Aggregated average bed rate adjusted by exchange rate.",
            kind=CdfKind.DynamicCurrency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            hide=True,
            function=lambda cls, **kwargs: cast(
                (
                    (
                        func.sum(
                            cast(cls.total_revenue, NUMERIC)
                            * func.cast(
                                cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                            )
                        )
                        if kwargs.get("currency")
                        else func.sum(cast(cls.total_revenue, NUMERIC))
                    )
                    / func.nullif(func.sum(cast(cls.guest_count, NUMERIC)), 0)
                ),
                FLOAT,
            ),
        ),
    )

    @hybrid_method
    def average_bed_rate_converted_rate_cases(cls, cases: list[Case], **kwargs):
        revenue = case((and_(*cases), cls.total_revenue), else_=None)
        guest_count = case((and_(*cases), cls.guest_count), else_=None)
        currency = kwargs.get("currency")

        return column_property(
            cast(
                (
                    cast(revenue, NUMERIC)
                    * func.cast(cls.historical_exchange_rates[currency], FLOAT)
                    if currency
                    else cast(revenue, NUMERIC)
                )
                / func.nullif(cast(guest_count, NUMERIC), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"Average Bed Rate {EXCHANGE_RATE_SUFFIX} Cases",
                description="Average rate per occupied bed with filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicCurrency,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def average_bed_rate_converted_rate_summary_cases(cls, cases: list[Case], **kwargs):
        revenue_sum = func.sum(
            cast(case((and_(*cases), cls.total_revenue), else_=None), NUMERIC)
        )
        guest_count_sum = func.sum(
            cast(case((and_(*cases), cls.guest_count), else_=None), NUMERIC)
        )
        currency = kwargs.get("currency")

        return column_property(
            cast(
                (
                    revenue_sum
                    * func.cast(cls.historical_exchange_rates[currency], FLOAT)
                    if currency
                    else revenue_sum
                )
                / func.nullif(guest_count_sum, 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"Average Bed Rate Summary {EXCHANGE_RATE_SUFFIX} Cases",
                description="Aggregated average bed rate with filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicCurrency,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @declared_attr
    def bed_occupancy(cls):
        return column_property(
            cast(
                cast(cls.guest_count, NUMERIC)
                / func.nullif(cast(cls.beds_capacity, NUMERIC), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Bed Occupancy",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are not removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
            ),
        )

    @declared_attr
    def bed_occupancy_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.guest_count, NUMERIC))
                / (
                    func.nullif(
                        (func.sum(cast(cls.beds_capacity, NUMERIC))),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Bed Occupancy Summary",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are not removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def bed_occupancy_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    case(
                        (and_(*cases), cls.guest_count),
                        else_=None,
                    ),
                    NUMERIC,
                )
                / func.nullif(
                    cast(
                        case(
                            (and_(*cases), cls.beds_capacity),
                            else_=None,
                        ),
                        NUMERIC,
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Bed Occupancy Cases",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are not removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def bed_occupancy_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast((case((and_(*cases), cls.guest_count), else_=None)), NUMERIC)
                )
                / func.nullif(
                    func.sum(
                        cast(
                            (case((and_(*cases), cls.beds_capacity), else_=None)),
                            NUMERIC,
                        )
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Bed Occupancy Summary Cases",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are not removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @declared_attr
    def adjusted_bed_occupancy(cls):
        return column_property(
            cast(
                cast(cls.guest_count, NUMERIC)
                / func.nullif(
                    cast(
                        cls.beds_capacity
                        - cls.out_of_service_beds_capacity
                        - cls.blocked_beds_capacity,
                        NUMERIC,
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Bed Occupancy",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are not removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
            ),
        )

    @declared_attr
    def adjusted_bed_occupancy_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.guest_count, NUMERIC))
                / (
                    func.nullif(
                        (
                            func.sum(
                                cast(
                                    cls.beds_capacity
                                    - cls.out_of_service_beds_capacity
                                    - cls.blocked_beds_capacity,
                                    NUMERIC,
                                )
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Bed Occupancy Summary",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def adjusted_bed_occupancy_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    case(
                        (and_(*cases), cls.guest_count),
                        else_=None,
                    ),
                    NUMERIC,
                )
                / func.nullif(
                    cast(
                        case(
                            (
                                and_(*cases),
                                cls.beds_capacity
                                - cls.out_of_service_beds_capacity
                                - cls.blocked_beds_capacity,
                            ),
                            else_=None,
                        ),
                        NUMERIC,
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Bed Occupancy Cases",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def adjusted_bed_occupancy_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast((case((and_(*cases), cls.guest_count), else_=None)), NUMERIC)
                )
                / func.nullif(
                    func.sum(
                        cast(
                            (
                                case(
                                    (
                                        and_(*cases),
                                        cls.beds_capacity
                                        - cls.out_of_service_beds_capacity
                                        - cls.blocked_beds_capacity,
                                    ),
                                    else_=None,
                                )
                            ),
                            NUMERIC,
                        )
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Bed Occupancy Summary Cases",
                description="""Percentage of guests divided by total physical bed space capacity.
                Blocked rooms and out of service beds are removed from capacity.""",
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    conversion_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Conversion Rate",
            description="The amount of the conversion rate for that day.",
            kind=CdfKind.Currency,
            function=lambda cls, **kwargs: (
                func.coalesce(
                    func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]],
                        FLOAT,
                    ),
                    1,
                )
                if kwargs.get("currency")
                else literal(1)
            ),
        ),
    )

    historical_exchange_rates = Column(
        JSONB,
        info=dict(
            category=CdfCategory.Production,
            name="Historical Exchange Rates",
            description="Historical Exchange Rates",
            kind=CdfKind.String,
            hide=True,
        ),
    )


class BedOccupancyView(BedOccupancyViewMixin, db.Model):
    __tablename__ = "beds_occupancy_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class BedOccupancyViewFFView(BedOccupancyViewMixin, db.Model):
    # TODO: Switch later because now only ff exist
    __tablename__ = "beds_occupancy_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
