from sqlalchemy import BIGINT, Column, DATE, FLOAT, INTEGER, TIMESTAMP, VARCHAR


from app.common.database import db
from app.enums import CdfKind
from app.enums.cdf_category import CdfCategory


class InvoicesViewMixin(object):
    id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Invoice,
            name="Invoice ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="Cloudbeds ID of the property.",
            kind=CdfKind.Identifier,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Number",
            description="ID of reservation or group associated with invoice.",
            kind=CdfKind.String,
        ),
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="Name of property issuing the invoice.",
            kind=CdfKind.String,
        ),
    )
    bill_from_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Name",
            description="Name of the business issuing the invoice, usually the property.",
            kind=CdfKind.String,
        ),
    )
    bill_from_address_line_1 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Address Line 1",
            description="Street address of invoice issuer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_address_line_2 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Address Line 2",
            description="Second line of invoice issuer's address.",
            kind=CdfKind.String,
        ),
    )
    bill_from_city = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From City",
            description="City of the invoice issuer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_state = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From State",
            description="State of the invoice issuer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Country Code",
            description="Country of the invoice issuer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_zip = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Postcode",
            description="Postal or zip code of the invoice issuer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_hotel_phone = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Phone Number",
            description="Phone number of the invoice issuer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_hotel_email = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Email",
            description="Email of the invoice issuer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_legal_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Legal Name",
            description="Legal name of invoicer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_tax_id_1 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Tax ID 1",
            description="Tax id of invoicer.",
            kind=CdfKind.String,
        ),
    )
    bill_from_tax_id_2 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Bill From Tax ID 2",
            description="Second tax id of invoicer, used where applicable.",
            kind=CdfKind.String,
        ),
    )
    bill_from_cnjp = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillFrom,
            name="Property CNPJ",
            description="Brazilian ID of invoicer.",
            kind=CdfKind.String,
        ),
    )
    bill_to_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To ID",
            description="Cloudbeds ID of invoicee  A guest ID or Group ID ",
            kind=CdfKind.Identifier,
        ),
    )
    bill_to_party = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To Party",
            description="Guest Name / Company Name",
            kind=CdfKind.String,
        ),
    )
    bill_to_address_line_1 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To Address Line 1",
            description="Street address of entity receiving the invoice.",
            kind=CdfKind.String,
        ),
    )
    bill_to_address_line_2 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To Address Line 2",
            description="Second address line of the entity receiving invoice.",
            kind=CdfKind.String,
        ),
    )
    bill_to_city = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To City",
            description="City of entity receiving the invoice.",
            kind=CdfKind.String,
        ),
    )
    bill_to_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To Country Code",
            description="Country code of the entity receiving invoice.",
            kind=CdfKind.String,
        ),
    )
    bill_to_state = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To State",
            description="State of the receiving entity.",
            kind=CdfKind.String,
        ),
    )
    bill_to_postal_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To Postcode",
            description="Postal or Zip Code of entity receiving invoice.",
            kind=CdfKind.String,
        ),
    )
    bill_to_tax_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.BillTo,
            name="Bill To Tax ID",
            description="The Tax ID of the Invoicee, may be an guest tax id or a company tax id.",
            kind=CdfKind.String,
        ),
    )
    reservation_checkin_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Check-In Date",
            description="Check-in date associated with invoice.",
            kind=CdfKind.Date,
        ),
    )
    reservation_checkout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Check-Out Date",
            description="Check-out date associated with invoice",
            kind=CdfKind.Date,
        ),
    )
    reservation_room_nights_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Room Nights",
            description="Length of stay associated with invoice.",
            kind=CdfKind.Number,
        ),
    )
    reservation_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Reservation,
            name="Reservation Booking Date Time - UTC",
            description="Booking date of reservation associated.",
            kind=CdfKind.Timestamp,
        ),
    )
    invoice_language_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Language Code",
            description="Code identifying the language of the invoice, for example, EN for English.",
            kind=CdfKind.String,
        ),
    )
    invoice_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Invoice,
            name="Invoice Status",
            description="Status of the invoice, which can be Open, Paid, or Voided. Credit notes do not have a status.",
            kind=CdfKind.PickList,
            options=(
                "Open",
                "Voided",
                "Paid",
                "Credit Note",
                "Processing",
                "Void Requested",
                "Failed",
                "Manually Reconciled",
                "Canceled",
                "Rejected",
            ),
        ),
    )
    invoice_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Invoice,
            name="Invoice Number",
            description="ID associated with invoice or credit note.",
            kind=CdfKind.String,
        ),
    )
    invoice_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Invoice,
            name="Invoice Type",
            description="Invoice or credit note.",
            kind=CdfKind.String,
        ),
    )
    invoice_generate_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Invoice,
            name="Invoice Date Time - Property Time",
            description="Date the invoice or credit note was issued, based upon the property's time zone.",
            kind=CdfKind.Timestamp,
        ),
    )
    credit_reason = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Invoice,
            name="Credit Reason",
            description="Reason for the credit note.",
            kind=CdfKind.String,
        ),
    )
    original_invoice_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Invoice,
            name="Original Invoice Number",
            description="Populated only for Credit Notes.",
            kind=CdfKind.String,
        ),
    )
    original_invoice_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Invoice,
            name="Original Invoice Date",
            description="Populated only for Credit Notes, Date the invoice was first issued.",
            kind=CdfKind.Date,
        ),
    )
    payment_due_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Invoice,
            name="Payment Due Date",
            description="Date the invoice must be paid by.",
            kind=CdfKind.Date,
        ),
    )
    invoice_currency_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.InvoiceTotals,
            name="Invoice Currency Code",
            description="Currency used in the invoice.",
            kind=CdfKind.String,
        ),
    )
    invoice_age_payment_due_date = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Invoice,
            name="Invoice Age - Payment Due Date",
            description="The length of time the invoice has past the invoice payment due date, in days.",
            kind=CdfKind.Number,
        ),
    )
    invoice_age_invoice_date = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Invoice,
            name="Invoice Age - Invoice Date",
            description="The length of time the invoice has past the invoice creation date, in days.",
            kind=CdfKind.Number,
        ),
    )
    total_gross_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.InvoiceTotals,
            name="Invoice Grand Total",
            description="""The invoice grand total is the total amount of the invoice, including all charges and fees associated with the goods or services provided.
            It encompasses the base cost of the products or services, any additional fees, taxes, and any applicable discounts.""",
            kind=CdfKind.Currency,
        ),
    )
    taxes_value_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.InvoiceTotals,
            name="Invoice Total Tax Amount",
            description="""This field represents the sum of all tax amounts associated with the invoice.
            It includes any applicable taxes, such as sales tax, value-added tax (VAT), or other taxes imposed
            on the goods or services provided in the invoice. It provides a breakdown of the tax component of the total invoice amount.""",
            kind=CdfKind.Currency,
        ),
    )
    balance_due_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.InvoiceTotals,
            name="Invoice Balance Due",
            description="""This field refers to the remaining amount that needs to be paid by the customer for a particular invoice.
            It represents the outstanding balance or the amount that is still owed after deducting any payments or credits applied
            to the invoice.""",
            kind=CdfKind.Currency,
        ),
    )

    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )

    group_profile_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Reservation,
            name="Group Profile Name",
            description="Name of the group profile associated to the invoice.",
            kind=CdfKind.String,
        ),
    )


class InvoicesView(InvoicesViewMixin, db.Model):
    __bind_key__ = "dataset_views"
    __tablename__ = "invoices_vue"
    __table_args__ = {"schema": "insights"}


class InvoicesFFView(InvoicesViewMixin, db.Model):
    __bind_key__ = "dataset_views"
    __tablename__ = "invoices_vue_ff"
    __table_args__ = {"schema": "insights"}
