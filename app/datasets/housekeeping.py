from sqlalchemy import BIGIN<PERSON>, Column, DATE, INTEGER, TIME, TIMESTAMP, VARCHAR

from app.common.constants.room_type_category import ROOM_TYPE_CATEGORIES
from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class HousekeepingViewMixin(object):
    id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Housekeeping ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.String,
        ),
        primary_key=True,
    )
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property.",
            kind=CdfKind.Identifier,
        ),
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property.",
            kind=CdfKind.String,
        ),
    )
    stay_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Stay Date",
            description="The dates that the guest stays in the hotel. For example, if a guest checks in on July 1st and checks out on July 3rd, "
            "their stay dates are July 1st and July 2nd.",
            kind=CdfKind.Date,
        ),
        primary_key=True,
    )
    room_id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Property,
            name="Room ID",
            description="A unique identifier assigned to each room in a property.",
            kind=CdfKind.Identifier,
        ),
    )
    room_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Name",
            description="The display name of a room.",
            kind=CdfKind.String,
        ),
    )
    room_type_id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type ID",
            description="The database ID associated with a room type. This can be useful when merging Cloudbeds data with data from other systems.",
            kind=CdfKind.Identifier,
        ),
    )
    room_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type",
            description="Room type, also known as Accommodation Type, is the name given to a group of rooms that have something in common, such as physical "
            "characteristics and are sold under the same price. Ex. Standard vs Deluxe rooms.",
            kind=CdfKind.String,
        ),
    )
    room_type_abbreviation = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type Abbreviation",
            description="A shortened name for the Accommodation Type.",
            kind=CdfKind.String,
        ),
    )
    estimated_arrival_time = Column(
        TIME,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Estimated Arrival Time",
            description="The estimated arrival time of the guest.",
            kind=CdfKind.Time,
        ),
    )
    room_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Room Status",
            description="Shows whether the room is currently occupied by a guest or vacant.",
            kind=CdfKind.PickList,
            options=(
                "Occupied",
                "Vacant",
            ),
        ),
    )
    room_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Room Count",
            description="The number of rooms.",
            kind=CdfKind.Number,
        ),
    )
    room_condition = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Room Condition",
            description="Shows the current condition of the rooms, either dirty or clean.",
            kind=CdfKind.PickList,
            options=(
                "Clean",
                "Dirty",
            ),
        ),
    )
    housekeeper_assigned = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Housekeeper Assigned",
            description="The housekeeping staff currently assigned to clean or inspect the room.",
            kind=CdfKind.String,
        ),
    )
    do_not_disturb = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Do Not Disturb",
            description="The Do Not Disturb status of the room.",
            kind=CdfKind.String,
        ),
    )
    accommodation_comments = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Accommodation Comments",
            description="Notes for maintenance or other issues to be aware of for this specific room.",
            kind=CdfKind.String,
        ),
    )
    room_condition_last_updated_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Room Condition Last Updated Date Time - UTC",
            description="When the Room Condition of the room last updated, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    room_condition_last_updated_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Room Condition Last Updated Date Time - Property",
            description="When the Room Condition of the room last updated, per the property's timezone.",
            kind=CdfKind.Timestamp,
        ),
    )
    frontdesk_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Housekeeping,
            name="Frontdesk Status",
            description="The Frontdesk Status of the room.",
            kind=CdfKind.PickList,
            options=(
                "Check-in",
                "Check-out",
                "Stayover",
                "Turnover",
                "Not Reserved",
            ),
        ),
    )
    room_type_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type Category",
            description="Shows if the room booked was either a private room or a shared room.",
            kind=CdfKind.PickList,
            options=ROOM_TYPE_CATEGORIES,
        ),
    )


class HousekeepingView(HousekeepingViewMixin, db.Model):
    __tablename__ = "housekeeping_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class HousekeepingFFView(HousekeepingViewMixin, db.Model):
    __tablename__ = "housekeeping_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
