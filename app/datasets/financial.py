from sqlalchemy import (
    BIGINT,
    Column,
    DATE,
    FLOAT,
    INTEGER,
    TIMESTAMP,
    VARCHAR,
    func,
    literal,
)
from sqlalchemy.dialects.postgresql import JSONB


from app.common.constants.report_formats import EXCHANGE_RATE_SUFFIX
from app.common.constants.reservation_status import RESERVATION_STATUSES
from app.common.constants.room_type_category import ROOM_TYPE_CATEGORIES
from app.common.database import db
from app.common.enums.features import LaunchDarklyFeature
from app.enums import CdfKind
from app.enums.cdf_category import CdfCategory


class FinancialViewMixin(object):
    id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.String,
        ),
        primary_key=True,
    )
    addon_charge_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Add-on Charge Type",
            description="How the guest is charged for the add-on item. Examples: by quantity, per reservation, per room, "
            "per night, per room per night, per guest per night, per guest. Will be empty for non-English MFD users.",
            kind=CdfKind.String,
        ),
    )
    addon_item = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Add-on Item",
            description="The add-on item purchased with the reservation. The field will be empty for non-English speakers Cloudbeds PMS users.",
            kind=CdfKind.String,
        ),
    )
    balance_due_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Amount",
            description="The amount of an individual transaction. Charges are positive while payments and credits are negative.",
            kind=CdfKind.Currency,
        ),
    )
    balance_due_amount_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Transaction Amount {EXCHANGE_RATE_SUFFIX}",
            description="The amount of an individual transaction. Charges are positive while payments and credits are negative.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.balance_due_amount
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.balance_due_amount
            ),
        ),
    )

    booking_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Date Time - UTC",
            description="When the reservation was originally made, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    booking_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Date Time - Property",
            description="When the reservation was originally made, per the property's time zone.",
            kind=CdfKind.Timestamp,
        ),
    )
    card_last_4_digits = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Card Last 4 Digits",
            description="Last 4 digits of card, needed when verifying what card has been used.",
            kind=CdfKind.String,
        ),
    )
    card_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Card Type",
            description="The credit card type on file for the reservation, for example Visa, Discover, etc.",
            kind=CdfKind.String,
        ),
    )
    channel_rate_plan = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Rate Plan - Channel Name",
            description="The rate plan name received from the channel.",
            kind=CdfKind.String,
        ),
    )
    checkin_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-In Date",
            description="Date the reservation starts, when the guest arrives, the first room night.",
            kind=CdfKind.Date,
        ),
    )
    checkout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-Out Date",
            description="Date the reservation ends and the guest departs",
            kind=CdfKind.Date,
        ),
    )
    credit_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Credits",
            description="The amount of payment. The amount being credited to the folio or bill.",
            kind=CdfKind.Currency,
        ),
    )

    credit_amount_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Credits {EXCHANGE_RATE_SUFFIX}",
            description="The amount of payment. The amount being credited to the folio or bill converted to the selected currency for the service date.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.credit_amount
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.credit_amount
            ),
        ),
    )

    conversion_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Conversion Rate",
            description="The amount of the conversion rate for that day.",
            kind=CdfKind.Currency,
            function=lambda cls, **kwargs: (
                func.coalesce(
                    func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]],
                        FLOAT,
                    ),
                    1,
                )
                if kwargs.get("currency")
                else literal(1)
            ),
        ),
    )

    historical_exchange_rates = Column(
        JSONB,
        info=dict(
            category=CdfCategory.Production,
            name="Historical Exchange Rates",
            description="Historical Exchange Rates",
            kind=CdfKind.String,
            hide=True,
        ),
    )

    debit_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Debits",
            description="Amount that is owed for an item, the amount charged.",
            kind=CdfKind.Currency,
        ),
    )

    debit_amount_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Debits {EXCHANGE_RATE_SUFFIX}",
            description="Amount that is owed for an item, the amount charged.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.debit_amount
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.debit_amount
            ),
        ),
    )

    fee_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Fee Type",
            description="The type of the Fee charged. These are set up in the Taxes and Fees section of the property configuration area of MyFrontDesk.",
            kind=CdfKind.String,
        ),
    )
    group_profile_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Name",
            description="Group Profile is the master profile under which a certain group of reservations is created or added. This could be a company, "
            "wedding party, travel agent, etc. It may or may not be associated with a group block allotment.",
            kind=CdfKind.String,
        ),
    )
    group_profile_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Type",
            description="The type of group: Group (one time event), Company, Travel Agent, Wholesaler.",
            kind=CdfKind.String,
        ),
    )
    invoice_created_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Invoice Create Date Time - UTC",
            description="Date the invoice was created, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    invoice_created_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Invoice Create Date Time - Property",
            description="Date the invoice was created, per the property's time zone.",
            kind=CdfKind.Timestamp,
        ),
    )
    invoice_guest_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Invoice Guest Name",
            description="Who the invoice was issued to.",
            kind=CdfKind.String,
        ),
    )
    is_hotel_collect_booking = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Hotel Collect Booking Flag",
            description="Channel Pay or Hotel Pay.",
            kind=CdfKind.Boolean,
        ),
    )
    is_refund = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Refund Flag",
            description="Indicates if the transaction is a refund.",
            kind=CdfKind.Boolean,
        ),
    )
    is_transaction_adjusted = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Adjustment Flag",
            description="Indicates if the transaction is an adjustment.",
            kind=CdfKind.Boolean,
        ),
    )
    is_transaction_deleted = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Deletion Flag",
            description="Indicates if the transaction is a deletion.",
            kind=CdfKind.Boolean,
        ),
    )
    is_void = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Void Flag",
            description="Indicates if the transaction is a void.",
            kind=CdfKind.Boolean,
        ),
    )
    item_service_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Item and Service Category",
            description="The category for the item, as it was set up in MyFrontDesk, in the Items and Services section of the Property Configuration.",
            kind=CdfKind.String,
        ),
    )
    item_service_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Item and Service Name",
            description="The type of the item purchased. These are set up in the Items and Service section of the property configuration area of"
            "MyFrontDesk. Does not include room revenue or taxes and fees.",
            kind=CdfKind.String,
        ),
    )
    latest_invoice_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Latest Invoice Number",
            description="The unique ID associated with the invoice.",
            kind=CdfKind.String,
        ),
    )
    payment_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Finance,
            name="Payment ID",
            description="""The unique identifier assigned to each payment transaction.
            This can be used to associate records between financial dataset and payment dataset.""",
            kind=CdfKind.Identifier,
        ),
    )
    payment_method = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Payment Method",
            description=" The type of payment that was provided. For example, credit card, bank transfer, check, cash, or voucher.",
            kind=CdfKind.String,
        ),
    )
    pos_charge_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="POS Charge Category",
            description='The category of the POS (Point of Sale) item purchased. For example, drip "coffee" and "espresso" might be in the "coffee and '
            'tea" category; "beer" might be in an "Alcohol" category.',
            kind=CdfKind.String,
        ),
    )
    pos_charge_description = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="POS Charge Description",
            description="The item purchased via a POS (Point of Sale) system that has been integrated into Cloudbeds. Often this will include items "
            "purchased via a cafe, restaurant, gift shop, etc.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_first_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest First Name",
            description="Primary guest first name.",
            kind=CdfKind.GuestLink,
        ),
    )
    primary_guest_full_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Full Name",
            description="First name and surname (last) name associated with the booking.",
            kind=CdfKind.GuestLink,
        ),
    )
    primary_guest_surname = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Surname",
            description="The primary guest surname, also referred to as the last name",
            kind=CdfKind.GuestLink,
        ),
    )
    primary_guest_status_level = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Status Level",
            description="The Guest Status level of the primary guest of the reservation.",
            kind=CdfKind.String,
        ),
    )
    private_rate_plan = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Rate Plan - Private Name",
            description="""The internal rate plan name that is unique to the rate plan.""",
            kind=CdfKind.String,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property.",
            kind=CdfKind.Identifier,
        ),
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property.",
            kind=CdfKind.String,
        ),
    )
    public_rate_plan = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Rate Plan - Public Name",
            description="""The rate plan name that is visible to the customer.""",
            kind=CdfKind.String,
        ),
    )
    quantity_amount = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Finance,
            name="Quantity",
            description="Number of items purchased.",
            kind=CdfKind.Number,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Number",
            description="The confirmation number that the CB System automatically assigns to a reservation and that uniquely identifies a reservation. "
            "Sometimes referred to as Reservation ID",
            kind=CdfKind.ReservationLink,
        ),
    )
    reservation_source = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source",
            description="How reservation was booked: Phone, Booking Engine, Expedia, etc. Sometimes referred to as the Channel.",
            kind=CdfKind.String,
            translate=False,
        ),
    )
    reservation_source_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source Category",
            description="The groups of reservation sources: Direct, Group, Wholesaler, OTA, or Travel Agent.",
            kind=CdfKind.String,
            translate=False,
        ),
    )
    reservation_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Status",
            description="Status of the Reservation: Confirmed, Confirmed Pending, Cancelled, In-House, Checked Out, No Show or Inquiry. Could be another custom status (hardcode).",
            kind=CdfKind.PickList,
            options=RESERVATION_STATUSES,
        ),
    )
    room_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Number",
            description="The number or name that identifies the room(s) associated with the transaction.",
            kind=CdfKind.String,
        ),
    )
    room_revenue_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Room Revenue Type",
            description="The type of charge associated with the Room Revenue: Room Rate, Cancellation Fee, No Show, or Manual charge.",
            kind=CdfKind.String,
        ),
    )
    room_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Type",
            description="The type of room, as categorized in MyFrontDesk.",
            kind=CdfKind.String,
        ),
    )
    tax_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Tax Type",
            description="The type of the tax charged. These are set up in the Taxes and Fees section of the property configuration area of MyFrontDesk.",
            kind=CdfKind.String,
        ),
    )
    transaction_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Code",
            description="The information entered by the customer when filling out the Code field in the Taxes and Fees or the Items and Services section.",
            kind=CdfKind.String,
        ),
    )
    transaction_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Date Time - UTC",
            description="""The date/time when transaction occurs, per UTC time zone. This cannot be backdated.""",
            kind=CdfKind.Timestamp,
        ),
    )
    transaction_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Date Time - Property",
            description="""The date/time when transaction occurs, per property's time zone. This cannot be backdated.""",
            kind=CdfKind.Timestamp,
        ),
    )
    transaction_notes = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Notes",
            description="The note associated with the transaction. Most transactions don't have notes, but we provide the option of showing notes for those that do.",
            kind=CdfKind.String,
        ),
    )
    transaction_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Status",
            description="The status of the transaction in terms of system posting.",
            kind=CdfKind.PickList,
            options=("Posted", "Pending"),
        ),
    )
    transaction_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Type",
            description="The type of transaction that occurred, such as Room Revenue, Add-on, or Tax.",
            kind=CdfKind.String,
        ),
    )
    user = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="User",
            description="Name of the Property's MyFrontDesk User that made the change/update.",
            kind=CdfKind.String,
        ),
    )
    internal_transaction_code_description = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Cloudbeds Transaction Code Description",
            description="The description of the internal transaction type. It is used to help users identify the purpose of a particular transaction type.",
            kind=CdfKind.String,
        ),
    )
    internal_transaction_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Cloudbeds Transaction Code",
            description="The unique code assigned to an internal transaction type. It is used for tracking and categorizing different types of transactions.",
            kind=CdfKind.String,
        ),
    )
    general_ledger_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Custom General Ledger Code",
            description="""The code assigned to a specific general ledger account.
            It is used for tracking and categorizing different types of financial transactions.
            Note: The use of this field requires proper configuration in the PMS.""",
            kind=CdfKind.String,
        ),
    )
    general_ledger_account = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Custom General Ledger Name",
            description="""The name of the general ledger account. It is used to help users identify the particular general ledger account.
            Note: The use of this field requires proper configuration in the PMS.""",
            kind=CdfKind.String,
        ),
    )
    custom_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Custom Transaction Code",
            description="""This field refers to a code that is customized by the property to categorize transactions.
            It is used to help users identify the purpose of a particular custom code.
            Note: The use of this field requires proper configuration in the PMS.""",
            kind=CdfKind.String,
        ),
    )
    res_room_identifier = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Reservation Number",
            description="""The ID or confirmation number that the CB system automatically assigns to a room
            within the reservation and that uniquely identifies the room and reservation.""",
            kind=CdfKind.String,
        ),
    )
    house_account_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="House Account Name",
            description="""Name of the house account associated to the transaction. House Accounts allows you to create one or multiple accounts
            to register transactions for items that are not connected to any specific room, guest, or reservation, like walk-in guests consumption.""",
            kind=CdfKind.String,
        ),
    )
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    item_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Item Type",
            description="Shows if the transaction is coming from a product, service, or meal plan.",
            kind=CdfKind.PickList,
            options=("Product", "Service", "Meal Plan"),
        ),
    )
    meal_plan = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Meal Plan",
            description="A comma separated list of meal plans included in the financial transaction.",
            kind=CdfKind.String,
        ),
    )
    room_type_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Type Category",
            description="Shows if the room booked was either a private room or a shared room type.",
            kind=CdfKind.PickList,
            options=ROOM_TYPE_CATEGORIES,
        ),
    )
    booking_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation ID",
            description="The unique identifier assigned to each Reservation. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
    )
    guest_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest ID",
            description="The unique identifier assigned to each guest. This is generated and used by the system.",
            kind=CdfKind.GuestLink,
        ),
    )
    service_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Finance,
            name="Service Date",
            description="""The date when product or service are delivered. With proper permission,
            service data can be backdated to recognize revenue and expenses in the correct period.""",
            kind=CdfKind.Date,
        ),
    )
    house_account_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="House Account ID",
            description="The unique identifier assigned to each house account. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
    )
    group_profile_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile ID",
            description="The unique identifier assigned to each group profile. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
    )
    group_profile_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Code",
            description="The unique code assigned to each group profile. This is generated and used by the system.",
            kind=CdfKind.String,
        ),
    )
    payment_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Payment Type",
            description="The type of payment that was provided. For example, ChargebackFee, ChargebackReversal.",
            kind=CdfKind.String,
        ),
    )
    benchmarking_transaction_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Benchmarking Transaction Type",
            description="The benchmarking bucket of the transaction per USALI. This corresponds to the revenue categories in the Occupancy revenue data.",
            kind=CdfKind.String,
        ),
    )
    transaction_description = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Description",
            description="The description of the transaction as shown in the PMS folio.",
            kind=CdfKind.String,
        ),
    )
    currency = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Original Currency",
            description="""The type of money used to complete the transaction at the
            time it was made represented by a currency code (e.g., EUR for Euro)""",
            kind=CdfKind.String,
        ),
    )


class FinancialView(FinancialViewMixin, db.Model):
    __bind_key__ = "dataset_views"
    __tablename__ = "financial_transactions_vue"
    __table_args__ = {"schema": "insights"}


class FinancialFFView(FinancialViewMixin, db.Model):
    __bind_key__ = "dataset_views"
    __tablename__ = "financial_transactions_vue_ff"
    __table_args__ = {"schema": "insights"}
