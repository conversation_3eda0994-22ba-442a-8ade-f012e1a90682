from sqlalchemy import BIGINT, Column, DATE, FLOAT, TIMESTAMP, VARCHAR

from app.common.constants.inventory_object import INVENTORY_OBJECT_TYPES
from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class PayoutMixin(object):
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property. Having a unique ID per property is useful when integrating with other systems.",
            kind=CdfKind.Identifier,
        ),
    )
    payout_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout ID",
            description="Unique identifier for the payout record.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    payout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout Date",
            description="Date when the payout was processed.",
            kind=CdfKind.Date,
        ),
    )
    payout_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout Status",
            description="Status of the payout.",
            kind=CdfKind.PickList,
            options=(
                "Successful",
                "Failed",
                "In Progress",
            ),
        ),
    )
    transaction_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Transaction ID",
            description="Unique identifier for the transaction.",
            kind=CdfKind.Identifier,
        ),
    )
    transaction_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Transaction Type",
            description="Type of transaction (e.g., Payment, Refund, Dispute, Adjustment).",
            kind=CdfKind.PickList,
            options=(
                "Adjustment",
                "Capture",
                "Chargeback",
                "ChargebackFee",
                "ChargebackReversal",
                "ManualCorrection",
                "Purchase",
                "Refund",
                "UnreferencedChargeback",
                "UnreferencedRefund",
            ),
        ),
    )
    link_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Payout,
            name="Link ID",
            description="ID used to associate the transaction with a reservation or entity.",
            kind=CdfKind.Identifier,
        ),
    )
    account_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Account ID",
            description="The Processor unique identifier for the property or hotel.",
            kind=CdfKind.Identifier,
        ),
    )
    inventory_object_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Inventory Object ID",
            description="A unique identifier assigned to each item or unit within the Account Receivable Ledger.",
            kind=CdfKind.Identifier,
        ),
    )
    inventory_object_identifier = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Inventory Object Identifier",
            description="External or user-facing identifier (e.g., reservation code)",
            kind=CdfKind.Identifier,
        ),
    )
    inventory_object_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Inventory Object Type",
            description="A classification that defines the kind or category of the inventory item e.g Group Profile, House Account, Reservation, AR Ledger etc.",
            kind=CdfKind.PickList,
            options=INVENTORY_OBJECT_TYPES,
        ),
    )
    inventory_object_title = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Inventory Object Title",
            description="Human-readable title or label for the object.",
            kind=CdfKind.String,
        ),
    )
    transaction_date = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Payout,
            name="Transaction Date",
            description="Date and time the transaction occurred.",
            kind=CdfKind.Date,
        ),
    )
    net_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout amount",
            description="Final amount paid out after deducting fees.",
            kind=CdfKind.Currency,
        ),
    )
    fee_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payout,
            name="Fee Amount",
            description="Transaction processing fee.",
            kind=CdfKind.Currency,
        ),
    )
    total_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payout,
            name="Payment Amount",
            description="Gross amount charged in the transaction.",
            kind=CdfKind.Currency,
        ),
    )
    currency = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Currency",
            description="Currency code for the transaction (e.g., USD, THB).",
            kind=CdfKind.String,
        ),
    )
    payment_method = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Method",
            description="Method used for the transaction (e.g., Card, PayPal, Terminal).",
            kind=CdfKind.String,
        ),
    )
    card_last_4_digits = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Card Last 4 Digits",
            description="Last 4 digits of the card used (if applicable).",
            kind=CdfKind.String,
        ),
    )
    adjustment_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Adjustment Type",
            description="Type of adjustment if applicable (e.g., CBP Fee Assessment, Chargeback).",
            kind=CdfKind.String,
        ),
    )
    card_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Card Type",
            description="The card type on file for the reservation, for example, Visa, Discover, etc.",
            kind=CdfKind.String,
        ),
    )


class PayoutView(PayoutMixin, db.Model):
    __tablename__ = "payouts_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class PayoutViewFF(PayoutMixin, db.Model):
    __tablename__ = "payouts_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
