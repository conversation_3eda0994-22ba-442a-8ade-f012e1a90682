from flask import g

from sqlalchemy.orm import class_mapper

from app.common.enums.features import LaunchDarklyFeature
from app.common.enums.multi_level import JoinFunction
from app.datasets.bed_occupancy import BedOccupancyView, BedOccupancyViewFFView
from app.datasets.invoices import InvoicesFFView, InvoicesView
from app.datasets.occupancy_v1 import OccupancyV1FFView, OccupancyV1View
from app.datasets.reservations import ReservationsFFView, ReservationsView
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.multi_levels.invoice_items import InvoiceItemsFFView, InvoiceItemsView
from app.multi_levels.occupancy_reservation import (
    OccupancyReservationsFFView,
    OccupancyReservationsView,
)
from app.multi_levels.room_nights import RoomNightsFFView, RoomNightsView
from app.multi_levels.room_reservations import (
    RoomReservationsFFView,
    RoomReservationsView,
)
from app.services.launch_darkly_service import LaunchDarklyService


class MultiLevel:
    def __init__(self, kind: MultiLevelEnum):
        self.kind = kind

    def __repr__(self):
        """Representation of Multi-Level class
        :return: string
        """
        return f"<Multi-level, kind={self.kind}>"

    @property
    def id(self):
        return self.kind.value

    @property
    def name(self):
        name = dict()
        name[MultiLevelEnum.RoomNights] = "Room Nights"
        name[MultiLevelEnum.RoomReservations] = "Room Reservations"
        name[MultiLevelEnum.InvoiceItems] = "Invoice Items"
        name[MultiLevelEnum.OccupancyReservation] = "Reservation"
        name[MultiLevelEnum.BedOccupancyReservation] = "Reservation"
        return name[self.kind]

    @property
    def cdfs(self):
        cdfs = [
            dict(
                column=column.key,
                name=column.info["name"],
                description=column.info["description"],
                kind=column.info["kind"],
                translate=column.info.get("translate", None),
            )
            for column in list(self.model.__table__.columns)
            + list(class_mapper(self.model).iterate_properties)
            if bool(column.info)
            and not column.info.get("hide", False)
            and (
                "feature_flag" not in column.info
                or (
                    LaunchDarklyService.has_feature_flag(
                        column.info["feature_flag"], g.property_id
                    )
                    if hasattr(g, "property_id")
                    else False
                )
            )
        ]
        cdfs = [cdf for cdf in sorted(cdfs, key=lambda cdf: cdf["name"])]

        return cdfs

    @property
    def model(self):
        try:
            if (
                self.kind.value == MultiLevelEnum.RoomNights.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.ReservationDatasetFF,
                    g.property_id,
                )
            ):
                return RoomNightsFFView
            if (
                self.kind.value == MultiLevelEnum.InvoiceItems.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.InvoiceDatasetFF,
                    g.property_id,
                )
            ):
                return InvoiceItemsFFView
            if (
                self.kind.value == MultiLevelEnum.RoomReservations.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.ReservationDatasetFF,
                    g.property_id,
                )
            ):
                return RoomReservationsFFView
            if (
                self.kind.value == MultiLevelEnum.OccupancyReservation.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.OccupancyV1DatasetFF,
                    g.property_id,
                )
            ):
                return OccupancyReservationsFFView
            if (
                self.kind.value == MultiLevelEnum.BedOccupancyReservation.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.BedOccupancyDatasetFF,
                    g.property_id,
                )
            ):
                return OccupancyReservationsFFView

        except:
            pass

        models = dict()
        models[MultiLevelEnum.RoomNights] = RoomNightsView
        models[MultiLevelEnum.RoomReservations] = RoomReservationsView
        models[MultiLevelEnum.InvoiceItems] = InvoiceItemsView
        models[MultiLevelEnum.OccupancyReservation] = OccupancyReservationsView
        models[MultiLevelEnum.BedOccupancyReservation] = OccupancyReservationsView

        return models[self.kind]

    @property
    def join(self):
        try:
            if (
                self.kind.value == MultiLevelEnum.RoomNights.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.ReservationDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    (RoomNightsFFView.booking_id == ReservationsFFView.id)
                    & (
                        RoomNightsFFView.organization_id
                        == ReservationsFFView.organization_id
                    )
                    & (RoomNightsFFView.property_id == ReservationsFFView.property_id)
                )
            if (
                self.kind.value == MultiLevelEnum.InvoiceItems.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.InvoiceDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    (InvoiceItemsFFView.invoice_id == InvoicesFFView.id)
                    & (
                        InvoiceItemsFFView.organization_id
                        == InvoicesFFView.organization_id
                    )
                    & (InvoiceItemsFFView.property_id == InvoicesFFView.property_id)
                )
            if (
                self.kind.value == MultiLevelEnum.RoomReservations.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.ReservationDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    (RoomReservationsFFView.booking_id == ReservationsFFView.id)
                    & (
                        RoomReservationsFFView.organization_id
                        == ReservationsFFView.organization_id
                    )
                    & (
                        RoomReservationsFFView.property_id
                        == ReservationsFFView.property_id
                    )
                )
            if (
                self.kind.value == MultiLevelEnum.OccupancyReservation.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.OccupancyV1DatasetFF,
                    g.property_id,
                )
            ):
                return (
                    (
                        OccupancyReservationsFFView.organization_id
                        == OccupancyV1FFView.organization_id
                    )
                    & (
                        OccupancyReservationsFFView.property_id
                        == OccupancyV1FFView.property_id
                    )
                    & (
                        OccupancyReservationsFFView.room_type_id
                        == OccupancyV1FFView.room_type_id
                    )
                    & (
                        OccupancyReservationsFFView.booking_id
                        == OccupancyV1FFView.booking_id
                    )
                    & (
                        OccupancyReservationsFFView.booking_room_id
                        == OccupancyV1FFView.booking_room_id
                    )
                    & (
                        OccupancyReservationsFFView.stay_date
                        == OccupancyV1FFView.stay_date
                    )
                )
            if (
                self.kind.value == MultiLevelEnum.BedOccupancyReservation.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.BedOccupancyDatasetFF,
                    g.property_id,
                )
            ):
                return (
                    (
                        OccupancyReservationsFFView.organization_id
                        == BedOccupancyViewFFView.organization_id
                    )
                    & (
                        OccupancyReservationsFFView.property_id
                        == BedOccupancyViewFFView.property_id
                    )
                    & (
                        OccupancyReservationsFFView.room_type_id
                        == BedOccupancyViewFFView.room_type
                    )
                    & (
                        OccupancyReservationsFFView.booking_id
                        == BedOccupancyViewFFView.reservation_id
                    )
                    & (
                        OccupancyReservationsFFView.booking_room_id
                        == BedOccupancyViewFFView.booking_room_id
                    )
                    & (
                        OccupancyReservationsFFView.stay_date
                        == BedOccupancyViewFFView.stay_date
                    )
                )

        except:
            pass

        join = dict()
        join[MultiLevelEnum.RoomNights] = (
            (RoomNightsView.booking_id == ReservationsView.id)
            & (RoomNightsView.property_id == ReservationsView.property_id)
            & (RoomNightsView.organization_id == ReservationsView.organization_id)
        )
        join[MultiLevelEnum.RoomReservations] = (
            (RoomReservationsView.booking_id == ReservationsView.id)
            & (RoomReservationsView.property_id == ReservationsView.property_id)
            & (RoomReservationsView.organization_id == ReservationsView.organization_id)
        )
        join[MultiLevelEnum.InvoiceItems] = (
            (InvoiceItemsView.invoice_id == InvoicesView.id)
            & (InvoiceItemsView.property_id == InvoicesView.property_id)
            & (InvoiceItemsView.organization_id == InvoicesView.organization_id)
        )

        join[MultiLevelEnum.OccupancyReservation] = (
            (
                OccupancyReservationsView.organization_id
                == OccupancyV1View.organization_id
            )
            & (OccupancyReservationsView.property_id == OccupancyV1View.property_id)
            & (OccupancyReservationsView.room_type_id == OccupancyV1View.room_type_id)
            & (OccupancyReservationsView.booking_id == OccupancyV1View.booking_id)
            & (
                OccupancyReservationsView.booking_room_id
                == OccupancyV1View.booking_room_id
            )
            & (OccupancyReservationsView.stay_date == OccupancyV1View.stay_date)
        )
        join[MultiLevelEnum.BedOccupancyReservation] = (
            (
                OccupancyReservationsView.organization_id
                == BedOccupancyView.organization_id
            )
            & (OccupancyReservationsView.property_id == BedOccupancyView.property_id)
            & (OccupancyReservationsView.room_type_id == BedOccupancyView.room_type_id)
            & (OccupancyReservationsView.booking_id == BedOccupancyView.booking_id)
            & (
                OccupancyReservationsView.booking_room_id
                == BedOccupancyView.booking_room_id
            )
            & (OccupancyReservationsView.stay_date == BedOccupancyView.stay_date)
        )
        return join[self.kind]

    @property
    def join_function(self):
        join_functions = dict()
        join_functions[MultiLevelEnum.RoomNights] = JoinFunction.JOIN.value
        join_functions[MultiLevelEnum.RoomReservations] = JoinFunction.JOIN.value
        join_functions[MultiLevelEnum.InvoiceItems] = JoinFunction.LEFT.value
        join_functions[MultiLevelEnum.OccupancyReservation] = JoinFunction.LEFT.value
        join_functions[MultiLevelEnum.BedOccupancyReservation] = JoinFunction.LEFT.value

        return join_functions[self.kind]
