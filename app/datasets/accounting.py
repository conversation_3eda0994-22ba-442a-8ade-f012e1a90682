from sqlalchemy import (
    BIGINT,
    Column,
    DATE,
    FLOAT,
    INTEGER,
    TIMESTAMP,
    VARCHAR,
)

from app.common.database import db
from app.enums import CdfKind
from app.enums.cdf_category import CdfCategory


class AccountingViewMixin(object):
    id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property.",
            kind=CdfKind.Identifier,
        ),
    )
    source_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Source Date Time - UTC",
            description="When the event was produced into Kafka before being processed by accounting service.",
            kind=CdfKind.Timestamp,
        ),
    )
    transaction_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Date Time - UTC",
            description="When the transaction occurred, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    service_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Finance,
            name="Service Date",
            description="Date when the transaction should be converted from pending to posted.",
            kind=CdfKind.Date,
        ),
    )
    internal_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Cloudbeds Transaction Code",
            description="The unique code assigned to an internal transaction type. It is used for tracking and categorizing different types of transactions.",
            kind=CdfKind.String,
        ),
    )
    internal_code_description = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Cloudbeds Transaction Code Description",
            description="The description of the internal transaction type. It is used to help users identify the purpose of a particular transaction type.",
            kind=CdfKind.String,
        ),
    )
    amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Amount",
            description="The amount of an individual transaction. Charges are positive while payments and credits are negative.",
            kind=CdfKind.Currency,
        ),
    )
    currency = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Currency",
            description="Currency applied when the transaction was made.",
            kind=CdfKind.String,
        ),
    )
    currency_scale = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Finance,
            name="Currency Scale",
            description="Scale of the currency applied when the transaction was made",
            kind=CdfKind.Number,
        ),
    )
    root_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Root ID",
            description="ID of the Root transaction. Useful for seeing all the historical ",
            kind=CdfKind.Identifier,
        ),
    )
    parent_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Finance,
            name="Parent ID",
            description="Id of the parent transaction.",
            kind=CdfKind.Identifier,
        ),
    )
    source_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Source ID",
            description="ID of the Source of the transaction. Right now we have 3 sources. House Account, Groups and Reservations.",
            kind=CdfKind.Identifier,
        ),
    )
    sub_source_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Sub Source ID",
            description="ID of the Sub Source of the transaction. Right now we only have a Sub Source when source_kind is RESERVATION. Sub Source will be Booking Room ID",
            kind=CdfKind.Identifier,
        ),
    )
    source_kind = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Source Kind",
            description="Kind of the Source of the transaction. Right now we have 3 sources. House Account, Groups and Reservations.",
            kind=CdfKind.PickList,
            options=("RESERVATION", "HOUSE_ACCOUNT", "GROUP_PROFILE"),
        ),
    )
    external_relation_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="External Relation ID",
            description="Unique identifier of the reason of the transaction. It can be the id of Rate, Tax, Fee, Addon, Item, Custom Item, etc.",
            kind=CdfKind.String,
        ),
    )
    external_relation_kind = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="External Relation Kind",
            description="Kind of the reason of the transaction. It can be RESERVATION, ROOM, PAYMENT, ITEM, ITEM_POS, ADDON, CITY_LEDGER, ROOM_REVENUE, FEE, TAX, ADJUSTMENT.",
            kind=CdfKind.PickList,
            options=(
                "ADDON",
                "ADJUSTMENT",
                "FEE",
                "ITEM",
                "ITEM_POS",
                "PAYMENT",
                "RESERVATION",
                "ROOM",
                "ROOM_REVENUE",
                "TAX",
                "PAYMENT_FEE",
            ),
        ),
    )
    origin_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Origin ID",
            description="ID of the origin of the transaction. This fields related to the internal code. If Internal Code is tax it will be the tax id.",
            kind=CdfKind.String,
        ),
    )
    general_ledger_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Custom General Ledger Code",
            description="The code assigned to a specific general ledger account. It is used for tracking and categorizing different types of financial transactions.",
            kind=CdfKind.String,
        ),
    )
    general_ledger_account = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Custom General Ledger Name",
            description="The name of the general ledger account. It is used to help users identify the particular general ledger account.",
            kind=CdfKind.String,
        ),
    )
    custom_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Custom Transaction Code",
            description="""This field refers to a code that is customized by the property to categorize transactions.
            It is used to help users identify the purpose of a particular custom code.""",
            kind=CdfKind.String,
        ),
    )
    routed_from = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Routed From",
            description="ID of the transaction that originally was routed.",
            kind=CdfKind.Identifier,
        ),
    )
    quantity = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Finance,
            name="Quantity",
            description="Number of items purchased.",
            kind=CdfKind.Number,
        ),
    )
    description = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Description",
            description="Description of the transaction. Autogenerated by Cloudbeds.",
            kind=CdfKind.String,
        ),
    )
    notes = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Transaction Notes",
            description="The note associated with the transaction. Most transactions don't have notes, but we provide the option of showing notes for those that do.",
            kind=CdfKind.String,
        ),
    )
    user_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="User ID",
            description="Id of MFD User who performed the action, 0 means is MFD System User",
            kind=CdfKind.Identifier,
        ),
    )
    created_at = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Created At - UTC",
            description="When the transaction was created on accounting service.",
            kind=CdfKind.Timestamp,
        ),
    )
    state = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="State",
            description="State of the Transaction. PENDING | POSTED.",
            kind=CdfKind.PickList,
            options=("POSTED", "PENDING"),
        ),
    )
    status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Status",
            description="Transaction Status only when the state is PENDING.",
            kind=CdfKind.String,
        ),
    )
    updated_at = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Finance,
            name="Updated At - UTC",
            description="When the transaction was updated on accounting service if the transaction was pending. Posted transaction are inmutable.",
            kind=CdfKind.Timestamp,
        ),
    )
    routed_source_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Routed source id",
            description="Unique identifier of the source of the routing. Only will have value if the transaction was routed and the state is PENDING.",
            kind=CdfKind.Identifier,
        ),
    )
    routed_source_kind = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Routed Source Kind",
            description="Kind of the routed. Only will have value if the transaction was routed and the state is PENDING.",
            kind=CdfKind.String,
        ),
    )


class AccountingView(AccountingViewMixin, db.Model):
    __bind_key__ = "dataset_views"
    __tablename__ = "accounting_vue"
    __table_args__ = {"schema": "insights"}
