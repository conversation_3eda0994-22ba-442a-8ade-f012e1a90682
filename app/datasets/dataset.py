from flask import g

from sqlalchemy.orm import class_mapper

from app.common.constants.country_codes import COUNTRY_CODES
from app.common.enums.countries import Countries
from app.common.enums.features import LaunchDarklyFeature
from app.datasets.accounting import AccountingView
from app.datasets.bed_occupancy import BedOccupancyView, BedOccupancyViewFFView
from app.datasets.financial import FinancialFFView, FinancialView
from app.datasets.guests import GuestsFFVie<PERSON>, GuestsView
from app.datasets.housekeeping import HousekeepingFFView, HousekeepingView
from app.datasets.invoices import InvoicesFFView, InvoicesView
from app.datasets.multi_level import MultiLevel
from app.datasets.occupancy import OccupancyView
from app.datasets.occupancy_v1 import OccupancyV1FFView, OccupancyV1View
from app.datasets.payment import PaymentFFView, PaymentView
from app.datasets.payout import PayoutView, PayoutViewFF
from app.datasets.reservations import ReservationsFFView, ReservationsView
from app.enums import Cdf, Dataset as DatasetEnum, FilterOperator, Sort
from app.enums.cdf_category import CdfCategory
from app.enums.multi_level import MultiLevel as MultiLevelEnum
from app.services.launch_darkly_service import LaunchDarklyService


class Dataset:
    def __init__(self, kind: DatasetEnum, check_feature_flag: bool = True):
        self.kind = kind
        self.feature_flag = check_feature_flag

    @property
    def has_organization_id(self):
        return self.model not in (OccupancyView,)

    @property
    def model(self):
        try:
            if (
                self.kind.value == DatasetEnum.Financial.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.FinancialDatasetFF, g.property_id
                )
            ):
                return FinancialFFView
            if (
                self.kind.value == DatasetEnum.Guests.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.GuestDatasetFF,
                    g.property_id,
                )
            ):
                return GuestsFFView
            if (
                self.kind.value == DatasetEnum.Reservations.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.ReservationDatasetFF,
                    g.property_id,
                )
            ):
                return ReservationsFFView
            if (
                self.kind.value == DatasetEnum.Payment.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.PaymentDatasetFF,
                    g.property_id,
                )
            ):
                return PaymentFFView
            if (
                self.kind.value == DatasetEnum.Invoices.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.InvoiceDatasetFF,
                    g.property_id,
                )
            ):
                return InvoicesFFView
            if (
                self.kind.value == DatasetEnum.Housekeeping.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.HousekeepingDatasetFF,
                    g.property_id,
                )
            ):
                return HousekeepingFFView
            if (
                self.kind.value == DatasetEnum.OccupancyV1.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.OccupancyV1DatasetFF,
                    g.property_id,
                )
            ):
                return OccupancyV1FFView
            if (
                self.kind.value == DatasetEnum.Payout.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.PayoutDatasetFF,
                    g.property_id,
                )
            ):
                return PayoutViewFF
            if (
                self.kind.value == DatasetEnum.BedOccupancy.value
                and LaunchDarklyService.has_feature_flag(
                    LaunchDarklyFeature.BedOccupancyDatasetFF,
                    g.property_id,
                )
            ):
                return BedOccupancyViewFFView

        except:
            pass

        models = dict()
        models[DatasetEnum.Financial] = FinancialView
        models[DatasetEnum.Guests] = GuestsView
        models[DatasetEnum.Reservations] = ReservationsView
        models[DatasetEnum.Occupancy] = OccupancyView
        models[DatasetEnum.Payment] = PaymentView
        models[DatasetEnum.Invoices] = InvoicesView
        models[DatasetEnum.OccupancyV1] = OccupancyV1View
        models[DatasetEnum.Housekeeping] = HousekeepingView
        models[DatasetEnum.Accounting] = AccountingView
        models[DatasetEnum.Payout] = PayoutView
        models[DatasetEnum.BedOccupancy] = BedOccupancyView

        return models[self.kind]

    @property
    def default_sorts(self):
        default_sorts = dict()
        default_sorts[DatasetEnum.Financial] = [
            dict(
                cdf=dict(
                    type=Cdf.Default.value,
                    column=FinancialView.transaction_datetime.key,
                ),
                direction=Sort.desc.value,
            )
        ]
        default_sorts[DatasetEnum.Guests] = [
            dict(
                cdf=dict(
                    type=Cdf.Default.value, column=GuestsView.booking_datetime.key
                ),
                direction=Sort.desc.value,
            )
        ]
        default_sorts[DatasetEnum.Reservations] = [
            dict(
                cdf=dict(
                    type=Cdf.Default.value, column=ReservationsView.booking_datetime.key
                ),
                direction=Sort.asc.value,
            )
        ]
        default_sorts[DatasetEnum.Occupancy] = [
            dict(
                cdf=dict(type=Cdf.Default.value, column=OccupancyView.stay_date.key),
                direction=Sort.desc.value,
            )
        ]
        default_sorts[DatasetEnum.Payment] = []
        default_sorts[DatasetEnum.Payout] = []
        default_sorts[DatasetEnum.Invoices] = []
        default_sorts[DatasetEnum.OccupancyV1] = []
        default_sorts[DatasetEnum.Housekeeping] = []
        default_sorts[DatasetEnum.Accounting] = []
        default_sorts[DatasetEnum.BedOccupancy] = []

        return default_sorts[self.kind]

    @property
    def static_filters(self):
        static_filters = dict()
        static_filters[DatasetEnum.Financial] = [
            dict(
                cdf=dict(
                    type=Cdf.Default.value,
                    column=FinancialView.is_transaction_deleted.key,
                ),
                operator=FilterOperator.Equals.value,
                value="No",
            ),
            {
                "or": [
                    dict(
                        cdf=dict(
                            type=Cdf.Default.value,
                            column=FinancialView.internal_transaction_code.key,
                        ),
                        operator=FilterOperator.NotListContains.value,
                        value=["6000", "6100", "7000", "7100"],
                    ),
                    dict(
                        cdf=dict(
                            type=Cdf.Default.value,
                            column=FinancialView.internal_transaction_code.key,
                        ),
                        operator=FilterOperator.IsNull.value,
                        value="",
                    ),
                ]
            },
        ]
        static_filters[DatasetEnum.Guests] = []
        static_filters[DatasetEnum.Reservations] = []
        static_filters[DatasetEnum.Occupancy] = []
        static_filters[DatasetEnum.Payment] = []
        static_filters[DatasetEnum.Payout] = []
        static_filters[DatasetEnum.Invoices] = []
        static_filters[DatasetEnum.OccupancyV1] = []
        static_filters[DatasetEnum.Housekeeping] = []
        static_filters[DatasetEnum.Accounting] = []

        return static_filters.get(self.kind, [])

    @property
    def cdfs(self):
        model_columns = [
            dict(column=column.key, **column.info)
            for column in list(self.model.__table__.columns)
            + list(class_mapper(self.model).iterate_properties)
            if bool(column.info)
            and not column.info.get("hide", False)
            and (
                "feature_flag" not in column.info
                or not self.feature_flag
                or (
                    LaunchDarklyService.has_feature_flag(
                        column.info["feature_flag"], g.property_id
                    )
                    if hasattr(g, "property_id")
                    else False
                )
            )
        ]
        categories = list(set([cdf["category"].value for cdf in model_columns]))
        cdfs = [
            dict(
                category=category,
                cdfs=[
                    dict(
                        column=cdf["column"],
                        name=cdf["name"],
                        description=cdf["description"],
                        kind=cdf["kind"],
                        translate=cdf.get("translate", None),
                    )
                    for cdf in sorted(model_columns, key=lambda cdf: cdf["name"])
                    if cdf["category"].value == category
                ],
            )
            for category in sorted(categories)
        ]
        return cdfs

    @property
    def flatten_cdfs(self):
        return [category for categories in self.cdfs for category in categories["cdfs"]]

    @property
    def non_feature_cdfs(self) -> list[dict]:
        """Method that will return a list of cdfs that does not belongs to a feature category"""

        categories = self.feature_categories + self.country_categories
        return [
            category
            for category in self.cdfs
            if category["category"]
            not in [category["category"] for category in categories]
        ]

    @property
    def feature_categories(self):
        feature_categories = dict()
        feature_categories[DatasetEnum.Financial] = []
        feature_categories[DatasetEnum.Guests] = []
        feature_categories[DatasetEnum.Reservations] = []
        feature_categories[DatasetEnum.Occupancy] = []
        feature_categories[DatasetEnum.Payment] = []
        feature_categories[DatasetEnum.Payout] = []
        feature_categories[DatasetEnum.Invoices] = []
        feature_categories[DatasetEnum.OccupancyV1] = []
        feature_categories[DatasetEnum.Housekeeping] = []
        feature_categories[DatasetEnum.Accounting] = []
        feature_categories[DatasetEnum.Payout] = []

        return feature_categories.get(self.kind, [])

    @property
    def country_categories(self):
        country_categories = dict()
        country_categories[DatasetEnum.Financial] = []
        country_categories[DatasetEnum.Guests] = [
            dict(
                category=CdfCategory.ThaiGovernment.value,
                country_code=COUNTRY_CODES[Countries.Thailand],
            ),
            dict(
                category=CdfCategory.ItalianGovernment.value,
                country_code=COUNTRY_CODES[Countries.Italy],
            ),
        ]
        country_categories[DatasetEnum.Reservations] = []
        country_categories[DatasetEnum.Occupancy] = []
        country_categories[DatasetEnum.Payment] = []
        country_categories[DatasetEnum.Invoices] = []
        country_categories[DatasetEnum.OccupancyV1] = []
        country_categories[DatasetEnum.Housekeeping] = []
        country_categories[DatasetEnum.Accounting] = []
        country_categories[DatasetEnum.Payout] = []

        return country_categories.get(self.kind, [])

    @property
    def multi_levels(self):
        multi_levels = dict()
        multi_levels[DatasetEnum.Financial] = []
        multi_levels[DatasetEnum.Guests] = []
        multi_levels[DatasetEnum.Reservations] = [
            MultiLevelEnum.RoomNights.value,
            MultiLevelEnum.RoomReservations.value,
        ]
        multi_levels[DatasetEnum.Occupancy] = []
        multi_levels[DatasetEnum.Payment] = []
        multi_levels[DatasetEnum.Payout] = []
        multi_levels[DatasetEnum.Invoices] = [MultiLevelEnum.InvoiceItems.value]
        multi_levels[DatasetEnum.OccupancyV1] = [
            MultiLevelEnum.OccupancyReservation.value
        ]
        multi_levels[DatasetEnum.Housekeeping] = []
        multi_levels[DatasetEnum.Accounting] = []
        multi_levels[DatasetEnum.BedOccupancy] = [
            MultiLevelEnum.BedOccupancyReservation.value
        ]

        return multi_levels.get(self.kind, [])

    @property
    def multi_level_flatten_cdfs(self):
        """Method that will return a list of all multi-level cdfs"""
        return [
            cdf
            for multi_level in self.multi_levels
            for cdf in MultiLevel(MultiLevelEnum(multi_level)).cdfs
        ]

    @property
    def flatten_all_cdfs(self):
        """Method that will return a list of all cdfs, including any multi-level cdfs"""
        return self.flatten_cdfs + self.multi_level_flatten_cdfs
