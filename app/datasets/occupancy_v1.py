from sqlalchemy import (
    BIGINT,
    Case,
    Column,
    DATE,
    FLOAT,
    INTEGER,
    NUMERIC,
    VARCHAR,
    and_,
    case,
    cast,
    func,
    literal,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.orm import column_property, declared_attr

from app.common.constants.report_formats import EXCHANGE_RATE_SUFFIX
from app.common.constants.room_type_category import ROOM_TYPE_CATEGORIES
from app.common.database import db
from app.common.enums.features import LaunchDarklyFeature
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class OccupancyV1View(db.Model):
    __tablename__ = "occupancy_v1_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"

    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="A unique identifier assigned to the organization for tracking.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="A unique identifier assigned to the property for tracking.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    room_type_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type ID",
            description="The database ID associated with a room type. This can be useful when merging Cloudbeds data with data from other systems.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    room_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type",
            description="""Room type, also known as Accommodation Type, is the name given to a group of rooms that have
            something in common, such as physical characteristics and are sold under the same price. Ex. Standard vs Deluxe rooms.""",
            kind=CdfKind.String,
        ),
    )
    stay_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Stay Date",
            description="The calendar date being observed for analysis.",
            kind=CdfKind.Date,
        ),
        primary_key=True,
    )
    booking_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Number",
            description="The confirmation number that the CB system automatically assigns to a reservation and that uniquely identifies a reservation.",
            kind=CdfKind.String,
        ),
    )
    booking_room_id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Room ID",
            description="The ID of the room associated with the reservation.",
            kind=CdfKind.Number,
            hide=True,
        ),
    )
    room_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Room Rate",
            description="""Total of all Room Rate transactions posted automatically to both transient and groups reservations
            on a nightly basis, per stay date, per booked room. This is part of Total Room Revenue.""",
            kind=CdfKind.Currency,
        ),
    )
    additional_room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Other Room Revenue",
            description="""Total of all Manual and No-show transactions posted manually by a user.
            This is part of Total Room Revenue. Please note that cancellation fees are not part of
            Other Room Revenue.""",
            kind=CdfKind.Currency,
        ),
    )
    room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Room Revenue",
            description="Room Rate plus Other Room Revenue.",
            kind=CdfKind.Currency,
        ),
    )
    adults_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Adults",
            description="The total number of adults associated with the room for a specific stay date.",
            kind=CdfKind.Number,
        ),
    )
    children_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Children",
            description="The total number of children associated with the room for a specific stay date.",
            kind=CdfKind.Number,
        ),
    )
    room_guest_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Guest Count",
            description="The total number of guests associated with the room for a specific stay date.",
            kind=CdfKind.Number,
        ),
    )
    rooms_sold = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Rooms Sold",
            description="Number of rooms sold or booked for a specific stay date.",
            kind=CdfKind.Currency,
        ),
    )
    capacity_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Capacity",
            description="The total number of rooms available for booking.",
            kind=CdfKind.Number,
        ),
    )

    @declared_attr
    def adr(cls):
        return column_property(
            cast(
                cast(cls.room_revenue, NUMERIC)
                / func.nullif(cast(cls.rooms_sold, NUMERIC), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="ADR",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
            ),
        )

    @declared_attr
    def adr_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.room_revenue, NUMERIC))
                / (
                    func.nullif(
                        (func.sum(cast(cls.rooms_sold, NUMERIC))),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Production,
                name="ADR Summary",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def adr_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    case(
                        (and_(*cases), cls.room_revenue),
                        else_=None,
                    ),
                    NUMERIC,
                )
                / func.nullif(
                    cast(
                        case(
                            (and_(*cases), cls.rooms_sold),
                            else_=None,
                        ),
                        NUMERIC,
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="ADR",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def adr_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast(
                        case(
                            (and_(*cases), cls.room_revenue),
                            else_=None,
                        ),
                        NUMERIC,
                    )
                )
                / (
                    func.nullif(
                        (
                            func.sum(
                                cast(
                                    case(
                                        (and_(*cases), cls.rooms_sold),
                                        else_=None,
                                    ),
                                    NUMERIC,
                                )
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Production,
                name="ADR Summary",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @declared_attr
    def revpar(cls):
        return column_property(
            cast(
                cast(cls.room_revenue, NUMERIC)
                / (func.nullif(cast(cls.capacity_count, NUMERIC), 0)),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="RevPAR",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
            ),
        )

    @declared_attr
    def revpar_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.room_revenue, NUMERIC))
                / func.nullif(func.sum(cast(cls.capacity_count, NUMERIC)), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="RevPAR Summary",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def revpar_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(cast(case((and_(*cases), cls.room_revenue), else_=None), NUMERIC))
            / (
                func.nullif(
                    cast(
                        (case((and_(*cases), cls.capacity_count), else_=None), NUMERIC),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="RevPAR",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def revpar_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast((case((and_(*cases), cls.room_revenue), else_=None)), NUMERIC)
                )
                / func.nullif(
                    func.sum(
                        cast(
                            (case((and_(*cases), cls.capacity_count), else_=None)),
                            NUMERIC,
                        )
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="RevPAR Summary",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @declared_attr
    def occupancy(cls):
        return column_property(
            cast(
                (
                    (
                        cast(
                            cls.rooms_sold,
                            NUMERIC,
                        )
                        / (func.nullif(cast(cls.capacity_count, NUMERIC), 0))
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
            ),
        )

    @declared_attr
    def occupancy_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.rooms_sold, NUMERIC))
                / (func.nullif(func.sum(cast(cls.capacity_count, NUMERIC)), 0)),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy Summary",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def occupancy_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                (
                    (
                        cast(
                            (case((and_(*cases), cls.rooms_sold), else_=None)),
                            NUMERIC,
                        )
                        / (
                            func.nullif(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.capacity_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                ),
                                0,
                            )
                        )
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def occupancy_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast((case((and_(*cases), cls.rooms_sold), else_=None)), NUMERIC)
                )
                / (
                    func.nullif(
                        func.sum(
                            cast(
                                (case((and_(*cases), cls.capacity_count), else_=None)),
                                NUMERIC,
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy Summary",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    # MFD occupancy formula is: Rooms Sold / (Capacity - Total Blocked - Total Out of Service ) *100
    # We will call this CDF Adjusted occupancy
    # This is the occupancy that will be used in the Cloudbeds user interface
    @declared_attr
    def mfd_occupancy(cls):
        return column_property(
            cast(
                (
                    (
                        cast(
                            cls.rooms_sold,
                            NUMERIC,
                        )
                        / (
                            func.nullif(
                                cast(cls.capacity_count, NUMERIC)
                                - cast(cls.blocked_room_count, NUMERIC)
                                - cast(cls.out_of_service_count, NUMERIC),
                                0,
                            )
                        )
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
            ),
        )

    @declared_attr
    def mfd_occupancy_summary(cls):
        return column_property(
            cast(
                cast(
                    func.sum(cast(cls.rooms_sold, NUMERIC))
                    / (
                        func.nullif(
                            func.sum(cast(cls.capacity_count, NUMERIC))
                            - func.sum(cast(cls.blocked_room_count, NUMERIC))
                            - func.sum(cast(cls.out_of_service_count, NUMERIC)),
                            0,
                        )
                    ),
                    NUMERIC,
                )
                * cast(100, NUMERIC),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy Summary",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def mfd_occupancy_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                (
                    (
                        cast(
                            (case((and_(*cases), cls.rooms_sold), else_=None)),
                            NUMERIC,
                        )
                        / (
                            func.nullif(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.capacity_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                                - cast(
                                    (
                                        case(
                                            (and_(*cases), cls.blocked_room_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                                - cast(
                                    (
                                        case(
                                            (and_(*cases), cls.out_of_service_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                ),
                                0,
                            )
                        )
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def mfd_occupancy_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    func.sum(
                        cast(
                            (case((and_(*cases), cls.rooms_sold), else_=None)), NUMERIC
                        )
                    )
                    / (
                        func.nullif(
                            func.sum(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.capacity_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                            )
                            - func.sum(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.blocked_room_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                            )
                            - func.sum(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.out_of_service_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                            ),
                            0,
                        )
                    ),
                    NUMERIC,
                )
                * cast(100, NUMERIC),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy Summary",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    room_taxes = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total taxes",
            description="Total of all taxes.",
            kind=CdfKind.Currency,
        ),
    )

    room_fees = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total fees",
            description="Total of all fees.",
            kind=CdfKind.Currency,
        ),
    )

    misc_income = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Miscellaneous Income",
            description="Total of miscellaneous income including cancellation fees and their adjustments.",
            kind=CdfKind.Currency,
        ),
    )

    total_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Revenue",
            description="""Sum of total room revenue and total other revenue.
            This includes items and services, add-ons, cancellation fees, adjustments.
            Excludes inclusive or exclusive taxes and fees.""",
            kind=CdfKind.Currency,
        ),
    )

    non_room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Other Revenue",
            description="Total of all non-room revenue including items & services, add-ons, adjustments.",
            kind=CdfKind.Currency,
        ),
    )
    out_of_service_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Out of Service Room Count",
            description="The number of rooms that are marked as Out of Service.",
            kind=CdfKind.Number,
        ),
    )
    blocked_room_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Blocked Room Count",
            description="The number of rooms that are marked as Blocked.",
            kind=CdfKind.Number,
        ),
    )


class OccupancyV1FFView(db.Model):
    __tablename__ = "occupancy_v1_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"

    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="A unique identifier assigned to the organization for tracking.",
            kind=CdfKind.Identifier,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="A unique identifier assigned to the property for tracking.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    room_type_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type ID",
            description="The database ID associated with a room type. This can be useful when merging Cloudbeds data with data from other systems.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    room_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type",
            description="""Room type, also known as Accommodation Type, is the name given to a group of rooms that have
            something in common, such as physical characteristics and are sold under the same price. Ex. Standard vs Deluxe rooms.""",
            kind=CdfKind.String,
        ),
    )
    stay_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Stay Date",
            description="The calendar date being observed for analysis.",
            kind=CdfKind.Date,
        ),
        primary_key=True,
    )
    booking_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Number",
            description="The confirmation number that the CB system automatically assigns to a reservation and that uniquely identifies a reservation.",
            kind=CdfKind.String,
        ),
    )
    booking_room_id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Room ID",
            description="The ID of the room associated with the reservation.",
            kind=CdfKind.Number,
            hide=True,
        ),
    )
    room_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Room Rate",
            description="""Total of all Room Rate transactions posted automatically to both transient and groups reservations
            on a nightly basis, per stay date, per booked room. This is part of Total Room Revenue.""",
            kind=CdfKind.Currency,
        ),
    )
    additional_room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Other Room Revenue",
            description="""Total of all Manual and No-show transactions posted manually by a user.
            This is part of Total Room Revenue. Please note that cancellation fees are not part of
            Other Room Revenue.""",
            kind=CdfKind.Currency,
        ),
    )
    room_rate_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Room Rate {EXCHANGE_RATE_SUFFIX}",
            description="""Total of all Room Rate transactions posted automatically to both transient and groups reservations
            on a nightly basis, per stay date, per booked room. This is part of Total Room Revenue.""",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.room_rate
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.room_rate
            ),
        ),
    )
    additional_room_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Other Room Revenue {EXCHANGE_RATE_SUFFIX}",
            description="""Total of all Manual and No-show transactions posted manually by a user.
            This is part of Total Room Revenue. Please note that cancellation fees are not part of
            Other Room Revenue.""",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.additional_room_revenue
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.additional_room_revenue
            ),
        ),
    )
    room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Room Revenue",
            description="Room Rate plus Other Room Revenue.",
            kind=CdfKind.Currency,
        ),
    )
    room_revenue_converted_rate = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total Room Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Room Rate plus Other Room Revenue.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.room_revenue
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.room_revenue
            ),
        ),
    )
    adults_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Adults",
            description="The total number of adults associated with the room for a specific stay date.",
            kind=CdfKind.Number,
        ),
    )
    children_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Children",
            description="The total number of children associated with the room for a specific stay date.",
            kind=CdfKind.Number,
        ),
    )
    room_guest_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Guest Count",
            description="The total number of guests associated with the room for a specific stay date.",
            kind=CdfKind.Number,
        ),
    )
    rooms_sold = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Rooms Sold",
            description="Number of rooms sold or booked for a specific stay date.",
            kind=CdfKind.Number,
        ),
    )

    capacity_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Capacity",
            description="The total number of rooms available for booking.",
            kind=CdfKind.Number,
        ),
    )

    @declared_attr
    def adr(cls):
        return column_property(
            cast(
                cast(cls.room_revenue, NUMERIC)
                / func.nullif(cast(cls.rooms_sold, NUMERIC), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="ADR",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
            ),
        )

    @declared_attr
    def adr_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.room_revenue, NUMERIC))
                / (
                    func.nullif(
                        (func.sum(cast(cls.rooms_sold, NUMERIC))),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Production,
                name="ADR Summary",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def adr_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    case(
                        (and_(*cases), cls.room_revenue),
                        else_=None,
                    ),
                    NUMERIC,
                )
                / func.nullif(
                    cast(
                        case(
                            (and_(*cases), cls.rooms_sold),
                            else_=None,
                        ),
                        NUMERIC,
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="ADR",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def adr_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast(
                        case(
                            (and_(*cases), cls.room_revenue),
                            else_=None,
                        ),
                        NUMERIC,
                    )
                )
                / (
                    func.nullif(
                        (
                            func.sum(
                                cast(
                                    case(
                                        (and_(*cases), cls.rooms_sold),
                                        else_=None,
                                    ),
                                    NUMERIC,
                                )
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Production,
                name="ADR Summary",
                description="The average daily rate for all rooms sold.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    adr_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"ADR {EXCHANGE_RATE_SUFFIX}",
            description="The average daily rate for all rooms sold, adjusted by currency exchange rate.",
            kind=CdfKind.DynamicCurrency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                (
                    cast(cls.room_revenue, NUMERIC)
                    * func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                    )
                    if kwargs.get("currency")
                    else cast(cls.room_revenue, NUMERIC)
                )
                / func.nullif(cast(cls.rooms_sold, NUMERIC), 0),
                FLOAT,
            ),
        ),
    )

    adr_converted_rate_summary = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"ADR Summary {EXCHANGE_RATE_SUFFIX}",
            description="The average daily rate for all rooms sold (aggregated), adjusted by currency exchange rate.",
            kind=CdfKind.DynamicCurrency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            hide=True,
            function=lambda cls, **kwargs: cast(
                func.sum(
                    cast(cls.room_revenue, NUMERIC)
                    * func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                    )
                    if kwargs.get("currency")
                    else cast(cls.room_revenue, NUMERIC)
                )
                / func.nullif(func.sum(cast(cls.rooms_sold, NUMERIC)), 0),
                FLOAT,
            ),
        ),
    )

    @hybrid_method
    def adr_converted_rate_cases(cls, cases: list[Case], **kwargs):
        revenue = case((and_(*cases), cls.room_revenue), else_=None)
        rooms_sold = case((and_(*cases), cls.rooms_sold), else_=None)
        currency = kwargs.get("currency", None)

        return column_property(
            cast(
                (
                    cast(revenue, NUMERIC)
                    * func.cast(cls.historical_exchange_rates[currency], FLOAT)
                    if currency
                    else cast(revenue, NUMERIC)
                )
                / func.nullif(cast(rooms_sold, NUMERIC), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"ADR {EXCHANGE_RATE_SUFFIX} Cases",
                description="ADR with conditional filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicCurrency,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def adr_converted_rate_summary_cases(cls, cases: list[Case], **kwargs):
        currency = kwargs.get("currency")

        revenue_expr = cast(
            case((and_(*cases), cls.room_revenue), else_=None),
            NUMERIC,
        )
        rooms_sold_expr = cast(
            case((and_(*cases), cls.rooms_sold), else_=None),
            NUMERIC,
        )

        if currency:
            revenue_sum = func.sum(
                revenue_expr * func.cast(cls.historical_exchange_rates[currency], FLOAT)
            )
        else:
            revenue_sum = func.sum(revenue_expr)

        rooms_sold_sum = func.sum(rooms_sold_expr)

        return column_property(
            cast(
                revenue_sum / func.nullif(rooms_sold_sum, 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"ADR {EXCHANGE_RATE_SUFFIX} Summary Cases",
                description="ADR summary with conditional filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicCurrency,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @declared_attr
    def revpar(cls):
        return column_property(
            cast(
                cast(cls.room_revenue, NUMERIC)
                / (func.nullif(cast(cls.capacity_count, NUMERIC), 0)),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="RevPAR",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
            ),
        )

    @declared_attr
    def revpar_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.room_revenue, NUMERIC))
                / func.nullif(func.sum(cast(cls.capacity_count, NUMERIC)), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="RevPAR Summary",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def revpar_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(cast(case((and_(*cases), cls.room_revenue), else_=None), NUMERIC))
            / (
                func.nullif(
                    cast(
                        (case((and_(*cases), cls.capacity_count), else_=None), NUMERIC),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Booking,
                name="RevPAR",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def revpar_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast((case((and_(*cases), cls.room_revenue), else_=None)), NUMERIC)
                )
                / func.nullif(
                    func.sum(
                        cast(
                            (case((and_(*cases), cls.capacity_count), else_=None)),
                            NUMERIC,
                        )
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="RevPAR Summary",
                description="The total room revenue earned per available room during the reporting period.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    revpar_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"RevPAR {EXCHANGE_RATE_SUFFIX}",
            description="RevPAR adjusted by currency exchange rate.",
            kind=CdfKind.DynamicCurrency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                (
                    cast(cls.room_revenue, NUMERIC)
                    * func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                    )
                    if kwargs.get("currency")
                    else cast(cls.room_revenue, NUMERIC)
                )
                / func.nullif(cast(cls.capacity_count, NUMERIC), 0),
                FLOAT,
            ),
        ),
    )

    revpar_converted_rate_summary = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"RevPAR Summary {EXCHANGE_RATE_SUFFIX}",
            description="Aggregated RevPAR adjusted by currency exchange rate.",
            kind=CdfKind.DynamicCurrency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            hide=True,
            function=lambda cls, **kwargs: cast(
                (
                    func.sum(
                        cast(cls.room_revenue, NUMERIC)
                        * func.cast(
                            cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                        )
                    )
                    if kwargs.get("currency")
                    else func.sum(cast(cls.room_revenue, NUMERIC))
                )
                / func.nullif(func.sum(cast(cls.capacity_count, NUMERIC)), 0),
                FLOAT,
            ),
        ),
    )

    @hybrid_method
    def revpar_converted_rate_cases(cls, cases: list[Case], **kwargs):
        revenue = case((and_(*cases), cls.room_revenue), else_=None)
        capacity = case((and_(*cases), cls.capacity_count), else_=None)
        currency = kwargs.get("currency")

        return column_property(
            cast(
                (
                    cast(revenue, NUMERIC)
                    * func.cast(cls.historical_exchange_rates[currency], FLOAT)
                    if currency
                    else cast(revenue, NUMERIC)
                )
                / func.nullif(cast(capacity, NUMERIC), 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"RevPAR {EXCHANGE_RATE_SUFFIX} Cases",
                description="RevPAR with conditional filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicCurrency,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def revpar_converted_rate_summary_cases(cls, cases: list[Case], **kwargs):
        currency = kwargs.get("currency")

        revenue_expr = cast(
            case((and_(*cases), cls.room_revenue), else_=None),
            NUMERIC,
        )
        capacity_expr = cast(
            case((and_(*cases), cls.capacity_count), else_=None),
            NUMERIC,
        )

        if currency:
            revenue_sum = func.sum(
                revenue_expr * func.cast(cls.historical_exchange_rates[currency], FLOAT)
            )
        else:
            revenue_sum = func.sum(revenue_expr)

        capacity_sum = func.sum(capacity_expr)

        return column_property(
            cast(
                revenue_sum / func.nullif(capacity_sum, 0),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"RevPAR Summary {EXCHANGE_RATE_SUFFIX} Cases",
                description="Aggregated RevPAR with filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicCurrency,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @declared_attr
    def occupancy(cls):
        return column_property(
            cast(
                (
                    (
                        cast(
                            cls.rooms_sold,
                            NUMERIC,
                        )
                        / (func.nullif(cast(cls.capacity_count, NUMERIC), 0))
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
            ),
        )

    @declared_attr
    def occupancy_summary(cls):
        return column_property(
            cast(
                func.sum(cast(cls.rooms_sold, NUMERIC))
                / (func.nullif(func.sum(cast(cls.capacity_count, NUMERIC)), 0)),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy Summary",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def occupancy_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                (
                    (
                        cast(
                            (case((and_(*cases), cls.rooms_sold), else_=None)),
                            NUMERIC,
                        )
                        / (
                            func.nullif(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.capacity_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                ),
                                0,
                            )
                        )
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def occupancy_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast((case((and_(*cases), cls.rooms_sold), else_=None)), NUMERIC)
                )
                / (
                    func.nullif(
                        func.sum(
                            cast(
                                (case((and_(*cases), cls.capacity_count), else_=None)),
                                NUMERIC,
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy Summary",
                description=(
                    "The percentage of Rooms Sold divided by Capacity. "
                    "This occupancy calculation does not account for "
                    "Blocked Room Count or Out of Service Room Count."
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    occupancy_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Occupancy {EXCHANGE_RATE_SUFFIX}",
            description="Occupancy adjusted by exchange rate.",
            kind=CdfKind.DynamicPercentage,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                (
                    (
                        cast(cls.rooms_sold, NUMERIC)
                        * func.cast(
                            cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                        )
                        if kwargs.get("currency")
                        else cast(cls.rooms_sold, NUMERIC)
                    )
                    / func.nullif(cast(cls.capacity_count, NUMERIC), 0)
                )
                * 100,
                FLOAT,
            ),
        ),
    )

    occupancy_converted_rate_summary = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Occupancy Summary {EXCHANGE_RATE_SUFFIX}",
            description="Aggregated occupancy adjusted by exchange rate.",
            kind=CdfKind.DynamicPercentage,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            hide=True,
            function=lambda cls, **kwargs: cast(
                (
                    (
                        func.sum(
                            cast(cls.rooms_sold, NUMERIC)
                            * func.cast(
                                cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                            )
                        )
                        if kwargs.get("currency")
                        else func.sum(cast(cls.rooms_sold, NUMERIC))
                    )
                    / func.nullif(func.sum(cast(cls.capacity_count, NUMERIC)), 0)
                )
                * 100,
                FLOAT,
            ),
        ),
    )

    @hybrid_method
    def occupancy_converted_rate_cases(cls, cases: list[Case], **kwargs):
        sold = case((and_(*cases), cls.rooms_sold), else_=None)
        capacity = case((and_(*cases), cls.capacity_count), else_=None)
        currency = kwargs.get("currency")

        return column_property(
            cast(
                (
                    (
                        cast(sold, NUMERIC)
                        * func.cast(cls.historical_exchange_rates[currency], FLOAT)
                        if currency
                        else cast(sold, NUMERIC)
                    )
                    / func.nullif(cast(capacity, NUMERIC), 0)
                )
                * 100,
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"Occupancy {EXCHANGE_RATE_SUFFIX} Cases",
                description="Occupancy with filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicPercentage,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def occupancy_converted_rate_summary_cases(cls, cases: list[Case], **kwargs):
        currency = kwargs.get("currency")

        sold_sum = (
            func.sum(
                cast(
                    case((and_(*cases), cls.rooms_sold), else_=None)
                    * func.cast(cls.historical_exchange_rates[currency], FLOAT),
                    NUMERIC,
                )
            )
            if currency
            else func.sum(
                cast(case((and_(*cases), cls.rooms_sold), else_=None), NUMERIC)
            )
        )

        capacity_sum = func.sum(
            cast(case((and_(*cases), cls.capacity_count), else_=None), NUMERIC)
        )

        return column_property(
            cast(
                (sold_sum / func.nullif(capacity_sum, 0)) * 100,
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"Occupancy Summary {EXCHANGE_RATE_SUFFIX} Cases",
                description="Aggregated occupancy with filters, adjusted by exchange rate.",
                kind=CdfKind.DynamicPercentage,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    # MFD occupancy formula is: Rooms Sold / (Capacity - Total Blocked - Total Out of Service ) *100
    # We will call this CDF Adjusted occupancy
    # This is the occupancy that will be used in the Cloudbeds user interface
    @declared_attr
    def mfd_occupancy(cls):
        return column_property(
            cast(
                (
                    (
                        cast(
                            cls.rooms_sold,
                            NUMERIC,
                        )
                        / (
                            func.nullif(
                                cast(cls.capacity_count, NUMERIC)
                                - cast(cls.blocked_room_count, NUMERIC)
                                - cast(cls.out_of_service_count, NUMERIC),
                                0,
                            )
                        )
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
            ),
        )

    @declared_attr
    def mfd_occupancy_summary(cls):
        return column_property(
            cast(
                cast(
                    func.sum(cast(cls.rooms_sold, NUMERIC))
                    / (
                        func.nullif(
                            func.sum(cast(cls.capacity_count, NUMERIC))
                            - func.sum(cast(cls.blocked_room_count, NUMERIC))
                            - func.sum(cast(cls.out_of_service_count, NUMERIC)),
                            0,
                        )
                    ),
                    NUMERIC,
                )
                * cast(100, NUMERIC),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy Summary",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def mfd_occupancy_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                (
                    (
                        cast(
                            (case((and_(*cases), cls.rooms_sold), else_=None)),
                            NUMERIC,
                        )
                        / (
                            func.nullif(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.capacity_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                                - cast(
                                    (
                                        case(
                                            (and_(*cases), cls.blocked_room_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                                - cast(
                                    (
                                        case(
                                            (and_(*cases), cls.out_of_service_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                ),
                                0,
                            )
                        )
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def mfd_occupancy_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    func.sum(
                        cast(
                            (case((and_(*cases), cls.rooms_sold), else_=None)), NUMERIC
                        )
                    )
                    / (
                        func.nullif(
                            func.sum(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.capacity_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                            )
                            - func.sum(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.blocked_room_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                            )
                            - func.sum(
                                cast(
                                    (
                                        case(
                                            (and_(*cases), cls.out_of_service_count),
                                            else_=None,
                                        )
                                    ),
                                    NUMERIC,
                                )
                            ),
                            0,
                        )
                    ),
                    NUMERIC,
                )
                * cast(100, NUMERIC),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Adjusted Occupancy Summary",
                description=(
                    """Percentage of rooms sold divided by total rooms available. For properties
                    with split inventory, Total Rooms Available includes physical rooms only.
                    Blocked rooms and out of service rooms are removed from Total Rooms Available."""
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    mfd_occupancy_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Adjusted Occupancy {EXCHANGE_RATE_SUFFIX}",
            description="Adjusted Occupancy with exchange rate applied to Rooms Sold.",
            kind=CdfKind.DynamicPercentage,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                (
                    (
                        cast(cls.rooms_sold, NUMERIC)
                        * func.cast(
                            cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                        )
                        if kwargs.get("currency")
                        else cast(cls.rooms_sold, NUMERIC)
                    )
                    / func.nullif(
                        cast(cls.capacity_count, NUMERIC)
                        - cast(cls.blocked_room_count, NUMERIC)
                        - cast(cls.out_of_service_count, NUMERIC),
                        0,
                    )
                )
                * 100,
                FLOAT,
            ),
        ),
    )

    mfd_occupancy_converted_rate_summary = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Adjusted Occupancy Summary {EXCHANGE_RATE_SUFFIX}",
            description="Aggregated Adjusted Occupancy with exchange rate applied to Rooms Sold.",
            kind=CdfKind.DynamicPercentage,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            hide=True,
            function=lambda cls, **kwargs: cast(
                (
                    func.sum(
                        cast(cls.rooms_sold, NUMERIC)
                        * func.cast(
                            cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                        )
                    )
                    if kwargs.get("currency")
                    else func.sum(cast(cls.rooms_sold, NUMERIC))
                )
                / func.nullif(
                    func.sum(cast(cls.capacity_count, NUMERIC))
                    - func.sum(cast(cls.blocked_room_count, NUMERIC))
                    - func.sum(cast(cls.out_of_service_count, NUMERIC)),
                    0,
                )
                * 100,
                FLOAT,
            ),
        ),
    )

    @hybrid_method
    def mfd_occupancy_converted_rate_cases(cls, cases: list[Case], **kwargs):
        sold = case((and_(*cases), cls.rooms_sold), else_=None)
        capacity = case((and_(*cases), cls.capacity_count), else_=None)
        blocked = case((and_(*cases), cls.blocked_room_count), else_=None)
        oos = case((and_(*cases), cls.out_of_service_count), else_=None)
        currency = kwargs.get("currency")

        return column_property(
            cast(
                (
                    (
                        cast(sold, NUMERIC)
                        * func.cast(cls.historical_exchange_rates[currency], FLOAT)
                        if currency
                        else cast(sold, NUMERIC)
                    )
                    / func.nullif(
                        cast(capacity, NUMERIC)
                        - cast(blocked, NUMERIC)
                        - cast(oos, NUMERIC),
                        0,
                    )
                )
                * 100,
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"Adjusted Occupancy {EXCHANGE_RATE_SUFFIX} Cases",
                description="Adjusted occupancy with filters and exchange rate.",
                kind=CdfKind.DynamicPercentage,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def mfd_occupancy_converted_rate_summary_cases(cls, cases: list[Case], **kwargs):
        currency = kwargs.get("currency")

        sold_sum = (
            func.sum(
                cast(
                    case((and_(*cases), cls.rooms_sold), else_=None),
                    NUMERIC,
                )
                * func.cast(cls.historical_exchange_rates[currency], FLOAT)
            )
            if currency
            else func.sum(
                cast(case((and_(*cases), cls.rooms_sold), else_=None), NUMERIC)
            )
        )

        capacity_sum = func.sum(
            cast(case((and_(*cases), cls.capacity_count), else_=None), NUMERIC)
        )
        blocked_sum = func.sum(
            cast(case((and_(*cases), cls.blocked_room_count), else_=None), NUMERIC)
        )
        oos_sum = func.sum(
            cast(case((and_(*cases), cls.out_of_service_count), else_=None), NUMERIC)
        )

        return column_property(
            cast(
                (sold_sum / func.nullif(capacity_sum - blocked_sum - oos_sum, 0)) * 100,
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.ConvertedCurrency,
                name=f"Adjusted Occupancy Summary {EXCHANGE_RATE_SUFFIX} Cases",
                description="Aggregated adjusted occupancy with filters and exchange rate.",
                kind=CdfKind.DynamicPercentage,
                feature_flag=LaunchDarklyFeature.ReportingCurrency,
                hide=True,
            ),
        )

    room_taxes = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total taxes",
            description="Total of all taxes.",
            kind=CdfKind.Currency,
        ),
    )

    room_taxes_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total taxes {EXCHANGE_RATE_SUFFIX}",
            description="Total of all taxes.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.room_taxes
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.room_taxes
            ),
        ),
    )

    room_fees = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total fees",
            description="Total of all fees.",
            kind=CdfKind.Currency,
        ),
    )

    room_fees_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total fees {EXCHANGE_RATE_SUFFIX}",
            description="Total of all fees.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cls.room_fees
                * func.cast(
                    cls.historical_exchange_rates[kwargs["currency"]],
                    FLOAT,
                )
                if kwargs.get("currency")
                else cls.room_fees
            ),
        ),
    )

    misc_income = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Miscellaneous Income",
            description="Total of miscellaneous income including cancellation fees and their adjustments.",
            kind=CdfKind.Currency,
        ),
    )

    misc_income_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Miscellaneous Income {EXCHANGE_RATE_SUFFIX}",
            description="Total of miscellaneous income adjusted by exchange rate, including cancellation fees and adjustments.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cast(
                    cls.misc_income
                    * func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                    )
                    if kwargs.get("currency")
                    else cls.misc_income,
                    FLOAT,
                )
            ),
        ),
    )

    total_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Revenue",
            description="""Sum of total room revenue and total other revenue.
            This includes items and services, add-ons, cancellation fees, adjustments.
            Excludes inclusive or exclusive taxes and fees.""",
            kind=CdfKind.Currency,
        ),
    )

    total_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Total revenue including rooms, items, services, cancellation fees and adjustments, adjusted by exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: (
                cast(
                    cls.total_revenue
                    * func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]], FLOAT
                    )
                    if kwargs.get("currency")
                    else cls.total_revenue,
                    FLOAT,
                )
            ),
        ),
    )

    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property.",
            kind=CdfKind.String,
        ),
    )
    conversion_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Conversion Rate",
            description="The amount of the conversion rate for that day.",
            kind=CdfKind.Currency,
            function=lambda cls, **kwargs: (
                func.coalesce(
                    func.cast(
                        cls.historical_exchange_rates[kwargs["currency"]],
                        FLOAT,
                    ),
                    1,
                )
                if kwargs.get("currency")
                else literal(1)
            ),
        ),
    )

    historical_exchange_rates = Column(
        JSONB,
        info=dict(
            category=CdfCategory.Production,
            name="Historical Exchange Rates",
            description="Historical Exchange Rates",
            kind=CdfKind.String,
            hide=True,
        ),
    )

    rooms_available = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Rooms Available",
            description=""""Total number of rooms that are available for sale for a
            specific stay date. Sold, Blocked and out of service rooms are included
            in Rooms Available. For properties with split inventory this will
            count physical rooms only by using the virtual to physical linked
            relationships specified in the PMS.""",
            kind=CdfKind.Number,
        ),
    )

    room_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Room ID",
            description="Unique identifier for the room.",
            kind=CdfKind.Identifier,
        ),
    )

    room_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Number",
            description="The number or name that identifies the room(s) associated with the reservation.",
            kind=CdfKind.String,
        ),
    )

    room_type_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type Category",
            description="Shows if the room booked was either a private room or a shared room type.",
            kind=CdfKind.PickList,
            options=ROOM_TYPE_CATEGORIES,
        ),
    )

    accommodation_kind = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Accommodation Kind",
            description="Indicates if the room is 'Physical' or 'Virtual' for properties using split inventory.",
            kind=CdfKind.PickList,
            options=("Physical", "Virtual"),
        ),
    )

    allotment_blocked_room_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Allotment Blocked Rooms",
            description="""Total number of rooms marked as Allotment Blocked.
            For properties with split inventory this will count physical rooms
            only by using the virtual to physical linked relationships defined
            in the PMS.
            """,
            kind=CdfKind.Number,
        ),
    )

    assumed_assignment_flag = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Assumed Assignment Flag",
            description="Flag indicating if the room was booked without being assigned a room and was given an assumed assignment for reporting purposes. (Yes/No)",
            kind=CdfKind.String,
        ),
    )

    total_room_revenue_adjustments = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Room Revenue Adjustments",
            description="Total of room revenue adjustments including manual room revenue adjustments. Excludes inclusive and exclusive taxes and fees adjustments.",
            kind=CdfKind.Currency,
        ),
    )

    total_room_revenue_adjustments_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total Room Revenue Adjustments {EXCHANGE_RATE_SUFFIX}",
            description="Total of room revenue adjustments including manual room revenue adjustments, adjusted by exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                cls.total_room_revenue_adjustments
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.total_room_revenue_adjustments,
                FLOAT,
            ),
        ),
    )

    non_room_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Other Revenue",
            description="""Total of other revenue not categorized as room
            revenue excluding inclusive and exclusive taxes and fees. This
            includes items and services, add-ons, cancellation fees, adjustments.
            """,
            kind=CdfKind.Currency,
        ),
    )

    non_room_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Other Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Total of other revenue not categorized as room revenue, adjusted by exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                cls.non_room_revenue
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.non_room_revenue,
                FLOAT,
            ),
        ),
    )

    other_revenue_adjustments = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Other Revenue Adjustments",
            description="""The total of adjustments for revenue from items and
            services, custom items (POS), add-ons and adjustments for each
            excluding inclusive and exclusive taxes and fees.
            """,
            kind=CdfKind.Currency,
        ),
    )

    other_revenue_adjustments_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Other Revenue Adjustments {EXCHANGE_RATE_SUFFIX}",
            description="Adjustments for revenue from items/services/POS/add-ons, adjusted by exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                cls.other_revenue_adjustments
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.other_revenue_adjustments,
                FLOAT,
            ),
        ),
    )

    total_other_revenue = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Other Revenue",
            description="""Total of other revenue not categorized as room revenue
            excluding inclusive and exclusive taxes and fees. This includes
            items and services, add-ons, cancellation fees, adjustments. """,
            kind=CdfKind.Currency,
        ),
    )

    total_other_revenue_converted_rate = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.ConvertedCurrency,
            name=f"Total Other Revenue {EXCHANGE_RATE_SUFFIX}",
            description="Total of other non-room revenue, adjusted by exchange rate.",
            kind=CdfKind.Currency,
            feature_flag=LaunchDarklyFeature.ReportingCurrency,
            function=lambda cls, **kwargs: cast(
                cls.total_other_revenue
                * func.cast(cls.historical_exchange_rates[kwargs["currency"]], FLOAT)
                if kwargs.get("currency")
                else cls.total_other_revenue,
                FLOAT,
            ),
        ),
    )

    original_currency = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Original Currency",
            description="""The type of money used to complete the transaction at the
            time it was made represented by a currency code (e.g., EUR for Euro)""",
            kind=CdfKind.String,
        ),
    )

    out_of_service_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Out of Service Rooms",
            description="""Total number of rooms marked as Out of Service. For
            properties with split inventory this will count physical rooms only
            by using the virtual to physical linked relationships defined in
            the PMS.""",
            kind=CdfKind.Number,
        ),
    )

    blocked_room_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Blocked Rooms",
            description="""Total number of rooms marked as Blocked. For properties
            with split inventory this will count physical rooms only by using the
            virtual to physical linked relationships defined in the PMS""",
            kind=CdfKind.Number,
        ),
    )
