from sqlalchemy import BIGINT, Column, DATE, FLOAT, TIMESTAMP, VARCHAR

from app.common.constants.inventory_object import INVENTORY_OBJECT_TYPES
from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class PaymentMixin(object):
    id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    card_issue_country = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Card Issue Country Code",
            description="The country associated with the issuing bank.",
            kind=CdfKind.String,
        ),
    )
    card_last_4_digits = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Card Last 4 Digits",
            description="Last 4 digits of card, needed when verifying what card has been used.",
            kind=CdfKind.String,
        ),
    )
    card_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Card Type",
            description="The card type on file for the reservation, for example, Visa, Discover, etc.",
            kind=CdfKind.String,
        ),
    )
    checkin_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-In Date",
            description="Date the reservation starts, when the guest arrives, the first room night.",
            kind=CdfKind.Date,
        ),
    )
    checkout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-Out Date",
            description="Date the reservation ends and the guest departs.",
            kind=CdfKind.Date,
        ),
    )
    payment_fee_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Fee",
            description="The processing fee charged for the transaction.",
            kind=CdfKind.Currency,
        ),
    )
    payment_gateway = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Payment Gateway",
            description="Name of the gateway processor.",
            kind=CdfKind.String,
        ),
    )
    payment_gateway_result = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Gateway Error Reason",
            description="Describes the reason for a failed transaction.",
            kind=CdfKind.String,
        ),
    )
    payment_gateway_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Gateway Status",
            description="The status of the gateway processing.",
            kind=CdfKind.String,
        ),
    )
    payment_gateway_transaction_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Gateway Transaction ID",
            description="The ID associated with the gateway request and response.",
            kind=CdfKind.String,
        ),
    )
    payment_method = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Payment Method",
            description="The method of payment used for each payment transaction, including Credit Card, Bank Transfer, PayPal or any custom payment method.",
            kind=CdfKind.String,
        ),
    )
    payment_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Payment Type",
            description="The state of a payment transaction, indicating whether it has been authorized, purchased, refunded, voided, captured, or a chargeback.",
            kind=CdfKind.String,
        ),
    )
    payment_net_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Net",
            description="The amount paid to the merchant. Equals amount submitted minus fees.",
            kind=CdfKind.Currency,
        ),
    )
    payment_schedule_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Schedule Date Time - UTC",
            description="The date a payment is scheduled to be processed, in UTC.",
            kind=CdfKind.Timestamp,
        ),
    )
    payment_submitted_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Amount Submitted",
            description="The amount that is requested to be paid by the card holder.",
            kind=CdfKind.Currency,
        ),
    )
    pos_entry_mode = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="POS Entry Mode",
            description="The means by which the card information was captured.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_full_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Full Name",
            description="First name and surname (last name) associated with the booking.",
            kind=CdfKind.String,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property. Having a unique ID per property is useful when integrating with other systems.",
            kind=CdfKind.Identifier,
        ),
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property.",
            kind=CdfKind.String,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Number",
            description="The ID automatically generated and used by Cloudbeds system to uniquely identify reservations. This can be found in the URL of the reservation"
            ", not to be confused with the Reservation Number that appears on the reservation detail page.",
            kind=CdfKind.String,
        ),
    )
    terminal_label = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Terminal Label",
            description="The merchant's name of the terminal.",
            kind=CdfKind.String,
        ),
    )
    refunded_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Refunded Amount",
            description="The amount that has been refunded for a particular transaction.",
            kind=CdfKind.Currency,
        ),
    )
    captured_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Captured Amount",
            description="The amount that has been captured for a particular transaction.",
            kind=CdfKind.Currency,
        ),
    )
    payout_id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout ID",
            description="The unique identifier assigned to each payout.",
            kind=CdfKind.String,
        ),
    )
    payout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout Date",
            description="The date and time when a payout was created.",
            kind=CdfKind.Date,
        ),
    )
    payout_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout Status",
            description="The current status of a payout.",
            kind=CdfKind.String,
        ),
    )
    payout_net_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout Amount",
            description="The total amount paid out to the beneficiary after deductions.",
            kind=CdfKind.Currency,
        ),
    )
    payout_fee_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payout,
            name="Payout Fee Amount",
            description="The fee deducted from the payout before disbursing the final amount.",
            kind=CdfKind.Currency,
        ),
    )
    payment_schedule_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Schedule Date Time - Property",
            description="The date payment is scheduled to be processed, per the property's timezone.",
            kind=CdfKind.Timestamp,
        ),
    )
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    payment_entry_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Entry Type",
            description="Indicates whether a transaction was processed or recorded.",
            kind=CdfKind.PickList,
            options=(
                "Recorded",
                "Processed",
            ),
        ),
    )
    cloudbeds_payment_flag = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Payment,
            name="Cloudbeds Payment Flag",
            description="Flag indicating whether the processed transaction was a Cloudbeds Payment.",
            kind=CdfKind.PickList,
            options=("Yes", "No"),
        ),
    )
    card_created_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Payment,
            name="Card Created Date Time - UTC",
            description="The date the card was created, in UTC.",
            kind=CdfKind.Timestamp,
        ),
    )
    group_profile_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Name",
            description="The master profile for a group of reservations, which may be associated with a group block.",
            kind=CdfKind.String,
        ),
    )
    house_account_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="House Account Name",
            description="Name of the house account associated with the transaction, used for registering transactions not connected to any specific room, guest, or reservation.",
            kind=CdfKind.String,
        ),
    )
    payment_fee_adjustment = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payment,
            name="Payment Fee Adjustment",
            description="An adjustment to the transaction fee, which includes interchange fees and may include other processing-related charges.",
            kind=CdfKind.Currency,
        ),
    )
    payment_total_fee = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Payment,
            name="Total Payment Fee",
            description="The sum of the processing fee and any adjustments.",
            kind=CdfKind.Currency,
        ),
    )
    inventory_object_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Inventory Object ID",
            description="A unique identifier assigned to each item or unit within the Account Receivable Ledger.",
            kind=CdfKind.Identifier,
        ),
    )
    inventory_object_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Inventory Object Type",
            description="A classification that defines the kind or category of the inventory item e.g Group Profile, House Account, Reservation, AR Ledger etc.",
            kind=CdfKind.PickList,
            options=INVENTORY_OBJECT_TYPES,
        ),
    )


class PaymentView(PaymentMixin, db.Model):
    __tablename__ = "payments_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class PaymentFFView(PaymentMixin, db.Model):
    __tablename__ = "payments_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
