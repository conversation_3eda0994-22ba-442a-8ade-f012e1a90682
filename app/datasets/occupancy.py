from sqlalchemy import (
    BIGINT,
    Case,
    Column,
    DATE,
    FLOAT,
    INTEGER,
    NUMERIC,
    VARCHAR,
    and_,
    case,
    cast,
    func,
)
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.orm import column_property

from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class OccupancyView(db.Model):
    __tablename__ = "occupancy_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"

    id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Occupancy ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.String,
        ),
        primary_key=True,
    )
    bed_based_capacity = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Production,
            name="Bed Based Capacity",
            description="The number of guests that a property can accommodate. For example a hotel with 20 rooms, each with a king bed, could accommodate "
            "40 people, 2 per king bed.",
            kind=CdfKind.Number,
        ),
    )
    beds_per_room = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Production,
            name="Bed Places per Accommodation",
            description="The capacity of the room type.",
            kind=CdfKind.Number,
        ),
    )
    blocked_room_duration = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Blocked Room Duration",
            description="How long the room was blocked for.",
            kind=CdfKind.Number,
        ),
    )
    blocked_room_type_a = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Blocked Room Type",
            description="The type of room that has been blocked.",
            kind=CdfKind.Number,
        ),
    )
    booking_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Date",
            description="Also known as Booking Date. When the reservation was originally made.",
            kind=CdfKind.Date,
        ),
    )
    booking_qty_type_a = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Room Nights",
            description="The number of room nights associated with the reservation. For example, if a reservation has two rooms that both check in on "
            "Sunday and check out on Tuesday, that would include 4 room nights.",
            kind=CdfKind.Number,
        ),
    )
    booking_room_id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Room ID",
            description="The ID of the room associated with the reservation.",
            kind=CdfKind.Number,
        ),
    )
    capacity_type_a = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Capacity",
            description="The total rooms that could be booked. Includes sold rooms and available rooms. Excludes out of service rooms.",
            kind=CdfKind.Number,
        ),
    )
    guest_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Guest Count",
            description="Total number of guests associated with the reservation, including adults and children.",
            kind=CdfKind.Number,
        ),
    )

    out_of_service_duration = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Out of Service Duration",
            description="The number of days a room is out of service for.",
            kind=CdfKind.Number,
        ),
    )
    out_of_service_type_a = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Out of Service Room Count",
            description="The number of rooms that are not available due to refurbishing, maintenance, or seasonality. These are not included in the capacity.",
            kind=CdfKind.Number,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds ID associated with the property. Having a unique ID per property is useful when integrating with other systems.",
            kind=CdfKind.Identifier,
        ),
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property.",
            kind=CdfKind.String,
        ),
    )
    reservation_source = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source",
            description="How reservation was booked: Phone, Booking Engine, Expedia, etc. Sometimes referred to as the Channel.",
            kind=CdfKind.String,
        ),
    )
    reservation_source_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source Category",
            description="The means by which the booking was made, for example, Direct, Group, Wholesaler, OTA, Travel Agent. These are categories of sources.",
            kind=CdfKind.String,
        ),
    )
    room_available_type_a = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Available Room Count",
            description="The unsold rooms that are available to be booked. Equals capacity minus occupied rooms minus out of service rooms.",
            kind=CdfKind.Number,
        ),
    )
    room_description = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Description",
            description="The description of the room.",
            kind=CdfKind.String,
        ),
    )
    room_id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Room ID",
            description="Database ID associated with a room. Can be useful when merging CB data with other data.",
            kind=CdfKind.Identifier,
        ),
    )
    room_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Number",
            description="The number or name that identifies the room(s) associated with the reservation. Each room is listed separately.",
            kind=CdfKind.String,
        ),
    )
    room_rate_additional_adults_amount = Column(
        NUMERIC(asdecimal=False),
        info=dict(
            category=CdfCategory.Booking,
            name="Room Rate Additional Adults Amount",
            description="The cost of adding adults to a room reservation.",
            kind=CdfKind.Currency,
        ),
    )
    room_rate_additional_kids_amount = Column(
        NUMERIC(asdecimal=False),
        info=dict(
            category=CdfCategory.Booking,
            name="Room Rate Additional Kids Amount",
            description="The cost of adding kids to a room reservation.",
            kind=CdfKind.Currency,
        ),
    )
    room_rate_amount = Column(
        NUMERIC(asdecimal=False),
        info=dict(
            category=CdfCategory.Booking,
            name="Room Rate",
            description="The room rate associated with the booking.",
            kind=CdfKind.Currency,
        ),
    )
    room_rate_base_amount = Column(
        NUMERIC(asdecimal=False),
        info=dict(
            category=CdfCategory.Booking,
            name="Room Rate Base Amount",
            description="The base rate of the room without any add-ons.",
            kind=CdfKind.Currency,
        ),
    )
    room_revenue_amount = Column(
        NUMERIC(asdecimal=False),
        info=dict(
            category=CdfCategory.Booking,
            name="Room Revenue",
            description="The room revenue associated with the booking. Covers the entire length of the stay. Includes the room rates. Does not include "
            "taxes, cancellation fees, and other fees.",
            kind=CdfKind.Currency,
        ),
    )
    room_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type",
            description="The type of the room(s), as categorized in MFD. If there are more than one room type associated with the reservation, they will "
            "be listed out separately. Will be empty for non-English MFD users.",
            kind=CdfKind.String,
        ),
    )
    room_type_abbreviation = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type Abbreviation",
            description="A shortened name for the room type.",
            kind=CdfKind.String,
        ),
    )
    room_type_id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Property,
            name="Room Type ID",
            description="The ID of the room type.",
            kind=CdfKind.Identifier,
        ),
    )
    stay_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Stay Date",
            description="The dates that the guest stays in the hotel. For example, if a guest checks in on July 1st and checks out on July 3rd, "
            "their stay dates are July 1st and July 2nd.",
            kind=CdfKind.Date,
        ),
    )
    children_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Children",
            description="Number of children in the reservation.",
            kind=CdfKind.Number,
        ),
    )
    adults_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Adults",
            description="Number of adults in the reservation.",
            kind=CdfKind.Number,
        ),
    )
    revpar = column_property(
        cast(
            cast(room_rate_amount, NUMERIC)
            / func.nullif(
                (cast(capacity_type_a, NUMERIC) - cast(out_of_service_type_a, NUMERIC)),
                0,
            ),
            FLOAT,
        ),
        info=dict(
            category=CdfCategory.Occupancy,
            name="RevPAR",
            description="Revenue per available room: Rev / (Occupancy - Out of Service Rooms).",
            kind=CdfKind.DynamicCurrency,
        ),
    )
    revpar_summary = column_property(
        cast(
            func.sum(room_rate_amount)
            / (
                func.nullif(
                    func.sum(cast(capacity_type_a, NUMERIC))
                    - func.sum(cast(out_of_service_type_a, NUMERIC)),
                    0,
                )
            ),
            FLOAT,
        ),
        info=dict(
            category=CdfCategory.Occupancy,
            name="RevPAR Summary",
            description="Summary of Revenue per available room: Rev / (Occupancy - Out of Service Rooms).",
            kind=CdfKind.DynamicCurrency,
            hide=True,
        ),
    )
    occupancy = column_property(
        cast(
            (
                cast(booking_qty_type_a, NUMERIC)
                / (
                    func.nullif(
                        (
                            cast(capacity_type_a, NUMERIC)
                            - cast(out_of_service_type_a, NUMERIC)
                        ),
                        0,
                    )
                )
            ),
            FLOAT,
        )
        * 100,
        info=dict(
            category=CdfCategory.Occupancy,
            name="Occupancy",
            description="The percent of available accommodations that are sold; equals the room nights divided by the capacity and excludes the out of service rooms.",
            kind=CdfKind.DynamicPercentage,
        ),
    )
    occupancy_summary = column_property(
        cast(
            func.sum(cast(booking_qty_type_a, NUMERIC))
            / (
                func.nullif(
                    (
                        cast(
                            func.sum(cast(capacity_type_a, NUMERIC))
                            - func.sum(cast(out_of_service_type_a, NUMERIC)),
                            FLOAT,
                        )
                    ),
                    0,
                )
            )
            * 100,
            FLOAT,
        ),
        info=dict(
            category=CdfCategory.Occupancy,
            name="Occupancy Summary",
            description=(
                "The summary of the percent of available accommodations that are sold; "
                "equals the room nights divided by the capacity and excludes the out "
                "of service rooms."
            ),
            kind=CdfKind.DynamicPercentage,
            hide=True,
        ),
    )
    adr = column_property(
        cast(
            cast(room_rate_amount, NUMERIC)
            / func.nullif(cast(booking_qty_type_a, NUMERIC), 0),
            FLOAT,
        ),
        info=dict(
            category=CdfCategory.Production,
            name="ADR",
            description="Average Daily Rate = Room Revenue / Room Nights.",
            kind=CdfKind.DynamicCurrency,
        ),
    )
    adr_summary = column_property(
        cast(
            func.sum(cast(room_rate_amount, NUMERIC))
            / (func.nullif((func.sum(cast(booking_qty_type_a, NUMERIC))), 0)),
            FLOAT,
        ),
        info=dict(
            category=CdfCategory.Production,
            name="ADR Summary",
            description="The summary of Average Daily Rate = Room Revenue / Room Nights.",
            kind=CdfKind.DynamicCurrency,
            hide=True,
        ),
    )
    revpar_calendar_logic_summary = column_property(
        cast(
            func.sum(cast(room_rate_amount, NUMERIC))
            / (
                func.nullif(
                    func.sum(cast(capacity_type_a, NUMERIC))
                    - func.sum(cast(out_of_service_type_a, NUMERIC)),
                    0,
                )
            ),
            FLOAT,
        ),
        info=dict(
            category=CdfCategory.Occupancy,
            name="RevPAR Calendar Logic Summary",
            description="Summary of Revenue per available room: Rev / (Occupancy - Out of Service Rooms).",
            kind=CdfKind.DynamicCurrency,
            hide=True,
        ),
    )

    @hybrid_method
    def revpar_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    case(
                        (and_(*cases), cls.room_rate_amount),
                        else_=None,
                    ),
                    NUMERIC,
                )
                / func.nullif(
                    (
                        cast(
                            case(
                                (and_(*cases), cls.capacity_type_a),
                                else_=None,
                            ),
                            NUMERIC,
                        )
                        - cast(
                            case(
                                (and_(*cases), cls.out_of_service_type_a),
                                else_=None,
                            ),
                            NUMERIC,
                        )
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="RevPAR",
                description="Revenue per available room: Rev / (Occupancy - Out of Service Rooms).",
                kind=CdfKind.DynamicCurrency,
            ),
        )

    @hybrid_method
    def revpar_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    case(
                        (and_(*cases), cls.room_rate_amount),
                        else_=None,
                    )
                )
                / (
                    func.nullif(
                        func.sum(
                            cast(
                                case(
                                    (and_(*cases), cls.capacity_type_a),
                                    else_=None,
                                ),
                                NUMERIC,
                            )
                        )
                        - func.sum(
                            cast(
                                case(
                                    (and_(*cases), cls.out_of_service_type_a),
                                    else_=None,
                                ),
                                NUMERIC,
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="RevPAR Summary",
                description="Summary of Revenue per available room: Rev / (Occupancy - Out of Service Rooms).",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def occupancy_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                (
                    cast(
                        case(
                            (and_(*cases), cls.booking_qty_type_a),
                            else_=None,
                        ),
                        NUMERIC,
                    )
                    / (
                        func.nullif(
                            (
                                cast(
                                    case(
                                        (and_(*cases), cls.capacity_type_a),
                                        else_=None,
                                    ),
                                    NUMERIC,
                                )
                                - cast(
                                    case(
                                        (and_(*cases), cls.out_of_service_type_a),
                                        else_=None,
                                    ),
                                    NUMERIC,
                                )
                            ),
                            0,
                        )
                    )
                ),
                FLOAT,
            )
            * 100,
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy",
                description="The percent of available accommodations that are sold; equals the room nights divided by the capacity and excludes the out of service rooms.",
                kind=CdfKind.DynamicPercentage,
            ),
        )

    @hybrid_method
    def occupancy_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast(
                        case(
                            (and_(*cases), cls.booking_qty_type_a),
                            else_=None,
                        ),
                        NUMERIC,
                    )
                )
                / (
                    func.nullif(
                        (
                            cast(
                                func.sum(
                                    cast(
                                        case(
                                            (and_(*cases), cls.capacity_type_a),
                                            else_=None,
                                        ),
                                        NUMERIC,
                                    )
                                )
                                - func.sum(
                                    cast(
                                        case(
                                            (and_(*cases), cls.out_of_service_type_a),
                                            else_=None,
                                        ),
                                        NUMERIC,
                                    )
                                ),
                                FLOAT,
                            )
                        ),
                        0,
                    )
                )
                * 100,
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="Occupancy Summary",
                description=(
                    "The summary of the percent of available accommodations that are sold; "
                    "equals the room nights divided by the capacity and excludes the out "
                    "of service rooms."
                ),
                kind=CdfKind.DynamicPercentage,
                hide=True,
            ),
        )

    @hybrid_method
    def adr_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                cast(
                    case(
                        (and_(*cases), cls.room_rate_amount),
                        else_=None,
                    ),
                    NUMERIC,
                )
                / func.nullif(
                    cast(
                        case(
                            (and_(*cases), cls.booking_qty_type_a),
                            else_=None,
                        ),
                        NUMERIC,
                    ),
                    0,
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Production,
                name="ADR",
                description="Average Daily Rate = Room Revenue / Room Nights.",
                kind=CdfKind.DynamicCurrency,
            ),
        )

    @hybrid_method
    def adr_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast(
                        case(
                            (and_(*cases), cls.room_rate_amount),
                            else_=None,
                        ),
                        NUMERIC,
                    )
                )
                / (
                    func.nullif(
                        (
                            func.sum(
                                cast(
                                    case(
                                        (and_(*cases), cls.booking_qty_type_a),
                                        else_=None,
                                    ),
                                    NUMERIC,
                                )
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Production,
                name="ADR Summary",
                description="The summary of Average Daily Rate = Room Revenue / Room Nights.",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )

    @hybrid_method
    def revpar_calendar_logic_summary_cases(cls, cases: list[Case], **kwargs):
        return column_property(
            cast(
                func.sum(
                    cast(
                        case(
                            (and_(*cases), cls.room_rate_amount),
                            else_=None,
                        ),
                        NUMERIC,
                    )
                )
                / (
                    func.nullif(
                        func.sum(
                            cast(
                                case(
                                    (and_(*cases), cls.capacity_type_a),
                                    else_=None,
                                ),
                                NUMERIC,
                            )
                        )
                        - func.sum(
                            cast(
                                case(
                                    (and_(*cases), cls.out_of_service_type_a),
                                    else_=None,
                                ),
                                NUMERIC,
                            )
                        ),
                        0,
                    )
                ),
                FLOAT,
            ),
            info=dict(
                category=CdfCategory.Occupancy,
                name="RevPAR Calendar Logic Summary",
                description="Summary of Revenue per available room: Rev / (Occupancy - Out of Service Rooms).",
                kind=CdfKind.DynamicCurrency,
                hide=True,
            ),
        )
