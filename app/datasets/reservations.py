from sqlalchemy import (
    BIGINT,
    Case,
    Column,
    DATE,
    FLOAT,
    INTEGER,
    TIMESTAMP,
    VARCHAR,
    and_,
    case,
    cast,
    func,
)
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.orm import column_property, declared_attr

from app.common.constants.country_codes import COUNTRY_CODES
from app.common.constants.reservation_status import RESERVATION_STATUSES
from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class ReservationsViewMixin(object):
    id = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.Identifier,
        ),
        primary_key=True,
    )
    active_booking_notes = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Active Reservation Notes",
            description="Information on the guest and the reservation in the original language.",
            kind=CdfKind.String,
        ),
    )
    active_group_profile_notes = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Active Group Profile Notes",
            description="Any active group profile notes that are associated with this Booking.",
            kind=CdfKind.String,
        ),
    )
    active_primary_guest_notes = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Active Guest Notes",
            description="Active notes regarding the guest. Can also be seen on the reservation page under Guest Notes.",
            kind=CdfKind.String,
        ),
    )
    adults_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Adults",
            description="Number of adults in the reservation.",
            kind=CdfKind.Number,
        ),
    )
    allotment_block_notes = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Allotment Block Notes",
            description="Messages associated with the group allotment block.",
            kind=CdfKind.String,
        ),
    )
    booking_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Date Time - UTC",
            description="When the reservation was originally made, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    booking_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Date Time - Property",
            description="When the reservation was originally made, per the property's timezone.",
            kind=CdfKind.Timestamp,
        ),
    )
    cancellation_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Cancellation Date Time - UTC",
            description="Date and time the reservation was cancelled, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    cancellation_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Cancellation Date Time - Property",
            description="Date and time the reservation was cancelled, per the property's timezone.",
            kind=CdfKind.Timestamp,
        ),
    )
    card_last_4_digits = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Card Last 4 Digits",
            description="Last 4 digits of card, needed when verifying what card has been used.",
            kind=CdfKind.String,
        ),
    )
    card_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Finance,
            name="Credit Card Type",
            description="The credit card type on file for the reservation, for example, Visa, Discover, etc.",
            kind=CdfKind.String,
        ),
    )
    channel_rate_plan = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Rate Plans - Channel Names",
            description="The rate plan name received from the channel.",
            kind=CdfKind.String,
        ),
    )
    checkin_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-In Date",
            description="Date the reservation starts, when the guest arrives, the first room night.",
            kind=CdfKind.Date,
        ),
    )
    checkout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-Out Date",
            description="Date the reservation ends and the guest departs.",
            kind=CdfKind.Date,
        ),
    )
    children_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Children",
            description="Number of children in the reservation.",
            kind=CdfKind.Number,
        ),
    )
    commission_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Commission",
            description="The commission amount for the reservation.",
            kind=CdfKind.Currency,
        ),
    )
    deposit_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Suggested Deposit",
            description="The recommended amount for the deposit that guests should pay when making the reservation, which is set up in the Deposit Policy page.",
            kind=CdfKind.Currency,
        ),
    )
    estimated_arrival_time = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Estimated Arrival Time",
            description="Estimated arrival time. Can help to plan for late or early arrivals.",
            kind=CdfKind.String,
        ),
    )
    grand_total_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Grand Total",
            description="Total room revenue associated with a reservation. Includes taxes and fees. Covers the entire length of stay.",
            kind=CdfKind.Currency,
        ),
    )
    group_allotment_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Allotment Code",
            description="The auto generated code that uniquely identifies an allotment. For example, b283824",
            kind=CdfKind.String,
        ),
    )
    group_allotment_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Allotment Name",
            description="The group block name is associated with the reservation. Is null if it is not associated with a group block.",
            kind=CdfKind.String,
        ),
    )
    group_profile_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Code",
            description="The auto generated code that uniquely identifies transactions associated with the group profile, for example, g572444.",
            kind=CdfKind.String,
        ),
    )
    group_profile_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Name",
            description="Group Profile is the master profile under which a certain group of reservation is created or added. This could be a company, "
            "wedding party, travel agent, etc. It may or may not be associated with a group block allotment.",
            kind=CdfKind.String,
        ),
    )
    group_profile_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Type",
            description="The type of group: Group (one time event), Company, Travel Agent, Wholesaler.",
            kind=CdfKind.String,
        ),
    )
    guest_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Guest Count",
            description="Total number of guests associated with the reservation, including adults and children.",
            kind=CdfKind.Number,
        ),
    )
    is_hotel_collect_booking = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Hotel Collect Booking Flag",
            description="Channel Pay or Hotel Pay",
            kind=CdfKind.Boolean,
        ),
    )
    is_opt_in_marketing_emails = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Opt-In for Marketing Emails",
            description="Flag to indicate that customer has opted in to marketing emails.",
            kind=CdfKind.Boolean,
        ),
    )
    is_repeat_guest = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Repeat Guest Flag",
            description="Flag that identifies if the guest is a repeat guest.",
            kind=CdfKind.Boolean,
        ),
    )
    primary_guest_address = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Address",
            description="First line of the primary guest's street address.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_address_line_2 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Address Line 2",
            description="Second line of the primary guest's street address.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_birth_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Date of Birth",
            description="Primary guest's date of birth.",
            kind=CdfKind.Date,
        ),
    )
    primary_guest_city = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest City",
            description="The city of the primary guest's address.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_document_expiration_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Document Expiration Date",
            description="Expiration date of the ID provided by the guest. For example the passport expiration date.",
            kind=CdfKind.Date,
        ),
    )
    primary_guest_document_issue_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Document Issue Date",
            description="Issue date of the ID provided by the guest. For example, the passport issue date of the driver's license issue date.",
            kind=CdfKind.Date,
        ),
    )
    primary_guest_document_issuing_country = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Document Issuing Country",
            description="The country of the primary guest's identification information.",
            kind=CdfKind.Country,
            options=tuple(country.value for country in COUNTRY_CODES.keys()),
        ),
    )
    primary_guest_document_issuing_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Document Issuing Country Code",
            description="The issuing country of the ID provided by the guest. For example, a Canadian passport would be CA.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_document_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Document Number",
            description="ID number of the ID provided by the guest. For example, a passport number.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_document_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Document Type",
            description="The type of ID provided by the guest. For example, a driver's license, passport, etc.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_email = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Email",
            description="Email address of the primary guest.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_first_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest First Name",
            description="Primary guest first name.",
            kind=CdfKind.GuestLink,
        ),
    )
    primary_guest_full_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Full Name",
            description="First name and surname (last name) associated with the booking.",
            kind=CdfKind.GuestLink,
        ),
    )
    primary_guest_gender = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Gender",
            description="Primary guest's gender.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest ID",
            description="The ID associated with the Guest. Automatically generated by the database for tracking.",
            kind=CdfKind.Identifier,
        ),
    )
    primary_guest_mobile_phone_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Mobile Phone Number",
            description="The mobile phone number of the primary guest.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_phone_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Phone Number",
            description="The phone number of the primary guest.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_postal_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Postal / ZIP Code",
            description="The postal code of the primary guest's address.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_residence_country = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Residence Country Name",
            description="The country of the primary guest's address.",
            kind=CdfKind.Country,
            options=tuple(country.value for country in COUNTRY_CODES.keys()),
        ),
    )
    primary_guest_residence_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Residence Country Code",
            description="The country code of the primary guest's address.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_state = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest State",
            description="The state of the primary guest's address.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_status_level = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Status Level",
            description="Identification of the primary guest's status as a VIP, Gold Member, Blacklisted, etc. Statuses are set up in the Guest Status "
            "section under Property Configuration in MFD.",
            kind=CdfKind.String,
        ),
    )
    primary_guest_surname = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.PrimaryGuest,
            name="Primary Guest Surname",
            description="The primary guest's surname, also referred to as the last name.",
            kind=CdfKind.GuestLink,
        ),
    )
    private_rate_plan = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Rate Plans - Private Names",
            description="""The internal rate plan name that is unique to the rate plan. If the reservation contains multiple rooms
            the values are separated by commas. OTA internal rate plan names are not supported, and will be empty.""",
            kind=CdfKind.String,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds property ID associated with the property.",
            kind=CdfKind.Identifier,
        ),
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property",
            kind=CdfKind.String,
        ),
    )
    public_rate_plan = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Rate Plans - Public Names",
            description="""The rate plan name that is visible to the customer.
            If the reservation contains multiple rooms the values are separated by commas.""",
            kind=CdfKind.String,
        ),
    )
    reservation_balance_due_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Reservation Balance Due",
            description="What is left to be paid for the reservation. Equals the reservation grand total minus the reservation amount paid.",
            kind=CdfKind.Currency,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Number",
            description="The confirmation number that the CB system automatically assigns to a reservation and that uniquely identifies a reservation. "
            "Sometimes referred to as Reservation ID.",
            kind=CdfKind.ReservationLink,
        ),
    )
    reservation_paid_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Reservation Paid Amount",
            description="The amount that has been paid toward the reservation.",
            kind=CdfKind.Currency,
        ),
    )
    reservation_source = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source",
            description="How reservation was booked: Phone, Booking Engine, Expedia, etc. Sometimes referred to as the channel.",
            kind=CdfKind.String,
            translate=False,
        ),
    )
    reservation_source_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source Category",
            description="The means by which the booking was made, for example, Direct, Group, Wholesaler, OTA, Travel Agent. These are categories of sources.",
            kind=CdfKind.String,
            translate=False,
        ),
    )
    reservation_special_requests = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Special Requests",
            description="Special requests from OTAs provided at the time of the booking, in the original language.",
            kind=CdfKind.String,
        ),
    )
    reservation_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Status",
            description="Status of the reservation: Confirmed, Confirmed Pending, Cancelled, In-House, Checked Out, or No Show. Could be another custom status (hardcode).",
            kind=CdfKind.PickList,
            options=RESERVATION_STATUSES,
        ),
    )
    room_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Count",
            description="The number of rooms associated with the booking. Most bookings only have 1 room, but larger groups may have many rooms "
            "under one booking.",
            kind=CdfKind.Number,
        ),
    )
    room_nights_count = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Nights",
            description="The number of room nights associated with the reservation, based on check-in and check-out date.",
            kind=CdfKind.Number,
        ),
    )
    room_numbers = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Numbers",
            description="The rooms' numbers. If there is more than one associated with the booking they will be concatenated into 1 field.",
            kind=CdfKind.String,
        ),
    )
    room_reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Reservation Number",
            description="The ID or confirmation number that the CB system automatically assigns to a room within the reservation and that uniquely "
            "identifies the room and reservation.",
            kind=CdfKind.String,
        ),
    )
    room_revenue_total_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Room Revenue Total",
            description="Total of the room rates for the length of stay. Does NOT include taxes and fees.",
            kind=CdfKind.Currency,
        ),
    )
    room_types = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Types",
            description="The type of rooms. If there is more than one associated with the booking they will be concatenated into 1 field. Will be empty for non-English MFD users.",
            kind=CdfKind.String,
        ),
    )
    taxes_value_amount = Column(
        FLOAT,
        info=dict(
            category=CdfCategory.Finance,
            name="Total Reservation Taxes",
            description="The total taxes for the reservation.",
            kind=CdfKind.Currency,
        ),
    )
    third_party_confirmation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Third Party Confirmation Number",
            description="Confirmation number from the OTA or travel agency.",
            kind=CdfKind.String,
        ),
    )
    booking_window = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Window",
            description="Number of Days between the Booking Date Time - Property and Check-in Date.",
            kind=CdfKind.Number,
        ),
    )
    booking_origin = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Origin",
            description="""The specific third party that the guest chose to use to make their reservation,
            which may have been made available through agreements between the hotel and a different third party.
            This means that if the guest made the reservation through Hotels.com, even if the hotel had an agreement with Expedia,
            the origin of the booking would still be Hotels.com.""",
            kind=CdfKind.String,
        ),
    )

    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )

    is_meal_plan_included = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Meal Plan Included",
            description="Flag to indicate if the meal plan is included in the reservation.",
            kind=CdfKind.PickList,
            options=("Yes", "No"),
        ),
    )
    room_type_categories = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Room Type Categories",
            description="Shows if the room booked was either a private room or a shared room.",
            kind=CdfKind.String,
        ),
    )

    @declared_attr
    def unassigned_accommodations(cls):
        room_types_count = case(
            (cls.room_types.is_(None), 0),
            (func.trim(cls.room_types) == "", 0),
            else_=func.array_length(func.string_to_array(cls.room_types, ","), 1),
        )
        rooms_numbers_count = case(
            (cls.room_numbers.is_(None), 0),
            (func.trim(cls.room_numbers) == "", 0),
            else_=func.array_length(func.string_to_array(cls.room_numbers, ","), 1),
        )
        return column_property(
            cast(room_types_count - rooms_numbers_count, INTEGER),
            info=dict(
                category=CdfCategory.Reservation,
                name="Unassigned Accommodations",
                description=(
                    "The count of unassigned accommodations for the reservation. "
                    "The difference between how many room_types were reserved and how many room_numbers were assigned."
                ),
                kind=CdfKind.Dynamic,
            ),
        )

    @declared_attr
    def unassigned_accommodations_summary(cls):
        room_types_count = case(
            (cls.room_types.is_(None), 0),
            (func.trim(cls.room_types) == "", 0),
            else_=func.array_length(func.string_to_array(cls.room_types, ","), 1),
        )
        room_numbers_count = case(
            (cls.room_numbers.is_(None), 0),
            (func.trim(cls.room_numbers) == "", 0),
            else_=func.array_length(func.string_to_array(cls.room_numbers, ","), 1),
        )
        unassigned_accommodation_expr = cast(
            room_types_count - room_numbers_count, INTEGER
        )

        return column_property(
            func.sum(unassigned_accommodation_expr),
            info=dict(
                category=CdfCategory.Reservation,
                name="Unassigned Accommodations Summary",
                description=(
                    "Sum of unassigned accommodations across all rows. "
                    "It sums the difference between how many room_types were reserved "
                    "and how many room_numbers were assigned."
                ),
                kind=CdfKind.Dynamic,
                hide=True,
            ),
        )

    @hybrid_method
    def unassigned_accommodations_cases(cls, cases: list[Case], **kwargs):
        room_types_count = case(
            (
                and_(*cases),
                case(
                    (cls.room_types.is_(None), 0),
                    (func.trim(cls.room_types) == "", 0),
                    else_=func.array_length(
                        func.string_to_array(cls.room_types, ","), 1
                    ),
                ),
            ),
            else_=0,
        )
        rooms_numbers_count = case(
            (
                and_(*cases),
                case(
                    (cls.room_numbers.is_(None), 0),
                    (func.trim(cls.room_numbers) == "", 0),
                    else_=func.array_length(
                        func.string_to_array(cls.room_numbers, ","), 1
                    ),
                ),
            ),
            else_=0,
        )
        return column_property(
            cast(room_types_count - rooms_numbers_count, INTEGER),
            info=dict(
                category=CdfCategory.Reservation,
                name="Unassigned Accommodations",
                description=(
                    "The count of unassigned accommodations for the reservation. "
                    "The difference between how many room_types were reserved and how many room_numbers were assigned."
                ),
                kind=CdfKind.Dynamic,
            ),
        )

    @hybrid_method
    def unassigned_accommodations_summary_cases(cls, cases: list[Case], **kwargs):
        room_types_count = case(
            (
                and_(*cases),
                case(
                    (cls.room_types.is_(None), 0),
                    (func.trim(cls.room_types) == "", 0),
                    else_=func.array_length(
                        func.string_to_array(cls.room_types, ","), 1
                    ),
                ),
            ),
            else_=0,
        )
        room_numbers_count = case(
            (
                and_(*cases),
                case(
                    (cls.room_numbers.is_(None), 0),
                    (func.trim(cls.room_numbers) == "", 0),
                    else_=func.array_length(
                        func.string_to_array(cls.room_numbers, ","), 1
                    ),
                ),
            ),
            else_=0,
        )
        unassigned_accommodation_expr = cast(
            room_types_count - room_numbers_count, INTEGER
        )

        return column_property(
            func.sum(unassigned_accommodation_expr),
            info=dict(
                category=CdfCategory.Reservation,
                name="Unassigned Accommodations Summary",
                description=(
                    "Sum of unassigned accommodations across all rows. "
                    "It sums the difference between how many room_types were reserved "
                    "and how many room_numbers were assigned."
                ),
                kind=CdfKind.Dynamic,
                hide=True,
            ),
        )


class ReservationsView(ReservationsViewMixin, db.Model):
    __bind_key__ = "dataset_views"
    __tablename__ = "reservations_vue"
    __table_args__ = {"schema": "insights"}


class ReservationsFFView(ReservationsViewMixin, db.Model):
    __bind_key__ = "dataset_views"
    __tablename__ = "reservations_vue_ff"
    __table_args__ = {"schema": "insights"}
