from sqlalchemy import BIGIN<PERSON>, Column, DATE, INTEGER, TIMESTAMP, VARCHAR

from app.common.constants.country_codes import COUNTRY_CODES
from app.common.constants.reservation_status import RESERVATION_STATUSES
from app.common.database import db
from app.enums.cdf_category import CdfCategory
from app.enums.cdf_kind import CdfKind


class GuestsViewMixin(object):
    id = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Guest ID",
            description="Unique identifier per row of records within this dataset. This is generated and used by the system.",
            kind=CdfKind.String,
        ),
        primary_key=True,
    )
    customer_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Booking,
            name="Customer ID",
            description="Unique identifier per guest within the property.",
            kind=CdfKind.Identifier,
        ),
    )
    booking_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Date Time - UTC",
            description="When the reservation was originally made, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    booking_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Booking Date Time - Property",
            description="Then the reservation was originally made, per the property's time zone.",
            kind=CdfKind.Timestamp,
        ),
    )
    cancellation_datetime = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Cancellation Date Time - UTC",
            description="Date and time the reservation was cancelled, in UTC time.",
            kind=CdfKind.Timestamp,
        ),
    )
    cancellation_datetime_property_timezone = Column(
        TIMESTAMP,
        info=dict(
            category=CdfCategory.Booking,
            name="Cancellation Date Time - Property",
            description="Date and time the reservation was cancelled, per the property's time zone.",
            kind=CdfKind.Timestamp,
        ),
    )
    checkin_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-In Date",
            description="Date the reservation starts, when the guest arrives, the first room night.",
            kind=CdfKind.Date,
        ),
    )
    checkout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Booking,
            name="Check-Out Date",
            description="Date the reservation ends and the guest departs.",
            kind=CdfKind.Date,
        ),
    )
    guest_address = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Address",
            description="First Line of the guest's street address.",
            kind=CdfKind.String,
        ),
    )
    guest_address_line_2 = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Address Line 2",
            description="Second line of the guest's street address.",
            kind=CdfKind.String,
        ),
    )
    guest_birth_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Date of Birth",
            description="Date of birth of the guest.",
            kind=CdfKind.Date,
        ),
    )
    guest_city = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest City",
            description="The city of the guest's address.",
            kind=CdfKind.String,
        ),
    )
    guest_document_expiration_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Document Expiration Date",
            description="Expiration date of the ID provided by the guest. For example, the passport expiration date.",
            kind=CdfKind.Date,
        ),
    )
    guest_document_issue_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Document Issue Date",
            description="Issue date of the ID provided by the guest. For example the passport issue date or the driver's license issue date.",
            kind=CdfKind.Date,
        ),
    )
    guest_document_issuing_country = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Document Issuing Country",
            description="The issuing country of the ID provided by the guest. For a Canadian passport it would be Canada.",
            kind=CdfKind.Country,
            options=tuple(country.value for country in COUNTRY_CODES.keys()),
        ),
    )
    guest_document_issuing_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Document Issuing Country Code",
            description="The issuing country code of the ID provided by the guest. For a Canadian passport it would be CA.",
            kind=CdfKind.String,
        ),
    )
    guest_document_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Document Number",
            description="ID number of the ID provided by the guest. For example a passport number",
            kind=CdfKind.String,
        ),
    )
    guest_document_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Document Type",
            description="The type of ID provided by the guest, for example, driver's license, passport, etc.",
            kind=CdfKind.String,
        ),
    )
    guest_email = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Email",
            description="Email address of the guest.",
            kind=CdfKind.String,
        ),
    )
    guest_first_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest First Name",
            description="Guest first name.",
            kind=CdfKind.String,
        ),
    )
    guest_full_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Full Name",
            description="The names of guests associated with the reservation. Guest Full Name = First Name + Surname.",
            kind=CdfKind.String,
        ),
    )
    guest_gender = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Gender",
            description="Guest's gender.",
            kind=CdfKind.String,
        ),
    )
    guest_mobile_phone_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Mobile Phone Number",
            description="The mobile phone number of the guest.",
            kind=CdfKind.String,
        ),
    )
    guest_phone_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Phone Number",
            description="The phone number of the guest.",
            kind=CdfKind.String,
        ),
    )
    guest_postal_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Postal / ZIP Code",
            description="The postal code of the guest's address.",
            kind=CdfKind.String,
        ),
    )
    guest_residence_country = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Residence Country",
            description="The country of the guest's address.",
            kind=CdfKind.Country,
            options=tuple(country.value for country in COUNTRY_CODES.keys()),
        ),
    )
    guest_residence_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Residence Country Code",
            description="The country code of the guest's address.",
            kind=CdfKind.String,
        ),
    )
    guest_state = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest State",
            description="The state of the guest's address.",
            kind=CdfKind.String,
        ),
    )
    guest_status_level = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Status Level",
            description="Identification on if the the primary guest is a VIP, Gold Member, Blacklisted, etc. Statuses are set up in the Guest Status "
            "section under Property Configuration in MyFrontDesk.",
            kind=CdfKind.String,
        ),
    )
    guest_surname = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Guest Surname",
            description="The last name of a guest.",
            kind=CdfKind.String,
        ),
    )
    is_opt_in_marketing_emails = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Opt-In for Marketing Emails",
            description="Flag that identifies if the guest has opted into marketing emails.",
            kind=CdfKind.Boolean,
        ),
    )
    is_primary_guest = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Primary Guest Flag",
            description="Flag that identifies if the guest is the the primary guest associated with the reservation.",
            kind=CdfKind.Boolean,
        ),
    )
    is_repeat_guest = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Repeat Guest Flag",
            description="Flag that identifies if the guest is a repeat guest.",
            kind=CdfKind.Boolean,
        ),
    )
    property_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Property ID",
            description="The Cloudbeds property ID associated with the property.",
            kind=CdfKind.Identifier,
        ),
    )
    property_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Property,
            name="Property Name",
            description="The name of the property.",
            kind=CdfKind.String,
        ),
    )
    reservation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Number",
            description="The confirmation number that the CB System automatically assigns to a reservation and that uniquely identifies a reservation. "
            "Sometimes referred to as Reservation ID.",
            kind=CdfKind.String,
        ),
    )
    reservation_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Status",
            description="Status of the reservation: Confirmed, Confirmed Pending, Cancelled, In-House, Checked Out, or No Show.",
            kind=CdfKind.PickList,
            options=RESERVATION_STATUSES,
        ),
    )
    third_party_confirmation_number = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Third Party Confirmation Number",
            description="Confirmation number from the OTA or travel agency.",
            kind=CdfKind.String,
        ),
    )
    guest_middle_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Guest Middle Name",
            description="Guest's Middle Name.",
            kind=CdfKind.String,
        ),
    )
    thai_visa_type = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Visa Type",
            description="The visa category.",
            kind=CdfKind.String,
        ),
    )
    thai_visa_expiry_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Visa Expiration Date",
            description="Expiry date of visa.",
            kind=CdfKind.Date,
        ),
    )
    thai_point_of_entry = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Point of Entry",
            description="Where the guest entered Thailand.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_property_relation = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Guest Property Relation",
            description="The property's relation to the guest, used for government reports.",
            kind=CdfKind.String,
        ),
    )
    thai_visa_issue_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Visa Issue Date",
            description="Thai visa issue date.",
            kind=CdfKind.Date,
        ),
    )
    property_checkin_time = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Property Check-In Time",
            description="Time Guests arrive, per property policy.",
            kind=CdfKind.String,
        ),
    )
    property_checkout_time = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Guest,
            name="Property Check-Out Time",
            description="Time Guests depart, per property policy.",
            kind=CdfKind.String,
        ),
    )
    italian_guest_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest ID",
            description="Guest's ID.",
            kind=CdfKind.Identifier,
        ),
    )
    italian_guest_type = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest Type",
            description="Guest type.",
            kind=CdfKind.Identifier,
        ),
    )
    italian_guest_birth_date_municipality_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest Birth Date Municipality Code",
            description="Italian municipality where the guest was born.",
            kind=CdfKind.String,
        ),
    )
    italian_guest_birth_date_province_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest Birth Date Province Code",
            description="Italian province where the guest was born.",
            kind=CdfKind.String,
        ),
    )
    italian_guest_birth_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest Birth Country Code",
            description="Country code where the guest was born.",
            kind=CdfKind.String,
        ),
    )
    italian_guest_nationality_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest Nationality Code",
            description="The guest's country of citizenship.",
            kind=CdfKind.String,
        ),
    )
    italian_guest_document_issuing_province_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest Document Issuing Province Code",
            description="Issuing province code of guest document.",
            kind=CdfKind.String,
        ),
    )
    italian_guest_document_issuing_municipality_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ItalianGovernment,
            name="Italian Guest Document Issuing Municipality Code",
            description="Issuing municipality code of guest document.",
            kind=CdfKind.String,
        ),
    )
    duration_of_stay = Column(
        INTEGER,
        info=dict(
            category=CdfCategory.Booking,
            name="Duration of Stay",
            description="The number of days for the guest's reservation.",
            kind=CdfKind.Number,
        ),
    )
    thai_checkin_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Check-in date in Buddhist Year",
            description="Date the reservation starts, when the guest arrives, the first room night on Buddhist Year.",
            kind=CdfKind.Date,
        ),
    )
    room_numbers = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Room Number",
            description="The room number associated with the guest.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_salutation = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Guest Salutation",
            description="Guest’s title, for example Mr. Mrs, Ms, Dr, etc.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_salutation_english = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Guest Salutation in English",
            description="Guest’s title, for example Mr. Mrs, Ms, Dr, etc.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_first_name_english = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Guest First Name in English",
            description="Guest first name.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_middle_name_english = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Guest Middle Name in English",
            description="Guest's Middle Name.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_surname_english = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Guest Surname in English",
            description="The last name of a guest.",
            kind=CdfKind.String,
        ),
    )
    thai_rr4_nationality_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai RR4 Nationality Code",
            description="The code for the Guest's nationality.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_residence_country_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Guest Residence Country Code",
            description="The country code of the guest's address.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_occupation_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Guest Occupation Code",
            description="Guest occupation.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_province_arriving_from = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Guest Province Arriving From",
            description="Province arriving from.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_country_arriving_from_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Code for Country Arriving From",
            description="Country arriving from.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_province_travelling_to = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Guest Province Travelling To",
            description="Province travelling to.",
            kind=CdfKind.String,
        ),
    )
    thai_guest_country_travelling_to_code = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Code for Country Travelling To",
            description="Country travelling to.",
            kind=CdfKind.String,
        ),
    )
    thai_checkout_date = Column(
        DATE,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai Check-Out Date",
            description="Date the reservation ends and the guest departs on Buda year.",
            kind=CdfKind.Date,
        ),
    )
    thai_rr4_data_status = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai RR4 Data Status",
            description="Data Status for the Thai RR4 Report.",
            kind=CdfKind.String,
        ),
    )
    thai_rr4_guest_note = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.ThaiGovernment,
            name="Thai RR4 Guest Note",
            description="Note for the Thai RR4 Report.",
            kind=CdfKind.String,
        ),
    )
    organization_id = Column(
        BIGINT,
        info=dict(
            category=CdfCategory.Property,
            name="Organization ID",
            description="The Cloudbeds ID associated with the property's organization.",
            kind=CdfKind.Identifier,
        ),
    )
    group_profile_name = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Group Profile Name",
            description="Name of the group profile associated to the guest’s reservation.",
            kind=CdfKind.String,
        ),
    )
    reservation_source = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source",
            description="How reservation was booked: Phone, Booking Engine, Expedia, etc. Sometimes referred to as the channel.",
            kind=CdfKind.String,
            translate=False,
        ),
    )
    reservation_source_category = Column(
        VARCHAR,
        info=dict(
            category=CdfCategory.Booking,
            name="Reservation Source Category",
            description="The means by which the booking was made, for example, Direct, Group, Wholesaler, OTA, Travel Agent. These are categories of sources.",
            kind=CdfKind.String,
            translate=False,
        ),
    )


class GuestsView(GuestsViewMixin, db.Model):
    __tablename__ = "guests_vue"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"


class GuestsFFView(GuestsViewMixin, db.Model):
    __tablename__ = "guests_vue_ff"
    __table_args__ = {"schema": "insights"}
    __bind_key__ = "dataset_views"
