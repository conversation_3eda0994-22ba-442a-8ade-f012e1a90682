import os
import re

from babel.messages.pofile import read_po

from app.common.cache import TIMEOUT_WEEK, cache
from app.common.keys import (
    TRANSLATION_KEYS,
    TRANSLATION_PATTERN,
    TRANSLATION_VALUES_AS_KEY,
)

TRANSLATIONS_DIR = "app/translations"


def get_translation_keys(overwrite_cache: bool = False):
    """Get translation keys from the .pot file and cache them for 1 day."""
    keys = cache.get(TRANSLATION_KEYS)
    if keys is None or overwrite_cache:
        with open("app/translations/messages.pot", "r", encoding="utf-8") as f:
            catalog = read_po(f)
            keys = [message.id for message in catalog if message.id]
        cache.set(TRANSLATION_KEYS, keys, timeout=TIMEOUT_WEEK)
    return keys


def get_translation_pattern(overwrite_cache: bool = False):
    """Get translation pattern from the cached keys."""
    pattern = cache.get(TRANSLATION_PATTERN)
    if pattern is None or overwrite_cache:
        keys = get_translation_keys()
        escaped_keys = [re.escape(k) for k in keys]
        pattern = r"\b(" + "|".join(escaped_keys) + r")\b"
        cache.set(TRANSLATION_PATTERN, pattern, timeout=TIMEOUT_WEEK)
    return pattern


def get_reverse_translation_map(overwrite_cache: bool = False, locale: str = None):
    """Build a dict from translated value -> list of keys, and cache it."""
    reverse_map = cache.get(TRANSLATION_VALUES_AS_KEY)

    if reverse_map is None or overwrite_cache:
        reverse_map = {}
        for lang in os.listdir(TRANSLATIONS_DIR):
            po_path = os.path.join(TRANSLATIONS_DIR, lang, "LC_MESSAGES", "messages.po")

            if not os.path.isfile(po_path):
                continue

            with open(po_path, "r", encoding="utf-8") as f:
                catalog = read_po(f)
                for msg in catalog:
                    if msg.id and msg.string:
                        reverse_map.setdefault(lang, {})
                        reverse_map[lang].setdefault(msg.string, []).append(msg.id)

        cache.set(TRANSLATION_VALUES_AS_KEY, reverse_map, timeout=TIMEOUT_WEEK)

    return reverse_map.get(locale) if locale else reverse_map
