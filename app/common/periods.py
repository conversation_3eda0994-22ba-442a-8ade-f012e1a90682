from app.api.v1_1.schemas.report.report_filters import DateOrRelativeDate
from app.enums.filter_operator import FilterOperator


def get_relative_start_date_from_end_date(
    start: str, end: str, property_timezone: str | None = None
) -> str:
    end = DateOrRelativeDate().get_converted_value(end, property_timezone)
    start = DateOrRelativeDate().get_converted_value(start, property_timezone, end)
    return start


def get_period_filter(report, period):
    start = period["start"]
    end = period["end"]
    if period.get("start_relative_to_end"):
        start = get_relative_start_date_from_end_date(
            start, end, report.property_timezone
        )
    period_filter = dict()
    period_filter["and"] = [
        dict(
            cdf=period["cdf"],
            operator=FilterOperator.GreaterThanOrEqual.value,
            value=start,
        ),
        dict(
            cdf=period["cdf"],
            operator=FilterOperator.LessThan.value,
            value=end,
        ),
    ]
    if report.filters:
        period_filter["and"].append(report.filters)
    return period_filter
