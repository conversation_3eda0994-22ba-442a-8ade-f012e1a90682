from apispec.ext.marshmallow import MarshmallowPlugin

import flask

from flask_smorest import Api as ApiOrig, Blueprint as BlueprintOrig


from marshmallow.fields import List
from marshmallow.utils import is_collection

from webargs.fields import DelimitedList
from webargs.flaskparser import FlaskParser

from app.schemas.error import ErrorSchema


class Blueprint(BlueprintOrig):
    FlaskParser.DEFAULT_VALIDATION_STATUS = 400


def _field2parameter(self, field, *, name, location):
    """Return an OpenAPI parameter as a `dict`, given a marshmallow
    :class:`Field <marshmallow.Field>`.
    https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.2.md#parameterObject
    """
    ret = {"in": location, "name": name}

    partial = getattr(field.parent, "partial", False)
    ret["required"] = field.required and (
        not partial or (is_collection(partial) and field.name not in partial)
    )

    prop = self.field2property(field)
    multiple = isinstance(field, List)

    if self.openapi_version.major < 3:
        if multiple:
            ret["collectionFormat"] = "multi"
        ret.update(prop)
    else:
        if multiple:
            ret["explode"] = False if isinstance(field, DelimitedList) else True
            ret["style"] = "form"
        if prop.get("description", None):
            ret["description"] = prop.pop("description")
        ret["schema"] = prop
    return ret


class Api(ApiOrig):
    DEFAULT_ERROR_RESPONSE_NAME = None
    ERROR_SCHEMA = ErrorSchema

    def __init__(self, app=None, *, spec_kwargs=None):
        app.config["API_TITLE"] = "Data Insights | REST API | Cloudbeds"
        app.config["API_VERSION"] = "v1.1"
        app.config["OPENAPI_VERSION"] = "3.0.2"
        app.config["OPENAPI_URL_PREFIX"] = app.config["SERVICE_ROUTE"]
        app.config["OPENAPI_SWAGGER_UI_PATH"] = "/"
        app.config["OPENAPI_JSON_PATH"] = "/openapi.json"
        app.config[
            "OPENAPI_SWAGGER_UI_URL"
        ] = "https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.10.3/"
        spec_kwargs["marshmallow_plugin"] = MarshmallowPlugin()
        spec_kwargs["marshmallow_plugin"].Converter._field2parameter = _field2parameter

        super().__init__(app, spec_kwargs=spec_kwargs)

    def _openapi_swagger_ui(self):
        """Expose OpenAPI spec with Swagger UI"""
        return flask.render_template(
            "swagger_ui.html",
            title=self.spec.title,
            swagger_ui_url=self._swagger_ui_url,
            swagger_ui_config=self._app.config.get("OPENAPI_SWAGGER_UI_CONFIG", {}),
        )

    def register_modules(self, modules):
        for module in modules:
            self.register_blueprint(
                module.blp,
                url_prefix=f"{self._app.config['SERVICE_ROUTE']}/{module.blp.url_prefix}",
            )
