from app.common.enums.permissions import (
    PermissionActions,
    Resources,
)

SERVICE_ID = "reporting_service"
STOCK_REPORT_PUBLISHER_ROLE_NAME = "stock_report_publisher"
STOCK_REPORT_PUBLISHER_PERMISSIONS = [
    {
        "resource": Resources.StockReport.value,
        "action": PermissionActions.Create.value,
    },
    {
        "resource": Resources.StockReport.value,
        "action": PermissionActions.Read.value,
    },
    {
        "resource": Resources.StockReport.value,
        "action": PermissionActions.Update.value,
    },
    {
        "resource": Resources.StockReport.value,
        "action": PermissionActions.Delete.value,
    },
    {
        "resource": Resources.StockReportRevision.value,
        "action": PermissionActions.Read.value,
    },
    {
        "resource": Resources.Report.value,
        "action": PermissionActions.Create.value,
    },
    {
        "resource": Resources.Report.value,
        "action": PermissionActions.Read.value,
    },
    {
        "resource": Resources.Report.value,
        "action": PermissionActions.Update.value,
    },
    {
        "resource": Resources.Report.value,
        "action": PermissionActions.Delete.value,
    },
]
ADMIN_ROLE_DESCRIPTION = "Admin can manage users,roles and policies."
STOCK_REPORT_PUBLISHER_ROLE_DESCRIPTION = (
    "Stock Report Publisher can manage stock reports and publishing rules."
)

MFD_ACCESS_REPORT_BUILDER_ACL = "access_report_builder"
