OPERATORS_MAP = {
    "begins": "Begins With",
    "not_begins": "Does Not Begin With",
    "ends": "Ends With",
    "not_ends": "Does Not End With",
    "contains": "Contains",
    "not_contains": "Does Not Contain",
    "list_contains": "In",
    "not_list_contains": "Not In",
    "is_null": "Is Null",
    "is_not_null": "Is Not Null",
    "is_empty": "Is Empty",
    "is_not_empty": "Is Not Empty",
    "equals": "Equals",
    "not_equals": "Does Not Equal",
    "greater_than": ">",
    "less_than": "<",
    "less_than_or_equal": "<=",
    "greater_than_or_equal": ">=",
}
