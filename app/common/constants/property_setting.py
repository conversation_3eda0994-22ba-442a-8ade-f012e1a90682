from app.enums.property_setting import PropertySetting, WeekDays

MAX_ROUNDING_PRECISION = 4
DEFAULT_ROUNDING_PRECISION = 2
DEFAULT_NUMBER_FORMAT = f"FM{'999,'*13}990.00"

property_setting_title = {
    PropertySetting.START_OF_WEEK.value: "Start of the week",
    PropertySetting.CURRENCY_CDF_ROUNDING_PRECISION.value: "Currency CDF Rounding Precision",
}

property_setting_options_type = {
    PropertySetting.START_OF_WEEK.value: "PickList",
    PropertySetting.CURRENCY_CDF_ROUNDING_PRECISION.value: "PickList",
}

# Returning Key Value because the name and the value needs to be updated with translations
property_setting_options = {
    PropertySetting.START_OF_WEEK.value: [
        dict(name=day.name, value=day.name) for day in WeekDays
    ],
    PropertySetting.CURRENCY_CDF_ROUNDING_PRECISION.value: [
        dict(name=str(value), value=str(value))
        for value in range(0, MAX_ROUNDING_PRECISION + 1)
    ],
}
