import uuid
from time import time

from flask import Flask, g, request

import jwt

import ldclient
from ldclient.config import Config

from marshmallow import ValidationError

from sqlalchemy.engine import URL
from sqlalchemy.exc import DataError, IntegrityError, OperationalError

from werkzeug.exceptions import HTTPException

from app.api.v1_1.blueprints import (
    chart,
    classic_reports,
    dataset,
    favorite,
    folder,
    health,
    hub,
    liveness,
    me,
    property,
    report,
    schedule,
    search,
    stock_report,
    stock_tag,
    tag,
    task,
)
from app.classic_reports.common.classic_report_datasets import CLASSIC_REPORT_DATASETS
from app.common import babel
from app.common.cache import cache
from app.common.constants.public_endpoints import PUBLIC_ENDPOINTS
from app.common.database import db
from app.common.exceptions import (
    InvalidUsage,
    general_errorhandler,
    handle_http_exception,
    sqlalchemy_errorhandler,
    validation_errorhandler,
)
from app.common.logger import configure_logger, logger as logger_service
from app.common.openapi import AUTH<PERSON><PERSON><PERSON><PERSON><PERSON>, SPEC_KWARGS, X_API_KEY, X_PROPERTY_ID
from app.common.smorest import Api
from app.common.translation_keys import (
    get_reverse_translation_map,
    get_translation_keys,
    get_translation_pattern,
)
from app.common.user import User
from app.services.organization_service import OrganizationService
from app.services.property_service import PropertyService
from app.services.token_service import TokenService


class Configuration:
    def __init__(self, app: Flask):
        self.app = app

    def configure(self) -> Flask:
        self.__configure_headers()
        self.__configure_databases()
        self.__configure_teardown()
        self.__configure_globals()
        self.__configure_smorest()
        self.__configure_cache()
        self.__configure_error_handlers()
        self.__configure_logger()
        self.__configure_launch_darkly()
        self.__configure_translation_keys()
        return self.app

    def __configure_headers(self):
        """Function that will configure the headers that are returned to the client"""

        @self.app.after_request
        def after_request(response):
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add(
                "Access-Control-Allow-Headers",
                f"Authorization, Content-Type, Origin, {X_API_KEY}, {X_PROPERTY_ID}",
            )
            response.headers.add(
                "Access-Control-Allow-Methods", "POST, GET, PUT, DELETE, OPTIONS, PATCH"
            )
            g.end_time = time()
            g.duration = g.end_time - g.start_time

            logger_service.debug(
                f"Request completed in: {g.duration} seconds",
                extra={
                    "request_duration": g.duration,
                    "request_start_time": g.start_time,
                    "request_end_time": g.end_time,
                },
            )
            return response

    def __configure_teardown(self):
        """Function to execute before the request response is sent to the client"""

        @self.app.teardown_request
        def shutdown_session(exception=None):
            db.session.close()

    def __configure_globals(self):
        """Function to configure all the globals that will be shared across the request made by the client"""

        @self.app.before_request
        def configure_globals():
            g.start_time = time()

            if request.endpoint in PUBLIC_ENDPOINTS:
                g.access_token = request.headers.get(AUTHORIZATION)
                return
            try:
                g.property_id = (
                    int(request.headers[X_PROPERTY_ID])
                    if request.headers.get(X_PROPERTY_ID)
                    else None
                )
            except:
                raise InvalidUsage.bad_request("X-PROPERTY-ID header is not an integer")

            g.island = (
                PropertyService.get_property_island(g.property_id)
                if g.property_id
                else None
            )
            g.request_id = str(uuid.uuid4())
            g.access_token = request.headers.get(AUTHORIZATION)
            g.api_key = request.headers.get(X_API_KEY)

            if g.api_key or (g.api_key is None and g.access_token is None):
                g.property_ids = []
                g.organization_id = None
                g.user = User(
                    id=None,
                    email="",
                    admin=False,
                    scopes=[],
                )

            if g.access_token:
                if g.property_id is None:
                    raise InvalidUsage.bad_request("X-PROPERTY-ID header is required")
                try:
                    token_service = TokenService(g.access_token)
                    user_id = token_service.get_user_id()

                    g.user = User(
                        id=user_id,
                        email=token_service.get_email(),
                        admin=token_service.is_super_admin(),
                        scopes=token_service.get_di_scopes(),
                        token_type=token_service.get_token_type(),
                    )

                    g.organization_id = (
                        OrganizationService.get_organization_id_by_property_id(
                            g.property_id
                        )
                    )
                    organization_property_ids = (
                        OrganizationService.get_organization_property_ids(
                            g.organization_id, g.property_id
                        )
                    )
                    token_property_ids = token_service.get_property_ids()

                    # Set Property Ids, use all the properties from the organization if super admin
                    g.property_ids = (
                        organization_property_ids
                        if g.user.admin
                        else [
                            property_id
                            for property_id in organization_property_ids
                            if property_id in token_property_ids
                        ]
                    )
                    g.locale = babel.get_locale()

                    if request.endpoint in CLASSIC_REPORT_DATASETS:
                        g.dataset_id = CLASSIC_REPORT_DATASETS.get(request.endpoint)

                except jwt.exceptions.InvalidTokenError as error:
                    logger_service.error(
                        "Invalid token error",
                        extra={"access_token": g.access_token, "error": str(error)},
                    )
                    raise InvalidUsage.server_error()
                except Exception as error:
                    logger_service.error(
                        "Access token is not valid",
                        extra={"access_token": g.access_token, "error": str(error)},
                    )
                    raise InvalidUsage.server_error()

                # Return error if Custom Claims are not present
                if not user_id:
                    logger_service.error(
                        "Token does not contain Custom Claims",
                        extra={"access_token": g.access_token},
                    )
                    raise InvalidUsage.forbidden(
                        "Origin from the access token is invalid"
                    )

            logger_service.debug(
                f"Request Started: {g.start_time} seconds",
                extra={"request_start_time": g.start_time},
            )

    def __configure_databases(self):
        """Function that will configure all the database connections for the given app"""
        database_url = URL.create(
            "postgresql",
            username=self.app.config["DATABASE_USER"],
            password=self.app.config["DATABASE_PASSWORD"],
            host=self.app.config["DATABASE_HOST"],
            port=self.app.config["DATABASE_PORT"],
            database=self.app.config["DATABASE_NAME"],
        )
        aurora_url = URL.create(
            "postgresql",
            username=self.app.config["AURORA_USER"],
            password=self.app.config["AURORA_PASSWORD"],
            host=self.app.config["AURORA_HOST"],
            port=self.app.config["AURORA_PORT"],
            database=self.app.config["AURORA_DATABASE"],
        )
        dataset_views_url = URL.create(
            "postgresql",
            username=self.app.config["DATASET_USER"],
            password=self.app.config["DATASET_PASSWORD"],
            host=self.app.config["AURORA_HOST"],
            port=self.app.config["AURORA_PORT"],
            database=self.app.config["AURORA_DATABASE"],
        )
        updated_at_views = URL.create(
            "postgresql",
            username=self.app.config["DATASET_USER"],
            password=self.app.config["DATASET_PASSWORD"],
            host=self.app.config["AURORA_HOST"],
            port=self.app.config["AURORA_PORT"],
            database=self.app.config["AURORA_DATABASE"],
            query={
                "options": f"-c statement_timeout={self.app.config['AURORA_UPDATED_AT_QUERY_TIMEOUT_MILISECONDS']}"
            },
        )
        self.app.config["SQLALCHEMY_DATABASE_URI"] = database_url
        self.app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
        self.app.config["SQLALCHEMY_BINDS"] = {
            "aurora": aurora_url,
            "dataset_views": dataset_views_url,
            "updated_at_views": updated_at_views,
        }

    def __configure_smorest(self):
        api = Api(self.app, spec_kwargs=SPEC_KWARGS(self.app))
        MODULES = (
            chart,
            classic_reports,
            dataset,
            folder,
            health,
            hub,
            me,
            report,
            property,
            schedule,
            search,
            stock_report,
            stock_tag,
            tag,
            favorite,
            liveness,
            task,
        )
        api.register_modules(MODULES)

    def __configure_cache(self):
        """Function that will initilize the cache based on a given configuration"""
        config = {
            "CACHE_TYPE": self.app.config["CACHE_TYPE"],
            "CACHE_REDIS_HOST": self.app.config["CACHE_REDIS_HOST"],
            "CACHE_REDIS_PORT": self.app.config["CACHE_REDIS_PORT"],
        }
        cache.init_app(self.app, config=config)

    def __configure_error_handlers(self):
        """Function that will register the error handlers over the different Exceptions type"""
        self.app.errorhandler(OperationalError)(sqlalchemy_errorhandler)
        self.app.errorhandler(DataError)(sqlalchemy_errorhandler)
        self.app.errorhandler(IntegrityError)(sqlalchemy_errorhandler)
        self.app.errorhandler(ValidationError)(validation_errorhandler)
        self.app.errorhandler(Exception)(general_errorhandler)
        self.app.errorhandler(HTTPException)(handle_http_exception)

    def __configure_logger(self):
        """Function that will configure the logger"""
        configure_logger(self.app.config["LOG_LEVEL"])

    def __configure_launch_darkly(self):
        """Function that will configure Launch Darkly Service"""
        ldclient.set_config(Config(self.app.config["LAUNCH_DARKLY_SDK_KEY"]))

    def __configure_translation_keys(self):
        """Function that will load the translation keys from the .pot file
        and overwrite the cache in case the translations have changed"""
        get_translation_keys(overwrite_cache=True)
        get_translation_pattern(overwrite_cache=True)
        get_reverse_translation_map(overwrite_cache=True)
