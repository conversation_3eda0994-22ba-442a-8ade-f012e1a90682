import datetime

from sqlalchemy import (
    BIGINT,
    Column,
    DateTime,
    Integer,
    JSON,
    String,
)


class CustomCdfMixin(object):
    id = Column(
        Integer, primary_key=True, comment="Primary key for each report custom cdf"
    )
    column = Column(
        String,
        nullable=False,
        comment="Column of report custom cdf automatically populated based on the name",
    )
    name = Column(String, nullable=False, comment="Name of each report custom cdf")
    description = Column(String, comment="Description of each report custom cdf")
    formula = Column(
        JSON,
        nullable=False,
        comment="Formula to generate the logic to build the report custom cdf SQL",
    )
    kind = Column(
        String,
        nullable=False,
        comment="Kind that the formula should generate as a SQL Data type",
    )
    user_id = Column(
        BIGINT, nullable=False, comment="User that created the report custom cdf"
    )
    created_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.now(datetime.timezone.utc),
        comment="Created at date of the report custom cdf",
    )
    updated_at = Column(
        DateTime,
        nullable=False,
        default=datetime.datetime.now(datetime.timezone.utc),
        onupdate=datetime.datetime.now(datetime.timezone.utc),
        comment="Updated at date of the report custom cdf",
    )
