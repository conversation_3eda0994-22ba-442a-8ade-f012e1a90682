import re


class BuildSearchQuery(object):
    """Build search query for partial match to table's columns.

    Args:
        query: the sql alchemy query to receive built filters.
        params: the column:value to partial match the search.
        model: the model of the table.

    Returns:
        flask_sqlalchemy.BaseQuery: query with filters applied
    """

    @classmethod
    def build_search_query(cls, query, params, model):
        filters = params.get("filters")

        # each filter in q should be table:partialtext
        for filter in filters:
            split = re.split("[-:]", filter)
            column = split[0]
            search_value = split[1]

            filter_column = getattr(model, column)

            query = query.filter(filter_column.ilike(f"%{search_value}%"))

        query = query.order_by(model.id)

        return query
