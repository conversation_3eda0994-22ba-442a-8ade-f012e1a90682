class SortMixin(object):
    """Get Sql Alchemy order by object

    Args:
        sort (dict): url sort argument, e.g. sort=column,asc|desc

    Returns:
        sql alchemy unary expression: order by sql alchemy object
    """

    @classmethod
    def get_order_by(cls, sort: dict):
        sort = sort["sort"].split(",")
        order_by = getattr(getattr(cls, sort[0]), sort[1])()
        return order_by
