import re
from typing import Optional


class User:
    def __init__(
        self,
        id: Optional[int],
        email: str,
        admin: bool = False,
        scopes: list[str] = [],
        token_type: Optional[str] = None,
        enabled_datasets: list[int] = [],
    ):
        self.id = id
        self.email = email
        self.admin = admin
        self.scopes = scopes
        self.token_type = token_type
        self.enabled_datasets = enabled_datasets

    @property
    def admin(self):
        return self.__admin

    @admin.setter
    def admin(self, admin):
        pattern1 = re.compile(r"(?i)[a-z]+\.[a-z]+@cloudbeds\.com$")
        pattern2 = re.compile(r"(?i)[a-zA-Z0-9-_]+@cloudbeds\.com$")

        self.__admin = (
            admin
            or bool(pattern1.match(self.email))
            or bool(pattern2.match(self.email))
        )
