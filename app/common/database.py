from typing import List

from flask_sqlalchemy import SQLAlchemy
from flask_sqlalchemy.model import Model


class CRUDMixin(Model):
    """Mixin that adds convenience methods for CRUD (create, read, update, delete) operations."""

    @classmethod
    def create(cls, **kwargs):
        """Create a new record and save it the database."""
        instance = cls(**kwargs)
        return instance.save()

    def update(self, commit=True, **kwargs):
        """Update specific fields of a record."""
        for attr, value in kwargs.items():
            setattr(self, attr, value)
        return commit and self.save() or self

    def save(self, commit=True):
        """Save the record."""
        db.session.add(self)
        if commit:
            db.session.commit()
        return self

    def delete(self, commit=True):
        """Remove the record from the database."""
        db.session.delete(self)
        return commit and db.session.commit()

    def bulk_update_mappings(self, rows: List[dict]) -> None:
        """Perform a bulk update of the given list of mapping dictionaries."""
        db.session.bulk_update_mappings(self, rows)
        db.session.commit()


class BindConfigSQLAlchemy(SQLAlchemy):
    def apply_driver_hacks(self, app, sa_url, options):
        """(Description is copied from source code)
        This method is called before engine creation and used to inject
        driver specific hacks into the options.  The `options` parameter is
        a dictionary of keyword arguments that will then be used to call
        the :func:`sqlalchemy.create_engine` function.

        (Custom Notes)
        This method will apply the default driver hacks, then set custom configuration
        options for each database engine.
        """
        super().apply_driver_hacks(app, sa_url, options)

        if sa_url.username == app.config["DATASET_USER"]:
            options["pool_size"] = app.config["DATASET_POOL_SIZE"]
            options["pool_recycle"] = app.config["DATASET_POOL_RECYCLE"]
            options["max_overflow"] = app.config["DATASET_MAX_OVERFLOW"]

        if sa_url.username == app.config["AURORA_USER"]:
            options["pool_size"] = app.config["AURORA_POOL_SIZE"]
            options["pool_recycle"] = app.config["AURORA_POOL_RECYCLE"]
            options["max_overflow"] = app.config["AURORA_MAX_OVERFLOW"]

        if sa_url.database == app.config["DATABASE_NAME"]:
            options["pool_size"] = app.config["DATABASE_POOL_SIZE"]
            options["pool_recycle"] = app.config["DATABASE_POOL_RECYCLE"]
            options["max_overflow"] = app.config["DATABASE_MAX_OVERFLOW"]

        return sa_url, options


db = BindConfigSQLAlchemy(model_class=CRUDMixin)
