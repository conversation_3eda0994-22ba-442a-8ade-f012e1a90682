SECURITY_KEYS = dict(
    Authorization="Authorization",
    APIKey="X-API-Key",
)

BASE_SECURITY = [
    {SECURITY_KEYS["Authorization"]: []},
]

FULL_SECURITY = [
    *BASE_SECURITY,
    {SECURITY_KEYS["APIKey"]: []},
]

AUTHORIZATION = "Authorization"
X_API_KEY = "X-API-KEY"
X_PROPERTY_ID = "X-PROPERTY-ID"

DESCRIPTION = """#### The Cloudbeds Data Insights API provides programmatic methods to access report data in [Data Insights](https://www.cloudbeds.com/measure-success/). With the Cloudbeds Data Insights API, you can:

- Build custom reports to display Cloudbeds data.
- Automate complex reporting tasks to save time.
- Integrate your Cloudbeds data with other business applications.
- Schedule your reports to your email in a custom frequency
- Access stock/custom reports created by Cloudbeds
- And many others
- Export Request has a limit of 100000 records
- Data and Summary requests has a limit of 100 records when the mode is Preview
- Data and Summary requests has a limit of 12000 records when the mode is Run

*Note: The API return application/json*."""

INFO = {
    "description": DESCRIPTION,
    "version": "1.1",
    "title": "Data Insights API",
    "termsOfService": "https://www.cloudbeds.com/terms/api/",
    "contact": {"email": "<EMAIL>"},
    "license": {"name": "Cloudbeds", "url": "https://www.cloudbeds.com/"},
}


def SECURITY_SCHEMES(app):
    return {
        SECURITY_KEYS["Authorization"]: {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": """##### Authorization refers to the process of granting a user or application access permissions to Cloudbeds data and features.

To authenticate your users, your App will need to implement an [authorization code flow](https://datatracker.ietf.org/doc/html/rfc6749#section-4.1).
[Cloudbeds API Authentication](https://hotels.cloudbeds.com/api/docs/#api-Authentication) lets you direct your users to an authorization dialog on Cloudbeds.
From there, the primary Cloudbeds experience will show the authorization dialog and handle the authorization on behalf of your App.
Your users will be able to authorize or decline.
After the user makes their choice, Cloudbeds will redirect the user to your App, where you can exchange the authorization code for an access token (if the user authorized your App), or handle a rejection (if the user did not authorize your App).

If using a cloudbeds API key, you can pass it as a header with the following format: `X-API-KEY: <your_api_key>`

###### Cloudbeds implements the [OAuth 2.0](https://integrations.cloudbeds.com/hc/en-us/articles/360006450433-OAuth-2-0) authorization framework.""",
        },
        SECURITY_KEYS["APIKey"]: {
            "type": "apiKey",
            "name": X_API_KEY,
            "in": "header",
            "description": "The API key is a unique identifier that authenticates requests associated with your project for usage and billing purposes.",
        },
    }


def SPEC_KWARGS(app):
    return {
        "info": INFO,
        "components": {"securitySchemes": SECURITY_SCHEMES(app)},
    }
