import json
import logging
import sys
from datetime import datetime

from flask import g, has_app_context, request

from sqlalchemy import Select
from sqlalchemy.dialects import postgresql

REPORTING_LOGGER = "reporting"
LAUNCH_DARKLY_LOGGER = "ldclient"

FALLBACK_KEYS = [
    "filename",
    "function",
    "level",
    "message",
    "user_id",
    "user_email",
    "property_id",
    "trace_id",
    "x-amzn-trace-id",
    "origin",
    "endpoint",
    "island",
    "time",
]

BUILTIN_ATTRS = {
    "args",
    "asctime",
    "created",
    "exc_info",
    "exc_text",
    "filename",
    "funcName",
    "levelname",
    "levelno",
    "lineno",
    "module",
    "msecs",
    "message",
    "msg",
    "name",
    "pathname",
    "process",
    "processName",
    "relativeCreated",
    "stack_info",
    "thread",
    "threadName",
}


def get_log_metadata():
    """Extracts logging metadata from Flask's g object into a dictionary."""
    metadata = {}

    if has_app_context():
        metadata.update(
            {
                "user_id": g.user.id if hasattr(g, "user") else "-",
                "user_email": g.user.email if hasattr(g, "user") else "-",
                "property_id": g.property_id if hasattr(g, "property_id") else "-",
                "user_property_ids": g.property_ids
                if hasattr(g, "property_ids")
                else "-",
                "trace_id": g.request_id if hasattr(g, "request_id") else "-",
                "x-amzn-trace-id": (
                    request.headers.get("x-amzn-trace-id", "-")
                    if request
                    else (g.x_amzn_trace_id if hasattr(g, "x_amzn_trace_id") else "-")
                ),
                "origin": request.origin if request and request.origin else "-",
                "endpoint": f"{request.method} -> {request.full_path}"
                if request
                else "-",
                "island": g.island if hasattr(g, "island") else "-",
            }
        )

    return metadata


class JSONFormatter(logging.Formatter):
    def __init__(self, date_format: str = "%Y-%m-%dT%H:%M:%S"):
        self.datefmt = date_format

    def format(self, record):
        message = record.getMessage()
        extra = self.extra_from_record(record)
        json_record = self.json_record(message, extra, record)
        return self.to_json(json_record)

    def to_json(self, record):
        """Converts record dict to a JSON string."""
        try:
            return json.dumps(record)
        except (TypeError, ValueError, OverflowError) as exception:
            try:
                return json.dumps(
                    {
                        **{key: record[key] for key in FALLBACK_KEYS if key in record},
                        **{"error": str(exception)},
                    }
                )
            except (TypeError, ValueError, OverflowError):
                return "{}"

    def extra_from_record(self, record):
        """Returns `extra` dict you passed to logger.

        The `extra` keyword argument is used to populate the `__dict__` of
        the `LogRecord`.

        Merges contextvars with extra metadata

        """
        extra = {
            attr_name: record.__dict__[attr_name]
            for attr_name in record.__dict__
            if attr_name not in BUILTIN_ATTRS
        }

        return extra

    def json_record(self, message, extra, record):
        """Prepares a JSON payload which will be logged.

        Override this method to change JSON log format.

        If is needed to use more python built in args please define it below

        :param message: Log message, e.g., `logger.info(msg='Create report')`.
        :param extra: Dictionary that was passed as `extra` param
            `logger.info('Creating Report, extra={'report_id': '52d6ce'})`.
        :param record: `LogRecord` we got from `JSONFormatter.format()`.
        :return: Dictionary which will be passed to JSON lib.

        """
        extra["filename"] = record.filename
        extra["function"] = record.funcName
        extra["level"] = record.levelname
        extra["message"] = message

        if has_app_context() and extra.get("x-amzn-trace-id", "-") == "-":
            extra.update(get_log_metadata())

        if hasattr(record, "stack_info"):
            extra["stack_info"] = record.stack_info

        if "time" not in extra:
            extra["time"] = datetime.utcnow().strftime(self.datefmt)

        if record.exc_info:
            extra["exc_info"] = self.formatException(record.exc_info)

        return extra


def configure_logger(level: int):
    logging_stream_handler = logging.StreamHandler()
    logging_stream_handler.setStream(stream=sys.stdout)
    logging_stream_handler.setFormatter(JSONFormatter())

    # Reporting Service Logger
    logger = logging.getLogger(REPORTING_LOGGER)
    logger.propagate = False
    logger.setLevel(level)
    logger.addHandler(logging_stream_handler)

    # Launch Darkly Logger
    ld_logger = logging.getLogger(LAUNCH_DARKLY_LOGGER)
    ld_logger.setLevel(logging.WARNING)
    ld_logger.addHandler(logging_stream_handler)


logger = logging.getLogger(REPORTING_LOGGER)


def log_query(query: Select, extras: dict = None):
    """Log SQL Alchemy Select Object Compiled Query."""
    query = query.compile(
        compile_kwargs=dict(literal_binds=True, dialect=postgresql)
    ).string
    logger.info(f"Query to be executed: {query}", extra=extras or {})
    return query
