from enum import Enum


class SupportedRules(Enum):
    FEATURE_IDS = "feature_ids"
    PROPERTY_IDS = "property_ids"
    COUNTRY_CODES = "country_codes"
    PROPERTY_TYPES = "property_types"


class RulesKind(Enum):
    String = 1  # property_ids
    PickList = 2  # feature_ids

    def __str__(self):
        return self.name


class SupportedRulesKind(Enum):
    FEATURE_IDS = 2
    PROPERTY_IDS = 1
    COUNTRY_CODES = 2
    PROPERTY_TYPES = 2


class SupportedRulesNames(Enum):
    FEATURE_IDS = "Products"
    PROPERTY_IDS = "Properties"
    COUNTRY_CODES = "Countries"
    PROPERTY_TYPES = "Property Types"
