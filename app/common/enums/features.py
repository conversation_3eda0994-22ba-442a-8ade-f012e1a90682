import itertools
from enum import Enum


BILLING_PORTAL_FEATURES = {
    "report_builder": ["Report Builder", "ReportBuilder"],
    "cb_websites": ["Cloudbeds Websites", "CloudbedsWebsites"],
    "mybookings_enabled": ["Booking Engine", "BookingEngine"],
    "cb_amplify_dashboard": ["Amplify"],
    "availability_enabled": ["Availability"],
    "cashiering_system_enabled": ["Cash Drawer", "CashDrawer"],
    "cb_payments_stripe_enabled": ["Cloudbeds Payments", "CloudbedsPayments"],
    "hardware_terminal": ["Cloudbeds Terminal", "CloudbedsTerminal"],
    "house_keeping_enabled": ["Housekeeping"],
    "invoicing_enabled": ["Invoicing"],
    "settings_multi_currency": ["Multi-Currency", "MultiCurrency"],
    "myallocator_enabled": ["My Allocator", "MyAllocator"],
    "pie_enabled": ["PIE"],
    "revenue_allocation": ["Revenue Allocation", "RevenueAllocation"],
    "shared_inventory_enabled": ["Shared Inventory", "SharedInventory"],
    "split_inventory": ["Split Inventory", "SplitInventory"],
    "whistle_enabled": ["Whistle+", "Whistle"],
    "group_housing_enabled": ["Group Housing", "GroupHousing"],
    "house_accounts_enabled": ["House Accounts", "HouseAccounts"],
    "cb_payments_dlocal_enabled": [
        "CBPay-dLocal",
    ],
}

BillingPortalFeatures = Enum(
    value="BillingPortalFeatures",
    names=itertools.chain.from_iterable(
        itertools.product(v, [k]) for k, v in BILLING_PORTAL_FEATURES.items()
    ),
)


class LaunchDarklyFeature(Enum):
    Explore = "feature.di.explore"
    ExploreReports = "feature.di.explore-reports"
    ReservationDatasetBreakfast = "feature.di.reservation-dataset.breakfast"
    # FF Datasets
    FinancialDatasetFF = "feature.di.financial-dataset.ff-view"
    GuestDatasetFF = "feature.di.guest-dataset.ff-view"
    PaymentDatasetFF = "feature.di.payment-dataset.ff-view"
    PayoutDatasetFF = "feature.di.payout-dataset.ff-view"
    ReservationDatasetFF = "feature.di.reservation-dataset.ff-view"
    InvoiceDatasetFF = "feature.di.invoice-dataset.ff-view"
    OccupancyV1DatasetFF = "feature.di.occupancy-dataset.v1.ff-view"
    OccupancyV1 = "feature.di.occupancy-dataset.v1"
    HousekeepingDatasetFF = "feature.di.housekeeping-dataset.ff-view"
    PublishSSE = "feature.di.publish-sse"
    AccountingDataset = "feature.di.accounting-dataset"
    PermissionService = "feature.di.permissions-service"
    CustomFieldsCDFsFF = "feature.di.custom-field-cdfs"
    BedOccupancyDataset = "feature.di.bed-occupancy-dataset"
    BedOccupancyDatasetFF = "feature.di.bed-occupancy-dataset.ff-view"
    # Classic Reports FF
    ClassicReportRevpar = "feature.di.classic-reports.revpar"
    ClassicReportOccupancy = "feature.di.classic-reports.occupancy"
    ClassicReportADR = "feature.di.classic-reports.adr"
    ClassicReportDailyRevenue = "feature.di.classic-reports.daily-revenue"
    ClassicReportPaymentLedger = "feature.di.classic-reports.payment-ledger"
    ClassicReportDailyFinancial = "feature.di.classic-reports.dfr"
    ClassicReportsAccountBalances = "feature.di.classic-reports.account-balances"
    ClassicReportTransactionsReport = "feature.di.classic-reports.transactions"
    ClassicReportPaymentReconciliationReport = (
        "feature.di.classic-reports.payment_reconciliation"
    )
    ExportPdf = "feature.di.export-pdf"
    ClassicReportReservationsByCountryReport = (
        "feature.di.classic-reports.reservations-by-country-report"
    )
    ClassicReportArrivalsReport = "feature.di.classic-reports.arrivals-report"
    ClassicReportChannelProductionReport = (
        "feature.di.classic-reports.channel-production-report"
    )
    ClassicReportDeparturesReport = "feature.di.classic-reports.departures-report"
    ClassicReportUserReconciliationReport = (
        "feature.di.classic-reports.user-reconciliation"
    )
    CustomCdfCaseStatements = "feature.di.custom-cdf-case-statements"
    ClassicReportNoShowReport = "feature.di.classic-reports.no-show-report"
    ClassicReportInHouseReport = "feature.di.classic-reports.in-house-report"
    ClassicReportCancellationReport = "feature.di.classic-reports.cancellation-report"
    ClassicReportReservationsByRatePlanReport = (
        "feature.di.classic-reports.reservations-by-rate-plan-report"
    )
    ClassicReportTax = "feature.di.classic-reports.tax"
    ClassicReportCommission = "feature.di.classic-reports.commission"
    ClassicReportAdjustments = "feature.di.classic-reports.adjustments"
    ClassicReportNotesReport = "feature.di.classic-reports.notes-report"
    CustomFieldsCDFsMetadataFFView = "feature.di.custom-field-cdfs-metadata.ff-view"
    CustomFieldsCDFsDataFFView = "feature.di.custom-field-cdfs-data.ff-view"
    ReportingCurrency = "feature.di.reporting-currency"
    FinancialDatasetRoomNumberNotNullFilter = (
        "feature.di.financial-dataset.room-number-not-null-filter"
    )
