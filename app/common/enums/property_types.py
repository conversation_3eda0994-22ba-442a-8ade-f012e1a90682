from enum import Enum


class PropertyTypes(Enum):
    Hotel = (1, "hotel", "Hotel")
    Resort = (2, "resort", "Resort")
    BedAndBreakfast = (3, "bed_and_breakfast", "Bed & Breakfast")
    Ranch = (4, "ranch", "Ranch")
    FlatHotel = (5, "flat_hotel", "Flat Hotel")
    Hostel = (6, "hostel", "Hostel")
    Motel = (7, "motel", "Motel")
    Boutique = (8, "boutique", "Boutique")
    GuestHouse = (9, "guest_house", "Guest House")
    VacationRental = (10, "vacation_rental", "Vacation Rental")
    Chain = (11, "chain", "Chain")
    OutdoorLodge = (12, "outdoor_lodge", "Outdoor Lodge")
    RVPark = (13, "rv_park", "RV Park")
    Campground = (14, "campground", "Campground")

    def __init__(self, key, name, value):
        self._value_ = value
        self._name_ = name
        self._db_key_ = key

    @staticmethod
    def get_by_key(key: int) -> "PropertyTypes":
        for hotel_type in PropertyTypes:
            if key == hotel_type._db_key_:
                return hotel_type
        raise ValueError(f"Invalid Hotel Type Key: {key}")
