from enum import Enum
from typing import OrderedDict

from app.enums.dataset import Dataset


class PermissionActions(Enum):
    Create = "create"
    Read = "read"
    Update = "update"
    Delete = "delete"


class Resources(Enum):
    Admin = "admin"
    Report = "report"
    StockReport = "stock_report"
    StockReportRevision = "stock_report_revision"
    ReportBuilder = "report_builder"
    PaymentReports = "payment_reports"
    FinancialReports = "financial_reports"
    ProductionReports = "production_reports"
    DailyActivityReports = "daily_activity_reports"
    PoliceReport = "police_report"
    HouseKeeping = "housekeeping"
    Hub = "hub"
    Chart = "chart"
    Tag = "tag"
    Folder = "folder"
    Favorite = "favorite"
    Schedule = "schedule"
    Dataset = "dataset"


class PermissionRole(Enum):
    VIEWER = "VIEWER"
    AUTHOR = "AUTHOR"
    PUBLISHER = "PUBLISHER"

    @classmethod
    def list(cls):
        return [role.value for role in cls]


class PermissionAction(Enum):
    VIEW = "ACTION_DI_VIEW"
    MANAGE = "ACTION_DI_MANAGE"
    PUBLISH = "ACTION_DI_PUBLISH"
    CLONE = "ACTION_DI_CLONE"
    CREATE = "ACTION_DI_CREATE"
    UPDATE = "ACTION_DI_UPDATE"
    DELETE = "ACTION_DI_DELETE"
    EXPORT = "ACTION_DI_EXPORT"
    MANAGE_FILTER_OPERATOR = "ACTION_DI_MANAGE_FILTER_OPERATOR"
    MANAGE_FILTER_VALUE = "ACTION_DI_MANAGE_FILTER_VALUE"
    MANAGE_METRICS = "ACTION_DI_MANAGE_METRICS"
    MANAGE_FILTERS = "ACTION_DI_MANAGE_FILTERS"
    SORT_COLUMNS = "ACTION_DI_SORT_COLUMNS"
    REARRANGE_COLUMNS = "ACTION_DI_REARRANGE_COLUMNS"
    ADJUST_GROUPING_BY_VALUE = "ACTION_DI_ADJUST_GROUPING_BY_VALUE"
    # Permissions below are not supported on Permission Service Actions
    REVISION = "ACTION_DI_REVISION"
    EXPLORE = "ACTION_DI_EXPLORE"
    EXPLORE_REPORT = "ACTION_DI_EXPLORE_REPORT"

    @classmethod
    def list(cls):
        return [action.value for action in cls]

    @classmethod
    def get_permission_actions(cls):
        return [
            action.value
            for action in cls
            if action not in (cls.REVISION, cls.EXPLORE, cls.EXPLORE_REPORT)
        ]


class Resource(Enum):
    STOCK_REPORTS = "stock_reports"
    REPORTS = "reports"
    SETTINGS = "settings"
    EXPLORE = "explore"


class PermissionResourcePrefix(Enum):
    STOCK_REPORT = "data_insights_stock_report"
    REPORT = "data_insights_report"


class PermissionResource(Enum):
    FINANCIAL_REPORTS = f"{PermissionResourcePrefix.REPORT.value}:financial"
    FINANCIAL_STOCK_REPORTS = f"{PermissionResourcePrefix.STOCK_REPORT.value}:financial"
    PAYMENT_REPORTS = f"{PermissionResourcePrefix.REPORT.value}:payments"
    PAYMENT_STOCK_REPORTS = f"{PermissionResourcePrefix.STOCK_REPORT.value}:payments"
    GUEST_REPORTS = f"{PermissionResourcePrefix.REPORT.value}:guests"
    GUEST_STOCK_REPORTS = f"{PermissionResourcePrefix.STOCK_REPORT.value}:guests"
    RESERVATION_REPORTS = f"{PermissionResourcePrefix.REPORT.value}:reservations"
    RESERVATION_STOCK_REPORTS = (
        f"{PermissionResourcePrefix.STOCK_REPORT.value}:reservations"
    )
    OCCUPANCY_REPORT = f"{PermissionResourcePrefix.REPORT.value}:occupancy"
    OCCUPANCY_STOCK_REPORTS = f"{PermissionResourcePrefix.STOCK_REPORT.value}:occupancy"
    INVOICE_REPORTS = f"{PermissionResourcePrefix.REPORT.value}:invoices"
    INVOICE_STOCK_REPORTS = f"{PermissionResourcePrefix.STOCK_REPORT.value}:invoices"
    HOUSEKEEPING_REPORTS = f"{PermissionResourcePrefix.REPORT.value}:housekeeping"
    HOUSEKEEPING_STOCK_REPORTS = (
        f"{PermissionResourcePrefix.STOCK_REPORT.value}:housekeeping"
    )

    @classmethod
    def list(cls):
        return [resource.value for resource in cls]


ROLES_HIERARCHY = OrderedDict(
    {
        PermissionRole.VIEWER.value: set(
            [
                "ACTION_DI_VIEW",
                "ACTION_DI_EXPORT",
                "ACTION_DI_MANAGE_FILTER_OPERATOR",
                "ACTION_DI_MANAGE_FILTER_VALUE",
                "ACTION_DI_SORT_COLUMNS",
                "ACTION_DI_REARRANGE_COLUMNS",
                "ACTION_DI_ADJUST_GROUPING_BY_VALUE",
            ]
        ),
        PermissionRole.AUTHOR.value: set(
            [
                "ACTION_DI_MANAGE",
                "ACTION_DI_CLONE",
                "ACTION_DI_CREATE",
                "ACTION_DI_UPDATE",
                "ACTION_DI_DELETE",
                "ACTION_DI_MANAGE_METRICS",
                "ACTION_DI_MANAGE_FILTERS",
            ]
        ),
        PermissionRole.PUBLISHER.value: set(
            ["ACTION_DI_PUBLISH", "ACTION_DI_REVISION"]
        ),
    }
)

PERMISSION_ACTION_MAPPING = {
    PermissionAction.VIEW.value: PermissionActions.Read.value,
    PermissionAction.CREATE.value: PermissionActions.Create.value,
    PermissionAction.UPDATE.value: PermissionActions.Update.value,
    PermissionAction.DELETE.value: PermissionActions.Delete.value,
}


PERMISSION_RESOURCE_TO_RESOURCE_MAPPING = {
    PermissionResource.FINANCIAL_REPORTS.value: "reports"
}

PERMISSION_RESOURCE_TO_DATASET_MAPPING = {
    PermissionResource.FINANCIAL_REPORTS.value: (Dataset.Financial,),
    PermissionResource.FINANCIAL_STOCK_REPORTS.value: (Dataset.Financial,),
    PermissionResource.RESERVATION_REPORTS.value: (Dataset.Reservations,),
    PermissionResource.RESERVATION_STOCK_REPORTS.value: (Dataset.Reservations,),
    PermissionResource.GUEST_REPORTS.value: (Dataset.Guests,),
    PermissionResource.GUEST_STOCK_REPORTS.value: (Dataset.Guests,),
    PermissionResource.PAYMENT_REPORTS.value: (Dataset.Payment, Dataset.Payout),
    PermissionResource.PAYMENT_STOCK_REPORTS.value: (Dataset.Payment,),
    PermissionResource.INVOICE_REPORTS.value: (Dataset.Invoices,),
    PermissionResource.INVOICE_STOCK_REPORTS.value: (Dataset.Invoices,),
    PermissionResource.HOUSEKEEPING_REPORTS.value: (Dataset.Housekeeping,),
    PermissionResource.HOUSEKEEPING_STOCK_REPORTS.value: (Dataset.Housekeeping,),
    PermissionResource.OCCUPANCY_REPORT.value: (
        Dataset.Occupancy,
        Dataset.OccupancyV1,
        Dataset.BedOccupancy,
    ),
    PermissionResource.OCCUPANCY_STOCK_REPORTS.value: (
        Dataset.Occupancy,
        Dataset.OccupancyV1,
        Dataset.BedOccupancy,
    ),
}


CLASSIC_REPORTS_TO_DATASET_MAPPING = {
    Resources.PaymentReports.value: [
        str(Dataset.Payment.value),
        str(Dataset.Payout.value),
    ],
    Resources.FinancialReports.value: [
        str(Dataset.Financial.value),
        str(Dataset.Invoices.value),
    ],
    Resources.ProductionReports.value: [
        str(Dataset.Reservations.value),
        str(Dataset.Occupancy.value),
        str(Dataset.OccupancyV1.value),
        str(Dataset.BedOccupancy.value),
    ],
    Resources.DailyActivityReports.value: [
        str(Dataset.Reservations.value),
        str(Dataset.Occupancy.value),
        str(Dataset.OccupancyV1.value),
        str(Dataset.BedOccupancy.value),
        str(Dataset.Guests.value),
    ],
    Resources.HouseKeeping.value: [str(Dataset.Housekeeping.value)],
    Resources.PoliceReport.value: [str(Dataset.Guests.value)],
}
