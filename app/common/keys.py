VALID_ACCESS_TOKEN_KEY = "okta.valid_access_token"
PROPERTIES_DI_DISABLED_KEY = "mfd.properties_di_disabled"
DATASETS_CDFS = "datasets.cdfs"
DATASET_MULTI_LEVELS = "dataset.multi_levels"
MULTI_LEVEL = "multi_level"
DATASET_CDF_OPTIONS = "dataset.cdf.options"
DATASET_UPDATED_AT = "dataset.updated_at"
PERMISSION_SERVICE_ROLE_KEY = "permission_service.role"
USER_POLICIES_KEY = "permission_service.user_policies"
PROPERTY_COUNTRY_CODE = "mfd.property_country_code"
CLASSIC_REPORTS_DATASETS = "mfd.classic_reports_datasets"
UNPERMITTED_REPORT_IDS_KEY = "reports.unpermitted_report_ids"
DATASET_UPDATED_AT_BY_PROPERTY_ID_ORGANIZATION_ID_KEY = (
    "dataset.updated_at_by_property_id_organization_id"
)
EXPLORE_CONTEXT_KEY = "explore.context"
EXPLORE_CHAT_KEY = "explore.chat"
PERMISSION_SERVICE_LIST_PERMISSION = "permission_service.list_permissions"
TRANSLATION_KEYS = "translation.keys"
TRANSLATION_PATTERN = "translation.pattern"
TRANSLATION_VALUES_AS_KEY = "translations.values"


def template(key, suffix):
    return f"{key}.{suffix}"


def valid_access_token_key(access_token):
    return template(VALID_ACCESS_TOKEN_KEY, access_token)


def datasets_cdfs_by_property_and_user(
    property_id, user_email, dataset_id, report_kind
):
    return template(
        DATASETS_CDFS, f"{property_id}.{user_email}.{dataset_id}.{report_kind}"
    )


def multi_level_by_id(multi_level_id):
    return template(MULTI_LEVEL, f"{multi_level_id}")


def multi_levels_by_dataset_id(dataset_id):
    return template(DATASET_MULTI_LEVELS, f"{dataset_id}")


def dataset_cdf_options(property_id, dataset_id, cdf):
    return template(DATASET_CDF_OPTIONS, f"{property_id}.{dataset_id}.{cdf}")


def whitelisted_emails_key():
    return template("S3", "whitelisted_emails")


def property_country_code_key(property_id):
    return template(PROPERTY_COUNTRY_CODE, f"{property_id}")


def classic_reports_datasets_key(user_id, property_id, api_key: int = 0):
    return template(CLASSIC_REPORTS_DATASETS, f"{user_id}.{property_id}.{api_key}")


def unpermitted_report_ids_key(property_id, property_ids):
    return template(UNPERMITTED_REPORT_IDS_KEY, f"{property_id}.{property_ids}")


def dataset_updated_at_by_property_id_organization_id_key(
    dataset_id, property_id, organization_id
):
    return template(
        DATASET_UPDATED_AT_BY_PROPERTY_ID_ORGANIZATION_ID_KEY,
        f"{dataset_id}.{property_id}.{organization_id}",
    )


def explore_context_key(context_id: str):
    return template(EXPLORE_CONTEXT_KEY, f"{context_id}")


def explore_chat_key(context_id: str):
    return template(EXPLORE_CHAT_KEY, f"{context_id}")


def list_permissions_key(property_id: str, user_id: str):
    return template(PERMISSION_SERVICE_LIST_PERMISSION, f"{property_id}.{user_id}")
