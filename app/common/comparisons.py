from app.common.periods import get_period_filter


def get_comparison_filter(report, comparison):
    """this method will get the "extra" filters that define each comparison (or period) and combine them with the reports filters
    comparisons reports use the same structure as the report filters, but period reports use a different structure
    we'll need to call the get_period_filter method to get the correct structure for period reports
    this is to get period summary reports to use the same class as comparison summary"""
    if comparison.get("start"):  # only period summary reports have a start and end date
        comparison_filter = get_period_filter(report, comparison)
    else:
        comparison_filter = {}
        if "filters" in comparison:
            comparison_filter = {
                key: value[:] if isinstance(value, list) else value
                for key, value in comparison["filters"].items()
            }

        if report.filters:
            if "and" in comparison_filter:
                comparison_filter["and"].append(report.filters)
            else:
                comparison_filter["and"] = [report.filters]

    return comparison_filter
