import traceback
from http import HTT<PERSON>tatus

from flask import g

from flask_smorest import abort

from app.common.logger import logger


DIVIDE_BY_ZERO = {
    "code": HTTPStatus.UNPROCESSABLE_ENTITY,
    "name": "Unprocessable Entity",
    "description": "The server encountered that the requested operation is performing a division by zero.",
    "message": "Division by zero. The divisor in numeric Custom CDF contains zero. To proceed, consider modifying the custom formula or adding a filter rule to remove results with zeroes.",
}

SERVER_ERROR_500 = {
    "code": HTTPStatus.INTERNAL_SERVER_ERROR,
    "name": "Internal Server Error",
    "description": "The server encountered an unexpected condition that prevented it from fulfilling the request.",
    "message": "A bug report has been distributed to the Cloudbeds team.",
}

VALIDATION_ERROR_400 = {
    "code": HTTPStatus.BAD_REQUEST,
    "name": "Bad request",
    "description": "The server encountered an unexpected condition that prevented it from fulfilling the request.",
    "message": "",
}


class InvalidUsage(Exception):
    @classmethod
    def bad_request(cls, message=None):
        logger.debug("Bad Request", extra={"reason": message})

        if message:
            return abort(HTTPStatus.BAD_REQUEST, message=message)

        return abort(HTTPStatus.BAD_REQUEST)

    @classmethod
    def not_authorized(cls, message="The server couldn't authenticate"):
        logger.debug("User is not authorized", extra={"access_token": g.access_token})
        return abort(HTTPStatus.UNAUTHORIZED, message=message)

    @classmethod
    def not_found(cls, message="The resource was not found"):
        return abort(HTTPStatus.NOT_FOUND, message=message)

    @classmethod
    def server_error(cls, message=SERVER_ERROR_500["message"]):
        return abort(HTTPStatus.INTERNAL_SERVER_ERROR, message=message)

    @classmethod
    def conflict(
        cls, message="There is a conflict with the current state of the target resource"
    ):
        return abort(HTTPStatus.CONFLICT, message=message)

    @classmethod
    def service_unavailable(cls, message="There is an unspecified Server Error"):
        return abort(HTTPStatus.SERVICE_UNAVAILABLE, message=message)

    @classmethod
    def too_many_requests(
        cls,
        message="The user has sent too many requests in a given amount of time (rate limiting)",
    ):
        return abort(HTTPStatus.TOO_MANY_REQUESTS, message=message)

    @classmethod
    def forbidden(cls, message="The user is not permitted.", **kwargs):
        logger.debug(
            message,
            extra=kwargs,
        )
        return abort(HTTPStatus.FORBIDDEN, message=message)

    @classmethod
    def unprocessable_entity(
        cls, message="The server could not process the request due to a client error."
    ):
        return abort(HTTPStatus.UNPROCESSABLE_ENTITY, message=message)


def validation_errorhandler(error):
    logger.info("Validation error", extra={"error": str(error)})
    error = type(
        "error", (object,), {**VALIDATION_ERROR_400, **{"message": str(error)}}
    )()
    return handle_http_exception(error)


def general_errorhandler(error):
    logger.error(
        "General Service error",
        extra={"error": str(error), "log_trace": traceback.format_exc()},
    )
    error = type("error", (object,), SERVER_ERROR_500)()
    return handle_http_exception(error)


def sqlalchemy_errorhandler(error):
    """Function to handle SQL Alchemy Errors"""
    logger.error("SQL Alchemy error", extra={"error": traceback.format_exc()})

    if any(value in error.args[0] for value in ["Divide by zero", "division by zero"]):
        return handle_http_exception(type("error", (object,), DIVIDE_BY_ZERO)())

    if "duplicate key value violates unique constraint" in error.args[0]:
        return handle_http_exception(
            type(
                "error",
                (object,),
                {
                    "code": HTTPStatus.CONFLICT,
                    "name": "Request Conflict",
                    "description": "The server encountered an unexpected condition that prevented it from fulfilling the request.",
                    "message": "Is not possible to create duplicated resources",
                },
            )()
        )

    return handle_http_exception(type("error", (object,), SERVER_ERROR_500)())


def handle_http_exception(error):
    """Return a JSON response containing a description of the error

    This method is registered at app init to handle ``HTTPException``.

    - When ``abort`` is called in the code, an ``HTTPException`` is
        triggered and Flask calls this handler.

    - When an exception is not caught in a view, Flask makes it an
        ``InternalServerError`` and calls this handler.

    flask-smorest republishes webargs's
    :func:`abort <webargs.flaskparser.abort>`. This ``abort`` allows the
    caller to pass kwargs and stores them in ``exception.data`` so that the
    error handler can use them to populate the response payload.

    Extra information expected by this handler:

    - `message` (``str``): a comment
    - `errors` (``dict``): errors, typically validation errors in
        parameters and request body
    """
    # Get additional info passed as kwargs when calling abort
    # data may not exist if HTTPException was raised without webargs abort
    data = getattr(error, "data", None)

    # Send the custom message, otherwise select suitable error message
    if hasattr(error, "message"):
        error_message = error.message
    elif data:
        if "message" in data:
            error_message = data["message"]
        if "errors" in data:
            error_message = data["errors"]
        elif "messages" in data:
            error_message = data["messages"]
    else:
        error_message = HTTPStatus(error.code).description

    payload = dict(
        error=dict(
            code=error.code,
            status=error.name,
            description=error.description,
            message=error_message,
        )
    )

    return payload, error.code
