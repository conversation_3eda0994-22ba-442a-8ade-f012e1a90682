<!DOCTYPE html>
<html>

  <head>
    <title>{{title}}</title>
    <link href="{{swagger_ui_url}}swagger-ui.css" rel="stylesheet" type="text/css"/>
  </head>

 <body>

   <div id="swagger-ui-container"></div>

   <script src="{{swagger_ui_url}}swagger-ui-standalone-preset.js"></script>
   <script src="{{swagger_ui_url}}swagger-ui-bundle.js"></script>
   <script>
     config = {
       url: window.location.protocol + '//' + window.location.host + window.location.pathname + "{{config['OPENAPI_JSON_PATH']}}",
       dom_id: '#swagger-ui-container'
     }

     var override_config = {{ swagger_ui_config | tojson }};
     for (var attrname in override_config) { config[attrname] = override_config[attrname]; }

     window.onload = function() {
       window.ui = SwaggerUIBundle({...config, deepLinking: true, displayRequestDuration: true })
     }
   </script>

 </body>

</html>