msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2022-08-04 15:51+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: Financial
msgid "Financial"
msgstr "Financial"

#: Guests
msgid "Guests"
msgstr "Guests"

#: Reservations
msgid "Reservations"
msgstr "Reservations"

#: Occupancy
msgid "Occupancy"
msgstr "Occupancy"

#: Payment
msgid "Payment"
msgstr "Payment"

#: Invoices
msgid "Invoices"
msgstr "Invoices"

msgid "Production"
msgstr "Production"

msgid "Revenue"
msgstr "Revenue"

msgid "Daily"
msgstr "Daily"

msgid "Government"
msgstr "Government"

msgid "Groups"
msgstr "Groups"

msgid "Housekeeping"
msgstr "Housekeeping"

msgid "Accounting"
msgstr "Accounting"

msgid "Audit"
msgstr "Audit"

msgid "Forecast"
msgstr "Forecast"

msgid "Rate reports"
msgstr "Rate reports"

msgid "Monthly"
msgstr "Monthly"

msgid "Owner reports"
msgstr "Owner reports"

msgid "Payments"
msgstr "Payments"

msgid "Occupancy (Legacy - Unsupported)"
msgstr "Occupancy (Beta)"

msgid "sources_website"
msgstr "Sources website"

msgid "sources_walk_in"
msgstr "sources_walk_in"

msgid "sources_phone"
msgstr "sources_phone"

msgid "sources_front_desk"
msgstr "sources_front_desk"

msgid "sources_email"
msgstr "sources_email"

msgid "sources_wholesaler"
msgstr "sources_wholesaler"

msgid "sources_ota"
msgstr "sources_ota"

msgid "sources_corporate_client"
msgstr "sources_corporate_client"

msgid "sources_travel_agent"
msgstr "sources_travel_agent"

msgid "sources_import"
msgstr "sources_import"

msgid "sources_facebook"
msgstr "sources_facebook"

msgid "sources_third_party"
msgstr "sources_third_party"

msgid "sources_direct"
msgstr "sources_direct"

msgid "sources_commission"
msgstr "sources_commission"

msgid "Payout"
msgstr "Payout"

msgid "Bed Occupancy"
msgstr "Bed Occupancy"

