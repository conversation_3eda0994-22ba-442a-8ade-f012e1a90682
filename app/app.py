from os import environ

from flask import Flask, current_app as app, json as flask_json

from flask_migrate import Migrate

from app.common.babel import babel, get_locale
from app.common.configuration import Configuration
from app.common.database import db
from app.common.gzip import Gzip
from app.settings import config

from celery_app.celery import celery_init_app


migrate = Migrate()
gzip = Gzip()


def create_app():
    app = Flask(__name__, template_folder="templates")
    env = environ.get("ENV")
    app.config.from_object(config.get(env, "DEFAULT"))
    app.url_map.strict_slashes = False

    app = Configuration(app).configure()
    flask_json.provider.DefaultJSONProvider.sort_keys = app.config["JSON_SORT_KEYS"]
    db.init_app(app)
    migrate.init_app(app, db)
    gzip.init_app(app)
    babel.init_app(app, locale_selector=get_locale)

    app.config.from_prefixed_env()
    celery_init_app(app)

    return app


@migrate.configure
def configure_alembic(config):
    database_url = "postgresql://{user}:{password}@{host}:{port}/{db}".format(
        user=app.config["DATABASE_USER"],
        password=app.config["DATABASE_PASSWORD"],
        host=app.config["DATABASE_HOST"],
        port=app.config["DATABASE_PORT"],
        db=app.config["DATABASE_NAME"],
    )
    config.set_main_option("sqlalchemy.url", database_url)
    return config
