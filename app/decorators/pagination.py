from functools import wraps

from flask import request

from app.api.v1_1.schemas.pagination import QueryPaginationSchema


def pagination(resource: str) -> dict:
    """Paginate database query with limit and offset if the parameters exist"""

    def decorator_paginated_results(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            query_pagination = dict()

            offset = (
                kwargs["offset"]
                if "offset" in kwargs
                else request.args.get("offset", None)
            )

            if offset:
                query_pagination["offset"] = offset

            limit = (
                kwargs["limit"]
                if "limit" in kwargs
                else request.args.get("limit", None)
            )

            if limit:
                query_pagination["limit"] = limit

            pagination_schema = QueryPaginationSchema()
            pagination_options = pagination_schema.load(query_pagination)
            offset = pagination_options["offset"]
            limit = pagination_options["limit"]
            query_result = func(*args, **kwargs)
            return dict(
                offset=offset,
                limit=limit,
                total=query_result.count(),
                **{resource: query_result.offset(offset).limit(limit).all()},
            )

        return wrapper

    return decorator_paginated_results
