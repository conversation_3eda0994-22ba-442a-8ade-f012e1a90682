from copy import deepcopy
from functools import wraps

from flask import g, has_request_context, request

from app.common.constants.permission_service import MFD_ACCESS_REPORT_BUILDER_ACL
from app.common.enums import (
    BillingPortalFeatures,
    Resources,
    TokenTypes,
)
from app.common.enums.permissions import (
    PermissionAction,
    PermissionRole,
    Resource,
)
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.openapi import BASE_SECURITY, X_API_KEY, X_PROPERTY_ID
from app.common.user import User
from app.enums import Dataset as DatasetEnum
from app.services.access_control_service import AccessControlService
from app.services.api_key_service import APIKeyService
from app.services.dataset_service import DatasetService
from app.services.permission_service import PermissionService
from app.services.property_feature_service import PropertyFeatureService
from app.services.user_service import UserService


def has_property_permission():
    """Decorator that will check if the user has access to the property ids associated with the token"""

    def decorator_has_property_permission(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if has_request_context:
                property_id = (
                    request.args.get("property_id")
                    if request.args.get("property_id")
                    else request.headers.get(X_PROPERTY_ID)
                )
                property_ids = request.args.get("property_ids")
            else:
                property_id = g.property_id
                property_ids = g.property_ids

            if property_id and (property_ids is None or not property_ids):
                property_ids = property_id

            if property_ids is None:
                raise InvalidUsage.bad_request("Property id is required")

            if isinstance(property_ids, str):
                property_ids = [int(property) for property in property_ids.split(",")]

            # Check that all properties are in the same organization when Organization ID is present
            if not all(property_id in g.property_ids for property_id in property_ids):
                raise InvalidUsage.forbidden(
                    f"Property IDs {property_ids} do not all belong to the organization ID {g.organization_id}"
                )

            if AccessControlService.is_allowed_to_access_properties(
                property_ids=property_ids
            ):
                return func(*args, **kwargs)

        wrapper._apidoc = deepcopy(getattr(wrapper, "_apidoc", {}))
        wrapper._apidoc.setdefault("manual_doc", {})
        wrapper._apidoc["manual_doc"]["security"] = BASE_SECURITY

        return wrapper

    return decorator_has_property_permission


def authorization(func):
    """Decorator that will verify the Authorization Header and it will validate the Access Token, checking permissions
    Use this decorator if you don't need to check for permissions for example Dataset, Me are views that does not need permissions
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        if not has_insights_access(g.user, g.property_id):
            raise InvalidUsage.forbidden(
                message="User does not have access to Data Insights",
                acls=UserService.get_insights_acls(g.user, g.property_id),
                access_token=g.access_token,
            )

        return func(*args, **kwargs)

    wrapper._apidoc = deepcopy(getattr(wrapper, "_apidoc", {}))
    wrapper._apidoc.setdefault("manual_doc", {})
    wrapper._apidoc["manual_doc"]["security"] = BASE_SECURITY
    return wrapper


def check_user_has_permission(
    action: PermissionAction,
    resource: Resources,
    role: PermissionRole = PermissionRole.VIEWER,
):
    # 403 decorator
    """Checks if logged in user can perform an action on a resource

    Args:
        action: PermissionAction
        resource (string):
        role: PermissionRole
    """

    def decorator_check_user_has_permission(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            authorized = False
            if has_request_context():
                api_key = request.headers.get(X_API_KEY)
                property_id = request.headers.get("X_PROPERTY_ID")
            else:
                api_key = g.get("api_key")
                property_id = g.get("property_id")

            if api_key:
                logger.debug("User is logging in with API Key")
                if not APIKeyService.is_api_key_valid(api_key):
                    raise InvalidUsage.not_authorized("API Key is not valid")
                else:
                    authorized = True

            if not authorized:
                authorized = is_user_authorized(
                    property_id, action, resource.value, role
                )

            return func(*args, **kwargs)

        return wrapper

    decorator_check_user_has_permission._apidoc = deepcopy(
        getattr(decorator_check_user_has_permission, "_apidoc", {})
    )
    decorator_check_user_has_permission._apidoc.setdefault("manual_doc", {})
    decorator_check_user_has_permission._apidoc["manual_doc"][
        "security"
    ] = BASE_SECURITY
    return decorator_check_user_has_permission


def has_insights_access(user: User, property_id: int) -> bool:
    return (
        PropertyFeatureService.has_feature_flag(
            BillingPortalFeatures.ReportBuilder.value, property_id
        )
        and UserService.has_acl(user, property_id, MFD_ACCESS_REPORT_BUILDER_ACL)
    ) or len(UserService.get_insights_acls(user, property_id))


def has_user_multiproperty_feature(property_id: str) -> bool | Exception:
    """Function that will check that the Viewer is only requesting data from the property is logged in to avoid Multiproperty feature"""
    if has_request_context():
        query_params_property_ids = (
            request.args.get("property_ids").split(",")
            if request.args.get("property_ids", None)
            else []
        )

        body_property_ids = (
            request.json.get("property_ids", [])
            if request.data and request.is_json
            else []
        )
    else:
        query_params_property_ids = g.get("property_ids", [])

        body_property_ids = g.get("property_ids", [])
    property_ids = list(
        set(
            query_params_property_ids
            + [str(property_id) for property_id in body_property_ids]
        )
    )

    if len(property_ids) > 1 or (
        len(property_ids) == 1 and property_id not in property_ids
    ):
        raise InvalidUsage.forbidden(
            "User does not have permission to use multiproperty features"
        )

    return True


def is_user_authorized(
    property_id: int, action: PermissionAction, resource: str, role: PermissionRole
) -> bool:
    # Any User that has access to MFD can perform any action on Favorite
    if resource == Resources.Favorite.value:
        return True

    if resource == Resources.Dataset.value:
        return g.dataset_id in UserService.get_insights_acls(g.user, property_id)

    paid_features = resource in (
        Resources.Report.value,
        Resources.Hub.value,
        Resources.Schedule.value,
        Resources.Folder.value,
        Resources.Tag.value,
        Resources.Chart.value,
    )

    free_features = action == PermissionAction.VIEW and (
        resource
        in (
            Resources.StockReport.value,
            Resources.Folder.value,
            Resources.Tag.value,
        )
    )

    read_stock_report = (
        resource == Resources.StockReport.value and action == PermissionAction.VIEW
    )

    read_report_or_stock_report = (
        resource in (Resources.StockReport.value, Resources.Report.value)
        and action == PermissionAction.VIEW
    )

    read_classic_report = resource in (
        Resources.PaymentReports.value,
        Resources.FinancialReports.value,
        Resources.ProductionReports.value,
        Resources.DailyActivityReports.value,
        Resources.PoliceReport.value,
    )

    # Initialize variables
    report_builder_enabled = False
    user_has_acl = False

    match g.user.token_type:
        case TokenTypes.PARTNER.value | TokenTypes.INTERNAL.value:
            # Token is minted for a partner and has insights scopes can read stock reports
            if read_report_or_stock_report:
                g.user.enabled_datasets = UserService.get_insights_acls(
                    g.user, property_id
                )
                if len(g.user.enabled_datasets) > 0:
                    return True
        case _:
            # Permission service says user has access to action/resource
            if PermissionService.is_whitelisted(g.user.email):
                g.user.enabled_datasets = DatasetEnum.values()
                return True

            # Check if report builder is enabled for property and
            # MFD roles data insights checkbox is checked
            report_builder_enabled = PropertyFeatureService.has_feature_flag(
                BillingPortalFeatures.ReportBuilder.value, property_id
            )
            user_has_acl = UserService.has_acl(
                g.user, property_id, MFD_ACCESS_REPORT_BUILDER_ACL
            )
            property_datasets = {
                dataset["id"]
                for dataset in DatasetService.get_datasets_by_property_id(
                    property_id, g.user.email
                )
            }

            # If user has report_builder has access to everything
            if (
                report_builder_enabled
                and user_has_acl
                and (paid_features or free_features)
            ):
                g.user.enabled_datasets = list(property_datasets)
                return True

            # Check if user is trying to read classic reports
            if (
                read_classic_report
                and has_user_multiproperty_feature(property_id)
                and UserService.has_acl(g.user, property_id, resource)
            ):
                return True

            # Report builder is not enabled for property and user has access to at least
            # one datasets' stock reports then allow free features and read stock report
            acl_datasets = set(UserService.get_insights_acls(g.user, property_id))
            g.user.enabled_datasets = list(acl_datasets.intersection(property_datasets))
            if (
                len(g.user.enabled_datasets) > 0
                and has_user_multiproperty_feature(property_id)
                and (free_features or read_stock_report)
            ):
                return True

            # When permission Service is enabled we need to check user is author on at least one resource
            if (
                report_builder_enabled
                and len(g.user.enabled_datasets) > 0
                and has_user_multiproperty_feature(property_id)
                and PermissionService.is_author(property_id, g.user, Resource.REPORTS)
            ):
                return True

    # User does not have access to action/resource
    raise InvalidUsage.forbidden(
        message=f"User does not have permission to {action.name} {resource}",
        acls=g.user.enabled_datasets,
        access_token=g.access_token,
        billing_portal_flag=report_builder_enabled,
        resource=resource,
        action=action.value,
        di_access=user_has_acl,
        is_whitelisted=PermissionService.is_whitelisted(g.user.email),
    )
