from copy import deepcopy
from functools import wraps

from flask import current_app as app, g, request

from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.common.openapi import FULL_SECURITY, X_API_KEY
from app.decorators.authorization import has_insights_access
from app.services.api_key_service import APIKeyService
from app.services.okta_service import OktaService


def api_key_or_authorization(func):
    @wraps(func)
    def wrapper(*f_args, **f_kwargs):
        api_key = request.headers.get(X_API_KEY)

        if api_key:
            logger.debug("User is logging with API Key")
            if not APIKeyService.is_api_key_valid(api_key):
                raise InvalidUsage.not_authorized("API Key is not valid")
        elif g.access_token and g.property_id:
            has_access = has_insights_access(g.user, g.property_id)

            if not has_access or not OktaService.is_valid_access_token(g.access_token):
                raise InvalidUsage.not_authorized("Access token is not valid")
        else:
            raise InvalidUsage.bad_request(
                "API Key or Authorization header are missing"
            )

        return func(*f_args, **f_kwargs)

    wrapper._apidoc = deepcopy(getattr(wrapper, "_apidoc", {}))
    wrapper._apidoc.setdefault("manual_doc", {})
    wrapper._apidoc["manual_doc"]["security"] = FULL_SECURITY
    return wrapper


__all__ = (app,)
