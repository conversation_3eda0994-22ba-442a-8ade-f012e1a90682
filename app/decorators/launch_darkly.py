from functools import wraps

from flask import g

from app.common.exceptions import InvalidUsage
from app.services.launch_darkly_service import LaunchDarklyService


def check_feature_flag(feature: str):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check if user has access to feature
            if LaunchDarklyService.has_feature_flag(feature, g.property_id):
                return func(*args, **kwargs)

            # User does not have access to action/resource
            raise InvalidUsage.forbidden(
                message=f"User does not have this feature enabled: {feature}",
                user_email=g.user.email,
                property_id=g.property_id,
            )

        return wrapper

    return decorator
