from app.common.constants.inventory_object import INVENTORY_OBJECT_TYPES
from app.common.constants.reservation_status import RESERVATION_STATUSES
from app.common.constants.room_type_category import ROOM_TYPE_CATEGORIES
from app.datasets.accounting import AccountingView
from app.datasets.financial import FinancialView
from app.datasets.guests import GuestsView
from app.datasets.housekeeping import HousekeepingView
from app.datasets.invoices import InvoicesView
from app.datasets.occupancy_v1 import OccupancyV1FFView
from app.datasets.payment import PaymentView
from app.datasets.payout import PayoutView
from app.datasets.reservations import ReservationsView


static_picklist_options = dict(
    # Accounting Picklists
    state=AccountingView.state.info["options"],
    source_kind=AccountingView.source_kind.info["options"],
    external_relation_kind=AccountingView.external_relation_kind.info["options"],
    # Financial Picklists
    transaction_status=FinancialView.transaction_status.info["options"],
    item_type=FinancialView.item_type.info["options"],
    # Guests Picklists
    guest_residence_country=GuestsView.guest_residence_country.info["options"],
    guest_document_issuing_country=GuestsView.guest_document_issuing_country.info[
        "options"
    ],
    # Housekeeping Picklists
    room_status=HousekeepingView.room_status.info["options"],
    frontdesk_status=HousekeepingView.frontdesk_status.info["options"],
    room_condition=HousekeepingView.room_condition.info["options"],
    # Invoices Picklists
    invoice_status=InvoicesView.invoice_status.info["options"],
    # Occupancy V1 Picklists
    accommodation_kind=OccupancyV1FFView.accommodation_kind.info["options"],
    # Payment Picklists
    cloudbeds_payment_flag=PaymentView.cloudbeds_payment_flag.info["options"],
    payment_entry_type=PaymentView.payment_entry_type.info["options"],
    # Payout Picklists
    payout_status=PayoutView.payout_status.info["options"],
    transaction_type=PayoutView.transaction_type.info["options"],
    # Reservation Picklists
    is_meal_plan_included=ReservationsView.is_meal_plan_included.info["options"],
    primary_guest_document_issuing_country=ReservationsView.primary_guest_document_issuing_country.info[
        "options"
    ],
    primary_guest_residence_country=ReservationsView.primary_guest_residence_country.info[
        "options"
    ],
    # CDF Picklists that span multiple datasets
    reservation_status=RESERVATION_STATUSES,
    room_type_category=ROOM_TYPE_CATEGORIES,
    inventory_object_type=INVENTORY_OBJECT_TYPES,
)
