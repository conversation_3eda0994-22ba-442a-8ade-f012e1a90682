[{"id": 1, "title": "Charges Trends by Transaction Type", "description": "Pivot table with the sum of the debits by the transaction type vs transaction month.", "dataset_id": 1, "columns": [{"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "transaction_type", "type": "default"}}], "group_columns": [{"cdf": {"column": "transaction_datetime", "type": "default"}, "modifier": "month"}], "filters": {"and": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_year"}, {"cdf": {"column": "debit_amount", "type": "default"}, "operator": "is_not_null", "value": null}]}, "settings": {"details": false, "totals": false}}, {"id": 2, "title": "Voids and Refunds", "description": "Grouping Row by Void Flag and Refund Flag with columns that provide transaction details.", "dataset_id": 1, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "user", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "primary_guest_first_name", "type": "default"}}, {"cdf": {"column": "is_transaction_adjusted", "type": "default"}}, {"cdf": {"column": "transaction_type", "type": "default"}}, {"cdf": {"column": "room_revenue_type", "type": "default"}}, {"cdf": {"column": "fee_type", "type": "default"}}, {"cdf": {"column": "tax_type", "type": "default"}}, {"cdf": {"column": "addon_item", "type": "default"}}, {"cdf": {"column": "item_service_type", "type": "default"}}, {"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "credit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "transaction_notes", "type": "default"}}], "group_rows": [{"cdf": {"column": "is_void", "type": "default"}}, {"cdf": {"column": "is_refund", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "transaction_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_month"}, {"or": [{"cdf": {"column": "is_void", "type": "default"}, "operator": "equals", "value": "Yes"}, {"cdf": {"column": "is_refund", "type": "default"}, "operator": "equals", "value": "Yes"}]}]}, "settings": {"details": true, "totals": true}}, {"id": 3, "title": "Total Revenue by Month by Room Type", "description": "Pivot table with sum of debits (Rev) by property name and room type vs transaction year.", "dataset_id": 1, "columns": [{"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "room_type", "type": "default"}}], "group_columns": [{"cdf": {"column": "transaction_datetime", "type": "default"}, "modifier": "year"}], "filters": {"and": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_year"}]}, "settings": {"details": false, "totals": true}}, {"id": 4, "title": "Charges by Guest, and Debit Type ", "description": "Pivot table with sum of debits (Rev) by reservation status and primary guest vs transaction type, recent and current customers.", "dataset_id": 1, "columns": [{"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}], "group_columns": [{"cdf": {"column": "transaction_type", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "tomorrow"}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"cdf": {"column": "debit_amount", "type": "default"}, "operator": "not_equals", "value": "0"}]}, "settings": {"details": false, "totals": false}}, {"id": 5, "title": "Transaction Report with Details Broken Out", "description": "A high detailed transaction report with 30 fields. This can be saved as a local reports and edited to include exactly what you want.", "dataset_id": 1, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "user", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_number", "type": "default"}}, {"cdf": {"column": "room_type", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "card_type", "type": "default"}}, {"cdf": {"column": "card_last_4_digits", "type": "default"}}, {"cdf": {"column": "transaction_type", "type": "default"}}, {"cdf": {"column": "is_void", "type": "default"}}, {"cdf": {"column": "tax_type", "type": "default"}}, {"cdf": {"column": "room_revenue_type", "type": "default"}}, {"cdf": {"column": "is_refund", "type": "default"}}, {"cdf": {"column": "pos_charge_description", "type": "default"}}, {"cdf": {"column": "pos_charge_category", "type": "default"}}, {"cdf": {"column": "item_service_type", "type": "default"}}, {"cdf": {"column": "item_service_category", "type": "default"}}, {"cdf": {"column": "fee_type", "type": "default"}}, {"cdf": {"column": "is_transaction_adjusted", "type": "default"}}, {"cdf": {"column": "addon_charge_type", "type": "default"}}, {"cdf": {"column": "addon_item", "type": "default"}}, {"cdf": {"column": "quantity_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "credit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "balance_due_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "transaction_notes", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}]}, "settings": {"totals": false, "details": true}}, {"id": 6, "title": "Expanded Reservation Report with Financials", "description": "A detailed reservation report with 30 fields.  This can be saved as a local reports and edited to include exactly what you want.", "dataset_id": 3, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "is_repeat_guest", "type": "default"}}, {"cdf": {"column": "primary_guest_status_level", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "booking_datetime", "type": "default"}}, {"cdf": {"column": "reservation_source_category", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "adults_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "children_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "taxes_value_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "active_booking_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}], "filters": {"or": [{"cdf": {"column": "booking_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "tomorrow"}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "today"}]}]}, "settings": {"totals": false, "details": true}}, {"id": 7, "title": "Reservation Report by Group Profile", "description": "A reservation with the key common fields including group profile information.", "dataset_id": 3, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "group_profile_code", "type": "default"}}, {"cdf": {"column": "group_profile_type", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "private_rate_plan", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}], "filters": {"or": [{"cdf": {"column": "booking_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "today"}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "today"}]}]}, "settings": {"totals": false, "details": true}}, {"id": 8, "title": "Reservation Report with Group Profile and Group Block information", "description": "A reservation with the key common fields including group profile and group block information.", "dataset_id": 3, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "group_profile_code", "type": "default"}}, {"cdf": {"column": "group_profile_type", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}], "filters": {"or": [{"cdf": {"column": "booking_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "tomorrow"}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "today"}]}]}, "settings": {"totals": false, "details": true}}, {"id": 9, "title": "Simple Reservation Report with Financials", "description": "A reservation report with key guest, reservation, source, rate and note information.", "dataset_id": 3, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "primary_guest_status_level", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}], "filters": {"or": [{"cdf": {"column": "booking_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "tomorrow"}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "today"}]}]}, "settings": {"totals": false, "details": true}}, {"id": 10, "title": "Room Nights and Room Rev by Gender and Country", "description": "Insight on the type of guests: a pivot table with sum of room nights and Total Price by guest country code and guest gender since the start of 2019.", "dataset_id": 3, "columns": [{"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "primary_guest_residence_country_code", "type": "default"}}], "group_columns": [{"cdf": {"column": "primary_guest_gender", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "booking_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "2019-01-01T19:08:10.000Z"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "not_list_contains", "value": ["Cancelled"]}]}, "settings": {"totals": false, "details": false}}, {"id": 11, "title": "Rate Plan Production", "description": "A summary table containing the amount of Room Revenue generated per Rate Plan per Month. Can be sliced per day if needed. Does not contain ADR or Room Nights.", "dataset_id": 1, "columns": [{"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_number", "type": "default"}}], "group_rows": [{"cdf": {"column": "private_rate_plan", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}], "group_columns": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "modifier": "month"}], "filters": {"and": [{"cdf": {"column": "transaction_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_year"}, {"or": [{"cdf": {"column": "transaction_type", "type": "default"}, "operator": "equals", "value": "Room Rate"}, {"cdf": {"column": "transaction_type", "type": "default"}, "operator": "equals", "value": "Room Revenue"}]}]}, "settings": {"totals": false, "details": false}}, {"id": 12, "title": "Simplified <PERSON><PERSON><PERSON><PERSON> - Next Year", "description": "Summary table containing total payments made in 2021 for reservations with check-in in 2022.  Sliced per check-in month. Does not include Payment Type [credit card, cash].", "dataset_id": 1, "columns": [{"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "credit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "transaction_notes", "type": "default"}}, {"cdf": {"column": "transaction_datetime", "type": "default"}}], "group_rows": [{"cdf": {"column": "checkin_date", "type": "default"}, "modifier": "month"}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_next_year"}, {"cdf": {"column": "transaction_type", "type": "default"}, "operator": "equals", "value": "Payment"}]}, "settings": {"totals": false, "details": false}}, {"id": 13, "title": "Total Debits and Credits by Month", "description": "A simple report for long term trends.", "dataset_id": 1, "columns": [{"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "credit_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "modifier": "month"}], "settings": {"totals": false, "details": false}}, {"id": 14, "title": "Tax and Fee Summary - Yesterday", "description": "Tax Report for yesterdays transactions, summarized by Tax or Fee type.", "dataset_id": 1, "columns": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "room_type", "type": "default"}}, {"cdf": {"column": "room_number", "type": "default"}}, {"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "transaction_type", "type": "default"}}, {"cdf": {"column": "tax_type", "type": "default"}}, {"cdf": {"column": "fee_type", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "operator": "less_than", "value": "today"}, {"or": [{"cdf": {"column": "transaction_type", "type": "default"}, "operator": "equals", "value": "Fee"}, {"cdf": {"column": "transaction_type", "type": "default"}, "operator": "equals", "value": "Tax"}]}]}, "settings": {"details": false, "totals": false}}, {"id": 15, "title": "Revenue by Type", "description": "Summary of yesterday's transactions.", "dataset_id": 1, "columns": [{"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "transaction_type", "type": "default"}}, {"cdf": {"column": "room_revenue_type", "type": "default"}}, {"cdf": {"column": "item_service_category", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "transaction_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"cdf": {"column": "debit_amount", "type": "default"}, "operator": "greater_than_or_equal", "value": "0"}]}, "settings": {"details": false, "totals": false}}, {"id": 16, "title": "Arrivals Report - Today and Tomorrow", "description": "Key information for guest checking in: Estimated Arrival, Room Types, Balance Due, Notes, Guest Totals, Etc.", "dataset_id": 3, "columns": [{"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "adults_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "children_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "estimated_arrival_time", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "room_numbers", "type": "default"}}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "active_booking_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}], "group_rows": [{"cdf": {"column": "checkout_date", "type": "default"}}], "filters": {"or": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "equals", "value": "today"}, {"cdf": {"column": "checkin_date", "type": "default"}, "operator": "equals", "value": "tomorrow"}]}, "settings": {"totals": false, "details": true}}, {"id": 17, "title": "Reservations Report Grouped by Booking Date", "description": "Reservations report Grouped by Booking Date. Includes basic Reservation parameters.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "reservation_source_category", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}, "modifier": "day"}], "filters": {"and": [{"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_month"}]}, "settings": {"totals": false, "details": true}}, {"id": 18, "title": "Reservations with Notes - Recently Booked, Checking-in, or In-House", "description": "Reservations report Grouped by Booking Date since start of last week. Includes basic Reservation parameters.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "primary_guest_status_level", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}, {"cdf": {"column": "active_booking_notes", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "booking_datetime", "type": "default"}}, {"cdf": {"column": "adults_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "cancellation_datetime_property_timezone", "type": "default"}}, {"cdf": {"column": "cancellation_datetime", "type": "default"}}, {"cdf": {"column": "children_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "primary_guest_document_issue_date", "type": "default"}}, {"cdf": {"column": "primary_guest_document_expiration_date", "type": "default"}}, {"cdf": {"column": "primary_guest_birth_date", "type": "default"}}, {"cdf": {"column": "primary_guest_city", "type": "default"}}, {"cdf": {"column": "primary_guest_address_line_2", "type": "default"}}, {"cdf": {"column": "primary_guest_address", "type": "default"}}, {"cdf": {"column": "is_opt_in_marketing_emails", "type": "default"}}, {"cdf": {"column": "primary_guest_first_name", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "primary_guest_document_type", "type": "default"}}, {"cdf": {"column": "primary_guest_document_number", "type": "default"}}, {"cdf": {"column": "primary_guest_document_issuing_country_code", "type": "default"}}, {"cdf": {"column": "primary_guest_document_issuing_country", "type": "default"}}, {"cdf": {"column": "property_id", "type": "default"}}, {"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "is_repeat_guest", "type": "default"}}, {"cdf": {"column": "primary_guest_surname", "type": "default"}}, {"cdf": {"column": "primary_guest_state", "type": "default"}}, {"cdf": {"column": "primary_guest_residence_country", "type": "default"}}, {"cdf": {"column": "primary_guest_residence_country_code", "type": "default"}}, {"cdf": {"column": "primary_guest_postal_code", "type": "default"}}, {"cdf": {"column": "primary_guest_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_gender", "type": "default"}}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "estimated_arrival_time", "type": "default"}}, {"cdf": {"column": "group_allotment_code", "type": "default"}}, {"cdf": {"column": "group_allotment_name", "type": "default"}}, {"cdf": {"column": "group_profile_code", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "group_profile_type", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "private_rate_plan", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "reservation_source_category", "type": "default"}}, {"cdf": {"column": "third_party_confirmation_number", "type": "default"}}, {"cdf": {"column": "room_reservation_number", "type": "default"}}, {"cdf": {"column": "room_numbers", "type": "default"}}, {"cdf": {"column": "card_last_4_digits", "type": "default"}}, {"cdf": {"column": "card_type", "type": "default"}}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "taxes_value_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}, "modifier": "day"}], "filters": {"and": [{"or": [{"cdf": {"column": "active_primary_guest_notes", "type": "default"}, "operator": "is_not_null", "value": ""}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}, "operator": "is_not_null", "value": ""}, {"cdf": {"column": "active_booking_notes", "type": "default"}, "operator": "is_not_null", "value": ""}, {"cdf": {"column": "primary_guest_status_level", "type": "default"}, "operator": "is_not_null", "value": ""}]}, {"or": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "equals", "value": "today"}, {"cdf": {"column": "checkin_date", "type": "default"}, "operator": "equals", "value": "tomorrow"}, {"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_week"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["In-House"]}]}]}, "settings": {"totals": false, "details": true}}, {"id": 20, "title": "Checking-Out Today", "description": "List for check out, collecting balances and cleaning.", "dataset_id": 3, "columns": [{"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "room_numbers", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "primary_guest_status_level", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}], "filters": {"and": [{"cdf": {"column": "checkout_date", "type": "default"}, "operator": "equals", "value": "today"}]}, "settings": {"totals": false, "details": true}}, {"id": 21, "title": "Arrivals Today", "description": "Check in list.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "room_numbers", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "is_repeat_guest", "type": "default"}}, {"cdf": {"column": "primary_guest_status_level", "type": "default"}}, {"cdf": {"column": "primary_guest_gender", "type": "default"}}, {"cdf": {"column": "primary_guest_birth_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "equals", "value": "today"}]}, "settings": {"totals": false, "details": true}}, {"id": 22, "title": "Room Revenue by Source and Rate Plan", "description": "YTD Revenue by Source and Rate Plan for Revenue Management and Marketing Analysis.", "dataset_id": 3, "columns": [{"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "private_rate_plan", "type": "default"}}], "group_columns": [{"cdf": {"column": "reservation_source", "type": "default"}}], "filters": {"or": [{"cdf": {"column": "booking_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "today"}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "today"}]}]}, "settings": {"totals": false, "details": false}}, {"id": 23, "title": "Room Revenue by Rate Plan and Room Type", "description": "YTD Revenue by Rate Plan and Room Type for Revenue Management Analysis.", "dataset_id": 3, "columns": [{"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "room_types", "type": "default"}}], "group_columns": [{"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "private_rate_plan", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_year"}]}, "settings": {"totals": false, "details": false}}, {"id": 24, "title": "Cancellation Report", "description": "Cancellation Report for Yesterday.", "dataset_id": 3, "columns": [{"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "cancellation_datetime_property_timezone", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "primary_guest_city", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_residence_country", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "cancellation_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["Cancelled"]}, {"cdf": {"column": "cancellation_datetime_property_timezone", "type": "default"}, "operator": "less_than_or_equal", "value": "tomorrow"}]}, "settings": {"totals": false, "details": false}}, {"id": 25, "title": "In-House Group Reservations", "description": "List of in-house reservations that belongs to groups.", "dataset_id": 3, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "group_profile_type", "type": "default"}}, {"cdf": {"column": "private_rate_plan", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}], "group_rows": [{"cdf": {"column": "group_profile_code", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "group_profile_name", "type": "default"}, "operator": "is_not_null", "value": ""}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["In-House"]}]}, "settings": {"totals": false, "details": false}}, {"id": 26, "title": "Deposit Report vs Reservation Total", "description": "Showing Deposit Received vs the Total Charges per Reservation, grouped by booking date.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "active_booking_notes", "type": "default"}}, {"cdf": {"column": "card_type", "type": "default"}}, {"cdf": {"column": "card_last_4_digits", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}], "group_rows": [{"cdf": {"column": "reservation_status", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}, "operator": "less_than", "value": "today"}, {"cdf": {"column": "deposit_amount", "type": "default"}, "operator": "greater_than", "value": "0"}]}, "settings": {"totals": false, "details": true}}, {"id": 27, "title": "Commission Report", "description": "Commission by source and group with details available.", "dataset_id": 3, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "room_reservation_number", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "commission_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "today"}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "less_than", "value": "start_next_week"}, {"cdf": {"column": "commission_amount", "type": "default"}, "operator": "greater_than", "value": "0"}]}, "settings": {"totals": false, "details": true}}, {"id": 28, "title": "No Show Report", "description": "No Show Report for Yesterday.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_city", "type": "default"}}, {"cdf": {"column": "primary_guest_residence_country", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}], "group_rows": [], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["No Show"]}]}, "settings": {"totals": false, "details": true}}, {"id": 30, "title": "Guest and Room List", "description": "List Reservation Statuses, Room Types, Rooms and Guests.", "dataset_id": 3, "columns": [{"cdf": {"column": "room_numbers", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "today"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["Confirmation Pending", "Confirmed", "In-House"]}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "tomorrow"}]}, "settings": {"totals": false, "details": true}}, {"id": 31, "title": "Guest Notes", "description": "Notes for tonight's guests.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "active_booking_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}, {"cdf": {"column": "is_repeat_guest", "type": "default"}}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "room_numbers", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_residence_country", "type": "default"}}], "group_rows": [{"cdf": {"column": "primary_guest_status_level", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "today"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["Confirmation Pending", "Confirmed", "In-House"]}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "tomorrow"}, {"or": [{"cdf": {"column": "active_group_profile_notes", "type": "default"}, "operator": "is_not_null", "value": null}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}, "operator": "is_not_null", "value": null}, {"cdf": {"column": "active_booking_notes", "type": "default"}, "operator": "is_not_null", "value": null}]}]}, "settings": {"totals": false, "details": true}}, {"id": 32, "title": "Channel Performance Summary", "description": "YTD channel room nights, rooms booked, room revenue, total revenue and commissions.", "dataset_id": 3, "columns": [{"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "commission_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "reservation_source_category", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "today"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["Confirmation Pending", "Confirmed", "In-House", "Checked Out"]}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_year"}]}, "settings": {"totals": false, "details": false}}, {"id": 33, "title": "Reservations by Rate Plan", "description": "Summary and Detailed information by Rate Plan, YTD.", "dataset_id": 3, "columns": [{"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "primary_guest_surname", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "commission_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "private_rate_plan", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "today"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["Confirmation Pending", "Confirmed", "In-House", "Checked Out"]}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_year"}]}, "settings": {"totals": true, "details": false}}, {"id": 34, "title": "Production by <PERSON>", "description": "With details by city, source and group.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_city", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "commission_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "primary_guest_residence_country", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "less_than_or_equal", "value": "today"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["Confirmation Pending", "Confirmed", "In-House", "Checked Out"]}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_year"}]}, "settings": {"totals": false, "details": false}}, {"id": 35, "title": "Payments and Balances Due", "description": "Simple List of Totals Bills, Amount Paid and the Balances Due by Guest.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [], "filters": {"and": [{"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "operator": "greater_than_or_equal", "value": "0"}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["No Show", "In-House", "Checked Out"]}, {"cdf": {"column": "checkout_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_month"}]}, "settings": {"totals": false, "details": true}}, {"id": 36, "title": "Rooming List for Selected Group", "description": "View the complete room list of all groups or replace the * (wild card) with a specific group's name.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "group_allotment_code", "type": "default"}}, {"cdf": {"column": "group_allotment_name", "type": "default"}}, {"cdf": {"column": "group_profile_code", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "group_profile_type", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "deposit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_paid_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}], "group_rows": [], "filters": {"and": [{"cdf": {"column": "group_profile_name", "type": "default"}, "operator": "equals", "value": ""}, {"or": [{"cdf": {"column": "group_profile_name", "type": "default"}, "operator": "is_not_empty", "value": ""}, {"cdf": {"column": "group_profile_name", "type": "default"}, "operator": "is_not_null", "value": ""}]}, {"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["Cancelled", "No Show", "Confirmation Pending", "Confirmed", "Checked Out", "In-House"]}]}, "settings": {"totals": false, "details": false}}, {"id": 37, "title": "Guest In House - Credit Limit", "description": "List of in-house reservations that have the current balance due higher than X amount of money. Used for daily verification if a guest has provided enough guarantee to cover their current expenses.", "dataset_id": 3, "columns": [{"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "primary_guest_first_name", "type": "default"}}, {"cdf": {"column": "primary_guest_surname", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "room_numbers", "type": "default"}}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [], "filters": {"or": [{"cdf": {"column": "reservation_status", "type": "default"}, "operator": "list_contains", "value": ["In-House"]}, {"cdf": {"column": "reservation_balance_due_amount", "type": "default"}, "operator": "greater_than_or_equal", "value": "50"}]}, "settings": {"totals": false, "details": false}}, {"id": 38, "title": "Expanded Transaction Report with Details Broken Out", "description": "A high detailed transaction report with 30+ fields. This can be saved as a local reports and edited to include exactly what you want.", "dataset_id": 1, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "user", "type": "default"}}, {"cdf": {"column": "transaction_datetime", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}}, {"cdf": {"column": "room_number", "type": "default"}}, {"cdf": {"column": "room_type", "type": "default"}}, {"cdf": {"column": "is_hotel_collect_booking", "type": "default"}}, {"cdf": {"column": "card_type", "type": "default"}}, {"cdf": {"column": "card_last_4_digits", "type": "default"}}, {"cdf": {"column": "transaction_type", "type": "default"}}, {"cdf": {"column": "is_void", "type": "default"}}, {"cdf": {"column": "tax_type", "type": "default"}}, {"cdf": {"column": "room_revenue_type", "type": "default"}}, {"cdf": {"column": "is_refund", "type": "default"}}, {"cdf": {"column": "pos_charge_description", "type": "default"}}, {"cdf": {"column": "pos_charge_category", "type": "default"}}, {"cdf": {"column": "item_service_type", "type": "default"}}, {"cdf": {"column": "item_service_category", "type": "default"}}, {"cdf": {"column": "fee_type", "type": "default"}}, {"cdf": {"column": "is_transaction_adjusted", "type": "default"}}, {"cdf": {"column": "addon_charge_type", "type": "default"}}, {"cdf": {"column": "addon_item", "type": "default"}}, {"cdf": {"column": "quantity_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "credit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "balance_due_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "transaction_notes", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "transaction_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "yesterday"}]}, "settings": {"totals": false, "details": true}}, {"id": 39, "title": "Items and Services Sold Pivot Table - YTD by Month", "description": "A pivot table: Rows have Item and Service Categories and Names; Values are the total charges per Items and Service, Columns have the Transaction Month.", "dataset_id": 1, "columns": [{"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "item_service_category", "type": "default"}}, {"cdf": {"column": "item_service_type", "type": "default"}}], "group_columns": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "modifier": "month"}], "filters": {"and": [{"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_current_year"}]}, "settings": {"totals": false, "details": false}}, {"id": 40, "title": "Guests Marketing Email Opt-in Report", "description": "List of guest names and their email addresses. Only contains names of guests who opted IN for marketing emails.", "dataset_id": 2, "columns": [{"cdf": {"column": "guest_first_name", "type": "default"}}, {"cdf": {"column": "guest_surname", "type": "default"}}, {"cdf": {"column": "guest_full_name", "type": "default"}}, {"cdf": {"column": "guest_email", "type": "default"}}, {"cdf": {"column": "guest_gender", "type": "default"}}, {"cdf": {"column": "guest_residence_country", "type": "default"}}, {"cdf": {"column": "guest_status_level", "type": "default"}}, {"cdf": {"column": "is_repeat_guest", "type": "default"}}], "group_rows": [], "group_columns": [], "filters": {"and": [{"cdf": {"column": "is_opt_in_marketing_emails", "type": "default"}, "operator": "equals", "value": "Yes"}]}, "settings": {"totals": false, "details": false}}, {"id": 41, "title": "Historic Guest Stays for Last 3 Years", "description": "Stays by property for last 3 years, includes all stays even if they were not the primary guest.", "dataset_id": 2, "columns": [{"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}}, {"cdf": {"column": "guest_gender", "type": "default"}}, {"cdf": {"column": "guest_city", "type": "default"}}, {"cdf": {"column": "guest_residence_country", "type": "default"}}, {"cdf": {"column": "is_repeat_guest", "type": "default"}}, {"cdf": {"column": "is_primary_guest", "type": "default"}}, {"cdf": {"column": "is_opt_in_marketing_emails", "type": "default"}}, {"cdf": {"column": "guest_phone_number", "type": "default"}}, {"cdf": {"column": "guest_email", "type": "default"}}], "group_rows": [{"cdf": {"column": "guest_status_level", "type": "default"}}, {"cdf": {"column": "guest_full_name", "type": "default"}}], "settings": {"totals": false, "details": true}}, {"id": 42, "title": "Occupancy Statistics by Week", "description": "Occupancy summary by week for properties without shared inventory.", "dataset_id": 4, "columns": [{"cdf": {"column": "room_available_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "booking_qty_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "out_of_service_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "capacity_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "occupancy", "type": "default"}}, {"cdf": {"column": "adr", "type": "default"}}, {"cdf": {"column": "revpar", "type": "default"}}], "group_rows": [{"cdf": {"column": "stay_date", "type": "default"}, "modifier": "month"}], "filters": {"and": [{"cdf": {"column": "stay_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_month"}, {"cdf": {"column": "stay_date", "type": "default"}, "operator": "less_than_or_equal", "value": "start_next_year"}]}, "settings": {"totals": false, "details": false}}, {"id": 43, "title": "Last Month's Occupancy by Room Type and Room", "description": "Occupancy summary from last month grouped by room type and room.", "dataset_id": 4, "columns": [{"cdf": {"column": "occupancy", "type": "default"}}, {"cdf": {"column": "capacity_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "out_of_service_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_available_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "booking_qty_type_a", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "adr", "type": "default"}}, {"cdf": {"column": "revpar", "type": "default"}}], "group_rows": [{"cdf": {"column": "room_type_abbreviation", "type": "default"}}, {"cdf": {"column": "room_number", "type": "default"}}], "filters": {"and": [{"cdf": {"column": "stay_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_month"}, {"cdf": {"column": "stay_date", "type": "default"}, "operator": "less_than", "value": "start_current_month"}]}, "settings": {"totals": false, "details": false}}, {"id": 44, "title": "Out of Service Rooms", "description": "Simple lists of out of service rooms per day.", "dataset_id": 4, "columns": [{"cdf": {"column": "out_of_service_type_a", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "room_type_abbreviation", "type": "default"}}, {"cdf": {"column": "room_number", "type": "default"}}], "group_columns": [{"cdf": {"column": "stay_date", "type": "default"}, "modifier": "day"}], "filters": {"and": [{"cdf": {"column": "stay_date", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_month"}, {"cdf": {"column": "stay_date", "type": "default"}, "operator": "less_than_or_equal", "value": "start_next_month"}]}, "settings": {"totals": false, "details": false}}, {"id": 48, "title": "Adjustments Report", "description": "Adjustment Report - Current and Prior Calendar month.", "dataset_id": 1, "columns": [{"cdf": {"column": "user", "type": "default"}}, {"cdf": {"column": "transaction_datetime_property_timezone", "type": "default"}}, {"cdf": {"column": "room_number", "type": "default"}}, {"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "primary_guest_first_name", "type": "default"}}, {"cdf": {"column": "primary_guest_surname", "type": "default"}}, {"cdf": {"column": "transaction_type", "type": "default"}}, {"cdf": {"column": "is_transaction_adjusted", "type": "default"}}, {"cdf": {"column": "is_void", "type": "default"}}, {"cdf": {"column": "is_refund", "type": "default"}}, {"cdf": {"column": "quantity_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "debit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "credit_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "transaction_notes", "type": "default"}}, {"cdf": {"column": "payment_method", "type": "default"}}, {"cdf": {"column": "card_type", "type": "default"}}, {"cdf": {"column": "card_last_4_digits", "type": "default"}}, {"cdf": {"column": "transaction_code", "type": "default"}}, {"cdf": {"column": "tax_type", "type": "default"}}, {"cdf": {"column": "fee_type", "type": "default"}}, {"cdf": {"column": "room_revenue_type", "type": "default"}}, {"cdf": {"column": "item_service_category", "type": "default"}}, {"cdf": {"column": "item_service_type", "type": "default"}}, {"cdf": {"column": "pos_charge_category", "type": "default"}}, {"cdf": {"column": "pos_charge_description", "type": "default"}}], "group_rows": [], "filters": {"and": [{"cdf": {"column": "transaction_datetime", "type": "default"}, "operator": "greater_than_or_equal", "value": "start_last_month"}, {"cdf": {"column": "is_transaction_adjusted", "type": "default"}, "operator": "equals", "value": "Yes"}]}, "settings": {"details": true, "totals": false}}, {"id": 49, "title": "Primary Guest History Report", "description": "Stock Report providing a summary of guest history for the last 3 yrs, including reservations counts, Total Spend, Room Nights and Room Counts. Grouped by Primary Guest Full Name & Guest Status Level.", "dataset_id": 3, "columns": [{"cdf": {"column": "primary_guest_id", "type": "default"}}, {"cdf": {"column": "is_repeat_guest", "type": "default"}}, {"cdf": {"column": "property_name", "type": "default"}}, {"cdf": {"column": "room_reservation_number", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "guest_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["count"]}, {"cdf": {"column": "grand_total_amount", "type": "default"}, "metrics": ["sum"]}], "group_rows": [{"cdf": {"column": "primary_guest_status_level", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}], "filters": {}, "settings": {"details": true, "totals": false}}, {"id": 50, "title": "Export for ALICE Software", "description": "Export for sending to the Alice Hotel Operations Application.", "dataset_id": 3, "columns": [{"cdf": {"column": "reservation_number", "type": "default"}}, {"cdf": {"column": "reservation_status", "type": "default"}}, {"cdf": {"column": "primary_guest_full_name", "type": "default"}}, {"cdf": {"column": "estimated_arrival_time", "type": "default"}}, {"cdf": {"column": "checkin_date", "type": "default"}}, {"cdf": {"column": "checkout_date", "type": "default"}}, {"cdf": {"column": "adults_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "children_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "reservation_source", "type": "default"}}, {"cdf": {"column": "public_rate_plan", "type": "default"}}, {"cdf": {"column": "room_types", "type": "default"}}, {"cdf": {"column": "primary_guest_mobile_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_phone_number", "type": "default"}}, {"cdf": {"column": "primary_guest_email", "type": "default"}}, {"cdf": {"column": "primary_guest_status_level", "type": "default"}}, {"cdf": {"column": "group_profile_code", "type": "default"}}, {"cdf": {"column": "group_profile_name", "type": "default"}}, {"cdf": {"column": "group_profile_type", "type": "default"}}, {"cdf": {"column": "booking_datetime_property_timezone", "type": "default"}}, {"cdf": {"column": "room_nights_count", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "room_revenue_total_amount", "type": "default"}, "metrics": ["sum"]}, {"cdf": {"column": "active_group_profile_notes", "type": "default"}}, {"cdf": {"column": "active_primary_guest_notes", "type": "default"}}, {"cdf": {"column": "active_booking_notes", "type": "default"}}], "group_rows": [], "filters": {"or": [{"cdf": {"column": "checkin_date", "type": "default"}, "operator": "equals", "value": "today"}, {"cdf": {"column": "checkin_date", "type": "default"}, "operator": "equals", "value": "tomorrow"}]}, "settings": {"totals": false, "details": false}}]