from types import SimpleNamespace

from sqlalchemy.orm.collections import InstrumentedList

from app.data.static_picklist import static_picklist_options
from app.datasets.dataset import Dataset
from app.datasets.multi_level import MultiLevel
from app.enums import CdfKind, Dataset as DatasetEnum
from app.enums.cdf import Cdf
from app.enums.multi_level import MultiLevel as MultiLevelEnum


class CDF:
    def __init__(
        self,
        dataset: DatasetEnum,
        name: str,
        is_custom_cdf: bool = False,
        is_custom_field_cdf: bool = False,
        multi_level: MultiLevelEnum = None,
        type: str = None,
    ):
        self.dataset = dataset
        self.name = name
        self.is_custom_cdf = is_custom_cdf
        self.is_custom_field_cdf = is_custom_field_cdf
        self.multi_level = multi_level
        self.type = type

        if self.is_custom_field_cdf:
            pass
        elif not self.is_custom_cdf:
            if type != Cdf.CustomField.value:
                if self.multi_level is None and self.cdf is None:
                    raise ValueError(
                        f"Cdf: {name} not found for this dataset: {dataset.name}"
                    )
                if (
                    self.multi_level
                    and self.multi_level.value not in Dataset(self.dataset).multi_levels
                ):
                    raise ValueError(
                        f"MultiLevel: {multi_level.name} not found for this dataset: {dataset.name}"
                    )
                if self.cdf is None:
                    raise ValueError(
                        f"Cdf: {name} not found for this multi-level: {multi_level.name}"
                    )

    @property
    def cdf(self) -> dict:
        cdfs = (
            Dataset(self.dataset, check_feature_flag=False).flatten_cdfs
            if self.multi_level is None
            else MultiLevel(self.multi_level).cdfs
        )
        return next((cdf for cdf in cdfs if cdf["column"] == self.name), None)

    def is_kind(self, kind: CdfKind) -> bool:
        return self.kind == kind

    @property
    def kind(self) -> CdfKind:
        return (
            CdfKind.String
            if self.type == "custom_field"
            else None
            if self.is_custom_cdf
            else self.cdf.get("kind")
        )

    @property
    def category(self) -> str:
        categories = (
            Dataset(self.dataset, check_feature_flag=False).cdfs
            if self.multi_level is None
            else None
        )
        return next(
            (
                category["category"]
                for category in categories
                for cdf in category["cdfs"]
                if cdf["column"] == self.name
            ),
            None,
        )

    @property
    def options(self) -> tuple:
        """Get the available options for the CDF that are PickList kind"""
        options = ()
        if not self.is_custom_cdf and self.cdf.get("kind") in (
            CdfKind.PickList,
            CdfKind.Country,
        ):
            options = static_picklist_options[self.name]
        return options

    def get_custom_cdf_in_custom_cdfs(self, custom_cdfs: list) -> dict:
        """
        Returns the CDF if exist in the list of custom_cdfs, if not None

            Parameters:
                    custom_cdfs (list): List of CDFs that can be InstrumentedList, list of dict{column}, list of dict{cdf}

            Returns:
                    cdf (dict): CDF dict {column: "value", type: cdf_type}} if the CDF exist, if not None
        """
        if custom_cdfs is None:
            return None

        if not isinstance(custom_cdfs, InstrumentedList):
            custom_cdfs = [
                (
                    SimpleNamespace(**custom_cdf)
                    if "cdf" not in custom_cdf
                    else SimpleNamespace(**custom_cdf["cdf"])
                )
                for custom_cdf in custom_cdfs
            ]

        return next(
            (
                custom_cdf
                for custom_cdf in custom_cdfs
                if custom_cdf.column == self.name
            ),
            None,
        )

    def is_dynamic_cdf(self) -> bool:
        """Return True if CDF Kind is Dynamic kind"""
        return self.kind in (
            CdfKind.DynamicCurrency,
            CdfKind.DynamicPercentage,
            CdfKind.Dynamic,
        )

    def is_translate_cdf(self, custom_cdfs=None) -> bool:
        if self.is_custom_field_cdf or (self.is_custom_cdf and custom_cdfs is None):
            return False
        elif self.is_custom_cdf:
            custom_cdf = self.get_custom_cdf_in_custom_cdfs(custom_cdfs)
            formula_cdfs = (
                [item["value"] for item in custom_cdf.formula if item["kind"] == "cdf"]
                if custom_cdf.kind == CdfKind.String.name
                else []
            )
            for item in custom_cdf.formula:
                if item["kind"] == "case":
                    for case in item["cases"]:
                        if case["then"]["kind"] == "cdf":
                            formula_cdfs.append(case["then"]["value"])
                        elif case["then"]["kind"] == "formula":
                            for it in case["then"]["value"]:
                                if it["kind"] == "cdf":
                                    formula_cdfs.append(it["value"])
                    if item["default_case"]["kind"] == "cdf":
                        formula_cdfs.append(item["default_case"]["value"])

            return any(
                (
                    CDF(
                        self.dataset,
                        column,
                        multi_level=None,  # TODO: add multi-level support
                        is_custom_cdf=False,
                        is_custom_field_cdf=False,
                    ).is_translate_cdf()
                    for column in formula_cdfs
                )
            )

        return self.cdf.get("translate", False)

    def get_custom_field_cdf_in_custom_field_cdfs(self, custom_field_cdfs):
        """
        Returns the CDF if exist in the list of custom_field_cdfs, if not None

            Parameters:
                    custom_cdfs (list): List of CDFs that can be InstrumentedList, list of dict{column}, list of dict{cdf}

            Returns:
                    cdf (dict): CDF dict {column: "value", type: cdf_type}} if the CDF exist, if not None
        """
        if custom_field_cdfs is None:
            return None

        if not isinstance(custom_field_cdfs, InstrumentedList):
            custom_field_cdfs = [
                (
                    SimpleNamespace(**custom_field_cdf)
                    if "cdf" not in custom_field_cdf
                    else SimpleNamespace(**custom_field_cdf["cdf"])
                )
                for custom_field_cdf in custom_field_cdfs
            ]

        return next(
            (
                custom_field_cdf
                for custom_field_cdf in custom_field_cdfs
                if custom_field_cdf.column == self.name
            ),
            None,
        )
