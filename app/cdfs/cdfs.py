from app.cdfs.cdf import CDF
from app.enums import Dataset as DatasetEnum
from app.enums.cdf import Cdf
from app.enums.multi_level import MultiLevel as MultiLevelEnum


class CDFs:
    """Class that represents a list of cdfs. It validates that each cdf on the list is valid and belong to the dataset"""

    def __init__(self, dataset: DatasetEnum, cdfs: list):
        self.dataset = dataset
        self.cdfs = cdfs

    @property
    def cdfs(self):
        return self.__cdfs

    @cdfs.setter
    def cdfs(self, cdfs):
        try:
            list(
                map(
                    lambda cdf: CDF(self.dataset, cdf["cdf"]["column"]),
                    self.get_default_cdfs(cdfs),
                )
            )
            list(
                map(
                    lambda cdf: CDF(
                        self.dataset,
                        cdf["cdf"]["column"],
                        False,
                        MultiLevelEnum(cdf["cdf"]["multi_level_id"]),
                    ),
                    self.get_multi_level_cdfs(cdfs),
                )
            )
            self.__cdfs = cdfs
        except ValueError as error:
            raise ValueError(error.args[0])

    @staticmethod
    def get_multi_level_cdfs(cdf_list) -> list[CDF]:
        return [cdf for cdf in cdf_list if "multi_level_id" in cdf["cdf"]]

    @staticmethod
    def get_multi_level_cdfs_by_id(cdf_list, multi_level_id: int) -> list[CDF]:
        return [
            cdf
            for cdf in cdf_list
            if "multi_level_id" in cdf["cdf"]
            and cdf["cdf"]["multi_level_id"] == multi_level_id
        ]

    @staticmethod
    def get_default_cdfs(cdf_list) -> list[CDF]:
        return [
            cdf
            for cdf in cdf_list
            if "multi_level_id" not in cdf["cdf"]
            and cdf["cdf"]["type"] == Cdf.Default.value
        ]

    @staticmethod
    def get_custom_cdfs(cdf_list) -> list[CDF]:
        return [
            cdf
            for cdf in cdf_list
            if "multi_level_id" not in cdf["cdf"]
            and cdf["cdf"]["type"] == Cdf.Custom.value
        ]

    @staticmethod
    def get_custom_field_cdfs(cdf_list) -> list[CDF]:
        return [
            cdf
            for cdf in cdf_list
            if "multi_level_id" not in cdf["cdf"]
            and cdf["cdf"]["type"] == Cdf.CustomField.value
        ]

    @staticmethod
    def has_duplicates(cdf_list: list[CDF]) -> bool:
        """Method that will validates if a CDF list contains duplicates"""
        default_cdfs = [
            f"{cdf['cdf']['column']}:{cdf['cdf']['type']}"
            for cdf in CDFs.get_default_cdfs(cdf_list)
        ]
        multilevel_cdfs = [
            f"{cdf['cdf']['column']}:{cdf['cdf']['multi_level_id']}"
            for cdf in CDFs.get_multi_level_cdfs(cdf_list)
        ]
        custom_cdfs = [
            f"{cdf['cdf']['column']}:{cdf['cdf']['type']}"
            for cdf in CDFs.get_custom_cdfs(cdf_list)
        ]

        if len(multilevel_cdfs + default_cdfs + custom_cdfs) != len(
            list(set(multilevel_cdfs + default_cdfs + custom_cdfs))
        ):
            return True

        return False

    @staticmethod
    def filter_cdfs_by_category(cdf_list: list[CDF], categories: list[str]):
        """Method that will return a list of cdfs excluding categories that are not on the giving list"""
        return [
            category for category in cdf_list if category["category"] not in categories
        ]

    @staticmethod
    def get_cdfs_from_categories(cdf_list: list[CDF], categories: list[str]):
        """Method that will return a list of cdfs that only belongs to the given categories"""
        return [
            cdf
            for category in cdf_list
            if category["category"] in categories
            for cdf in category["cdfs"]
        ]

    @staticmethod
    def get_cdfs_from_filters(filters: dict, cdfs: list = []):
        """Method that will receive the Report Filters and it will return a list of cdfs using recursion"""
        for key in filters:
            for rule in filters[key]:
                if "and" in rule or "or" in rule:
                    CDFs.get_cdfs_from_filters(rule, cdfs)
                else:
                    cdfs.append(rule)

        return cdfs

    @staticmethod
    def flatten_metrics(cdfs: list[CDF]) -> list[str]:
        """Takes CDFs, some with metrics and returns strings of each column-metric

        Args:
            cdfs (list[CDF]): list of report cdfs

        Returns:
            list[str]: list of strings for each column-metric combination
        """
        flattened_metrics = []
        for column in cdfs:
            metrics = column.get("metrics")
            if metrics:
                for metric in metrics:
                    flattened_metrics.append(f"{column['cdf']['column']}-{metric}")
            else:
                flattened_metrics.append(column["cdf"]["column"])
        return flattened_metrics

    @staticmethod
    def update_filter_columns(filters, old_value, new_value):
        if isinstance(filters, dict):
            # Recursively process dictionary
            for key, value in filters.items():
                if key == "column" and value == old_value:
                    filters[key] = new_value
                else:
                    CDFs.update_filter_columns(value, old_value, new_value)
        elif isinstance(filters, list):
            # Recursively process lists
            for item in filters:
                CDFs.update_filter_columns(item, old_value, new_value)
        return filters
