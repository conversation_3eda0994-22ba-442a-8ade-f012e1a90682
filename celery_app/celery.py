import logging
import sys
from pathlib import Path

from celery import Celery, Task
from celery.signals import (
    after_setup_logger,
    worker_ready,
    worker_shutdown,
)
from celery.utils.log import get_logger

from flask import Flask

from app.common.logger import JSONFormatter

from celery_app.probes.bootstraps import LivenessProbe


logger = get_logger(__name__)


def celery_init_app(app: Flask) -> Celery:
    class FlaskTask(Task):
        def __call__(self, *args: object, **kwargs: object) -> object:
            with app.app_context():
                return self.run(*args, **kwargs)

    celery_app = Celery(app.name, task_cls=FlaskTask)
    celery_app.steps["worker"].add(LivenessProbe)
    celery_app.config_from_object(app.config["CELERY"])
    celery_app.set_default()
    logger.setLevel(app.config["CELERY_LOG_LEVEL"])
    app.extensions["celery"] = celery_app
    return celery_app


@after_setup_logger.connect
def setup_logger(logger: logging.Logger, **_):
    logging_stream_handler = logging.StreamHandler()
    logging_stream_handler.setStream(stream=sys.stdout)
    logging_stream_handler.setFormatter(JSONFormatter())
    logger.handlers.clear()
    logger.addHandler(logging_stream_handler)


READINESS_FILE = Path("/tmp/celery_ready")


@worker_ready.connect
def worker_ready(**_):
    READINESS_FILE.touch()


@worker_shutdown.connect
def worker_shutdown(**_):
    READINESS_FILE.unlink(missing_ok=True)
