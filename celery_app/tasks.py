import re
import uuid
from contextlib import contextmanager
from datetime import datetime, timezone
from io import BytesIO
from os.path import join

from celery import shared_task
from celery.exceptions import Reject

from flask import current_app, g

from app.common.constants.languages import DEFAULT_LANGUAGE
from app.common.logger import logger
from app.common.user import User
from app.enums.export import Format, View
from app.enums.report import ReportKind
from app.enums.sse import SSEKind
from app.enums.task import TaskNames, TaskStatus
from app.schemas.scheduled_email import EmailChartSchema, ReportLinksSchema
from app.services.chart_service import ChartService
from app.services.di_token_service import DITokenService
from app.services.email_service import EmailService
from app.services.permission_service import PermissionService
from app.services.property_service import PropertyService
from app.services.report_service import ReportService
from app.services.s3_service import S3Service
from app.services.schedule_service import ScheduleService
from app.services.sse_service import SSEService
from app.services.stock_report_service import StockReportService
from app.services.user_service import UserService


def set_user_g(user_g: dict):
    g.request_id = user_g.get("request_id")
    g.property_id = user_g.get("property_id")
    g.organization_id = user_g.get("organization_id")
    g.property_ids = user_g.get("property_ids")
    g.api_key = user_g.get("api_key")
    g.access_token = user_g.get("access_token")
    g.user = User(
        id=user_g.get("user").get("id"),
        email=user_g.get("user").get("email"),
        admin=user_g.get("user").get("admin"),
        scopes=user_g.get("user").get("scopes"),
        token_type=user_g.get("user").get("token_type"),
        enabled_datasets=user_g.get("user").get("enabled_datasets"),
    )
    g.island = user_g.get("island")
    g.x_amzn_trace_id = user_g.get("x_amzn_trace_id")
    g.locale = user_g.get("locale")


def create_insights_link(entity_id: int, property_id: int, api_entity: str) -> str:
    sub_domain = PropertyService.get_property_sub_domain(property_id)
    base_url = f"https://{sub_domain}.{current_app.config['MFD_DOMAIN']}/"
    match api_entity:
        case "schedule":
            ui_entity = f"subscriptions?id={entity_id}"
        case ReportKind.StockReport.value:
            ui_entity = f"cloudbeds-reports/{entity_id}"
        case ReportKind.Report.value:
            ui_entity = f"saved-reports/{entity_id}"
        case _:
            ui_entity = f"{api_entity}/{entity_id}"

    return f"{base_url}connect/{property_id}#/insights/{ui_entity}"


def get_charts_for_email(title: str, charts: list):
    return [
        EmailChartSchema().dump(
            dict(
                report_title=title,
                chart=chart,
            )
        )
        for chart in charts
    ]


@contextmanager
def app_context():
    with current_app.app_context():
        yield


@shared_task(name="export_report_by_id")
def export_report_by_id(
    report_id: int,
    view: str,
    format: str,
    include_charts: bool,
    property_id: int,
    property_ids: list[int],
    user_g: dict,
):
    with app_context():
        try:
            set_user_g(user_g)
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORT_BY_ID.value,
                task_id=export_report_by_id.request.id,
                status=TaskStatus.STARTED.value,
            )
            report_description = ReportService.get_by_id(report_id, g.user).description
            report_export = ReportService.get_report_export_by_id(
                report_id,
                view,
                format,
                include_charts,
                property_id,
                g.organization_id,
                g.user,
                property_ids,
            )

            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORT_BY_ID.value,
                task_id=export_report_by_id.request.id,
                status=TaskStatus.RUNNING.value,
            )
            email_response = EmailService().send_export_link(
                subject=report_export["filename"],
                title=report_export["filename"],
                description=report_description,
                recipients=[g.user.email],
                created_at=report_export["generated_at"],
                download_link=report_export["url"],
                report_links=[
                    ReportLinksSchema().dump(
                        dict(
                            report_title=report_export["filename"],
                            report_url=create_insights_link(
                                report_id, property_id, ReportKind.Report.value
                            ),
                        )
                    ),
                ],
                charts=get_charts_for_email(
                    report_export.get("filename"), report_export.get("charts", [])
                ),
                report_kind=ReportKind.Report.value,
            )
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORT_BY_ID.value,
                task_id=export_report_by_id.request.id,
                status=TaskStatus.SUCCESS.value,
                result=dict(
                    url=report_export["url"],
                    format=report_export["format"],
                    view=report_export["view"],
                    filename=report_export["filename"],
                    generated_at=report_export["generated_at"].strftime(
                        "%Y-%m-%d%H:%M:%S"
                    ),
                ),
            )
        except Exception as exception:
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORT_BY_ID.value,
                task_id=export_report_by_id.request.id,
                status=TaskStatus.FAILURE.value,
            )
            logger.error(
                "Failed to export report by id", extra=dict(exception=str(exception))
            )
            raise_task_exception(export_report_by_id, exception)

        return (
            dict(
                url=report_export.get("url"),
                format=report_export.get("format"),
                view=report_export.get("view"),
                filename=report_export.get("filename"),
                generated_at=report_export.get("generated_at"),
                email_response=email_response,
            )
            if g.user.email
            else report_export
        )


@shared_task(name="export_report_by_query")
def export_report_by_query(
    report_query: dict,
    query: dict,
    property_id: int,
    user_g: dict,
):
    with app_context():
        try:
            set_user_g(user_g)
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORT_BY_QUERY.value,
                task_id=export_report_by_query.request.id,
                status=TaskStatus.STARTED.value,
            )
            report_export = ReportService.get_report_export(
                report_query, query, property_id, g.organization_id, g.user
            )
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORT_BY_QUERY.value,
                task_id=export_report_by_query.request.id,
                status=TaskStatus.SUCCESS.value,
                result=dict(
                    url=report_export["url"],
                    format=report_export["format"],
                    view=report_export["view"],
                    filename=report_export["filename"],
                    generated_at=report_export["generated_at"].strftime(
                        "%Y-%m-%d%H:%M:%S"
                    ),
                ),
            )
            return (
                dict(
                    url=report_export.get("url"),
                    format=report_export.get("format"),
                    view=report_export.get("view"),
                    filename=report_export.get("filename"),
                    generated_at=report_export.get("generated_at"),
                    email=EmailService().send_export_link(
                        subject=report_export["filename"],
                        title=report_export["filename"],
                        description="Exported Data Insights Report",
                        recipients=[g.user.email],
                        created_at=report_export["generated_at"],
                        download_link=report_export["url"],
                        charts=get_charts_for_email(
                            report_export.get("filename"),
                            report_export.get("charts", []),
                        ),
                        report_kind=ReportKind.Report.value,
                    ),
                )
                if g.user.email
                else report_export
            )
        except Exception as exception:
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORT_BY_QUERY.value,
                task_id=export_report_by_query.request.id,
                status=TaskStatus.FAILURE.value,
            )
            logger.error(
                "Failed to export report by query", extra=dict(exception=str(exception))
            )

            raise_task_exception(export_report_by_query, exception)


@shared_task(name="export_stock_report_by_id")
def export_stock_report_by_id(
    stock_report_id: int,
    property_id: int,
    query: dict,
    user_g: dict,
):
    with app_context():
        set_user_g(user_g)

        try:
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_STOCK_REPORT_BY_ID.value,
                task_id=export_stock_report_by_id.request.id,
                status=TaskStatus.STARTED.value,
            )
            stock_report_description = StockReportService.get_by_id(
                stock_report_id,
                g.user,
                property_id,
                include_cloudbeds=g.user.admin,
            ).description
            report_export = StockReportService.get_export_by_id(
                stock_report_id, property_id, g.organization_id, query, g.user
            )

            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_STOCK_REPORT_BY_ID.value,
                task_id=export_stock_report_by_id.request.id,
                status=TaskStatus.SUCCESS.value,
                result=dict(
                    url=report_export["url"],
                    format=query["format"],
                    view=query["view"],
                    filename=report_export["filename"],
                    generated_at=report_export["generated_at"].strftime(
                        "%Y-%m-%d%H:%M:%S"
                    ),
                ),
            )

            return (
                dict(
                    url=report_export.get("url"),
                    format=report_export.get("format"),
                    view=report_export.get("view"),
                    filename=report_export.get("filename"),
                    generated_at=report_export.get("generated_at"),
                    email=EmailService().send_export_link(
                        subject=report_export["filename"],
                        title=report_export["filename"],
                        description=stock_report_description,
                        recipients=[g.user.email],
                        created_at=report_export["generated_at"],
                        download_link=report_export["url"],
                        report_links=[
                            ReportLinksSchema().dump(
                                dict(
                                    report_title=report_export["filename"],
                                    report_url=create_insights_link(
                                        stock_report_id,
                                        property_id,
                                        ReportKind.StockReport.value,
                                    ),
                                )
                            ),
                        ],
                        charts=get_charts_for_email(
                            report_export.get("filename"),
                            report_export.get("charts", []),
                        ),
                        report_kind=ReportKind.StockReport.value,
                    ),
                )
                if g.user.email
                else report_export
            )

        except Exception as exception:
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_STOCK_REPORT_BY_ID.value,
                task_id=export_stock_report_by_id.request.id,
                status=TaskStatus.FAILURE.value,
            )
            logger.error(
                "Failed to export stock report by id",
                extra=dict(exception=str(exception)),
            )

            raise_task_exception(export_stock_report_by_id, exception)


@shared_task(name="export_stock_report_by_query")
def export_stock_report_by_query(
    stock_report_id: int,
    header_property_id: dict,
    query: dict,
    report_query: dict,
    user_g: dict,
):
    with app_context():
        try:
            set_user_g(user_g)
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_STOCK_REPORT_BY_QUERY.value,
                task_id=export_stock_report_by_query.request.id,
                status=TaskStatus.STARTED.value,
            )
            property_id = header_property_id.get("property_id")
            stock_report_description = StockReportService.get_by_id(
                stock_report_id,
                g.user,
                property_id,
            ).description
            report_query = StockReportService.get_query_report_by_id(
                report_query,
                stock_report_id,
                g.user,
                property_id,
                not PermissionService.is_whitelisted(
                    g.user.email,
                ),
            )

            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_STOCK_REPORT_BY_QUERY.value,
                task_id=export_stock_report_by_query.request.id,
                status=TaskStatus.RUNNING.value,
            )

            if (
                query.get("view") == View.Formatted.value
                and query.get("format") == Format.XLSX.value
            ):
                charts = ChartService.get_all_by_datasource_kind_and_id(
                    ReportKind.StockReport.value, stock_report_id
                )
                report_export = ReportService.get_report_export(
                    report_query,
                    query,
                    header_property_id.get("property_id"),
                    g.organization_id,
                    g.user,
                    charts,
                    ReportKind.StockReport.value,
                )
            else:
                report_export = ReportService.get_report_export(
                    report_query,
                    query,
                    header_property_id.get("property_id"),
                    g.organization_id,
                    g.user,
                    ReportKind.StockReport.value,
                )
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_STOCK_REPORT_BY_QUERY.value,
                task_id=export_stock_report_by_query.request.id,
                status=TaskStatus.SUCCESS.value,
                result=dict(
                    url=report_export["url"],
                    format=query["format"],
                    view=query["view"],
                    filename=report_export["filename"],
                    generated_at=report_export["generated_at"].strftime(
                        "%Y-%m-%d%H:%M:%S"
                    ),
                ),
            )
            return (
                dict(
                    url=report_export.get("url"),
                    format=report_export.get("format"),
                    view=report_export.get("view"),
                    filename=report_export.get("filename"),
                    generated_at=report_export.get("generated_at"),
                    email=EmailService().send_export_link(
                        subject=report_export["filename"],
                        title=report_export["filename"],
                        description=stock_report_description,
                        recipients=[g.user.email],
                        created_at=report_export["generated_at"],
                        download_link=report_export["url"],
                        charts=get_charts_for_email(
                            report_export.get("filename"),
                            report_export.get("charts", []),
                        ),
                        report_kind=ReportKind.StockReport.value,
                    ),
                )
                if g.user.email
                else report_export
            )
        except Exception as exception:
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_STOCK_REPORT_BY_QUERY.value,
                task_id=export_stock_report_by_query.request.id,
                status=TaskStatus.FAILURE.value,
            )
            logger.error(
                "Failed to export stock report by query",
                extra=dict(exception=str(exception)),
            )
            raise_task_exception(export_stock_report_by_query, exception)


@shared_task(name="export_reports_by_ids")
def export_reports_by_ids(
    report_ids: list[int],
    report_property_ids: dict,
    name: str,
    include_charts: bool,
    user_g: dict,
):
    with app_context():
        try:
            path = ["reports", "exports"]
            set_user_g(user_g)
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORTS_BY_IDS.value,
                task_id=export_reports_by_ids.request.id,
                status=TaskStatus.STARTED.value,
            )
            generated_at = datetime.now(timezone.utc)

            path = join(
                str(user_g.get("organization_id")),
                *path,
                View.Formatted.value,
                Format.XLSX.value,
                generated_at.strftime("%Y-%m-%d%H:%M:%S"),
                str(uuid.uuid4()),
            )

            filename = f"{name}.{Format.XLSX.value}"
            s3_service = S3Service(path, filename, Format.XLSX.value, False, None, None)
            object_key = f"{path}/{filename}"
            s3_service.s3.upload_fileobj(
                BytesIO(), Bucket=s3_service.bucket, Key=object_key
            )

            reports_to_export = ReportService.get_reports_to_export(
                report_ids,
                report_property_ids,
                include_charts,
                g.organization_id,
                g.user,
            )
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORTS_BY_IDS.value,
                task_id=export_reports_by_ids.request.id,
                status=TaskStatus.RUNNING.value,
            )

            workbook_data = ReportService.combine_exports(reports_to_export, s3_service)

            s3_service.s3.put_object(
                Body=BytesIO(workbook_data), Bucket=s3_service.bucket, Key=object_key
            )

            charts = []
            for report in reports_to_export:
                charts.extend(
                    get_charts_for_email(
                        report.get("report_title"), report.get("chart_pngs", [])
                    )
                )

            # Upload images to S3
            base_key = re.sub(f"{filename}|.xlsx", "", s3_service.key)
            for index, chart in enumerate(charts):
                chart_key = f"{base_key}chart-{index}.png"
                s3_service.s3.put_object(
                    Body=chart.get("chart"), Bucket=s3_service.bucket, Key=chart_key
                )

            url = s3_service.s3.generate_presigned_url(
                "get_object",
                Params={"Bucket": s3_service.bucket, "Key": object_key},
                ExpiresIn=604800,
            )
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORTS_BY_IDS.value,
                task_id=export_reports_by_ids.request.id,
                status=TaskStatus.SUCCESS.value,
                result=dict(
                    url=url,
                    format=Format.XLSX.value,
                    view=View.Formatted.value,
                    filename=filename,
                    generated_at=generated_at.strftime("%Y-%m-%d%H:%M:%S"),
                ),
            )

            return dict(
                url=url,
                format=Format.XLSX.value,
                view=View.Formatted.value,
                filename=filename,
                generated_at=generated_at,
            )
        except Exception as exception:
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EXPORT_REPORTS_BY_IDS.value,
                task_id=export_reports_by_ids.request.id,
                status=TaskStatus.FAILURE.value,
            )
            logger.error(
                "Failed to export report by ids", extra=dict(exception=str(exception))
            )
            raise_task_exception(export_reports_by_ids, exception)


@shared_task(name="email_export_report_by_ids")
def email_export_reports_by_ids(
    report_ids: list[int],
    subject: str,
    recipients: list[str],
    user_g: dict,
    property_ids: dict,
    view: str,
    format: str,
    schedule_id: int,
):
    logger.info(
        "Starting email_export_reports_by_ids",
        extra=dict(report_ids=report_ids, schedule_id=schedule_id),
    )
    with app_context():
        try:
            set_user_g(user_g)
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EMAIL_EXPORT_REPORT_BY_IDS.value,
                task_id=email_export_reports_by_ids.request.id,
                status=TaskStatus.STARTED.value,
            )

            schedule = ScheduleService.get_by_id(schedule_id)
            schedule_user = UserService.get_created_by(schedule.user_id)
            property_name = PropertyService.get_property_profile(
                schedule.property_id
            ).get("hotel_name")
            property_language = PropertyService.get_property(schedule.property_id).get(
                "lang", DEFAULT_LANGUAGE
            )
            schedule_url = create_insights_link(
                schedule_id, schedule.property_id, "schedule"
            )
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EMAIL_EXPORT_REPORT_BY_IDS.value,
                task_id=email_export_reports_by_ids.request.id,
                status=TaskStatus.RUNNING.value,
            )

            if (
                view == View.Formatted.value
                and format == Format.XLSX.value
                and len(report_ids) > 1
            ):
                logger.debug(
                    "Exporting multiple reports",
                    extra=dict(report_ids=report_ids, schedule_id=schedule_id),
                )
                reports_export = export_reports_by_ids(
                    report_ids=report_ids,
                    report_property_ids=property_ids,
                    name=subject,
                    include_charts=True,
                    user_g=user_g,
                )
                email_title = "Multiple Reports"
                reports = [
                    ReportService.get_by_id(report_id, g.user)
                    for report_id in report_ids
                ]
                report_links = [
                    ReportLinksSchema().dump(
                        dict(
                            report_title=report.title,
                            report_url=create_insights_link(
                                report.id, report.property_id, ReportKind.Report.value
                            ),
                        )
                    )
                    for report in reports
                ]
                charts = []  # No charts in the email body for multiple reports
                description = "Scheduled Report Pack"

            elif len(report_ids) == 1:
                logger.debug(
                    "Exporting single report",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
                logger.debug(
                    "Report Service get by ID",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
                report = ReportService.get_by_id(report_ids[0], g.user)
                property_id = report.property_id
                logger.debug(
                    "Report Service get by ID Completed",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
                logger.debug(
                    "Report Service get report export by ID",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
                reports_export = ReportService.get_report_export_by_id(
                    report_id=report_ids[0],
                    view=view,
                    export_format=format,
                    include_charts=True,
                    property_id=property_id,
                    organization_id=g.organization_id,
                    user=g.user,
                    property_ids=report.property_ids,
                )
                logger.debug(
                    "Report Service get report export by ID complete",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
                email_title = report.title
                description = report.description
                logger.debug(
                    "Report Links Schema dump",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
                report_links = [
                    ReportLinksSchema().dump(
                        dict(
                            report_title=report.title,
                            report_url=create_insights_link(
                                report.id, report.property_id, ReportKind.Report.value
                            ),
                        )
                    )
                ]
                logger.debug(
                    "Get Charts for Email",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
                charts = get_charts_for_email(
                    reports_export.get("filename"), reports_export.get("charts", [])
                )
                logger.debug(
                    "Get Charts for email completed",
                    extra=dict(
                        report_ids=report_ids,
                        schedule_id=schedule_id,
                        user=g.user.id,
                        enabled_datasets=g.user.enabled_datasets,
                    ),
                )
            else:
                logger.error(
                    "Failed to email export reports by ids",
                    extra=dict(
                        info=f"Invalid report export options, {len(report_ids)} reports {view} {format}, aborting."
                    ),
                )
                raise ValueError(
                    f"Invalid report export options, {len(report_ids)} reports"
                    f"{view} {format}, aborting."
                )

            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EMAIL_EXPORT_REPORT_BY_IDS.value,
                task_id=email_export_reports_by_ids.request.id,
                status=TaskStatus.SUCCESS.value,
                result=dict(
                    url=reports_export["url"],
                    format=Format.XLSX.value,
                    view=View.Formatted.value,
                    filename=reports_export.get("filename"),
                    generated_at=reports_export["generated_at"].strftime(
                        "%Y-%m-%d%H:%M:%S"
                    ),
                ),
            )

            require_login = bool(
                schedule.settings and schedule.settings.get("require_login")
            )
            download_token = DITokenService.mint_token(
                email_export_reports_by_ids.request.id,
                extra={"require_login": require_login, "schedule_id": schedule_id},
            )

            emails = list()
            for recipient in recipients:
                user_id = UserService.get_id_by_email(recipient)

                # If user exists
                if user_id:
                    user_property_ids = UserService.get_user_properties(user_id)
                    active_property_ids = (
                        PropertyService.get_active_property_ids(user_property_ids)
                        if user_property_ids
                        and schedule.property_id not in user_property_ids
                        else [schedule.property_id]
                    )

                # If user does not exist, send the email with the schedule property id
                else:
                    active_property_ids = [schedule.property_id]

                download_link = EmailService().build_download_link(
                    schedule.property_id,
                    active_property_ids,
                    download_token,
                    require_login,
                    property_language,
                )
                if download_link:
                    emails.append(
                        EmailService().send_export_link(
                            subject=subject,
                            title=email_title,
                            description=description,
                            recipients=[recipient],
                            created_at=reports_export["generated_at"],
                            download_link=download_link,
                            charts=charts,
                            report_links=report_links,
                            schedule_id=schedule_id,
                            schedule_user_email=schedule_user["email"],
                            schedule_url=schedule_url,
                            property_name=property_name,
                            report_kind=ReportKind.Report.value,
                            subscription_recipients=recipients,
                        )
                    )

            logger.info(
                "Successfully sent email_export_reports_by_ids",
                extra=dict(report_ids=report_ids, schedule_id=schedule_id),
            )
            return dict(
                url=reports_export.get("url"),
                format=reports_export.get("format"),
                view=reports_export.get("view"),
                filename=reports_export.get("filename"),
                generated_at=reports_export.get("generated_at"),
                emails=emails,
            )

        except Exception as exception:
            SSEService.publish(
                kind=SSEKind.TASK.value,
                name=TaskNames.EMAIL_EXPORT_REPORT_BY_IDS.value,
                task_id=email_export_reports_by_ids.request.id,
                status=TaskStatus.FAILURE.value,
            )
            logger.error(
                "Failed to email export reports by ids",
                extra=dict(exception=str(exception)),
            )
            raise_task_exception(email_export_reports_by_ids, exception)


def raise_task_exception(task, exception):
    exc_message = dict(
        exception=str(exception),
        exc_data=exception.data if hasattr(exception, "data") else {},
    )
    task.update_state(
        state="FAILURE", meta={"exc_type": "Exception", "exc_message": exc_message}
    )
    raise Reject(reason=str(exception), requeue=False)
